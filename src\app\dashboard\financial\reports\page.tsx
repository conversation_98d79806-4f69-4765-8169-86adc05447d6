"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  Download,
  Printer,
  Calendar,
  FileText,
  BarChart3,
  PieChart,
  TrendingUp,
  DollarSign,
  Calculator,
  Building,
  CreditCard,
  Wallet,
  Target,
  Eye,
  Filter,
  Trash2,
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

// Mock reports data
const availableReports = [
  {
    id: 'profit-loss',
    name: 'Profit & Loss Statement',
    description: 'Comprehensive income statement showing revenue, expenses, and net profit',
    category: 'Financial Statements',
    icon: Calculator,
    color: 'text-blue-500',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    lastGenerated: '2024-01-20',
    frequency: 'Monthly',
  },
  {
    id: 'balance-sheet',
    name: 'Balance Sheet',
    description: 'Statement of financial position showing assets, liabilities, and equity',
    category: 'Financial Statements',
    icon: Building,
    color: 'text-green-500',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    lastGenerated: '2024-01-20',
    frequency: 'Monthly',
  },
  {
    id: 'cash-flow',
    name: 'Cash Flow Statement',
    description: 'Analysis of cash inflows and outflows from operations, investing, and financing',
    category: 'Financial Statements',
    icon: Wallet,
    color: 'text-purple-500',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    lastGenerated: '2024-01-20',
    frequency: 'Monthly',
  },
  {
    id: 'revenue-analysis',
    name: 'Revenue Analysis',
    description: 'Detailed breakdown of revenue by service, customer, and time period',
    category: 'Performance Reports',
    icon: TrendingUp,
    color: 'text-emerald-500',
    bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
    lastGenerated: '2024-01-19',
    frequency: 'Weekly',
  },
  {
    id: 'expense-analysis',
    name: 'Expense Analysis',
    description: 'Comprehensive analysis of expenses by category and department',
    category: 'Performance Reports',
    icon: CreditCard,
    color: 'text-red-500',
    bgColor: 'bg-red-50 dark:bg-red-900/20',
    lastGenerated: '2024-01-19',
    frequency: 'Weekly',
  },
  {
    id: 'customer-profitability',
    name: 'Customer Profitability',
    description: 'Analysis of profit margins and revenue contribution by customer',
    category: 'Performance Reports',
    icon: Target,
    color: 'text-orange-500',
    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
    lastGenerated: '2024-01-18',
    frequency: 'Monthly',
  },
  {
    id: 'budget-variance',
    name: 'Budget vs Actual',
    description: 'Comparison of actual performance against budgeted figures',
    category: 'Budget Reports',
    icon: BarChart3,
    color: 'text-indigo-500',
    bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
    lastGenerated: '2024-01-17',
    frequency: 'Monthly',
  },
  {
    id: 'financial-ratios',
    name: 'Financial Ratios',
    description: 'Key financial ratios and performance indicators analysis',
    category: 'Analysis Reports',
    icon: PieChart,
    color: 'text-pink-500',
    bgColor: 'bg-pink-50 dark:bg-pink-900/20',
    lastGenerated: '2024-01-16',
    frequency: 'Quarterly',
  },
  {
    id: 'tax-summary',
    name: 'Tax Summary',
    description: 'Summary of tax obligations and payments for compliance reporting',
    category: 'Compliance Reports',
    icon: FileText,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
    lastGenerated: '2024-01-15',
    frequency: 'Quarterly',
  },
]

// This will be updated to use translations in the component

export default function FinancialReports() {
  const router = useRouter()
  const { t } = useI18n()
  const [selectedCategory, setSelectedCategory] = useState("All Reports")
  const [selectedPeriod, setSelectedPeriod] = useState("current")
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    // Set initial category to translated value
    if (selectedCategory === "All Reports") {
      setSelectedCategory(t('finance.financial.reports.categories.allReports'))
    }
  }, [])

  const reportCategories = [
    t('finance.financial.reports.categories.allReports'),
    t('finance.financial.reports.categories.financialStatements'),
    t('finance.financial.reports.categories.performanceReports'),
    t('finance.financial.reports.categories.budgetReports'),
    t('finance.financial.reports.categories.analysisReports'),
    t('finance.financial.reports.categories.complianceReports'),
  ]

  const getTranslatedReports = () => {
    return availableReports.map(report => {
      // Map report IDs to translation keys
      const translationKey = report.id.replace(/-/g, '')
      const nameKey = `finance.financial.reports.reportTypes.${translationKey}.name` as any
      const descKey = `finance.financial.reports.reportTypes.${translationKey}.description` as any
      
      return {
        ...report,
        name: t(nameKey) || report.name,
        description: t(descKey) || report.description,
        category: report.category === 'Financial Statements' ? t('finance.financial.reports.categories.financialStatements') :
                  report.category === 'Performance Reports' ? t('finance.financial.reports.categories.performanceReports') :
                  report.category === 'Budget Reports' ? t('finance.financial.reports.categories.budgetReports') :
                  report.category === 'Analysis Reports' ? t('finance.financial.reports.categories.analysisReports') :
                  report.category === 'Compliance Reports' ? t('finance.financial.reports.categories.complianceReports') :
                  report.category,
        frequency: report.frequency === 'Monthly' ? t('finance.financial.reports.frequencies.monthly') :
                   report.frequency === 'Weekly' ? t('finance.financial.reports.frequencies.weekly') :
                   report.frequency === 'Quarterly' ? t('finance.financial.reports.frequencies.quarterly') :
                   report.frequency === 'Annual' ? t('finance.financial.reports.frequencies.annual') :
                   report.frequency
      }
    })
  }

  const translatedReports = getTranslatedReports()

  const filteredReports = translatedReports.filter(report =>
    selectedCategory === t('finance.financial.reports.categories.allReports') || report.category === selectedCategory
  )

  const handleGenerateReport = (reportId: string) => {
    switch (reportId) {
      case 'profit-loss':
        router.push('/dashboard/financial/profit-loss')
        break
      case 'balance-sheet':
        router.push('/dashboard/financial/balance-sheet')
        break
      case 'cash-flow':
        router.push('/dashboard/financial/cash-flow')
        break
      case 'revenue-analysis':
        router.push('/dashboard/financial/revenue-analysis')
        break
      case 'expense-analysis':
        router.push('/dashboard/financial/expense-analysis')
        break
      case 'customer-profitability':
        router.push('/dashboard/financial/customer-profitability')
        break
      case 'budget-variance':
        router.push('/dashboard/financial/budget-variance')
        break
      case 'financial-ratios':
        router.push('/dashboard/financial/financial-ratios')
        break
      case 'tax-summary':
        router.push('/dashboard/financial/tax-summary')
        break
      default:
        alert(`وظيفة إنشاء تقرير ${reportId} ستكون متاحة قريباً`)
    }
  }

  const handleViewReport = (reportId: string) => {
    switch (reportId) {
      case 'profit-loss':
        router.push('/dashboard/financial/profit-loss')
        break
      case 'balance-sheet':
        router.push('/dashboard/financial/balance-sheet')
        break
      case 'cash-flow':
        router.push('/dashboard/financial/cash-flow')
        break
      case 'revenue-analysis':
        router.push('/dashboard/financial/revenue-analysis')
        break
      case 'expense-analysis':
        router.push('/dashboard/financial/expense-analysis')
        break
      case 'customer-profitability':
        router.push('/dashboard/financial/customer-profitability')
        break
      case 'budget-variance':
        router.push('/dashboard/financial/budget-variance')
        break
      case 'financial-ratios':
        router.push('/dashboard/financial/financial-ratios')
        break
      case 'tax-summary':
        router.push('/dashboard/financial/tax-summary')
        break
      default:
        alert(`وظيفة عرض تقرير ${reportId} ستكون متاحة قريباً`)
    }
  }

  const handleDownloadReport = (reportId: string) => {
    alert(`Download ${reportId} functionality would be implemented here`)
  }

  const handleDeleteReport = (reportId: string, reportName: string) => {
    if (confirm(t('finance.financial.reports.confirmDelete', { reportName }))) {
      // Here you would implement the actual delete functionality
      // For now, we'll just show a success message
      alert(t('finance.financial.reports.deleteSuccess', { reportName }))
      // In a real implementation, you would:
      // 1. Call an API to delete the report
      // 2. Update the local state to remove the report from the list
      // 3. Show a toast notification
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('finance.financial.reports.title')}</h2>
            <p className="text-muted-foreground">
              {t('finance.financial.reports.subtitle')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {reportCategories.map(category => (
                <SelectItem key={category} value={category}>{category}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current">{t('finance.financial.period.current')}</SelectItem>
              <SelectItem value="previous">{t('finance.financial.period.previous')}</SelectItem>
              <SelectItem value="ytd">{t('finance.financial.period.ytd')}</SelectItem>
              <SelectItem value="quarterly">{t('finance.financial.period.quarterly')}</SelectItem>
              <SelectItem value="annual">{t('finance.financial.period.annual')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="bg-slate-50/50 dark:bg-slate-900/20 rounded-lg p-6 border border-slate-200/60 dark:border-slate-700/60">
        <div className="grid gap-4 md:grid-cols-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{availableReports.length}</div>
            <div className="text-sm text-muted-foreground">{t('finance.financial.reports.stats.availableReports')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {availableReports.filter(r => r.frequency === 'Monthly').length}
            </div>
            <div className="text-sm text-muted-foreground">{t('finance.financial.reports.stats.monthlyReports')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {availableReports.filter(r => r.frequency === 'Weekly').length}
            </div>
            <div className="text-sm text-muted-foreground">{t('finance.financial.reports.stats.weeklyReports')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {availableReports.filter(r => r.frequency === 'Quarterly').length}
            </div>
            <div className="text-sm text-muted-foreground">{t('finance.financial.reports.stats.quarterlyReports')}</div>
          </div>
        </div>
      </div>

      {/* Reports Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredReports.map((report) => {
          const IconComponent = report.icon
          return (
            <Card key={report.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className={`${report.bgColor} rounded-t-lg`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <IconComponent className={`h-6 w-6 ${report.color}`} />
                    <div>
                      <CardTitle className="text-lg">{report.name}</CardTitle>
                      <Badge variant="outline" className="mt-1">
                        {report.category}
                      </Badge>
                    </div>
                  </div>
                  <Badge variant="secondary">
                    {report.frequency}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-4">
                <p className="text-sm text-muted-foreground mb-4">
                  {report.description}
                </p>
                <div className="flex items-center justify-between text-xs text-muted-foreground mb-4">
                  <span>{t('finance.financial.reports.lastGenerated')}</span>
                  <span>{new Date(report.lastGenerated).toLocaleDateString()}</span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    className="flex-1"
                    onClick={() => handleGenerateReport(report.id)}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    {t('finance.financial.buttons.generate')}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleViewReport(report.id)}
                  >
                    <Eye className="mr-1 h-4 w-4" />
                    {t('finance.financial.buttons.view')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Recent Reports Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-blue-500" />
            <span>{t('finance.financial.reports.recentReports')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('finance.financial.reports.table.reportName')}</TableHead>
                <TableHead>{t('finance.financial.reports.table.category')}</TableHead>
                <TableHead>{t('finance.financial.reports.table.generated')}</TableHead>
                <TableHead>{t('finance.financial.reports.table.frequency')}</TableHead>
                <TableHead>{t('finance.financial.reports.table.status')}</TableHead>
                <TableHead className="text-right">{t('finance.financial.reports.table.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {translatedReports.slice(0, 5).map((report) => (
                <TableRow key={report.id}>
                  <TableCell className="font-medium">{report.name}</TableCell>
                  <TableCell>{report.category}</TableCell>
                  <TableCell>{new Date(report.lastGenerated).toLocaleDateString()}</TableCell>
                  <TableCell>{report.frequency}</TableCell>
                  <TableCell>
                    <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      {t('finance.financial.reports.table.available')}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex space-x-1 justify-end">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewReport(report.id)}
                        title={t('finance.financial.buttons.view')}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDownloadReport(report.id)}
                        title={t('finance.financial.buttons.download')}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteReport(report.id, report.name)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                        title={t('finance.financial.buttons.delete')}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
