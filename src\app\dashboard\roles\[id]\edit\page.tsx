"use client"

import { useState, useEffect } from "react"
import { useRouter, useParams } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, CheckSquare, Square } from "lucide-react"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

interface Permission {
  id: string
  name: string
  nameAr?: string
  module: string
  action: string
  resource?: string
  description?: string
}

interface Role {
  id: string
  name: string
  nameAr?: string
  description?: string
  permissions: {
    permission: Permission
  }[]
}

export default function EditRolePage() {
  const { direction } = useI18n()
  const router = useRouter()
  const params = useParams()
  const roleId = params.id as string

  const [role, setRole] = useState<Role | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    description: '',
  })
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [selectedPermissions, setSelectedPermissions] = useState<Set<string>>(new Set())
  const [permissionsByModule, setPermissionsByModule] = useState<Record<string, Permission[]>>({})
  const [loading, setLoading] = useState(false)
  const [loadingData, setLoadingData] = useState(true)

  useEffect(() => {
    if (roleId) {
      loadRoleData()
      loadPermissions()
    }
  }, [roleId])

  const loadRoleData = async () => {
    try {
      const response = await fetch(`/api/roles/${roleId}`)
      if (response.ok) {
        const roleData = await response.json()
        setRole(roleData)
        setFormData({
          name: roleData.name || '',
          nameAr: roleData.nameAr || '',
          description: roleData.description || '',
        })
        
        // Set selected permissions
        const rolePermissionIds = new Set(roleData.permissions.map((rp: any) => rp.permission.id))
        setSelectedPermissions(rolePermissionIds)
      } else {
        toast.error('Failed to load role data')
        router.push('/dashboard/roles')
      }
    } catch (error) {
      console.error('Error loading role:', error)
      toast.error('Failed to load role data')
      router.push('/dashboard/roles')
    }
  }

  const loadPermissions = async () => {
    try {
      setLoadingData(true)
      const response = await fetch('/api/permissions?groupByModule=true')
      if (response.ok) {
        const data = await response.json()
        setPermissionsByModule(data)
        
        // Flatten permissions for easier access
        const allPermissions = Object.values(data).flat()
        setPermissions(allPermissions)
      }
    } catch (error) {
      console.error('Error loading permissions:', error)
      toast.error('Failed to load permissions')
    } finally {
      setLoadingData(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(permissionId)) {
        newSet.delete(permissionId)
      } else {
        newSet.add(permissionId)
      }
      return newSet
    })
  }

  const handleModuleToggle = (module: string) => {
    const modulePermissions = permissionsByModule[module] || []
    const modulePermissionIds = modulePermissions.map(p => p.id)
    const allSelected = modulePermissionIds.every(id => selectedPermissions.has(id))

    setSelectedPermissions(prev => {
      const newSet = new Set(prev)
      if (allSelected) {
        // Deselect all module permissions
        modulePermissionIds.forEach(id => newSet.delete(id))
      } else {
        // Select all module permissions
        modulePermissionIds.forEach(id => newSet.add(id))
      }
      return newSet
    })
  }

  const handleSelectAll = () => {
    setSelectedPermissions(new Set(permissions.map(p => p.id)))
  }

  const handleDeselectAll = () => {
    setSelectedPermissions(new Set())
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast.error('Role name is required')
      return
    }

    setLoading(true)
    try {
      const roleData = {
        ...formData,
        permissionIds: Array.from(selectedPermissions),
      }

      const response = await fetch(`/api/roles/${roleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(roleData),
      })

      if (response.ok) {
        toast.success(
          direction === 'rtl' ? 'تم تحديث الدور بنجاح' : 'Role updated successfully'
        )
        router.push('/dashboard/roles')
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to update role')
      }
    } catch (error) {
      console.error('Error updating role:', error)
      toast.error('Failed to update role')
    } finally {
      setLoading(false)
    }
  }

  const getModuleDisplayName = (module: string) => {
    const moduleNames: Record<string, { en: string; ar: string }> = {
      dashboard: { en: 'Dashboard', ar: 'لوحة التحكم' },
      customers: { en: 'Customers', ar: 'العملاء' },
      suppliers: { en: 'Suppliers', ar: 'الموردين' },
      products: { en: 'Products', ar: 'المنتجات' },
      categories: { en: 'Categories', ar: 'الفئات' },
      units: { en: 'Units', ar: 'الوحدات' },
      tasks: { en: 'Tasks', ar: 'المهام' },
      invoices: { en: 'Invoices', ar: 'الفواتير' },
      quotations: { en: 'Quotations', ar: 'عروض الأسعار' },
      purchases: { en: 'Purchases', ar: 'المشتريات' },
      expenses: { en: 'Expenses', ar: 'المصروفات' },
      financial: { en: 'Financial', ar: 'المالية' },
      employees: { en: 'Employees', ar: 'الموظفين' },
      reports: { en: 'Reports', ar: 'التقارير' },
      settings: { en: 'Settings', ar: 'الإعدادات' },
      users: { en: 'Users', ar: 'المستخدمين' },
      roles: { en: 'Roles', ar: 'الأدوار' },
      pos: { en: 'POS', ar: 'نقطة البيع' },
      leads: { en: 'Leads', ar: 'العملاء المحتملين' },
    }

    return direction === 'rtl' 
      ? moduleNames[module]?.ar || module 
      : moduleNames[module]?.en || module
  }

  if (loadingData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading role data...</p>
        </div>
      </div>
    )
  }

  if (!role) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-muted-foreground">Role not found</p>
          <Button onClick={() => router.push('/dashboard/roles')} className="mt-4">
            Back to Roles
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex-1 space-y-6 p-8 pt-6 ${direction === 'rtl' ? 'rtl' : 'ltr'}`} dir={direction}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className={direction === 'rtl' ? 'flex-row-reverse' : ''}
          >
            <ArrowLeft className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2 rotate-180' : 'mr-2'}`} />
            {direction === 'rtl' ? 'رجوع' : 'Back'}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">
              {direction === 'rtl' ? 'تعديل الدور' : 'Edit Role'}
            </h2>
            <p className="text-muted-foreground">
              {direction === 'rtl' 
                ? 'تعديل معلومات الدور وصلاحياته' 
                : 'Edit role information and permissions'
              }
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>
              {direction === 'rtl' ? 'معلومات الدور' : 'Role Information'}
            </CardTitle>
            <CardDescription>
              {direction === 'rtl' 
                ? 'تعديل المعلومات الأساسية للدور' 
                : 'Edit the basic information for the role'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  {direction === 'rtl' ? 'اسم الدور (إنجليزي)' : 'Role Name (English)'} *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={direction === 'rtl' ? 'أدخل اسم الدور' : 'Enter role name'}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="nameAr">
                  {direction === 'rtl' ? 'اسم الدور (عربي)' : 'Role Name (Arabic)'}
                </Label>
                <Input
                  id="nameAr"
                  value={formData.nameAr}
                  onChange={(e) => handleInputChange('nameAr', e.target.value)}
                  placeholder={direction === 'rtl' ? 'أدخل اسم الدور بالعربية' : 'Enter role name in Arabic'}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">
                {direction === 'rtl' ? 'الوصف' : 'Description'}
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder={direction === 'rtl' ? 'أدخل وصف الدور' : 'Enter role description'}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Permissions */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>
                  {direction === 'rtl' ? 'الصلاحيات' : 'Permissions'}
                </CardTitle>
                <CardDescription>
                  {direction === 'rtl' 
                    ? 'اختر الصلاحيات التي يمكن لهذا الدور الوصول إليها' 
                    : 'Select the permissions this role can access'
                  }
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="flex items-center gap-2"
                >
                  <CheckSquare className="h-4 w-4" />
                  {direction === 'rtl' ? 'تحديد الكل' : 'Select All'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleDeselectAll}
                  className="flex items-center gap-2"
                >
                  <Square className="h-4 w-4" />
                  {direction === 'rtl' ? 'إلغاء تحديد الكل' : 'Deselect All'}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {Object.entries(permissionsByModule).map(([module, modulePermissions]) => {
                const allSelected = modulePermissions.every(p => selectedPermissions.has(p.id))
                const someSelected = modulePermissions.some(p => selectedPermissions.has(p.id))

                return (
                  <div key={module} className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          id={`module-${module}`}
                          checked={allSelected}
                          onCheckedChange={() => handleModuleToggle(module)}
                          className={direction === 'rtl' ? 'ml-3' : 'mr-3'}
                        />
                        <Label 
                          htmlFor={`module-${module}`} 
                          className="text-lg font-semibold cursor-pointer"
                        >
                          {getModuleDisplayName(module)}
                        </Label>
                      </div>
                      <Badge variant={someSelected ? "default" : "secondary"}>
                        {modulePermissions.filter(p => selectedPermissions.has(p.id)).length}/{modulePermissions.length}
                      </Badge>
                    </div>
                    
                    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 ${direction === 'rtl' ? 'mr-6' : 'ml-6'}`}>
                      {modulePermissions.map((permission) => (
                        <div key={permission.id} className="flex items-center space-x-2 p-2 border rounded hover:bg-gray-50">
                          <Checkbox
                            id={permission.id}
                            checked={selectedPermissions.has(permission.id)}
                            onCheckedChange={() => handlePermissionToggle(permission.id)}
                            className={direction === 'rtl' ? 'ml-2' : 'mr-2'}
                          />
                          <Label 
                            htmlFor={permission.id} 
                            className="text-sm cursor-pointer flex-1"
                          >
                            {direction === 'rtl' && permission.nameAr 
                              ? permission.nameAr 
                              : permission.name
                            }
                          </Label>
                        </div>
                      ))}
                    </div>
                    <Separator />
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className={`flex gap-4 ${direction === 'rtl' ? 'flex-row-reverse' : ''}`}>
          <Button type="submit" disabled={loading} size="lg">
            {loading 
              ? (direction === 'rtl' ? 'جاري الحفظ...' : 'Updating...') 
              : (direction === 'rtl' ? 'تحديث الدور' : 'Update Role')
            }
          </Button>
          <Button 
            type="button" 
            variant="outline" 
            size="lg"
            onClick={() => router.back()}
          >
            {direction === 'rtl' ? 'إلغاء' : 'Cancel'}
          </Button>
        </div>
      </form>
    </div>
  )
}
