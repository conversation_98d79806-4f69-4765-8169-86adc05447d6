import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    // Get all payments with invoice and project info
    const payments = await prisma.payment.findMany({
      include: {
        invoice: {
          include: {
            task: {
              include: {
                project: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            }
          }
        },
        customer: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: { date: 'desc' }
    })

    // Get all invoices with payments for testing project
    const testingProjectInvoices = await prisma.invoice.findMany({
      where: {
        task: {
          project: {
            name: {
              contains: 'testing',
              mode: 'insensitive'
            }
          }
        }
      },
      include: {
        payments: true,
        task: {
          include: {
            project: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      allPayments: payments.map(payment => ({
        id: payment.id,
        amount: payment.amount,
        method: payment.method,
        date: payment.date,
        reference: payment.reference,
        invoiceNumber: payment.invoice.number,
        projectName: payment.invoice.task?.project?.name || 'No Project',
        customerName: payment.customer?.name || 'Unknown Customer'
      })),
      testingProjectInvoices: testingProjectInvoices.map(invoice => ({
        id: invoice.id,
        number: invoice.number,
        total: invoice.total,
        status: invoice.status,
        projectName: invoice.task?.project?.name || 'No Project',
        payments: invoice.payments.map(payment => ({
          id: payment.id,
          amount: payment.amount,
          method: payment.method,
          date: payment.date,
          reference: payment.reference
        }))
      })),
      summary: {
        totalPayments: payments.length,
        testingProjectInvoices: testingProjectInvoices.length,
        testingProjectPayments: testingProjectInvoices.reduce((sum, inv) => sum + inv.payments.length, 0)
      }
    })
  } catch (error) {
    console.error('Debug payments error:', error)
    return NextResponse.json({ error: 'Debug failed', details: error }, { status: 500 })
  }
} 