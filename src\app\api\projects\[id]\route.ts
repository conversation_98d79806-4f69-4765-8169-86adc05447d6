import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { Decimal } from '@prisma/client/runtime/library'

// Helper function to calculate actual cost from expenses
async function calculateActualCost(projectId: string): Promise<Decimal> {
  const expenses = await prisma.expense.findMany({
    where: { 
      projectId,
      status: { in: ['APPROVED', 'PAID'] } // Only count approved or paid expenses
    },
    select: { amount: true }
  })
  
  const total = expenses.reduce((sum, expense) => sum + Number(expense.amount), 0)
  return new Decimal(total)
}

// Helper function to calculate project progress from tasks
async function calculateProjectProgress(projectId: string): Promise<number> {
  const tasks = await prisma.task.findMany({
    where: { projectId },
    select: { status: true }
  })
  
  if (tasks.length === 0) return 0
  
  const completedTasks = tasks.filter(task => task.status === 'COMPLETED').length
  return Math.round((completedTasks / tasks.length) * 100)
}

// GET /api/projects/[id] - Get single project
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            company: true,
            mobile: true,
            phone: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        tasks: {
          select: {
            id: true,
            title: true,
            status: true,
            priority: true,
            createdAt: true,
          },
        },
        teamMembers: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            joinedAt: 'asc',
          },
        },
        _count: {
          select: {
            tasks: true,
            teamMembers: true,
          },
        },
      },
    })

    if (!project) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Calculate and update actual cost and progress automatically
    const actualCost = await calculateActualCost(id)
    const progress = await calculateProjectProgress(id)
    
    // Determine if status should be automatically updated
    let shouldUpdateStatus = false
    let newStatus = project.status
    
    // Auto-complete project when all tasks are done (progress = 100%)
    if (progress === 100 && project.status === 'IN_PROGRESS') {
      newStatus = 'COMPLETED'
      shouldUpdateStatus = true
    }
    
    // Update the project with calculated values if they've changed or status needs update
    if (Number(project.actualCost || 0) !== actualCost.toNumber() || 
        project.progress !== progress || 
        shouldUpdateStatus) {
      const updateData: any = {
        actualCost,
        progress,
        updatedAt: new Date()
      }
      
      if (shouldUpdateStatus) {
        updateData.status = newStatus
      }
      
      await prisma.project.update({
        where: { id },
        data: updateData
      })
      
      // Update the project object to return the latest values
      project.actualCost = actualCost
      project.progress = progress
      if (shouldUpdateStatus) {
        project.status = newStatus
      }
    }

    return NextResponse.json(project)
  } catch (error) {
    console.error('Error fetching project:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/projects/[id] - Update project
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const {
      code,
      name,
      nameAr,
      description,
      status,
      priority,
      startDate,
      endDate,
      budget,
      actualCost,
      progress,
      clientId,
      managerId,
      notes,
    } = body

    // Check if project exists
    const existingProject = await prisma.project.findUnique({
      where: { id },
    })

    if (!existingProject) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Calculate actual cost and progress automatically
    const calculatedActualCost = await calculateActualCost(id)
    const calculatedProgress = await calculateProjectProgress(id)

    // Prepare update data
    const updateData: any = {}
    
    if (code !== undefined) updateData.code = code
    if (name !== undefined) updateData.name = name
    if (nameAr !== undefined) updateData.nameAr = nameAr || null
    if (description !== undefined) updateData.description = description || null
    if (status !== undefined) updateData.status = status
    if (priority !== undefined) updateData.priority = priority
    if (startDate !== undefined) updateData.startDate = new Date(startDate)
    if (endDate !== undefined) updateData.endDate = endDate ? new Date(endDate) : null
    if (budget !== undefined) updateData.budget = budget ? parseFloat(budget) : null
    if (clientId !== undefined) updateData.clientId = clientId || null
    if (managerId !== undefined) updateData.managerId = managerId || null
    if (notes !== undefined) updateData.notes = notes || null

    // Use calculated values instead of manual input
    updateData.actualCost = calculatedActualCost
    updateData.progress = calculatedProgress

    // Auto-update status based on progress
    if (calculatedProgress === 100 && existingProject.status === 'IN_PROGRESS' && !status) {
      updateData.status = 'COMPLETED'
    } else if (status === 'COMPLETED') {
      // If status is being set to COMPLETED, ensure progress is 100%
      updateData.progress = 100
    }

    const project = await prisma.project.update({
      where: { id },
      data: updateData,
      include: {
        client: {
          select: {
            id: true,
            name: true,
            company: true,
            mobile: true,
            phone: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        tasks: {
          select: {
            id: true,
            title: true,
            status: true,
            priority: true,
            createdAt: true,
          },
        },
        teamMembers: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            joinedAt: 'asc',
          },
        },
        _count: {
          select: {
            tasks: true,
            teamMembers: true,
          },
        },
      },
    })

    return NextResponse.json(project)
  } catch (error) {
    console.error('Error updating project:', error)
    return NextResponse.json(
      { error: 'Failed to update project' },
      { status: 500 }
    )
  }
}

// DELETE /api/projects/[id] - Delete project
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if project exists
    const existingProject = await prisma.project.findUnique({
      where: { id },
    })

    if (!existingProject) {
      return NextResponse.json({ error: 'Project not found' }, { status: 404 })
    }

    // Delete project (this will cascade delete related records)
    await prisma.project.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Project deleted successfully' })
  } catch (error) {
    console.error('Error deleting project:', error)
    return NextResponse.json(
      { error: 'Failed to delete project' },
      { status: 500 }
    )
  }
}
