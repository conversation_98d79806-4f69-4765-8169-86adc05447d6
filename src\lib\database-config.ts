/**
 * Database Configuration Utility
 * Similar to PHP Capsule setup with environment-based configuration
 */

export interface DatabaseConfig {
  driver: string;
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  charset: string;
  collation: string;
  prefix: string;
  strict: boolean;
  engine: string | null;
  options: {
    persistent: boolean;
    connectionTimeout: number;
    prefetchSize: number;
    bufferSize: number;
    useBufferedQuery: boolean;
    sqlMode: string;
  };
}

/**
 * Get database configuration from environment variables
 * Similar to PHP env() function
 */
function env(key: string, defaultValue: any = null): any {
  const value = process.env[key];
  if (value === undefined) {
    return defaultValue;
  }
  
  // Convert string booleans to actual booleans
  if (value === 'true') return true;
  if (value === 'false') return false;
  
  // Convert string numbers to actual numbers
  if (!isNaN(Number(value)) && value !== '') {
    return Number(value);
  }
  
  return value;
}

/**
 * Database configuration similar to PHP Capsule setup
 */
export const databaseConfig: DatabaseConfig = {
  driver: env('DB_DRIVER', 'mysql'),
  host: env('DB_HOST', 'localhost'),
  port: env('DB_PORT', 3306),
  database: env('DB_DATABASE', 'print_next_db'),
  username: env('DB_USERNAME', 'root'),
  password: env('DB_PASSWORD', ''),
  charset: env('DB_CHARSET', 'utf8mb4'),
  collation: env('DB_COLLATION', 'utf8mb4_unicode_ci'),
  prefix: env('DB_PREFIX', ''),
  strict: false,
  engine: null,
  options: {
    // Performance: Enable persistent connections to reduce connection overhead
    persistent: env('DB_PERSISTENT_CONNECTIONS', true),
    
    // Performance: Connection timeout and prefetch settings
    connectionTimeout: env('DB_CONNECTION_TIMEOUT', 30),
    prefetchSize: env('DB_PREFETCH_SIZE', 1000),
    
    // Performance: Buffer optimization
    bufferSize: env('DB_BUFFER_SIZE', 1024 * 1024), // 1MB buffer
    useBufferedQuery: env('DB_USE_BUFFERED_QUERY', true),
    
    // Performance: Initialize with optimal SQL mode for production
    sqlMode: env('DB_SQL_MODE', 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'),
  },
};

/**
 * Generate DATABASE_URL from structured configuration
 * Similar to how PHP Capsule builds connection string
 */
export function generateDatabaseUrl(config: DatabaseConfig = databaseConfig): string {
  const { driver, username, password, host, port, database } = config;
  
  // Handle empty password
  const auth = password ? `${username}:${password}` : username;
  
  return `${driver}://${auth}@${host}:${port}/${database}`;
}

/**
 * Get Prisma connection options based on environment
 * Similar to PHP Capsule performance optimizations
 */
export function getPrismaOptions() {
  const isProduction = env('CI_ENVIRONMENT', env('NODE_ENV', 'development')) === 'production';
  const isDevelopment = env('CI_ENVIRONMENT', env('NODE_ENV', 'development')) === 'development';
  
  const baseOptions: any = {
    // Connection pool settings for performance
    datasources: {
      db: {
        url: env('DATABASE_URL', generateDatabaseUrl()),
      },
    },
  };
  
  // Development: Enable query logging for debugging
  if (isDevelopment && env('ENABLE_QUERY_LOG', true)) {
    baseOptions.log = ['query', 'error', 'info', 'warn'];
  }
  
  // Production optimizations
  if (isProduction) {
    // Connection pool optimization for production
    baseOptions.datasources.db.url += `?connection_limit=10&pool_timeout=20&socket_timeout=60`;
  }
  
  return baseOptions;
}

/**
 * Database connection health check
 * Similar to PHP connection validation
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    const { PrismaClient } = await import('@prisma/client');
    const prisma = new PrismaClient(getPrismaOptions());
    
    // Simple query to test connection
    await prisma.$queryRaw`SELECT 1`;
    await prisma.$disconnect();
    
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
}

/**
 * Initialize database with performance optimizations
 * Similar to PHP Capsule bootEloquent()
 */
export function initializeDatabase() {
  const isProduction = env('CI_ENVIRONMENT', env('NODE_ENV', 'development')) === 'production';
  const isDevelopment = env('CI_ENVIRONMENT', env('NODE_ENV', 'development')) === 'development';
  
  console.log(`🗄️  Database Configuration:`);
  console.log(`   Driver: ${databaseConfig.driver}`);
  console.log(`   Host: ${databaseConfig.host}:${databaseConfig.port}`);
  console.log(`   Database: ${databaseConfig.database}`);
  console.log(`   Charset: ${databaseConfig.charset}`);
  console.log(`   Environment: ${env('CI_ENVIRONMENT', env('NODE_ENV', 'development'))}`);
  
  if (isDevelopment) {
    console.log(`   Query Logging: ${env('ENABLE_QUERY_LOG', true) ? 'Enabled' : 'Disabled'}`);
  }
  
  if (isProduction) {
    console.log(`   Performance Optimizations: Enabled`);
    console.log(`   Persistent Connections: ${databaseConfig.options.persistent}`);
    console.log(`   Connection Timeout: ${databaseConfig.options.connectionTimeout}s`);
  }
}

/**
 * Export configuration for use in other parts of the application
 */
export default databaseConfig; 