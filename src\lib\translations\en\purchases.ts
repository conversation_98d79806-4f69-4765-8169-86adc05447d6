export const purchases = {
  // Page titles and descriptions
  title: "Purchase Orders",
  description: "Manage supplier purchase orders and procurement",
  addPurchase: "Add Purchase Order",
  createPurchase: "Create Purchase Order",
  editPurchase: "Edit Purchase Order",
  viewDetails: "View Details",
  duplicatePurchase: "Duplicate Purchase Order",
  
  // Purchase order properties
  purchaseOrderId: "Purchase Order ID",
  purchaseOrderNumber: "Purchase Order Number",
  purchaseDate: "Purchase Date",
  expectedDeliveryDate: "Expected Delivery Date",
  deliveryDate: "Delivery Date",
  supplier: "Supplier",
  selectSupplier: "Select Supplier",
  requestedBy: "Requested By",
  approvedBy: "Approved By",
  receivedBy: "Received By",
  priority: "Priority",
  status: "Status",
  paymentTerms: "Payment Terms",
  paymentMethod: "Payment Method",
  
  // Purchase order details
  items: "Items",
  itemDescription: "Description",
  product: "Product",
  quantity: "Quantity",
  unitPrice: "Unit Price",
  discount: "Discount",
  amount: "Amount",
  addItem: "Add Item",
  removeItem: "Remove Item",
  
  // Calculations
  subtotal: "Subtotal",
  tax: "Tax",
  taxRate: "Tax Rate",
  shippingCost: "Shipping Cost",
  totalAmount: "Total Amount",
  totalPaid: "Total Paid",
  totalDue: "Total Due",
  
  // Purchase order status
  draft: "Draft",
  pending: "Pending",
  approved: "Approved",
  ordered: "Ordered",
  partiallyReceived: "Partially Received",
  received: "Received",
  completed: "Completed",
  cancelled: "Cancelled",
  
  // Priority levels
  low: "Low",
  medium: "Medium",
  high: "High",
  urgent: "Urgent",
  
  // Actions
  saveDraft: "Save Draft",
  submit: "Submit",
  approve: "Approve",
  reject: "Reject",
  order: "Order",
  receive: "Receive",
  complete: "Complete",
  cancel: "Cancel",
  print: "Print",
  download: "Download",
  send: "Send",
  duplicate: "Duplicate",
  delete: "Delete",
  
  // Receiving
  receiveOrder: "Receive Order",
  partialReceive: "Partial Receive",
  receivedQuantity: "Received Quantity",
  remainingQuantity: "Remaining Quantity",
  qualityCheck: "Quality Check",
  receiptNotes: "Receipt Notes",
  
  // Payment information
  payOnDelivery: "Pay on Delivery",
  net30: "Net 30",
  net60: "Net 60",
  prepaid: "Prepaid",
  creditCard: "Credit Card",
  bankTransfer: "Bank Transfer",
  cash: "Cash",
  check: "Check",
  
  // Search and filters
  searchPurchases: "Search purchase orders...",
  searchPlaceholder: "Search by PO number, supplier, or product...",
  allStatus: "All Status",
  allPriorities: "All Priorities",
  filterByStatus: "Filter by Status",
  filterByPriority: "Filter by Priority",
  filterBySupplier: "Filter by Supplier",
  filterByRequestedBy: "Filter by Requested By",
  dateRange: "Date Range",
  
  // Statistics
  totalPurchases: "Total Purchase Orders",
  pendingPurchases: "Pending Orders",
  approvedPurchases: "Approved Orders",
  completedPurchases: "Completed Orders",
  totalPurchaseValue: "Total Purchase Value",
  averageOrderValue: "Average Order Value",
  
  // Messages and confirmations
  purchaseCreatedSuccessfully: "Purchase order created successfully",
  purchaseUpdatedSuccessfully: "Purchase order updated successfully",
  purchaseDeletedSuccessfully: "Purchase order deleted successfully",
  purchaseApprovedSuccessfully: "Purchase order approved successfully",
  purchaseRejectedSuccessfully: "Purchase order rejected successfully",
  purchaseOrderedSuccessfully: "Purchase order placed successfully",
  purchaseReceivedSuccessfully: "Purchase order received successfully",
  purchaseCompletedSuccessfully: "Purchase order completed successfully",
  purchaseCancelledSuccessfully: "Purchase order cancelled successfully",
  
  // Error messages
  failedToCreatePurchase: "Failed to create purchase order",
  failedToUpdatePurchase: "Failed to update purchase order",
  failedToDeletePurchase: "Failed to delete purchase order",
  failedToApprovePurchase: "Failed to approve purchase order",
  failedToRejectPurchase: "Failed to reject purchase order",
  failedToOrderPurchase: "Failed to place purchase order",
  failedToReceivePurchase: "Failed to receive purchase order",
  failedToCompletePurchase: "Failed to complete purchase order",
  failedToCancelPurchase: "Failed to cancel purchase order",
  
  // Confirmations
  confirmApprovePurchase: "Are you sure you want to approve this purchase order?",
  confirmRejectPurchase: "Are you sure you want to reject this purchase order?",
  confirmOrderPurchase: "Are you sure you want to place this purchase order?",
  confirmReceivePurchase: "Are you sure you want to mark this purchase order as received?",
  confirmCompletePurchase: "Are you sure you want to complete this purchase order?",
  confirmCancelPurchase: "Are you sure you want to cancel this purchase order?",
  confirmDeletePurchase: "Are you sure you want to delete this purchase order?",
  
  // Form placeholders
  purchaseOrderNumberPlaceholder: "Enter purchase order number...",
  selectSupplierPlaceholder: "Select supplier...",
  selectProductPlaceholder: "Select product...",
  notesPlaceholder: "Add notes or special instructions...",
  receiptNotesPlaceholder: "Add receipt notes...",
  
  // Empty states
  noPurchasesFound: "No purchase orders found",
  noPurchasesFoundSearch: "No purchase orders found matching your search",
  createFirstPurchase: "Create your first purchase order",
  noSupplier: "No supplier selected",
  noItems: "No items added",
  
  // Purchase order details
  purchaseDetails: "Purchase Order Details",
  purchaseInformation: "Purchase Information",
  itemsOrdered: "Items Ordered",
  deliveryInformation: "Delivery Information",
  paymentInformation: "Payment Information",
  approvalHistory: "Approval History",
  receiptHistory: "Receipt History",
  
  // Reports
  purchaseReport: "Purchase Report",
  supplierReport: "Supplier Report",
  purchaseAnalysis: "Purchase Analysis",
  costAnalysis: "Cost Analysis",
  deliveryPerformance: "Delivery Performance",
  
  // Validation
  supplierRequired: "Supplier is required",
  purchaseDateRequired: "Purchase date is required",
  expectedDeliveryDateRequired: "Expected delivery date is required",
  itemsRequired: "At least one item is required",
  quantityRequired: "Quantity is required",
  unitPriceRequired: "Unit price is required",
  invalidQuantity: "Quantity must be a positive number",
  invalidUnitPrice: "Unit price must be a positive number",
  deliveryDateBeforePurchaseDate: "Delivery date cannot be before purchase date",
  
  // Loading states
  loadingPurchases: "Loading purchase orders...",
  savingPurchase: "Saving purchase order...",
  deletingPurchase: "Deleting purchase order...",
  approvingPurchase: "Approving purchase order...",
  rejectingPurchase: "Rejecting purchase order...",
  orderingPurchase: "Placing purchase order...",
  receivingPurchase: "Receiving purchase order...",
  
  // Workflow
  approval: "Approval",
  approvalRequired: "Approval Required",
  approvalPending: "Approval Pending",
  approvedStatus: "Approved",
  rejectedStatus: "Rejected",
  workflow: "Workflow",
  
  // Delivery
  delivery: "Delivery",
  deliveryAddress: "Delivery Address",
  deliveryInstructions: "Delivery Instructions",
  shippingMethod: "Shipping Method",
  trackingNumber: "Tracking Number",
  
  // Quality control
  qualityControl: "Quality Control",
  qualityApproved: "Quality Approved",
  qualityRejected: "Quality Rejected",
  qualityNotes: "Quality Notes",
  
  // Inventory integration
  updateInventory: "Update Inventory",
  addToInventory: "Add to Inventory",
  inventoryUpdated: "Inventory updated successfully",
  
  // New keys from the code block
  dashboard: "Purchase Orders",
  dashboardDescription: "Manage your purchase orders and supplier relationships",
  createPurchaseOrder: "Create Purchase Order",
  totalOrders: "Total Orders",
  allPurchaseOrders: "All purchase orders",
  pendingOrders: "Pending Orders",
  pendingValue: "pending value",
  inTransit: "In Transit",
  ordersShipped: "Orders shipped",
  totalValue: "Total Value",
  ordersReceived: "orders received",
  searchPurchases: "Search purchases...",
  filterByStatus: "Filter by status",
  allStatuses: "All Statuses",
  draft: "Draft",
  pending: "Pending",
  approved: "Approved",
  ordered: "Ordered",
  received: "Received",
  cancelled: "Cancelled",
  purchaseNumber: "Purchase #",
  date: "Date",
  payoutStatus: "Payout Status",
  total: "Total",
  paid: "Paid",
  actions: "Actions",
  noPurchasesFoundMatchingSearch: "No purchases found matching your search.",
  noPurchasesFoundCreateFirstPurchaseOrder: "No purchases found. Create your first purchase order!",
  unknownSupplier: "Unknown Supplier",
  openMenu: "Open menu",
  editPurchase: "Edit Purchase",
  printPDF: "Print/PDF",
  emailPurchase: "Email Purchase",
  createPayout: "Create Payout",
  markAsReceived: "Mark as Received",
  viewAndManagePurchaseOrderDetails: "View and manage purchase order details",
  orderDate: "Order Date",
  expectedDelivery: "Expected Delivery",
  purchaseItems: "Purchase Items",
  description: "Description",
  notes: "Notes",
  purchaseSummary: "Purchase Summary",
  taxAmount: "Tax Amount",
  discountAmount: "Discount Amount",
  updating: "Updating...",
  updatePurchase: "Update Purchase",
  cancel: "Cancel",

  // Create form specific keys
  create: {
    title: "Create Purchase Order",
    subtitle: "Create a new purchase order for your suppliers",
    saveDraft: "Save Draft",
    createPurchaseOrder: "Create Purchase Order",
    purchaseOrderInformation: "Purchase Order Information",
    supplier: "Supplier",
    expectedDeliveryDate: "Expected Delivery Date",
    purchaseOrderStatus: "Purchase Order Status",
    notes: "Notes",
    pleaseDeliverDuringBusinessHours: "Please deliver during business hours.",
    purchaseItems: "Purchase Items",
    addItem: "Add Item",
    description: "Description",
    product: "Product",
    quantity: "Quantity",
    unitPrice: "Unit Price",
    total: "Total",
    action: "Action",
    itemDescription: "Item description...",
    discountSettings: "Discount Settings",
    purchaseOrderSummary: "Purchase Order Summary",
    subtotal: "Subtotal",
    discount: "Discount",
    total: "Total",
    items: "Items",
    status: "Status",
    saveAsDraft: "Save as Draft",
    noSupplierFound: "No supplier found",
    addNewSupplier: "Add New Supplier",
    supplierName: "Supplier Name",
    enterSupplierName: "Enter supplier name...",
    mobileNumber: "Mobile Number",
    email: "Email",
    address: "Address",
    supplierAddress: "Supplier address...",
    addSupplier: "Add Supplier",
    supplierExists: "Supplier exists",
    selectSupplierFromDropdown: "Please select supplier from dropdown.",
    // Placeholders for search and inputs
    searchSuppliersPlaceholder: "Search suppliers by name or mobile...",
    searchProductsPlaceholder: "Search products...",
    selectSupplierPlaceholder: "Select supplier...",
    selectProductPlaceholder: "Select product",
    noProductFound: "No product found.",
    quantityPlaceholder: "1",
    unitPricePlaceholder: "0.000",
    discountPlaceholder: "0.00 OMR",
    discountPercentagePlaceholder: "0.00%",
    // VAT and discount info
    enterPercentage: "Enter percentage (0-100%)",
    enterAmountInOMR: "Enter amount in OMR",
    defaultRateFromSettings: "Default rate from settings",
    vatRatePercent: "VAT Rate (%)",
    // Status options
    statusOptions: {
      draft: "Draft",
      pending: "Pending", 
      approved: "Approved",
      ordered: "Ordered"
    },
    // Discount type options
    discountTypeOptions: {
      amount: "Amount",
      percentage: "Percentage"
    }
  }
};