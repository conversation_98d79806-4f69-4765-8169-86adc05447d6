import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { whatsappService } from '@/lib/whatsapp'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const invoiceId = searchParams.get('invoiceId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const where: Record<string, unknown> = {}
    if (invoiceId) {
      where.invoiceId = invoiceId
    }

    const [payments, total] = await Promise.all([
      prisma.payment.findMany({
        where,
        skip,
        take: limit,
        include: {
          invoice: {
            include: {
              customer: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.payment.count({ where }),
    ])

    return NextResponse.json({
      payments,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      invoiceId,
      amount,
      method,
      date,
      reference,
      notes,
    } = body

    if (!invoiceId || !amount || !method || amount <= 0) {
      return NextResponse.json(
        { error: 'Invoice ID, amount, and payment method are required' },
        { status: 400 }
      )
    }

    // Get the invoice
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        customer: true,
        payments: true,
      },
    })

    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // Calculate current amount paid
    const currentAmountPaid = invoice.payments.reduce(
      (sum, payment) => sum + parseFloat(payment.amount.toString()),
      0
    )

    const newAmountPaid = currentAmountPaid + parseFloat(amount)
    const balance = parseFloat(invoice.total.toString()) - newAmountPaid

    // Determine new invoice status
    let newStatus = invoice.status
    if (balance <= 0) {
      newStatus = 'PAID'
    } else if (newAmountPaid > 0 && balance > 0) {
      newStatus = 'PARTIAL'
    }

    // Create the payment record
    const payment = await prisma.payment.create({
      data: {
        amount: parseFloat(amount),
        method,
        date: date ? new Date(date) : new Date(),
        reference: reference || null,
        notes: notes || null,
        invoiceId,
        customerId: invoice.customerId || '', // Add the required customerId
      },
      include: {
        invoice: {
          include: {
            customer: true,
          },
        },
      },
    })

    // Update invoice status
    await prisma.invoice.update({
      where: { id: invoiceId },
      data: { status: newStatus },
    })

    // Send WhatsApp notification to customer if payment is recorded
    if (invoice.customer?.phone) {
      try {
        await whatsappService.sendPaymentConfirmation(
          invoice.customer.phone,
          invoice.number,
          parseFloat(amount),
          balance <= 0 ? 'PAID' : 'PARTIAL',
          'en'
        )
      } catch (error) {
        console.error('Error sending WhatsApp notification:', error)
        // Don't fail the payment recording if notification fails
      }
    }

    return NextResponse.json(payment, { status: 201 })
  } catch (error) {
    console.error('Error creating payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
