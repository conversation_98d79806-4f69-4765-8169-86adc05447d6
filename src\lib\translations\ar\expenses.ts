export const expenses = {
  // Page titles and descriptions
  title: "المصروفات",
  description: "إدارة مصروفات الشركة",
  manageTypes: "إدارة أنواع المصروفات",
  addExpense: "إضافة مصروف",
  editExpense: "تعديل المصروف",
  viewExpense: "عرض المصروف",
  
  // Search and filters
  searchExpenses: "البحث في المصروفات",
  status: "الحالة",
  allStatuses: "جميع الحالات",
  pending: "في الانتظار",
  approved: "موافق عليه",
  rejected: "مرفوض",
  paid: "مدفوع",
  type: "النوع",
  allCategories: "جميع الفئات",
  
  // Statistics
  totalExpenses: "إجمالي المصروفات",
  pendingExpenses: "المصروفات المعلقة",
  
  // Statistics text
  expensesLabel: "مصروفات",
  averageLabel: "المتوسط:",
  pendingLabel: "معلق",
  approvedLabel: "موافق عليه",
  paidLabel: "مدفوع",
  noReceipt: "لا يوجد إيصال",
  
  // Status translations
  statusLabels: {
    PENDING: "في الانتظار",
    APPROVED: "موافق عليه", 
    REJECTED: "مرفوض",
    PAID: "مدفوع"
  },
  
  // Payment method translations
  paymentMethodLabels: {
    CASH: "نقد",
    CARD: "بطاقة",
    BANK_TRANSFER: "تحويل بنكي", 
    CHEQUE: "شيك",
    CHECK: "شيك",
    OTHER: "أخرى"
  },
  
  // Expense details
  expense: "المصروف",
  paymentMethod: "طريقة الدفع",
  receipt: "الإيصال",
  
  // Actions
  approve: "موافقة",
  reject: "رفض",
  markAsPaid: "تحديد كمدفوع",

  // View expense page
  view: {
    title: "المصروف",
    createdOn: "تم الإنشاء في",
    back: "رجوع",
    actions: "الإجراءات",
    editExpense: "تعديل المصروف",
    downloadReceipt: "تحميل الإيصال",
    approve: "موافقة",
    reject: "رفض",
    markAsPaid: "تحديد كمدفوع",
    delete: "حذف",
    
    // Overview cards
    amount: "المبلغ",
    expenseAmount: "مبلغ المصروف",
    status: "الحالة",
    currentStatus: "الحالة الحالية",
    type: "النوع",
    expenseCategory: "فئة المصروف",
    method: "الطريقة",
    paymentMethod: "طريقة الدفع",
    
    // Details section
    expenseDetails: "تفاصيل المصروف",
    description: "الوصف",
    date: "التاريخ", 
    createdBy: "تم الإنشاء بواسطة",
    expenseType: "نوع المصروف",
    project: "المشروع",
    code: "الكود",
    notes: "الملاحظات",
    receipt: "الإيصال",
    
    // Timeline
    activityTimeline: "الجدول الزمني للأنشطة",
    expenseCreated: "تم إنشاء المصروف",
    expenseUpdated: "تم تحديث المصروف",
    by: "بواسطة",
    
    // Loading and error states
    loadingExpense: "جاري تحميل المصروف...",
    expenseNotFound: "المصروف غير موجود",
    
    // Alert messages
    expenseApprovedSuccess: "✅ تم الموافقة على المصروف بنجاح!",
    expenseApprovedError: "❌ فشل في الموافقة على المصروف",
    pleaseEnterRejectionReason: "يرجى إدخال سبب الرفض:",
    expenseRejected: "✅ تم رفض المصروف",
    expenseRejectedError: "❌ فشل في رفض المصروف",
    expenseMarkedAsPaidSuccess: "✅ تم تحديد المصروف كمدفوع!",
    expenseMarkedAsPaidError: "❌ فشل في تحديد المصروف كمدفوع",
    confirmDeleteExpense: "هل أنت متأكد من حذف المصروف",
    expenseDeletedSuccess: "✅ تم حذف المصروف بنجاح!",
    expenseDeletedError: "❌ فشل في حذف المصروف",
    receiptDownloadInfo: "سيتم تنفيذ وظيفة تحميل الإيصال هنا",
    noReceiptAvailable: "لا يوجد إيصال متاح لهذا المصروف"
  },

  // Edit expense page  
  edit: {
    title: "تعديل المصروف",
    subtitle: "تحديث المصروف",
    back: "رجوع",
    
    // Loading and error states
    loadingExpense: "جاري تحميل المصروف...",
    expenseNotFound: "المصروف غير موجود",
    expenseNotFoundDescription: "المصروف الذي تبحث عنه غير موجود.",
    backToExpenses: "رجوع إلى المصروفات",
    
    // Form sections
    expenseDetails: "تفاصيل المصروف",
    summary: "الملخص",
    actions: "الإجراءات",
    
    // Form fields
    date: "التاريخ",
    dateRequired: "التاريخ *",
    amount: "المبلغ",
    amountRequired: "المبلغ *",
    amountPlaceholder: "0.000",
    description: "الوصف",
    descriptionRequired: "الوصف *",
    descriptionPlaceholder: "أدخل وصف المصروف",
    expenseType: "نوع المصروف",
    expenseTypeRequired: "نوع المصروف *",
    selectExpenseTypePlaceholder: "اختر نوع المصروف",
    paymentMethodRequired: "طريقة الدفع *",
    status: "الحالة",
    notes: "الملاحظات",
    notesPlaceholder: "ملاحظات إضافية (اختياري)",
    
    // Summary labels
    expenseNumber: "رقم المصروف:",
    summaryAmount: "المبلغ:",
    summaryType: "النوع:",
    summaryPayment: "الدفع:",
    summaryStatus: "الحالة:",
    notSelected: "غير محدد",
    
    // Action buttons
    updateExpense: "تحديث المصروف",
    updating: "جاري التحديث...",
    cancel: "إلغاء",
    
    // Validation messages
    pleaseEnterDescription: "يرجى إدخال الوصف",
    pleaseEnterValidAmount: "يرجى إدخال مبلغ صحيح", 
    pleaseSelectExpenseType: "يرجى اختيار نوع المصروف",
    
    // Success/error messages
    expenseUpdatedSuccess: "تم تحديث المصروف بنجاح!",
    expenseUpdateError: "فشل في تحديث المصروف. يرجى المحاولة مرة أخرى.",
    failedToLoadExpenseData: "فشل في تحميل بيانات المصروف"
  },

  // Create form specific keys
  create: {
    title: "إضافة مصروف جديد",
    subtitle: "تسجيل مصروف عمل جديد",
    back: "رجوع",
    expenseDetails: "تفاصيل المصروف",
    date: "التاريخ",
    dateRequired: "التاريخ *",
    amount: "المبلغ",
    amountRequired: "المبلغ *",
    amountPlaceholder: "0.00",
    description: "الوصف",
    descriptionRequired: "الوصف *",
    descriptionPlaceholder: "أدخل وصف المصروف",
    expenseType: "نوع المصروف",
    expenseTypeRequired: "نوع المصروف *",
    selectExpenseTypePlaceholder: "اختر نوع المصروف",
    paymentMethodRequired: "طريقة الدفع *",
    notes: "الملاحظات",
    notesPlaceholder: "ملاحظات إضافية (اختياري)",
    receiptSection: "الإيصال",
    uploadReceipt: "رفع الإيصال",
    receiptFormats: "PNG, JPG, GIF أو PDF حتى 5 ميجابايت",
    receiptAttached: "مرفق",
    receiptNotAttached: "غير مرفق",
    summary: "الملخص",
    summaryAmount: "المبلغ:",
    summaryType: "النوع:",
    summaryPayment: "الدفع:",
    summaryReceipt: "الإيصال:",
    typeNotSelected: "غير محدد",
    actions: "الإجراءات",
    saveAsDraft: "حفظ كمسودة",
    submitForApproval: "إرسال للموافقة",
    // Payment method options
    paymentMethods: {
      cash: "نقد",
      card: "بطاقة", 
      bankTransfer: "تحويل بنكي",
      cheque: "شيك"
    },
    // Expense types
    expenseTypes: {
      officeSupplies: "مستلزمات المكتب",
      travel: "السفر والنقل",
      utilities: "المرافق", 
      marketing: "التسويق والإعلان",
      equipment: "المعدات والصيانة",
      professional: "الخدمات المهنية",
      meals: "الوجبات والترفيه",
      software: "البرمجيات والاشتراكات",
      insurance: "التأمين",
      rent: "الإيجار والمرافق"
    },
    // Validation messages
    pleaseEnterDescription: "يرجى إدخال الوصف",
    pleaseEnterValidAmount: "يرجى إدخال مبلغ صحيح",
    pleaseSelectExpenseType: "يرجى اختيار نوع المصروف",
    fileSizeLimit: "يجب أن يكون حجم الملف أقل من 5 ميجابايت",
    allowedFileTypes: "مسموح فقط بملفات JPEG و PNG و GIF و PDF",
    // Success messages
    expenseSavedAsDraft: "تم حفظ المصروف كمسودة بنجاح!",
    expenseSubmittedForApproval: "تم إرسال المصروف للموافقة بنجاح!",
    // Error messages
    failedToSaveExpense: "فشل في حفظ المصروف. يرجى المحاولة مرة أخرى."
  },

  // Expense Types page
  types: {
    title: "أنواع المصروفات",
    subtitle: "إدارة فئات المصروفات لتتبع أفضل للمصروفات",
    addExpenseType: "إضافة نوع مصروف",
    editExpenseType: "تعديل نوع المصروف",
    addNewExpenseType: "إضافة نوع مصروف جديد",
    searchExpenseTypes: "البحث في أنواع المصروفات...",
    // Form fields
    name: "الاسم",
    nameRequired: "الاسم *",
    namePlaceholder: "أدخل اسم نوع المصروف",
    arabicName: "الاسم العربي",
    arabicNamePlaceholder: "أدخل الاسم العربي (اختياري)",
    description: "الوصف",
    descriptionPlaceholder: "أدخل الوصف (اختياري)",
    active: "نشط",
    // Table headers
    status: "الحالة",
    created: "تم الإنشاء",
    actions: "الإجراءات",
    // Status badges
    activeStatus: "نشط",
    inactiveStatus: "غير نشط",
    noDescription: "لا يوجد وصف",
    // Actions
    edit: "تعديل",
    activate: "تفعيل",
    deactivate: "إلغاء تفعيل",
    delete: "حذف",
    cancel: "إلغاء",
    create: "إنشاء",
    update: "تحديث",
    openMenu: "فتح القائمة",
    // Validation messages
    pleaseEnterExpenseTypeName: "يرجى إدخال اسم نوع المصروف",
    // Confirmation messages
    confirmDeleteExpenseType: "هل أنت متأكد من حذف",
    // Success messages
    expenseTypeCreatedSuccessfully: "تم الإنشاء بنجاح!",
    expenseTypeUpdatedSuccessfully: "تم التحديث بنجاح!",
    expenseTypeDeletedSuccessfully: "تم الحذف بنجاح!",
    expenseTypeActivatedSuccessfully: "تم التفعيل بنجاح!",
    expenseTypeDeactivatedSuccessfully: "تم إلغاء التفعيل بنجاح!",
    // Error messages
    failedToCreateExpenseType: "فشل في إنشاء نوع المصروف. يرجى المحاولة مرة أخرى.",
    failedToUpdateExpenseType: "فشل في تحديث نوع المصروف. يرجى المحاولة مرة أخرى.",
    failedToDeleteExpenseType: "فشل في حذف نوع المصروف. قد يحتوي على مصروفات مرتبطة.",
    failedToUpdateExpenseTypeStatus: "فشل في تحديث حالة نوع المصروف. يرجى المحاولة مرة أخرى."
  }
}; 