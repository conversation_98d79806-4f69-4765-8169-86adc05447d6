"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Upload, X, FileImage, File, CheckCircle } from "lucide-react"

export default function CompleteTaskPage() {
  const router = useRouter()
  const params = useParams()
  const taskId = params.id as string

  const [task, setTask] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [completionNotes, setCompletionNotes] = useState("")
  const [actualHours, setActualHours] = useState("")
  const [files, setFiles] = useState<File[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([])

  useEffect(() => {
    fetchTask()
  }, [taskId])

  const fetchTask = async () => {
    try {
      const response = await fetch(`/api/tasks/${taskId}`)
      if (response.ok) {
        const data = await response.json()
        setTask(data)
        setActualHours(data.estimatedHours?.toString() || "")
      } else {
        console.error('Failed to fetch task')
        router.push('/dashboard/tasks')
      }
    } catch (error) {
      console.error('Error fetching task:', error)
      router.push('/dashboard/tasks')
    } finally {
      setLoading(false)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || [])
    setFiles(prev => [...prev, ...selectedFiles])
  }

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }

  const uploadFiles = async () => {
    if (files.length === 0) return []

    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        const data = await response.json()
        return data.files
      } else {
        throw new Error('Failed to upload files')
      }
    } catch (error) {
      console.error('Error uploading files:', error)
      throw error
    }
  }

  const handleComplete = async () => {
    try {
      setSubmitting(true)

      // Upload files first
      let attachments = []
      if (files.length > 0) {
        attachments = await uploadFiles()
      }

      // Update task status with completion data
      const response = await fetch(`/api/tasks/${taskId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: 'COMPLETED',
          completionNotes,
          actualHours: actualHours ? parseFloat(actualHours) : null,
          attachments,
        }),
      })

      if (response.ok) {
        alert(`✅ Task "${task.title}" completed successfully!`)
        router.push('/dashboard/tasks')
      } else {
        alert('Failed to complete task')
      }
    } catch (error) {
      console.error('Error completing task:', error)
      alert('Error completing task')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>Loading task...</div>
        </div>
      </div>
    )
  }

  if (!task) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>Task not found</div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Tasks
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Complete Task</h2>
          <p className="text-muted-foreground">
            Mark task as completed and upload work files
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Task Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Task Details
            </CardTitle>
            <CardDescription>Review task information before completion</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium">Title</Label>
              <div className="text-lg font-semibold">{task.title}</div>
            </div>
            
            <div>
              <Label className="text-sm font-medium">Description</Label>
              <div className="text-sm text-muted-foreground">{task.description || 'No description'}</div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Customer</Label>
                <div className="text-sm">{task.customer?.name || 'No customer'}</div>
              </div>
              <div>
                <Label className="text-sm font-medium">Priority</Label>
                <Badge variant="outline">{task.priority}</Badge>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Estimated Hours</Label>
                <div className="text-sm">{task.estimatedHours || 'Not specified'}</div>
              </div>
              <div>
                <Label className="text-sm font-medium">Assigned To</Label>
                <div className="text-sm">{task.assignedTo?.name || 'Unassigned'}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Completion Form */}
        <Card>
          <CardHeader>
            <CardTitle>Completion Details</CardTitle>
            <CardDescription>Add completion notes and upload work files</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="actualHours">Actual Hours Spent</Label>
              <Input
                id="actualHours"
                type="number"
                step="0.5"
                placeholder="Enter actual hours"
                value={actualHours}
                onChange={(e) => setActualHours(e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="completionNotes">Completion Notes</Label>
              <Textarea
                id="completionNotes"
                placeholder="Add notes about the completed work..."
                value={completionNotes}
                onChange={(e) => setCompletionNotes(e.target.value)}
                rows={4}
              />
            </div>

            <div>
              <Label>Upload Work Files</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-2">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="text-blue-600 hover:text-blue-500">Upload files</span>
                    <span className="text-gray-500"> or drag and drop</span>
                  </label>
                  <input
                    id="file-upload"
                    type="file"
                    multiple
                    accept="image/*,.pdf,.doc,.docx"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Images, PDF, DOC up to 10MB each
                </p>
              </div>

              {/* File List */}
              {files.length > 0 && (
                <div className="mt-4 space-y-2">
                  <Label>Selected Files:</Label>
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center space-x-2">
                        {file.type.startsWith('image/') ? (
                          <FileImage className="h-4 w-4 text-blue-500" />
                        ) : (
                          <File className="h-4 w-4 text-gray-500" />
                        )}
                        <span className="text-sm">{file.name}</span>
                        <span className="text-xs text-gray-500">
                          ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex space-x-2 pt-4">
              <Button
                onClick={handleComplete}
                disabled={submitting}
                className="flex-1"
              >
                {submitting ? 'Completing...' : 'Complete Task'}
              </Button>
              <Button
                variant="outline"
                onClick={() => router.back()}
                disabled={submitting}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
