/**
 * Shared invoice storage utility for POS and Invoice modules
 */

import { getStorageItem, setStorageItem } from './storage'

export interface Invoice {
  id: string
  number: string
  date: string
  dueDate: string
  customer: string
  customerData?: any
  status: 'PAID' | 'UNPAID' | 'PARTIAL' | 'OVERDUE'
  subtotal: number
  taxAmount: number
  total: number
  amountPaid?: number
  paymentMethod?: 'cash' | 'card'
  items: InvoiceItem[]
  source: 'manual' | 'pos'
  createdAt: string
  updatedAt: string
}

export interface InvoiceItem {
  id: string
  name: string
  nameAr: string
  quantity: number
  price: number
  total: number
}

const INVOICES_STORAGE_KEY = 'invoices_data'

// Get all invoices from storage
export const getInvoices = (): Invoice[] => {
  try {
    const invoicesData = getStorageItem(INVOICES_STORAGE_KEY)
    if (invoicesData) {
      return JSON.parse(invoicesData)
    }
    return getDefaultInvoices()
  } catch (error) {
    console.error('Error loading invoices:', error)
    return getDefaultInvoices()
  }
}

// Save invoices to storage
export const saveInvoices = (invoices: Invoice[]): void => {
  try {
    setStorageItem(INVOICES_STORAGE_KEY, JSON.stringify(invoices))
  } catch (error) {
    console.error('Error saving invoices:', error)
  }
}

// Add new invoice
export const addInvoice = (invoice: Omit<Invoice, 'id' | 'number' | 'createdAt' | 'updatedAt'>): Invoice => {
  const invoices = getInvoices()
  const newInvoice: Invoice = {
    ...invoice,
    id: generateInvoiceId(),
    number: generateInvoiceNumber(invoices.length + 1),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
  
  invoices.unshift(newInvoice) // Add to beginning for latest first
  saveInvoices(invoices)
  return newInvoice
}

// Update existing invoice
export const updateInvoice = (id: string, updates: Partial<Invoice>): Invoice | null => {
  const invoices = getInvoices()
  const index = invoices.findIndex(inv => inv.id === id)
  
  if (index === -1) return null
  
  invoices[index] = {
    ...invoices[index],
    ...updates,
    updatedAt: new Date().toISOString(),
  }
  
  saveInvoices(invoices)
  return invoices[index]
}

// Delete invoice
export const deleteInvoice = (id: string): boolean => {
  const invoices = getInvoices()
  const filteredInvoices = invoices.filter(inv => inv.id !== id)
  
  if (filteredInvoices.length === invoices.length) return false
  
  saveInvoices(filteredInvoices)
  return true
}

// Get invoice by ID
export const getInvoiceById = (id: string): Invoice | null => {
  const invoices = getInvoices()
  return invoices.find(inv => inv.id === id) || null
}

// Generate unique invoice ID
const generateInvoiceId = (): string => {
  return `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Generate invoice number
const generateInvoiceNumber = (sequence: number): string => {
  return `INV-${sequence.toString().padStart(3, '0')}`
}

// Create POS invoice from cart data
export const createPOSInvoice = (
  cart: any[],
  customer: any | null,
  paymentMethod: 'cash' | 'card',
  subtotal: number,
  taxAmount: number,
  total: number,
  amountPaid: number
): Invoice => {
  const items: InvoiceItem[] = cart.map(item => ({
    id: item.id,
    name: item.name,
    nameAr: item.nameAr,
    quantity: item.quantity,
    price: item.price,
    total: item.total,
  }))

  const invoiceData = {
    date: new Date().toISOString().split('T')[0],
    dueDate: new Date().toISOString().split('T')[0], // POS sales are immediate
    customer: customer ? customer.nameEn : 'Walk-in Customer',
    customerData: customer,
    status: (amountPaid >= total ? 'PAID' : 'PARTIAL') as 'PAID' | 'PARTIAL',
    subtotal,
    taxAmount,
    total,
    amountPaid,
    paymentMethod,
    items,
    source: 'pos' as const,
  }

  return addInvoice(invoiceData)
}

// Default invoices for initial setup
const getDefaultInvoices = (): Invoice[] => {
  return [
    {
      id: "1",
      number: "INV-001",
      date: "2024-01-15",
      dueDate: "2024-02-15",
      customer: "ABC Corporation",
      status: "PAID",
      subtotal: 1000.00,
      taxAmount: 150.00,
      total: 1150.00,
      amountPaid: 1150.00,
      items: [
        {
          id: "1",
          name: "Business Cards",
          nameAr: "بطاقات أعمال",
          quantity: 2,
          price: 500.00,
          total: 1000.00,
        }
      ],
      source: 'manual',
      createdAt: "2024-01-15T10:00:00Z",
      updatedAt: "2024-01-15T10:00:00Z",
    },
    {
      id: "2",
      number: "INV-002",
      date: "2024-01-16",
      dueDate: "2024-02-16",
      customer: "XYZ Enterprises",
      status: "UNPAID",
      subtotal: 750.00,
      taxAmount: 112.50,
      total: 862.50,
      amountPaid: 0,
      items: [
        {
          id: "2",
          name: "A4 Paper Ream",
          nameAr: "رزمة ورق A4",
          quantity: 10,
          price: 75.00,
          total: 750.00,
        }
      ],
      source: 'manual',
      createdAt: "2024-01-16T10:00:00Z",
      updatedAt: "2024-01-16T10:00:00Z",
    },
    {
      id: "3",
      number: "INV-003",
      date: "2024-01-17",
      dueDate: "2024-02-17",
      customer: "Legal Associates",
      status: "PARTIAL",
      subtotal: 500.00,
      taxAmount: 75.00,
      total: 575.00,
      amountPaid: 300.00,
      items: [
        {
          id: "3",
          name: "Brochure Design",
          nameAr: "تصميم بروشور",
          quantity: 1,
          price: 500.00,
          total: 500.00,
        }
      ],
      source: 'manual',
      createdAt: "2024-01-17T10:00:00Z",
      updatedAt: "2024-01-17T10:00:00Z",
    },
    {
      id: "4",
      number: "INV-004",
      date: "2024-01-10",
      dueDate: "2024-02-10",
      customer: "Tech Solutions",
      status: "OVERDUE",
      subtotal: 2000.00,
      taxAmount: 300.00,
      total: 2300.00,
      amountPaid: 0,
      items: [
        {
          id: "4",
          name: "Banner Printing",
          nameAr: "طباعة بانر",
          quantity: 4,
          price: 500.00,
          total: 2000.00,
        }
      ],
      source: 'manual',
      createdAt: "2024-01-10T10:00:00Z",
      updatedAt: "2024-01-10T10:00:00Z",
    },
  ]
}
