"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Plus, Search, Edit, Trash2, Phone, Mail, UserCheck, Clock, TrendingUp, DollarSign, Target, Award, Users, MoreHorizontal, FileText, Eye } from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"

interface Employee {
  id: string
  name: string
  email: string
  phone: string
  role: string
  isActive: boolean
  avatar?: string
  joinDate?: string
  tasksCompleted?: number
  tasksInProgress?: number
  completionRate?: number
  salesData?: {
    totalSales?: number
    commission?: number
    monthlyTarget?: number
    invoicesCount?: number
    avgOrderValue?: number
    conversionRate?: number
    rank?: number
    growth?: number
  }
  // Document fields
  idCardNumber?: string
  idCardExpiry?: string
  passportNumber?: string
  passportExpiry?: string
  visaNumber?: string
  visaExpiry?: string
  licenseNumber?: string
  licenseExpiry?: string
  contractExpiry?: string
}

const roleColors = {
  ADMIN: "bg-purple-100 text-purple-800",
  MANAGER: "bg-blue-100 text-blue-800",
  EMPLOYEE: "bg-green-100 text-green-800",
}

export default function EmployeesPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [currentView, setCurrentView] = useState<"employees" | "sales">("employees")
  const [employees, setEmployees] = useState<Employee[]>([])
  const [roleFilter, setRoleFilter] = useState("all")
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null)
  const [newEmployeeData, setNewEmployeeData] = useState({
    name: "",
    email: "",
    phone: "",
    role: "EMPLOYEE" as "ADMIN" | "MANAGER" | "EMPLOYEE",
    password: "",
    confirmPassword: ""
  })
  const [showPasswordFields, setShowPasswordFields] = useState(false)

  // Load employees on component mount
  useEffect(() => {
    const loadEmployees = async () => {
      try {
        const response = await fetch('/api/employees')
        if (response.ok) {
          const data = await response.json()
          // Handle both array and object with employees property
          const employeesList = Array.isArray(data) ? data : (data.employees || [])
          setEmployees(employeesList)
        } else {
          console.error('Failed to load employees')
          setEmployees([])
        }
      } catch (error) {
        console.error('Error loading employees:', error)
        setEmployees([])
      }
    }
    loadEmployees()
  }, [])

  const filteredEmployees = Array.isArray(employees) ? employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.role.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesRole = roleFilter === "all" || employee.role.toLowerCase() === roleFilter.toLowerCase()

    return matchesSearch && matchesRole
  }) : []

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  // CRUD Functions
  const addEmployee = async (employeeData: any) => {
    try {
      const response = await fetch('/api/employees', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(employeeData)
      })
      if (response.ok) {
        return await response.json()
      }
      return null
    } catch (error) {
      console.error('Error adding employee:', error)
      return null
    }
  }

  const updateEmployee = async (id: string, employeeData: any) => {
    try {
      const response = await fetch(`/api/employees/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(employeeData)
      })
      if (response.ok) {
        return await response.json()
      }
      return null
    } catch (error) {
      console.error('Error updating employee:', error)
      return null
    }
  }

  const handleAddEmployee = async () => {
    if (!newEmployeeData.name || !newEmployeeData.email || !newEmployeeData.phone) {
      alert('Please fill in all required fields')
      return
    }

    if (newEmployeeData.password !== newEmployeeData.confirmPassword) {
      alert('Passwords do not match')
      return
    }

    const newEmployee = await addEmployee({
      name: newEmployeeData.name,
      email: newEmployeeData.email,
      phone: newEmployeeData.phone,
      role: newEmployeeData.role,
      password: newEmployeeData.password,
    })

    if (newEmployee) {
      setEmployees(prev => [newEmployee, ...prev])
      setNewEmployeeData({
        name: "",
        email: "",
        phone: "",
        role: "EMPLOYEE",
        password: "",
        confirmPassword: ""
      })
      setIsDialogOpen(false)
      alert(`Employee ${newEmployee.name} added successfully!`)
    } else {
      alert('Failed to add employee. Please try again.')
    }
  }

  const handleViewEmployee = (employee: Employee) => {
    router.push(`/dashboard/employees/${employee.id}`)
  }

  const handleEditEmployee = (employee: Employee) => {
    router.push(`/dashboard/employees/${employee.id}/edit`)
  }

  const handleUpdateEmployee = async () => {
    if (!editingEmployee) return

    const updated = await updateEmployee(editingEmployee.id, {
      name: newEmployeeData.name,
      email: newEmployeeData.email,
      phone: newEmployeeData.phone,
      role: newEmployeeData.role,
    })

    if (updated) {
      setEmployees(prev => prev.map(emp => emp.id === editingEmployee.id ? updated : emp))
      setEditingEmployee(null)
      setNewEmployeeData({
        name: "",
        email: "",
        phone: "",
        role: "EMPLOYEE",
        password: "",
        confirmPassword: ""
      })
      setIsDialogOpen(false)
      alert(`Employee ${updated.name} updated successfully!`)
    } else {
      alert('Failed to update employee. Please try again.')
    }
  }

  const handleDeleteEmployee = async (employee: Employee) => {
    if (confirm(`Are you sure you want to delete employee ${employee.name}?`)) {
      try {
        const response = await fetch(`/api/employees/${employee.id}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          setEmployees(prev => Array.isArray(prev) ? prev.filter(emp => emp.id !== employee.id) : [])
          alert(`Employee ${employee.name} deleted successfully!`)
        } else {
          const error = await response.json()
          alert(`Failed to delete employee: ${error.error}`)
        }
      } catch (error) {
        console.error('Delete error:', error)
        alert(`Failed to delete employee: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }

  const handleToggleStatus = async (employee: Employee) => {
    try {
      const response = await fetch(`/api/employees/${employee.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !employee.isActive })
      })

      if (response.ok) {
        const updated = await response.json()
        setEmployees(prev => Array.isArray(prev) ? prev.map(emp => emp.id === employee.id ? updated : emp) : [])
        alert(`Employee ${employee.name} ${updated.isActive ? 'activated' : 'deactivated'} successfully!`)
      } else {
        const error = await response.json()
        alert(`Failed to update employee status: ${error.error}`)
      }
    } catch (error) {
      console.error('Error updating employee status:', error)
      alert('Failed to update employee status. Please try again.')
    }
  }

  // Reset dialog state
  const resetDialogState = () => {
    setEditingEmployee(null)
    setShowPasswordFields(false)
    setNewEmployeeData({
      name: "",
      email: "",
      phone: "",
      role: "EMPLOYEE",
      password: "",
      confirmPassword: ""
    })
  }

  // Calculate statistics with proper number conversion and safety checks
  const employeesArray = Array.isArray(employees) ? employees : []
  const totalEmployees = employeesArray.length
  const activeEmployees = employeesArray.filter(emp => emp.isActive).length
  const totalTasks = employeesArray.reduce((sum, emp) => sum + (Number(emp.tasksCompleted) || 0) + (Number(emp.tasksInProgress) || 0), 0)

  // Calculate average completion rate with null/undefined handling
  const validCompletionRates = employeesArray.filter(emp => emp.completionRate != null && !isNaN(Number(emp.completionRate)))
  const avgCompletionRate = validCompletionRates.length > 0
    ? validCompletionRates.reduce((sum, emp) => sum + Number(emp.completionRate), 0) / validCompletionRates.length
    : 0

  const totalSales = employeesArray.reduce((sum, emp) => sum + (Number(emp.salesData?.totalSales) || 0), 0)
  const totalCommission = employeesArray.reduce((sum, emp) => sum + (Number(emp.salesData?.commission) || 0), 0)

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-3xl font-bold tracking-tight">
            {currentView === "employees" ? t('employees.title') : t('employees.salesPerformance')}
          </h2>
          <div className="flex items-center space-x-2">
            <Button
              variant={currentView === "employees" ? "default" : "outline"}
              size="sm"
              onClick={() => setCurrentView("employees")}
            >
              <UserCheck className="mr-2 h-4 w-4" />
              {t('employees.employeesView')}
            </Button>
            <Button
              variant={currentView === "sales" ? "default" : "outline"}
              size="sm"
              onClick={() => setCurrentView("sales")}
            >
              <TrendingUp className="mr-2 h-4 w-4" />
              {t('employees.salesPerformanceView')}
            </Button>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={() => router.push('/dashboard/employees/documents')}>
            <FileText className="mr-2 h-4 w-4" />
            {t('employees.documentInformation')}
          </Button>
          <Button onClick={() => router.push('/dashboard/employees/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('employees.addEmployee')}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-gradient-to-br from-emerald-50 to-green-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-emerald-600">{t('employees.totalEmployees')}</p>
              <p className="text-3xl font-bold text-emerald-900">{totalEmployees}</p>
            </div>
            <div className="rounded-full bg-emerald-100 p-3">
              <Users className="h-6 w-6 text-emerald-600" />
            </div>
          </div>
          <p className="text-xs text-emerald-600 mt-2">{activeEmployees} {t('employees.activeEmployees')}</p>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-blue-50 to-cyan-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">{t('employees.tasks')}</p>
              <p className="text-3xl font-bold text-blue-900">{totalTasks}</p>
            </div>
            <div className="rounded-full bg-blue-100 p-3">
              <Target className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <p className="text-xs text-blue-600 mt-2">{avgCompletionRate.toFixed(1)}% avg completion rate</p>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-violet-50 to-purple-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-violet-600">{t('employees.totalSales')}</p>
              <p className="text-2xl font-bold text-violet-900">{formatCurrency(totalSales)}</p>
            </div>
            <div className="rounded-full bg-violet-100 p-3">
              <DollarSign className="h-6 w-6 text-violet-600" />
            </div>
          </div>
          <p className="text-xs text-violet-600 mt-2">Team performance</p>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-amber-50 to-yellow-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-amber-600">{t('employees.commission')}</p>
              <p className="text-2xl font-bold text-amber-900">{formatCurrency(totalCommission)}</p>
            </div>
            <div className="rounded-full bg-amber-100 p-3">
              <Award className="h-6 w-6 text-amber-600" />
            </div>
          </div>
          <p className="text-xs text-amber-600 mt-2">Earned commissions</p>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('employees.searchEmployees')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('employees.role')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('common.selectAll')}</SelectItem>
            <SelectItem value="admin">{t('employees.admin')}</SelectItem>
            <SelectItem value="manager">{t('employees.manager')}</SelectItem>
            <SelectItem value="employee">{t('employees.employee')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {currentView === "employees" ? (
        <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('employees.employee')}</TableHead>
              <TableHead>{t('employees.role')}</TableHead>
              <TableHead>{t('employees.contact')}</TableHead>
              <TableHead>{t('employees.performance')}</TableHead>
              <TableHead>{t('employees.tasks')}</TableHead>
              <TableHead>{t('employees.joinDate')}</TableHead>
              <TableHead>{t('employees.status')}</TableHead>
              <TableHead className="text-right">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredEmployees.map((employee) => (
              <TableRow key={employee.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={employee.avatar} alt={employee.name} />
                      <AvatarFallback>{getInitials(employee.name)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium"
                        onClick={() => handleViewEmployee(employee)}
                      >
                        {employee.name}
                      </Button>
                      <div className="text-sm text-muted-foreground">ID: {employee.id}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={roleColors[employee.role as keyof typeof roleColors]}>
                    {employee.role}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm">
                      <Mail className="mr-1 h-3 w-3" />
                      {employee.email}
                    </div>
                    <div className="flex items-center text-sm">
                      <Phone className="mr-1 h-3 w-3" />
                      {employee.phone}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-sm font-medium">{Number(employee.completionRate || 0).toFixed(1)}%</div>
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${Number(employee.completionRate || 0)}%` }}
                      ></div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm">
                      <UserCheck className="mr-1 h-3 w-3 text-green-600" />
                      {Number(employee.tasksCompleted || 0)} {t('employees.completed')}
                    </div>
                    <div className="flex items-center text-sm">
                      <Clock className="mr-1 h-3 w-3 text-blue-600" />
                      {Number(employee.tasksInProgress || 0)} {t('employees.inProgress')}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{employee.joinDate}</TableCell>
                <TableCell>
                  <Badge variant={employee.isActive ? "default" : "secondary"}>
                    {employee.isActive ? t('employees.active') : t('employees.inactive')}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewEmployee(employee)}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t('employees.viewEmployee')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditEmployee(employee)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('employees.editEmployee')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleToggleStatus(employee)}>
                        <UserCheck className="mr-2 h-4 w-4" />
                        {employee.isActive ? t('employees.inactive') : t('employees.active')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteEmployee(employee)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('employees.deleteEmployee')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Sales Performance Statistics */}
          <div className="bg-slate-50/50 dark:bg-slate-900/20 rounded-lg p-6 border border-slate-200/60 dark:border-slate-700/60">
            <div className="grid gap-4 md:grid-cols-4">
              <div className="rounded-lg border bg-white dark:bg-slate-800 p-4 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">{formatCurrency(employees.reduce((sum, emp) => sum + (Number(emp.salesData?.totalSales) || 0), 0))}</div>
                    <div className="text-sm text-muted-foreground">Total Sales</div>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
              </div>
              <div className="rounded-lg border bg-white dark:bg-slate-800 p-4 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">{employees.reduce((sum, emp) => sum + (Number(emp.salesData?.invoicesCount) || 0), 0)}</div>
                    <div className="text-sm text-muted-foreground">Total Invoices</div>
                  </div>
                  <Target className="h-8 w-8 text-blue-500" />
                </div>
              </div>
              <div className="rounded-lg border bg-white dark:bg-slate-800 p-4 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">{formatCurrency(employees.length > 0 ? employees.reduce((sum, emp) => sum + (Number(emp.salesData?.avgOrderValue) || 0), 0) / employees.length : 0)}</div>
                    <div className="text-sm text-muted-foreground">Avg Order Value</div>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-500" />
                </div>
              </div>
              <div className="rounded-lg border bg-white dark:bg-slate-800 p-4 shadow-sm">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-2xl font-bold">{formatCurrency(employees.reduce((sum, emp) => sum + (Number(emp.salesData?.commission) || 0), 0))}</div>
                    <div className="text-sm text-muted-foreground">Total Commission</div>
                  </div>
                  <Award className="h-8 w-8 text-yellow-500" />
                </div>
              </div>
            </div>
          </div>

          {/* Sales Performance Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Employee</TableHead>
                  <TableHead>Sales Performance</TableHead>
                  <TableHead>Target Achievement</TableHead>
                  <TableHead>Invoices</TableHead>
                  <TableHead>Avg Order Value</TableHead>
                  <TableHead>Conversion Rate</TableHead>
                  <TableHead>Commission</TableHead>
                  <TableHead>Rank</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {employees
                  .filter(emp => emp.isActive && emp.salesData)
                  .sort((a, b) => (a.salesData?.rank || 999) - (b.salesData?.rank || 999))
                  .map((employee) => (
                  <TableRow key={employee.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar>
                          <AvatarImage src={employee.avatar} alt={employee.name} />
                          <AvatarFallback>{getInitials(employee.name)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium"
                            onClick={() => handleViewEmployee(employee)}
                          >
                            {employee.name}
                          </Button>
                          <div className="text-sm text-muted-foreground">{employee.role}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-lg font-bold text-green-600">{formatCurrency(Number(employee.salesData?.totalSales) || 0)}</div>
                        <div className="flex items-center text-sm">
                          {(employee.salesData?.growth || 0) >= 0 ? (
                            <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                          ) : (
                            <TrendingUp className="mr-1 h-3 w-3 text-red-500 rotate-180" />
                          )}
                          <span className={(employee.salesData?.growth || 0) >= 0 ? "text-green-600" : "text-red-600"}>
                            {(employee.salesData?.growth || 0) >= 0 ? "+" : ""}{employee.salesData?.growth || 0}%
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>{formatCurrency(Number(employee.salesData?.totalSales) || 0)}</span>
                          <span className="text-muted-foreground">/ {formatCurrency(Number(employee.salesData?.monthlyTarget) || 0)}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              ((Number(employee.salesData?.totalSales) || 0) / (Number(employee.salesData?.monthlyTarget) || 1)) >= 1
                                ? 'bg-green-500'
                                : ((Number(employee.salesData?.totalSales) || 0) / (Number(employee.salesData?.monthlyTarget) || 1)) >= 0.8
                                ? 'bg-yellow-500'
                                : 'bg-red-500'
                            }`}
                            style={{ width: `${Math.min(((Number(employee.salesData?.totalSales) || 0) / (Number(employee.salesData?.monthlyTarget) || 1)) * 100, 100)}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-center">
                          {(((Number(employee.salesData?.totalSales) || 0) / (Number(employee.salesData?.monthlyTarget) || 1)) * 100).toFixed(1)}%
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-center">
                        <div className="text-lg font-semibold">{Number(employee.salesData?.invoicesCount) || 0}</div>
                        <div className="text-sm text-muted-foreground">invoices</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-center">
                        <div className="font-medium">{formatCurrency(Number(employee.salesData?.avgOrderValue) || 0)}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-center">
                        <div className="font-medium">{Number(employee.salesData?.conversionRate) || 0}%</div>
                        <div className="w-16 bg-gray-200 rounded-full h-1 mx-auto mt-1">
                          <div
                            className="bg-blue-500 h-1 rounded-full"
                            style={{ width: `${Number(employee.salesData?.conversionRate) || 0}%` }}
                          ></div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-center">
                        <div className="font-medium text-green-600">{formatCurrency(Number(employee.salesData?.commission) || 0)}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-center">
                        <Badge className={
                          (employee.salesData?.rank || 999) === 1 ? "bg-yellow-100 text-yellow-800" :
                          (employee.salesData?.rank || 999) === 2 ? "bg-gray-100 text-gray-800" :
                          (employee.salesData?.rank || 999) === 3 ? "bg-orange-100 text-orange-800" :
                          "bg-blue-100 text-blue-800"
                        }>
                          #{employee.salesData?.rank || 'N/A'}
                        </Badge>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}


    </div>
  )
}
