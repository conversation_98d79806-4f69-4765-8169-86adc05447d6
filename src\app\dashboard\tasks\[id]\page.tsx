"use client"

import { useState, useEffect } from "react"
import { useR<PERSON><PERSON>, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  ArrowLeft, 
  Edit, 
  Clock, 
  User, 
  Calendar, 
  FileText, 
  Download,
  Image as ImageIcon,
  File
} from "lucide-react"

export default function TaskViewPage() {
  const router = useRouter()
  const params = useParams()
  const taskId = params.id as string

  const [task, setTask] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTask()
  }, [taskId])

  const fetchTask = async () => {
    try {
      const response = await fetch(`/api/tasks/${taskId}`)
      if (response.ok) {
        const data = await response.json()
        setTask(data)
      } else {
        console.error('Failed to fetch task')
        router.push('/dashboard/tasks')
      }
    } catch (error) {
      console.error('Error fetching task:', error)
      router.push('/dashboard/tasks')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    const colors = {
      NEW: "bg-blue-100 text-blue-800",
      IN_PROGRESS: "bg-yellow-100 text-yellow-800",
      COMPLETED: "bg-green-100 text-green-800",
      CANCELLED: "bg-red-100 text-red-800",
    }
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      LOW: "bg-gray-100 text-gray-800",
      MEDIUM: "bg-orange-100 text-orange-800",
      HIGH: "bg-red-100 text-red-800",
      URGENT: "bg-purple-100 text-purple-800",
    }
    return colors[priority as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>Loading task...</div>
        </div>
      </div>
    )
  }

  if (!task) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>Task not found</div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Tasks
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{task.title}</h2>
            <p className="text-muted-foreground">Task Details</p>
          </div>
        </div>
        <Button onClick={() => router.push(`/dashboard/tasks/${taskId}/edit`)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit Task
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Task Information */}
        <Card>
          <CardHeader>
            <CardTitle>Task Information</CardTitle>
            <CardDescription>Basic task details and assignment</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Description</div>
              <div className="text-sm">{task.description || 'No description provided'}</div>
            </div>

            <Separator />

            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Status</div>
                <Badge className={getStatusColor(task.status)}>
                  {task.status.replace('_', ' ')}
                </Badge>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Priority</div>
                <Badge className={getPriorityColor(task.priority)}>
                  {task.priority}
                </Badge>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Customer</div>
                <div className="text-sm">{task.customer?.name || 'No customer assigned'}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Assigned To</div>
                <div className="flex items-center text-sm">
                  <User className="mr-1 h-3 w-3" />
                  {task.assignedTo?.name || 'Unassigned'}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Estimated Hours</div>
                <div className="flex items-center text-sm">
                  <Clock className="mr-1 h-3 w-3" />
                  {task.estimatedHours || 'Not specified'}
                </div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Actual Hours</div>
                <div className="flex items-center text-sm">
                  <Clock className="mr-1 h-3 w-3" />
                  {task.actualHours || 'Not recorded'}
                </div>
              </div>
            </div>

            {task.notes && (
              <>
                <Separator />
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Notes</div>
                  <div className="text-sm">{task.notes}</div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Timeline and Completion */}
        <Card>
          <CardHeader>
            <CardTitle>Timeline & Completion</CardTitle>
            <CardDescription>Task progress and completion details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Created</div>
              <div className="flex items-center text-sm">
                <Calendar className="mr-1 h-3 w-3" />
                {formatDate(task.createdAt)}
              </div>
              <div className="text-xs text-muted-foreground">
                by {task.createdBy?.name}
              </div>
            </div>

            {task.startTime && (
              <div>
                <div className="text-sm font-medium text-muted-foreground">Started</div>
                <div className="flex items-center text-sm">
                  <Calendar className="mr-1 h-3 w-3" />
                  {formatDate(task.startTime)}
                </div>
              </div>
            )}

            {task.endTime && (
              <div>
                <div className="text-sm font-medium text-muted-foreground">Completed</div>
                <div className="flex items-center text-sm">
                  <Calendar className="mr-1 h-3 w-3" />
                  {formatDate(task.endTime)}
                </div>
              </div>
            )}

            {task.completionNotes && (
              <>
                <Separator />
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Completion Notes</div>
                  <div className="text-sm">{task.completionNotes}</div>
                </div>
              </>
            )}

            {task.attachments && task.attachments.length > 0 && (
              <>
                <Separator />
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-2">Work Files</div>
                  <div className="space-y-2">
                    {task.attachments.map((file: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div className="flex items-center space-x-2">
                          {file.type?.startsWith('image/') ? (
                            <ImageIcon className="h-4 w-4 text-blue-500" />
                          ) : (
                            <File className="h-4 w-4 text-gray-500" />
                          )}
                          <span className="text-sm">{file.originalName}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(file.path, '_blank')}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
