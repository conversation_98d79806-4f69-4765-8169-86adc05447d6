"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, CheckSquare, Square } from "lucide-react"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

export default function AddPermissionPage() {
  const { direction } = useI18n()
  const router = useRouter()
  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    module: '',
    resource: '',
    description: '',
  })
  const [selectedActions, setSelectedActions] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleActionToggle = (action: string) => {
    setSelectedActions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(action)) {
        newSet.delete(action)
      } else {
        newSet.add(action)
      }
      return newSet
    })
  }

  const handleSelectAllActions = () => {
    setSelectedActions(new Set(actions))
  }

  const handleDeselectAllActions = () => {
    setSelectedActions(new Set())
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name.trim() || !formData.module.trim() || selectedActions.size === 0) {
      toast.error('Name, module, and at least one action are required')
      return
    }

    setLoading(true)
    try {
      const actionsArray = Array.from(selectedActions)
      let successCount = 0
      let errorCount = 0

      // Create permissions for each selected action
      for (const action of actionsArray) {
        const permissionData = {
          ...formData,
          name: `${formData.name} - ${action}`,
          action,
        }

        try {
          const response = await fetch('/api/permissions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(permissionData),
          })

          if (response.ok) {
            successCount++
          } else {
            errorCount++
            const error = await response.json()
            console.error(`Failed to create permission for action ${action}:`, error)
          }
        } catch (error) {
          errorCount++
          console.error(`Error creating permission for action ${action}:`, error)
        }
      }

      if (successCount > 0) {
        toast.success(
          direction === 'rtl'
            ? `تم إنشاء ${successCount} صلاحية بنجاح`
            : `${successCount} permission(s) created successfully`
        )
      }

      if (errorCount > 0) {
        toast.error(
          direction === 'rtl'
            ? `فشل في إنشاء ${errorCount} صلاحية`
            : `Failed to create ${errorCount} permission(s)`
        )
      }

      if (successCount > 0) {
        router.push('/dashboard/permissions')
      }
    } catch (error) {
      console.error('Error creating permissions:', error)
      toast.error('Failed to create permissions')
    } finally {
      setLoading(false)
    }
  }

  const modules = [
    'dashboard', 'customers', 'suppliers', 'products', 'categories', 'units',
    'tasks', 'invoices', 'quotations', 'purchases', 'expenses', 'financial',
    'employees', 'reports', 'settings', 'users', 'roles', 'pos', 'leads'
  ]

  const actions = [
    'view', 'create', 'edit', 'delete', 'export', 'print', 'approve',
    'assign', 'manage', 'access', 'sell', 'convert', 'stock'
  ]

  const getModuleDisplayName = (module: string) => {
    const moduleNames: Record<string, { en: string; ar: string }> = {
      dashboard: { en: 'Dashboard', ar: 'لوحة التحكم' },
      customers: { en: 'Customers', ar: 'العملاء' },
      suppliers: { en: 'Suppliers', ar: 'الموردين' },
      products: { en: 'Products', ar: 'المنتجات' },
      categories: { en: 'Categories', ar: 'الفئات' },
      units: { en: 'Units', ar: 'الوحدات' },
      tasks: { en: 'Tasks', ar: 'المهام' },
      invoices: { en: 'Invoices', ar: 'الفواتير' },
      quotations: { en: 'Quotations', ar: 'عروض الأسعار' },
      purchases: { en: 'Purchases', ar: 'المشتريات' },
      expenses: { en: 'Expenses', ar: 'المصروفات' },
      financial: { en: 'Financial', ar: 'المالية' },
      employees: { en: 'Employees', ar: 'الموظفين' },
      reports: { en: 'Reports', ar: 'التقارير' },
      settings: { en: 'Settings', ar: 'الإعدادات' },
      users: { en: 'Users', ar: 'المستخدمين' },
      roles: { en: 'Roles', ar: 'الأدوار' },
      pos: { en: 'POS', ar: 'نقطة البيع' },
      leads: { en: 'Leads', ar: 'العملاء المحتملين' },
    }

    return direction === 'rtl'
      ? moduleNames[module]?.ar || module
      : moduleNames[module]?.en || module
  }

  const getActionDisplayName = (action: string) => {
    const actionNames: Record<string, { en: string; ar: string }> = {
      view: { en: 'View', ar: 'عرض' },
      create: { en: 'Create', ar: 'إنشاء' },
      edit: { en: 'Edit', ar: 'تعديل' },
      delete: { en: 'Delete', ar: 'حذف' },
      export: { en: 'Export', ar: 'تصدير' },
      print: { en: 'Print', ar: 'طباعة' },
      approve: { en: 'Approve', ar: 'موافقة' },
      assign: { en: 'Assign', ar: 'تعيين' },
      manage: { en: 'Manage', ar: 'إدارة' },
      access: { en: 'Access', ar: 'وصول' },
      sell: { en: 'Sell', ar: 'بيع' },
      convert: { en: 'Convert', ar: 'تحويل' },
      stock: { en: 'Stock', ar: 'مخزون' },
    }

    return direction === 'rtl'
      ? actionNames[action]?.ar || action
      : actionNames[action]?.en || action
  }

  return (
    <div className={`flex-1 space-y-6 p-8 pt-6 ${direction === 'rtl' ? 'rtl' : 'ltr'}`} dir={direction}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
            className={direction === 'rtl' ? 'flex-row-reverse' : ''}
          >
            <ArrowLeft className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2 rotate-180' : 'mr-2'}`} />
            {direction === 'rtl' ? 'رجوع' : 'Back'}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">
              {direction === 'rtl' ? 'إضافة صلاحية جديدة' : 'Add New Permission'}
            </h2>
            <p className="text-muted-foreground">
              {direction === 'rtl' 
                ? 'أنشئ صلاحية جديدة للنظام' 
                : 'Create a new system permission'
              }
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>
              {direction === 'rtl' ? 'معلومات الصلاحية' : 'Permission Information'}
            </CardTitle>
            <CardDescription>
              {direction === 'rtl' 
                ? 'أدخل المعلومات الأساسية للصلاحية' 
                : 'Enter the basic information for the permission'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  {direction === 'rtl' ? 'اسم الصلاحية (إنجليزي)' : 'Permission Name (English)'} *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={direction === 'rtl' ? 'أدخل اسم الصلاحية' : 'Enter permission name'}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="nameAr">
                  {direction === 'rtl' ? 'اسم الصلاحية (عربي)' : 'Permission Name (Arabic)'}
                </Label>
                <Input
                  id="nameAr"
                  value={formData.nameAr}
                  onChange={(e) => handleInputChange('nameAr', e.target.value)}
                  placeholder={direction === 'rtl' ? 'أدخل اسم الصلاحية بالعربية' : 'Enter permission name in Arabic'}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="module">
                  {direction === 'rtl' ? 'الوحدة' : 'Module'} *
                </Label>
                <select
                  id="module"
                  value={formData.module}
                  onChange={(e) => handleInputChange('module', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">
                    {direction === 'rtl' ? 'اختر الوحدة' : 'Select Module'}
                  </option>
                  {modules.map(module => (
                    <option key={module} value={module}>
                      {getModuleDisplayName(module)}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="resource">
                  {direction === 'rtl' ? 'المورد' : 'Resource'}
                </Label>
                <Input
                  id="resource"
                  value={formData.resource}
                  onChange={(e) => handleInputChange('resource', e.target.value)}
                  placeholder={direction === 'rtl' ? 'أدخل المورد (اختياري)' : 'Enter resource (optional)'}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">
                {direction === 'rtl' ? 'الوصف' : 'Description'}
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder={direction === 'rtl' ? 'أدخل وصف الصلاحية' : 'Enter permission description'}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Actions Selection */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>
                  {direction === 'rtl' ? 'الإجراءات' : 'Actions'} *
                </CardTitle>
                <CardDescription>
                  {direction === 'rtl'
                    ? 'اختر الإجراءات التي تريد إنشاء صلاحيات لها'
                    : 'Select the actions you want to create permissions for'
                  }
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAllActions}
                  className="flex items-center gap-2"
                >
                  <CheckSquare className="h-4 w-4" />
                  {direction === 'rtl' ? 'تحديد الكل' : 'Select All'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleDeselectAllActions}
                  className="flex items-center gap-2"
                >
                  <Square className="h-4 w-4" />
                  {direction === 'rtl' ? 'إلغاء تحديد الكل' : 'Deselect All'}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Label className="text-lg font-semibold">
                    {direction === 'rtl' ? 'الإجراءات المتاحة' : 'Available Actions'}
                  </Label>
                </div>
                <Badge variant="default">
                  {selectedActions.size}/{actions.length}
                </Badge>
              </div>

              <div className={`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3`}>
                {actions.map((action) => (
                  <div key={action} className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-gray-50">
                    <Checkbox
                      id={action}
                      checked={selectedActions.has(action)}
                      onCheckedChange={() => handleActionToggle(action)}
                      className={direction === 'rtl' ? 'ml-2' : 'mr-2'}
                    />
                    <Label
                      htmlFor={action}
                      className="text-sm cursor-pointer flex-1 font-medium"
                    >
                      {getActionDisplayName(action)}
                    </Label>
                  </div>
                ))}
              </div>

              {selectedActions.size > 0 && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <Label className="text-sm font-medium text-blue-900">
                    {direction === 'rtl' ? 'الصلاحيات التي سيتم إنشاؤها:' : 'Permissions to be created:'}
                  </Label>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {Array.from(selectedActions).map((action) => (
                      <Badge key={action} variant="secondary" className="text-xs">
                        {formData.name ? `${formData.name} - ${getActionDisplayName(action)}` : getActionDisplayName(action)}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className={`flex gap-4 ${direction === 'rtl' ? 'flex-row-reverse' : ''}`}>
          <Button type="submit" disabled={loading || selectedActions.size === 0} size="lg">
            {loading
              ? (direction === 'rtl' ? 'جاري الحفظ...' : 'Creating...')
              : selectedActions.size > 1
                ? (direction === 'rtl' ? `إنشاء ${selectedActions.size} صلاحيات` : `Create ${selectedActions.size} Permissions`)
                : (direction === 'rtl' ? 'إنشاء الصلاحية' : 'Create Permission')
            }
          </Button>
          <Button 
            type="button" 
            variant="outline" 
            size="lg"
            onClick={() => router.back()}
          >
            {direction === 'rtl' ? 'إلغاء' : 'Cancel'}
          </Button>
        </div>
      </form>
    </div>
  )
}
