import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            nameAr: true,
          },
        },
        supplier: {
          select: {
            id: true,
            name: true,
            company: true,
          },
        },
        unitModel: {
          select: {
            id: true,
            name: true,
            nameAr: true,
            symbol: true,
            symbolAr: true,
          },
        },
        invoiceItems: {
          select: {
            id: true,
            quantity: true,
            invoice: {
              select: {
                id: true,
                number: true,
                date: true,
                customer: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
          take: 10,
        },
      },
    })

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error('Error fetching product:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const body = await request.json()
    const {
      name,
      nameAr,
      description,
      sku,
      type,
      price,
      cost,
      stock,
      minStock,
      unit,
      image,
      categoryId,
      supplierId,
      unitId
    } = body

    if (!name || !price) {
      return NextResponse.json(
        { error: 'Name and price are required' },
        { status: 400 }
      )
    }

    const product = await prisma.product.update({
      where: { id },
      data: {
        name,
        nameAr,
        description,
        sku,
        type: type || 'PHYSICAL',
        price: parseFloat(price),
        costPrice: cost ? parseFloat(cost) : null,
        currentStock: parseInt(stock) || 0,
        minStock: parseInt(minStock) || 0,
        unit: unit || 'piece',
        image: image !== undefined ? image : undefined,
        categoryId,
        supplierId,
        unitId,
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            nameAr: true,
          },
        },
        supplier: {
          select: {
            id: true,
            name: true,
            company: true,
          },
        },
        unitModel: {
          select: {
            id: true,
            name: true,
            nameAr: true,
            symbol: true,
            symbolAr: true,
          },
        },
      },
    })

    return NextResponse.json(product)
  } catch (error) {
    console.error('Error updating product:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    // Check if product has any invoice items or purchase items
    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        invoiceItems: true,
        purchaseItems: true,
      },
    })

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 })
    }

    if (product.invoiceItems.length > 0 || product.purchaseItems.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete product with existing invoice or purchase items' },
        { status: 400 }
      )
    }

    await prisma.product.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Product deleted successfully' })
  } catch (error) {
    console.error('Error deleting product:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
