/**
 * Employee API utility - replaces localStorage with database API calls
 */

export interface Employee {
  id: string
  name: string
  email: string
  phone: string
  role: 'ADMIN' | 'MANAGER' | 'EMPLOYEE'
  isActive: boolean
  avatar?: string
  joinDate: string
  tasksCompleted: number
  tasksInProgress: number
  completionRate: number
  salesData?: {
    totalSales: number
    monthlyTarget: number
    invoicesCount: number
    avgOrderValue: number
    conversionRate: number
    commission: number
    rank: number
    growth: number
  }
  createdAt: string
  updatedAt: string
}

// Get all employees from API
export const getEmployees = async (): Promise<Employee[]> => {
  try {
    const response = await fetch('/api/employees')
    if (!response.ok) {
      throw new Error('Failed to fetch employees')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading employees:', error)
    return []
  }
}

// Add new employee via API
export const addEmployee = async (employee: {
  name: string
  email: string
  phone: string
  role: 'ADMIN' | 'MANAGER' | 'EMPLOYEE'
  password: string
}): Promise<Employee | null> => {
  try {
    const response = await fetch('/api/employees', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(employee),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create employee')
    }

    return await response.json()
  } catch (error) {
    console.error('Error creating employee:', error)
    return null
  }
}

// Update existing employee via API
export const updateEmployee = async (id: string, updates: {
  name?: string
  email?: string
  phone?: string
  role?: 'ADMIN' | 'MANAGER' | 'EMPLOYEE'
  isActive?: boolean
}): Promise<Employee | null> => {
  try {
    const response = await fetch(`/api/employees/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update employee')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating employee:', error)
    return null
  }
}

// Delete employee via API
export const deleteEmployee = async (id: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/employees/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      console.error('Delete employee API error:', error)
      throw new Error(error.error || 'Failed to delete employee')
    }

    return true
  } catch (error) {
    console.error('Error deleting employee:', error)
    throw error // Re-throw to show the actual error message
  }
}

// Get employee by ID via API
export const getEmployeeById = async (id: string): Promise<Employee | null> => {
  try {
    const response = await fetch(`/api/employees/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch employee')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading employee:', error)
    return null
  }
}


