"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Save, Calendar, Clock, Bell } from "lucide-react"
import { useI18n } from "@/lib/i18n"

export default function CreateEventPage() {
  const router = useRouter()
  const { t, language, direction } = useI18n()
  const [saving, setSaving] = useState(false)
  const [users, setUsers] = useState<any[]>([])
  
  // Translated options
  const EVENT_TYPES = [
    { value: 'REMINDER', label: t('calendar.types.reminder') || 'Reminder' },
    { value: 'MEETING', label: t('calendar.types.meeting') || 'Meeting' },
    { value: 'DEADLINE', label: t('calendar.types.deadline') || 'Deadline' },
    { value: 'RENEWAL', label: t('calendar.types.renewal') || 'Renewal' },
    { value: 'EXPIRATION', label: t('calendar.types.expiration') || 'Expiration' },
    { value: 'TASK_DUE', label: t('calendar.types.taskDue') || 'Task Due' },
    { value: 'INVOICE_DUE', label: t('calendar.types.invoiceDue') || 'Invoice Due' },
    { value: 'PAYMENT_DUE', label: t('calendar.types.paymentDue') || 'Payment Due' },
    { value: 'HOLIDAY', label: t('calendar.types.holiday') || 'Holiday' },
    { value: 'OTHER', label: t('calendar.types.other') || 'Other' },
  ]

  const EVENT_CATEGORIES = [
    { value: 'DOCUMENT', label: t('calendar.categories.document') || 'Document' },
    { value: 'FINANCIAL', label: t('calendar.categories.financial') || 'Financial' },
    { value: 'LEGAL', label: t('calendar.categories.legal') || 'Legal' },
    { value: 'HR', label: t('calendar.categories.hr') || 'HR' },
    { value: 'BUSINESS', label: t('calendar.categories.business') || 'Business' },
    { value: 'PERSONAL', label: t('calendar.categories.personal') || 'Personal' },
    { value: 'SYSTEM', label: t('calendar.categories.system') || 'System' },
    { value: 'OTHER', label: t('calendar.categories.other') || 'Other' },
  ]

  const EVENT_PRIORITIES = [
    { value: 'LOW', label: t('calendar.priorities.low') || 'Low' },
    { value: 'MEDIUM', label: t('calendar.priorities.medium') || 'Medium' },
    { value: 'HIGH', label: t('calendar.priorities.high') || 'High' },
    { value: 'URGENT', label: t('calendar.priorities.urgent') || 'Urgent' },
    { value: 'CRITICAL', label: t('calendar.priorities.critical') || 'Critical' },
  ]

  const RECURRING_TYPES = [
    { value: 'DAILY', label: t('calendar.recurringTypes.daily') || 'Daily' },
    { value: 'WEEKLY', label: t('calendar.recurringTypes.weekly') || 'Weekly' },
    { value: 'MONTHLY', label: t('calendar.recurringTypes.monthly') || 'Monthly' },
    { value: 'QUARTERLY', label: t('calendar.recurringTypes.quarterly') || 'Quarterly' },
    { value: 'YEARLY', label: t('calendar.recurringTypes.yearly') || 'Yearly' },
    { value: 'CUSTOM', label: t('calendar.recurringTypes.custom') || 'Custom' },
  ]
  
  const [formData, setFormData] = useState({
    title: '',
    titleAr: '',
    description: '',
    type: 'REMINDER',
    category: 'DOCUMENT',
    startDate: new Date().toISOString().split('T')[0],
    startTime: '09:00',
    endDate: '',
    endTime: '',
    dueDate: '',
    dueTime: '',
    isAllDay: false,
    priority: 'MEDIUM',
    notifyBefore: 7,
    isRecurring: false,
    recurringType: '',
    recurringInterval: 1,
    assignedToId: '',
    notes: '',
  })

  // Load users
  useEffect(() => {
    const loadUsers = async () => {
      try {
        const response = await fetch('/api/users')
        if (response.ok) {
          const data = await response.json()
          setUsers(data.users || data)
        }
      } catch (error) {
        console.error('Error loading users:', error)
      }
    }

    loadUsers()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const eventData = {
        ...formData,
        startDate: formData.isAllDay 
          ? formData.startDate 
          : `${formData.startDate}T${formData.startTime}:00`,
        endDate: formData.endDate 
          ? (formData.isAllDay 
              ? formData.endDate 
              : `${formData.endDate}T${formData.endTime || '17:00'}:00`)
          : null,
        dueDate: formData.dueDate 
          ? (formData.isAllDay 
              ? formData.dueDate 
              : `${formData.dueDate}T${formData.dueTime || '09:00'}:00`)
          : null,
        assignedToId: formData.assignedToId || null,
        recurringType: formData.isRecurring ? formData.recurringType : null,
        recurringInterval: formData.isRecurring ? formData.recurringInterval : null,
      }

      const response = await fetch('/api/calendar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(eventData)
      })

      if (response.ok) {
        alert(t('calendar.eventCreatedSuccessfully') || 'Event created successfully!')
        router.push('/dashboard/calendar')
      } else {
        const error = await response.json()
        alert(`${t('calendar.failedToCreateEvent') || 'Failed to create event'}: ${error.error}`)
      }
    } catch (error) {
      console.error('Error creating event:', error)
      alert(t('calendar.failedToCreateEvent') || 'Failed to create event')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className={`flex-1 space-y-6 p-8 pt-6 ${direction === 'rtl' ? 'font-arabic' : ''}`} dir={direction}>
      <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{t('common.back') || 'Back'}</span>
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('calendar.createEvent') || 'Create New Event'}</h2>
          <p className="text-muted-foreground">
            {t('calendar.manageDescription') || 'Add a new calendar event, reminder, or deadline'}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('calendar.eventInformation') || 'Event Information'}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">{t('calendar.eventTitle') || 'Event Title'} *</Label>
                  <Input
                    id="title"
                    placeholder={t('calendar.eventTitle') || 'Enter event title'}
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="titleAr">{t('calendar.eventTitleAr') || 'Event Title (Arabic)'}</Label>
                  <Input
                    id="titleAr"
                    placeholder="عنوان الحدث"
                    dir="rtl"
                    value={formData.titleAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, titleAr: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">{t('calendar.eventType') || 'Event Type'}</Label>
                  <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('calendar.selectEventType') || 'Select event type'} />
                    </SelectTrigger>
                    <SelectContent>
                      {EVENT_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">{t('calendar.category') || 'Category'}</Label>
                  <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('calendar.selectCategory') || 'Select category'} />
                    </SelectTrigger>
                    <SelectContent>
                      {EVENT_CATEGORIES.map(category => (
                        <SelectItem key={category.value} value={category.value}>{category.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">{t('calendar.priority') || 'Priority'}</Label>
                  <Select value={formData.priority} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('calendar.selectPriority') || 'Select priority'} />
                    </SelectTrigger>
                    <SelectContent>
                      {EVENT_PRIORITIES.map(priority => (
                        <SelectItem key={priority.value} value={priority.value}>{priority.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isAllDay"
                    checked={formData.isAllDay}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isAllDay: !!checked }))}
                  />
                  <Label htmlFor="isAllDay">{t('calendar.allDayEvent') || 'All Day Event'}</Label>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="startDate">{t('calendar.startDate') || 'Start Date'} *</Label>
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="startDate"
                        type="date"
                        className="pl-8"
                        value={formData.startDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                        required
                      />
                    </div>
                    {!formData.isAllDay && (
                      <div className="relative">
                        <Clock className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="time"
                          className="pl-8 w-32"
                          value={formData.startTime}
                          onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">{t('calendar.endDate') || 'End Date'}</Label>
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="endDate"
                        type="date"
                        className="pl-8"
                        value={formData.endDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                      />
                    </div>
                    {!formData.isAllDay && formData.endDate && (
                      <div className="relative">
                        <Clock className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="time"
                          className="pl-8 w-32"
                          value={formData.endTime}
                          onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dueDate">{t('calendar.dueDate') || 'Due Date'}</Label>
                  <div className="flex space-x-2">
                    <div className="relative flex-1">
                      <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="dueDate"
                        type="date"
                        className="pl-8"
                        value={formData.dueDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                      />
                    </div>
                    {!formData.isAllDay && formData.dueDate && (
                      <div className="relative">
                        <Clock className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="time"
                          className="pl-8 w-32"
                          value={formData.dueTime}
                          onChange={(e) => setFormData(prev => ({ ...prev, dueTime: e.target.value }))}
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="assignedToId">{t('calendar.assignTo') || 'Assign To'}</Label>
                  <Select value={formData.assignedToId || 'none'} onValueChange={(value) => setFormData(prev => ({ ...prev, assignedToId: value === 'none' ? '' : value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('calendar.selectAssignee') || 'Select assignee'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">{t('calendar.unassigned') || 'Unassigned'}</SelectItem>
                      {users.map(user => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.name} ({user.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Notification Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">{t('calendar.notificationSettings') || 'Notification Settings'}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="notifyBefore">{t('calendar.notifyBefore') || 'Notify Before (Days)'}</Label>
                  <div className="relative">
                    <Bell className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="notifyBefore"
                      type="number"
                      min="0"
                      className="pl-8"
                      value={formData.notifyBefore}
                      onChange={(e) => setFormData(prev => ({ ...prev, notifyBefore: parseInt(e.target.value) || 0 }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="isRecurring"
                      checked={formData.isRecurring}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isRecurring: !!checked }))}
                    />
                    <Label htmlFor="isRecurring">{t('calendar.recurringEvent') || 'Recurring Event'}</Label>
                  </div>
                  {formData.isRecurring && (
                    <div className="flex space-x-2">
                      <Select value={formData.recurringType} onValueChange={(value) => setFormData(prev => ({ ...prev, recurringType: value }))}>
                        <SelectTrigger className="flex-1">
                          <SelectValue placeholder={t('calendar.selectFrequency') || 'Select frequency'} />
                        </SelectTrigger>
                        <SelectContent>
                          {RECURRING_TYPES.map(type => (
                            <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formData.recurringType === 'CUSTOM' && (
                        <Input
                          type="number"
                          min="1"
                          className="w-20"
                          value={formData.recurringInterval}
                          onChange={(e) => setFormData(prev => ({ ...prev, recurringInterval: parseInt(e.target.value) || 1 }))}
                        />
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Description and Notes */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="description">{t('calendar.description') || 'Description'}</Label>
                <Textarea
                  id="description"
                  placeholder={t('calendar.eventDescriptionPlaceholder') || 'Event description...'}
                  rows={3}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">{t('calendar.additionalNotes') || 'Additional Notes'}</Label>
                <Textarea
                  id="notes"
                  placeholder={t('calendar.additionalNotesPlaceholder') || 'Any additional notes...'}
                  rows={2}
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                {t('calendar.cancel') || 'Cancel'}
              </Button>
              <Button type="submit" disabled={saving}>
                <Save className="mr-2 h-4 w-4" />
                {saving ? (t('calendar.creating') || 'Creating...') : (t('calendar.createEvent') || 'Create Event')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
