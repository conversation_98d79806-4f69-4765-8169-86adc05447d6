# Sidebar Submenu Text Display Fix

## Issue Description

The products submenu in the navigation sidebar was showing only icons without titles/text labels. This affected the user experience as users couldn't identify what each submenu item represented.

## Root Cause

The issue was in the sidebar component (`src/components/layout/sidebar.tsx`) on line 315. The code was trying to access `subItem.nameAr` property which doesn't exist on submenu items.

**Problematic Code:**
```typescript
{direction === 'rtl' ? subItem.nameAr : subItem.name}
```

**Problem:** The submenu items are defined using translation keys like `t('navigation.productManagement')`, so they don't have separate `nameAr` properties. The `subItem.nameAr` was `undefined`, causing the text to not display.

## Solution

Fixed the submenu text display by removing the conditional check for `nameAr` and using only the translated `name` property:

**Fixed Code:**
```typescript
{subItem.name}
```

## Verification

All required translation keys exist in both English and Arabic:

### English (`src/lib/translations/en/navigation.ts`):
- `productManagement: "Product Management"`
- `createProduct: "Create Product"`
- `stockManagement: "Stock Management"`
- `categoryManagement: "Category Management"`
- `unitManagement: "Unit Management"`

### Arabic (`src/lib/translations/ar/navigation.ts`):
- `productManagement: "إدارة المنتجات"`
- `createProduct: "إنشاء منتج"`
- `stockManagement: "إدارة المخزون"`
- `categoryManagement: "إدارة الفئات"`
- `unitManagement: "إدارة الوحدات"`

## Files Modified

- `src/components/layout/sidebar.tsx` - Fixed submenu text display logic

## Expected Result

After this fix:
1. Products submenu should display both icons AND text labels
2. Text should be properly translated based on the current language (Arabic/English)
3. All submenu items should be clearly identifiable by users

## Testing Steps

1. Navigate to the application
2. Expand the "Products" menu in the sidebar
3. Verify that all submenu items show both icons and text:
   - Product Management / إدارة المنتجات
   - Create Product / إنشاء منتج
   - Stock Management / إدارة المخزون
   - Category Management / إدارة الفئات
   - Unit Management / إدارة الوحدات
4. Switch language and verify translations work correctly 