export const financial = {
  title: "Financial Dashboard",
  subtitle: "Comprehensive financial reports, profit & loss analysis, and business insights",

  // Common translations used across financial reports
  common: {
    loading: "Loading financial data...",
    error: "Error loading data",
    noData: "No data available",
    retry: "Retry",
    refresh: "Refresh",
    export: "Export",
    print: "Print",
    share: "Share",
    download: "Download",
    save: "Save",
    cancel: "Cancel",
    close: "Close",
    back: "Back",
    all: "All",
    search: "Search",
    filter: "Filter",
    view: "View",
    edit: "Edit",
    delete: "Delete",
    add: "Add",
    create: "Create",
    update: "Update",
    submit: "Submit",
    confirm: "Confirm",
    success: "Success",
    warning: "Warning",
    total: "Total",
    amount: "Amount",
    date: "Date",
    description: "Description",
    status: "Status",
    category: "Category",
    type: "Type",
    name: "Name",
    value: "Value",
    percentage: "Percentage",
    period: "Period",
    from: "From",
    to: "To",
    today: "Today",
    thisMonth: "This Month",
    lastMonth: "Last Month",
    thisYear: "This Year",
    lastYear: "Last Year",
    monthly: "Monthly",
    quarterly: "Quarterly",
    yearly: "Yearly",
    annual: "Annual",
    noDataFound: "No data found",
    loadingData: "Loading data...",
    generatingReport: "Generating report...",
    dataUpdated: "Data updated successfully",
    reportGenerated: "Report generated successfully",
    operationCompleted: "Operation completed successfully",
    operationFailed: "Operation failed",
    actionRequired: "Action required",
    upcomingDeadline: "Upcoming deadline",
    overdue: "Overdue",
    completed: "Completed",
    pending: "Pending",
    inProgress: "In Progress",
    approved: "Approved",
    rejected: "Rejected",
    active: "Active",
    inactive: "Inactive",
    available: "Available",
    unavailable: "Unavailable",
    upToDate: "Up to date",
    current: "Current",
    previous: "Previous",
    recentTransactions: "Recent Transactions",
    noTransactionsFound: "No transactions found",
    allUpToDate: "All up to date"
  },
  period: {
    lastMonth: "Last Month",
    last3Months: "Last 3 Months",
    last6Months: "Last 6 Months",
    lastYear: "Last Year",
    ytd: "Year to Date",
    current: "Current Period",
    previous: "Previous Period",
    quarterly: "Quarterly",
    annual: "Annual"
  },
  buttons: {
    reports: "Reports",
    plStatement: "P&L Statement",
    back: "Back",
    generate: "Generate", 
    view: "View",
    download: "Download",
    print: "Print"
  },
  cards: {
    profitMargin: "Profit Margin",
    netProfit: "Net Profit",
    totalExpenses: "Total Expenses",
    totalRevenue: "Total Revenue",
    healthyMargin: "Healthy margin ratio",
    fromLastPeriod: "from last period",
    paid: "Paid",
    approved: "Approved",
    pending: "Pending",
    rejected: "Rejected",
    revenueBreakdown: "Revenue Breakdown",
    expenseBreakdown: "Expense Breakdown",
    cashFlowAnalysis: "Cash Flow Analysis",
    cashInflow: "Cash Inflow",
    cashOutflow: "Cash Outflow",
    netCashFlow: "Net Cash Flow",
    inflow: "Inflow",
    outflow: "Outflow",
    net: "Net",
    keyMetrics: "Key Metrics",
    target: "Target:"
  },
  charts: {
    profitMarginTrend: "Profit Margin Trend",
    revenueVsExpensesTrend: "Revenue vs Expenses Trend",
    cashFlow: "Cash Flow",
    revenueBreakdown: "Revenue Breakdown",
    expenseBreakdown: "Expense Breakdown",
    revenue: "Revenue",
    expenses: "Expenses",
    profit: "Profit", 
    profitMargin: "Profit Margin"
  },
  kpis: {
    revenuePerCustomer: "Revenue per Customer",
    customerAcquisitionCost: "Customer Acquisition Cost",
    averageOrderValue: "Average Order Value",
    monthlyRecurringRevenue: "Monthly Recurring Revenue"
  },
  loading: "Loading financial data...",
  noData: "No financial data available",
  
  // Financial Reports Page
  reports: {
    title: "Financial Reports",
    subtitle: "Generate and access comprehensive financial reports and analysis",
    categories: {
      allReports: "All Reports",
      financialStatements: "Financial Statements",
      performanceReports: "Performance Reports",
      budgetReports: "Budget Reports",
      analysisReports: "Analysis Reports",
      complianceReports: "Compliance Reports"
    },
    stats: {
      availableReports: "Available Reports",
      monthlyReports: "Monthly Reports",
      weeklyReports: "Weekly Reports",
      quarterlyReports: "Quarterly Reports"
    },
    table: {
      reportName: "Report Name",
      category: "Category",
      generated: "Generated",
      frequency: "Frequency",
      status: "Status",
      actions: "Actions",
      available: "Available"
    },
    lastGenerated: "Last Generated:",
    recentReports: "Recent Reports",
    frequencies: {
      monthly: "Monthly",
      weekly: "Weekly",
      quarterly: "Quarterly", 
      annual: "Annual"
    },
    reportTypes: {
      profitLoss: {
        name: "Profit & Loss Statement",
        description: "Comprehensive income statement showing revenue, expenses, and net profit"
      },
      balanceSheet: {
        name: "Balance Sheet",
        description: "Statement of financial position showing assets, liabilities, and equity"
      },
      cashFlow: {
        name: "Cash Flow Statement",
        description: "Analysis of cash inflows and outflows from operations, investing, and financing"
      },
      revenueAnalysis: {
        name: "Revenue Analysis",
        description: "Detailed breakdown of revenue by service, customer, and time period"
      },
      expenseAnalysis: {
        name: "Expense Analysis",
        description: "Comprehensive analysis of expenses by category and department"
      },
      customerProfitability: {
        name: "Customer Profitability",
        description: "Analysis of profit margins and revenue contribution by customer"
      },
      budgetVariance: {
        name: "Budget vs Actual",
        description: "Comparison of actual performance against budgeted figures"
      },
      financialRatios: {
        name: "Financial Ratios",
        description: "Key financial ratios and performance indicators analysis"
      },
      taxSummary: {
        name: "Tax Summary",
        description: "Summary of tax obligations and payments for compliance reporting"
      }
    }
  },

  // Budget Variance Report
  budgetVariance: {
    title: "Budget vs Actual Report",
    subtitle: "Compare actual performance against budgeted figures and analyze variances",
    overview: {
      title: "Budget Overview",
      totalBudget: "Total Budget",
      totalActual: "Total Actual",
      totalVariance: "Total Variance",
      variancePercentage: "Variance %",
      favorableVariance: "Favorable Variance",
      unfavorableVariance: "Unfavorable Variance",
      onBudget: "On Budget"
    },
    categories: {
      revenue: "Revenue",
      expenses: "Expenses",
      operatingExpenses: "Operating Expenses",
      capitalExpenses: "Capital Expenses",
      marketing: "Marketing",
      salaries: "Salaries",
      utilities: "Utilities",
      rent: "Rent",
      supplies: "Supplies",
      travel: "Travel",
      other: "Other"
    },
    table: {
      category: "Category",
      budget: "Budget",
      actual: "Actual",
      variance: "Variance",
      variancePercent: "Variance %",
      status: "Status"
    },
    status: {
      favorable: "Favorable",
      unfavorable: "Unfavorable",
      onTarget: "On Target",
      overBudget: "Over Budget",
      underBudget: "Under Budget"
    },
    charts: {
      budgetVsActual: "Budget vs Actual",
      varianceAnalysis: "Variance Analysis",
      monthlyTrend: "Monthly Budget Trend",
      categoryBreakdown: "Category Breakdown",
      varianceByCategory: "Variance by Category"
    },
    filters: {
      period: "Period",
      category: "Category",
      department: "Department",
      showFavorable: "Show Favorable Only",
      showUnfavorable: "Show Unfavorable Only",
      threshold: "Variance Threshold"
    },
    summary: {
      totalCategories: "Total Categories",
      categoriesOverBudget: "Categories Over Budget",
      categoriesUnderBudget: "Categories Under Budget",
      averageVariance: "Average Variance",
      largestVariance: "Largest Variance",
      budgetUtilization: "Budget Utilization"
    },
    actions: {
      exportReport: "Export Report",
      printReport: "Print Report",
      scheduleReport: "Schedule Report",
      shareReport: "Share Report"
    }
  },

  // Financial Ratios Report
  financialRatios: {
    title: "Financial Ratios Report",
    subtitle: "Key financial ratios and performance metrics analysis",
    overview: {
      totalRatios: "Total Ratios",
      healthyRatios: "Healthy Ratios",
      warningRatios: "Warning Ratios",
      criticalRatios: "Critical Ratios",
      overallScore: "Overall Financial Health Score",
      lastUpdated: "Last Updated",
      dataAccuracy: "Data Accuracy"
    },
    categories: {
      liquidity: "Liquidity Ratios",
      profitability: "Profitability Ratios",
      efficiency: "Efficiency Ratios",
      leverage: "Leverage Ratios",
      market: "Market Ratios",
      growth: "Growth Ratios"
    },
    ratios: {
      // Liquidity Ratios
      currentRatio: "Current Ratio",
      quickRatio: "Quick Ratio",
      cashRatio: "Cash Ratio",
      workingCapitalRatio: "Working Capital Ratio",

      // Profitability Ratios
      grossProfitMargin: "Gross Profit Margin",
      netProfitMargin: "Net Profit Margin",
      operatingMargin: "Operating Margin",
      returnOnAssets: "Return on Assets (ROA)",
      returnOnEquity: "Return on Equity (ROE)",
      returnOnInvestment: "Return on Investment (ROI)",

      // Efficiency Ratios
      assetTurnover: "Asset Turnover",
      inventoryTurnover: "Inventory Turnover",
      receivablesTurnover: "Receivables Turnover",
      payablesTurnover: "Payables Turnover",
      workingCapitalTurnover: "Working Capital Turnover",

      // Leverage Ratios
      debtToEquity: "Debt-to-Equity Ratio",
      debtToAssets: "Debt-to-Assets Ratio",
      equityRatio: "Equity Ratio",
      interestCoverage: "Interest Coverage Ratio",
      debtServiceCoverage: "Debt Service Coverage",

      // Growth Ratios
      revenueGrowth: "Revenue Growth Rate",
      profitGrowth: "Profit Growth Rate",
      assetGrowth: "Asset Growth Rate",
      equityGrowth: "Equity Growth Rate"
    },
    table: {
      ratio: "Ratio",
      value: "Value",
      benchmark: "Benchmark",
      status: "Status",
      trend: "Trend",
      category: "Category",
      description: "Description",
      formula: "Formula"
    },
    status: {
      excellent: "Excellent",
      good: "Good",
      fair: "Fair",
      poor: "Poor",
      critical: "Critical",
      healthy: "Healthy",
      warning: "Warning",
      improving: "Improving",
      declining: "Declining",
      stable: "Stable"
    },
    charts: {
      ratioTrends: "Ratio Trends Over Time",
      categoryComparison: "Ratio Categories Comparison",
      benchmarkComparison: "Benchmark vs Actual",
      healthScore: "Financial Health Score",
      liquidityAnalysis: "Liquidity Analysis",
      profitabilityAnalysis: "Profitability Analysis",
      efficiencyAnalysis: "Efficiency Analysis",
      leverageAnalysis: "Leverage Analysis"
    },
    filters: {
      period: "Period",
      category: "Category",
      status: "Status",
      showTrends: "Show Trends",
      showBenchmarks: "Show Benchmarks",
      compareToIndustry: "Compare to Industry"
    },
    summary: {
      financialHealth: "Financial Health",
      keyInsights: "Key Insights",
      recommendations: "Recommendations",
      riskFactors: "Risk Factors",
      strengths: "Strengths",
      weaknesses: "Weaknesses",
      industryComparison: "Industry Comparison",
      historicalTrend: "Historical Trend"
    },
    actions: {
      exportReport: "Export Ratios Report",
      printReport: "Print Report",
      shareReport: "Share Report",
      scheduleReport: "Schedule Report",
      viewTrends: "View Trends",
      compareIndustry: "Compare to Industry",
      downloadData: "Download Data",
      refreshData: "Refresh Data"
    },
    insights: {
      liquidityInsight: "Your liquidity position indicates",
      profitabilityInsight: "Profitability metrics show",
      efficiencyInsight: "Operational efficiency is",
      leverageInsight: "Debt management appears",
      overallInsight: "Overall financial health is",
      recommendation: "We recommend focusing on"
    }
  },

  // Tax Summary Report
  taxSummary: {
    title: "Tax Summary Report",
    subtitle: "Comprehensive tax reporting and compliance summary",
    overview: {
      totalTaxCollected: "Total Tax Collected",
      totalTaxPaid: "Total Tax Paid",
      netTaxPosition: "Net Tax Position",
      taxableRevenue: "Taxable Revenue",
      taxableExpenses: "Taxable Expenses",
      vatRate: "VAT Rate",
      taxPeriod: "Tax Period",
      complianceStatus: "Compliance Status",
      nextFilingDate: "Next Filing Date",
      lastFilingDate: "Last Filing Date"
    },
    categories: {
      vatOnSales: "VAT on Sales",
      vatOnPurchases: "VAT on Purchases",
      inputVat: "Input VAT",
      outputVat: "Output VAT",
      vatRefund: "VAT Refund",
      vatPayable: "VAT Payable",
      exemptSales: "Exempt Sales",
      zeroRatedSales: "Zero-Rated Sales"
    },
    periods: {
      monthly: "Monthly",
      quarterly: "Quarterly",
      annually: "Annually",
      currentMonth: "Current Month",
      currentQuarter: "Current Quarter",
      currentYear: "Current Year",
      lastMonth: "Last Month",
      lastQuarter: "Last Quarter",
      lastYear: "Last Year"
    },
    table: {
      description: "Description",
      taxableAmount: "Taxable Amount",
      taxRate: "Tax Rate",
      taxAmount: "Tax Amount",
      date: "Date",
      invoiceNumber: "Invoice Number",
      customer: "Customer",
      supplier: "Supplier",
      category: "Category",
      status: "Status",
      reference: "Reference"
    },
    status: {
      compliant: "Compliant",
      pending: "Pending",
      overdue: "Overdue",
      filed: "Filed",
      paid: "Paid",
      unpaid: "Unpaid",
      refunded: "Refunded",
      processing: "Processing"
    },
    charts: {
      monthlyTaxTrend: "Monthly Tax Trend",
      taxByCategory: "Tax by Category",
      vatAnalysis: "VAT Analysis",
      complianceOverview: "Compliance Overview",
      taxableVsExempt: "Taxable vs Exempt Sales",
      inputVsOutputVat: "Input vs Output VAT"
    },
    filters: {
      period: "Period",
      taxType: "Tax Type",
      status: "Status",
      dateRange: "Date Range",
      customer: "Customer",
      showDetails: "Show Details",
      includeExempt: "Include Exempt"
    },
    summary: {
      taxLiability: "Tax Liability",
      taxCredits: "Tax Credits",
      netPosition: "Net Position",
      complianceScore: "Compliance Score",
      upcomingDeadlines: "Upcoming Deadlines",
      recommendations: "Recommendations",
      riskFactors: "Risk Factors",
      auditTrail: "Audit Trail"
    },
    actions: {
      exportReport: "Export Tax Report",
      printReport: "Print Report",
      fileReturn: "File Tax Return",
      scheduleReport: "Schedule Report",
      downloadData: "Download Data",
      generateReturn: "Generate Return",
      viewDetails: "View Details",
      refreshData: "Refresh Data"
    },
    compliance: {
      filingStatus: "Filing Status",
      paymentStatus: "Payment Status",
      nextDueDate: "Next Due Date",
      penaltiesInterest: "Penalties & Interest",
      auditHistory: "Audit History",
      documentationStatus: "Documentation Status"
    },
    calculations: {
      grossSales: "Gross Sales",
      exemptSales: "Exempt Sales",
      taxableSales: "Taxable Sales",
      outputVatCalculated: "Output VAT Calculated",
      inputVatClaimed: "Input VAT Claimed",
      vatPayable: "VAT Payable",
      vatRefundable: "VAT Refundable"
    }
  }
};