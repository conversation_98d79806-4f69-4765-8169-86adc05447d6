export const financial = {
  title: "Financial Dashboard",
  subtitle: "Comprehensive financial reports, profit & loss analysis, and business insights",
  period: {
    lastMonth: "Last Month",
    last3Months: "Last 3 Months",
    last6Months: "Last 6 Months",
    lastYear: "Last Year",
    ytd: "Year to Date",
    current: "Current Period",
    previous: "Previous Period",
    quarterly: "Quarterly",
    annual: "Annual"
  },
  buttons: {
    reports: "Reports",
    plStatement: "P&L Statement",
    back: "Back",
    generate: "Generate", 
    view: "View",
    download: "Download",
    print: "Print"
  },
  cards: {
    profitMargin: "Profit Margin",
    netProfit: "Net Profit",
    totalExpenses: "Total Expenses",
    totalRevenue: "Total Revenue",
    healthyMargin: "Healthy margin ratio",
    fromLastPeriod: "from last period",
    paid: "Paid",
    approved: "Approved",
    pending: "Pending",
    rejected: "Rejected",
    revenueBreakdown: "Revenue Breakdown",
    expenseBreakdown: "Expense Breakdown",
    cashFlowAnalysis: "Cash Flow Analysis",
    cashInflow: "Cash Inflow",
    cashOutflow: "Cash Outflow",
    netCashFlow: "Net Cash Flow",
    inflow: "Inflow",
    outflow: "Outflow",
    net: "Net",
    keyMetrics: "Key Metrics",
    target: "Target:"
  },
  charts: {
    profitMarginTrend: "Profit Margin Trend",
    revenueVsExpensesTrend: "Revenue vs Expenses Trend",
    cashFlow: "Cash Flow",
    revenueBreakdown: "Revenue Breakdown",
    expenseBreakdown: "Expense Breakdown",
    revenue: "Revenue",
    expenses: "Expenses",
    profit: "Profit", 
    profitMargin: "Profit Margin"
  },
  kpis: {
    revenuePerCustomer: "Revenue per Customer",
    customerAcquisitionCost: "Customer Acquisition Cost",
    averageOrderValue: "Average Order Value",
    monthlyRecurringRevenue: "Monthly Recurring Revenue"
  },
  loading: "Loading financial data...",
  noData: "No financial data available",
  
  // Financial Reports Page
  reports: {
    title: "Financial Reports",
    subtitle: "Generate and access comprehensive financial reports and analysis",
    categories: {
      allReports: "All Reports",
      financialStatements: "Financial Statements",
      performanceReports: "Performance Reports",
      budgetReports: "Budget Reports",
      analysisReports: "Analysis Reports",
      complianceReports: "Compliance Reports"
    },
    stats: {
      availableReports: "Available Reports",
      monthlyReports: "Monthly Reports",
      weeklyReports: "Weekly Reports",
      quarterlyReports: "Quarterly Reports"
    },
    table: {
      reportName: "Report Name",
      category: "Category",
      generated: "Generated",
      frequency: "Frequency",
      status: "Status",
      actions: "Actions",
      available: "Available"
    },
    lastGenerated: "Last Generated:",
    recentReports: "Recent Reports",
    frequencies: {
      monthly: "Monthly",
      weekly: "Weekly",
      quarterly: "Quarterly", 
      annual: "Annual"
    },
    reportTypes: {
      profitLoss: {
        name: "Profit & Loss Statement",
        description: "Comprehensive income statement showing revenue, expenses, and net profit"
      },
      balanceSheet: {
        name: "Balance Sheet",
        description: "Statement of financial position showing assets, liabilities, and equity"
      },
      cashFlow: {
        name: "Cash Flow Statement",
        description: "Analysis of cash inflows and outflows from operations, investing, and financing"
      },
      revenueAnalysis: {
        name: "Revenue Analysis",
        description: "Detailed breakdown of revenue by service, customer, and time period"
      },
      expenseAnalysis: {
        name: "Expense Analysis",
        description: "Comprehensive analysis of expenses by category and department"
      },
      customerProfitability: {
        name: "Customer Profitability",
        description: "Analysis of profit margins and revenue contribution by customer"
      },
      budgetVariance: {
        name: "Budget vs Actual",
        description: "Comparison of actual performance against budgeted figures"
      },
      financialRatios: {
        name: "Financial Ratios",
        description: "Key financial ratios and performance indicators analysis"
      },
      taxSummary: {
        name: "Tax Summary",
        description: "Summary of tax obligations and payments for compliance reporting"
      }
    }
  },

  // Budget Variance Report
  budgetVariance: {
    title: "Budget vs Actual Report",
    subtitle: "Compare actual performance against budgeted figures and analyze variances",
    overview: {
      title: "Budget Overview",
      totalBudget: "Total Budget",
      totalActual: "Total Actual",
      totalVariance: "Total Variance",
      variancePercentage: "Variance %",
      favorableVariance: "Favorable Variance",
      unfavorableVariance: "Unfavorable Variance",
      onBudget: "On Budget"
    },
    categories: {
      revenue: "Revenue",
      expenses: "Expenses",
      operatingExpenses: "Operating Expenses",
      capitalExpenses: "Capital Expenses",
      marketing: "Marketing",
      salaries: "Salaries",
      utilities: "Utilities",
      rent: "Rent",
      supplies: "Supplies",
      travel: "Travel",
      other: "Other"
    },
    table: {
      category: "Category",
      budget: "Budget",
      actual: "Actual",
      variance: "Variance",
      variancePercent: "Variance %",
      status: "Status"
    },
    status: {
      favorable: "Favorable",
      unfavorable: "Unfavorable",
      onTarget: "On Target",
      overBudget: "Over Budget",
      underBudget: "Under Budget"
    },
    charts: {
      budgetVsActual: "Budget vs Actual",
      varianceAnalysis: "Variance Analysis",
      monthlyTrend: "Monthly Budget Trend",
      categoryBreakdown: "Category Breakdown",
      varianceByCategory: "Variance by Category"
    },
    filters: {
      period: "Period",
      category: "Category",
      department: "Department",
      showFavorable: "Show Favorable Only",
      showUnfavorable: "Show Unfavorable Only",
      threshold: "Variance Threshold"
    },
    summary: {
      totalCategories: "Total Categories",
      categoriesOverBudget: "Categories Over Budget",
      categoriesUnderBudget: "Categories Under Budget",
      averageVariance: "Average Variance",
      largestVariance: "Largest Variance",
      budgetUtilization: "Budget Utilization"
    },
    actions: {
      exportReport: "Export Report",
      printReport: "Print Report",
      scheduleReport: "Schedule Report",
      shareReport: "Share Report"
    }
  }
};