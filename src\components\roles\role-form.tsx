"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { useI18n } from "@/lib/i18n"

interface Permission {
  id: string
  name: string
  nameAr?: string
  module: string
  action: string
  resource?: string
  description?: string
}

interface Role {
  id?: string
  name: string
  nameAr?: string
  description?: string
  permissions?: Array<{
    permission: Permission
  }>
}

interface RoleFormProps {
  role?: Role
  onSave: (roleData: any) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export function RoleForm({ role, onSave, onCancel, loading = false }: RoleFormProps) {
  const { direction } = useI18n()
  const [formData, setFormData] = useState({
    name: role?.name || '',
    nameAr: role?.nameAr || '',
    description: role?.description || '',
  })
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [selectedPermissions, setSelectedPermissions] = useState<Set<string>>(new Set())
  const [permissionsByModule, setPermissionsByModule] = useState<Record<string, Permission[]>>({})
  const [loadingPermissions, setLoadingPermissions] = useState(true)

  useEffect(() => {
    loadPermissions()
  }, [])

  useEffect(() => {
    if (role?.permissions) {
      const rolePermissionIds = new Set(role.permissions.map(rp => rp.permission.id))
      setSelectedPermissions(rolePermissionIds)
    }
  }, [role])

  const loadPermissions = async () => {
    try {
      setLoadingPermissions(true)
      const response = await fetch('/api/permissions?groupByModule=true')
      if (response.ok) {
        const data = await response.json()
        setPermissionsByModule(data)
        
        // Flatten permissions for easier access
        const allPermissions = Object.values(data).flat()
        setPermissions(allPermissions)
      }
    } catch (error) {
      console.error('Error loading permissions:', error)
    } finally {
      setLoadingPermissions(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions(prev => {
      const newSet = new Set(prev)
      if (newSet.has(permissionId)) {
        newSet.delete(permissionId)
      } else {
        newSet.add(permissionId)
      }
      return newSet
    })
  }

  const handleModuleToggle = (module: string) => {
    const modulePermissions = permissionsByModule[module] || []
    const modulePermissionIds = modulePermissions.map(p => p.id)
    const allSelected = modulePermissionIds.every(id => selectedPermissions.has(id))

    setSelectedPermissions(prev => {
      const newSet = new Set(prev)
      if (allSelected) {
        // Deselect all module permissions
        modulePermissionIds.forEach(id => newSet.delete(id))
      } else {
        // Select all module permissions
        modulePermissionIds.forEach(id => newSet.add(id))
      }
      return newSet
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    const roleData = {
      ...formData,
      permissionIds: Array.from(selectedPermissions),
    }

    if (role?.id) {
      roleData.id = role.id
    }

    await onSave(roleData)
  }

  const getModuleDisplayName = (module: string) => {
    const moduleNames: Record<string, { en: string; ar: string }> = {
      dashboard: { en: 'Dashboard', ar: 'لوحة التحكم' },
      customers: { en: 'Customers', ar: 'العملاء' },
      suppliers: { en: 'Suppliers', ar: 'الموردين' },
      products: { en: 'Products', ar: 'المنتجات' },
      categories: { en: 'Categories', ar: 'الفئات' },
      units: { en: 'Units', ar: 'الوحدات' },
      tasks: { en: 'Tasks', ar: 'المهام' },
      invoices: { en: 'Invoices', ar: 'الفواتير' },
      quotations: { en: 'Quotations', ar: 'عروض الأسعار' },
      purchases: { en: 'Purchases', ar: 'المشتريات' },
      expenses: { en: 'Expenses', ar: 'المصروفات' },
      financial: { en: 'Financial', ar: 'المالية' },
      employees: { en: 'Employees', ar: 'الموظفين' },
      reports: { en: 'Reports', ar: 'التقارير' },
      settings: { en: 'Settings', ar: 'الإعدادات' },
      users: { en: 'Users', ar: 'المستخدمين' },
      roles: { en: 'Roles', ar: 'الأدوار' },
      pos: { en: 'POS', ar: 'نقطة البيع' },
      leads: { en: 'Leads', ar: 'العملاء المحتملين' },
    }

    return direction === 'rtl' 
      ? moduleNames[module]?.ar || module 
      : moduleNames[module]?.en || module
  }

  if (loadingPermissions) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading permissions...</p>
        </div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className={`space-y-6 ${direction === 'rtl' ? 'rtl' : 'ltr'}`} dir={direction}>
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>
            {direction === 'rtl' ? 'معلومات الدور' : 'Role Information'}
          </CardTitle>
          <CardDescription>
            {direction === 'rtl' 
              ? 'أدخل المعلومات الأساسية للدور' 
              : 'Enter the basic information for the role'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">
                {direction === 'rtl' ? 'اسم الدور (إنجليزي)' : 'Role Name (English)'}
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder={direction === 'rtl' ? 'أدخل اسم الدور' : 'Enter role name'}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="nameAr">
                {direction === 'rtl' ? 'اسم الدور (عربي)' : 'Role Name (Arabic)'}
              </Label>
              <Input
                id="nameAr"
                value={formData.nameAr}
                onChange={(e) => handleInputChange('nameAr', e.target.value)}
                placeholder={direction === 'rtl' ? 'أدخل اسم الدور بالعربية' : 'Enter role name in Arabic'}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">
              {direction === 'rtl' ? 'الوصف' : 'Description'}
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder={direction === 'rtl' ? 'أدخل وصف الدور' : 'Enter role description'}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Permissions */}
      <Card>
        <CardHeader>
          <CardTitle>
            {direction === 'rtl' ? 'الصلاحيات' : 'Permissions'}
          </CardTitle>
          <CardDescription>
            {direction === 'rtl' 
              ? 'اختر الصلاحيات التي يمكن لهذا الدور الوصول إليها' 
              : 'Select the permissions this role can access'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-96">
            <div className="space-y-4">
              {Object.entries(permissionsByModule).map(([module, modulePermissions]) => {
                const allSelected = modulePermissions.every(p => selectedPermissions.has(p.id))
                const someSelected = modulePermissions.some(p => selectedPermissions.has(p.id))

                return (
                  <div key={module} className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`module-${module}`}
                        checked={allSelected}
                        onCheckedChange={() => handleModuleToggle(module)}
                        className={direction === 'rtl' ? 'ml-2' : 'mr-2'}
                      />
                      <Label 
                        htmlFor={`module-${module}`} 
                        className="text-sm font-medium cursor-pointer"
                      >
                        {getModuleDisplayName(module)}
                      </Label>
                      <Badge variant={someSelected ? "default" : "secondary"} className="text-xs">
                        {modulePermissions.filter(p => selectedPermissions.has(p.id)).length}/{modulePermissions.length}
                      </Badge>
                    </div>
                    
                    <div className={`grid grid-cols-1 md:grid-cols-2 gap-2 ${direction === 'rtl' ? 'mr-6' : 'ml-6'}`}>
                      {modulePermissions.map((permission) => (
                        <div key={permission.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={permission.id}
                            checked={selectedPermissions.has(permission.id)}
                            onCheckedChange={() => handlePermissionToggle(permission.id)}
                            className={direction === 'rtl' ? 'ml-2' : 'mr-2'}
                          />
                          <Label 
                            htmlFor={permission.id} 
                            className="text-sm cursor-pointer"
                          >
                            {direction === 'rtl' && permission.nameAr 
                              ? permission.nameAr 
                              : permission.name
                            }
                          </Label>
                        </div>
                      ))}
                    </div>
                    <Separator />
                  </div>
                )
              })}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className={`flex gap-2 ${direction === 'rtl' ? 'flex-row-reverse' : ''}`}>
        <Button type="submit" disabled={loading}>
          {loading 
            ? (direction === 'rtl' ? 'جاري الحفظ...' : 'Saving...') 
            : (direction === 'rtl' ? 'حفظ' : 'Save')
          }
        </Button>
        <Button type="button" variant="outline" onClick={onCancel}>
          {direction === 'rtl' ? 'إلغاء' : 'Cancel'}
        </Button>
      </div>
    </form>
  )
}
