"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  ArrowLeft, 
  Edit, 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  IdCard, 
  BookOpen, 
  Car, 
  FileCheck, 
  FileText,
  Shield,
  Clock,
  CheckCircle,
  AlertTriangle,
  Target,
  DollarSign,
  TrendingUp
} from "lucide-react"
import { useI18n } from "@/lib/i18n"

interface Employee {
  id: string
  name: string
  email: string
  phone: string
  role: string
  isActive: boolean
  createdAt: string
  joinDate?: string
  tasksCompleted?: number
  tasksInProgress?: number
  completionRate?: number
  idCardNumber?: string
  idCardExpiry?: string
  passportNumber?: string
  passportExpiry?: string
  visaNumber?: string
  visaExpiry?: string
  licenseNumber?: string
  licenseExpiry?: string
  contractExpiry?: string
  salesData?: {
    totalSales?: number
    commission?: number
    monthlyTarget?: number
    invoicesCount?: number
    avgOrderValue?: number
    conversionRate?: number
    rank?: number
    growth?: number
  }
}

const roleColors = {
  ADMIN: "bg-purple-100 text-purple-800",
  MANAGER: "bg-blue-100 text-blue-800",
  EMPLOYEE: "bg-green-100 text-green-800",
}

export default function ViewEmployeePage() {
  const router = useRouter()
  const params = useParams()
  const { t, formatCurrency } = useI18n()
  const [employee, setEmployee] = useState<Employee | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadEmployee = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/employees/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setEmployee(data)
        } else {
          alert('Failed to load employee data')
          router.push('/dashboard/employees')
        }
      } catch (error) {
        console.error('Error loading employee:', error)
        alert('Failed to load employee data')
        router.push('/dashboard/employees')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      loadEmployee()
    }
  }, [params.id, router])

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getDaysUntilExpiry = (expiryDate: string) => {
    const now = new Date()
    const expiry = new Date(expiryDate)
    const diffTime = expiry.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getExpiryStatus = (daysUntil: number) => {
    if (daysUntil < 0) return { label: "Expired", color: "text-red-600", bgColor: "bg-red-100" }
    if (daysUntil <= 7) return { label: "Critical", color: "text-red-500", bgColor: "bg-red-50" }
    if (daysUntil <= 30) return { label: "Warning", color: "text-orange-500", bgColor: "bg-orange-50" }
    if (daysUntil <= 90) return { label: "Caution", color: "text-yellow-600", bgColor: "bg-yellow-50" }
    return { label: "Valid", color: "text-green-600", bgColor: "bg-green-50" }
  }

  const handleCreateRenewalEvent = async (docType: string, expiry: string) => {
    if (!employee) return
    
    try {
      const eventData = {
        title: `${docType} Renewal - ${employee.name}`,
        titleAr: `تجديد ${docType} - ${employee.name}`,
        type: 'RENEWAL',
        category: 'HR',
        startDate: expiry,
        dueDate: expiry,
        priority: 'HIGH',
        relatedEntityType: 'employee',
        relatedEntityId: employee.id,
        assignedToId: employee.id,
        notes: `${docType} renewal reminder for ${employee.name}`,
        notifyBefore: 30,
      }

      const response = await fetch('/api/calendar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(eventData)
      })

      if (response.ok) {
        alert(`Renewal reminder created for ${employee.name}'s ${docType}`)
      } else {
        alert('Failed to create renewal reminder')
      }
    } catch (error) {
      console.error('Error creating renewal event:', error)
      alert('Failed to create renewal reminder')
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!employee) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h3 className="text-lg font-medium">Employee not found</h3>
            <p className="text-muted-foreground">The employee you&apos;re looking for doesn&apos;t exist.</p>
            <Button onClick={() => router.push('/dashboard/employees')} className="mt-4">
              Back to Employees
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>{t('employees.back')}</span>
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('employees.employeeDetails')}</h2>
            <p className="text-muted-foreground">
              {t('employees.viewEmployeeInformation')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={() => router.push(`/dashboard/employees/${employee.id}/edit`)}>
            <Edit className="mr-2 h-4 w-4" />
            {t('employees.editEmployee')}
          </Button>
        </div>
      </div>

      {/* Employee Header */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-6">
            <Avatar className="h-20 w-20">
              <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${employee.name}`} />
              <AvatarFallback className="text-lg">{getInitials(employee.name)}</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center space-x-3">
                <h1 className="text-2xl font-bold">{employee.name}</h1>
                <Badge className={roleColors[employee.role as keyof typeof roleColors]}>
                  {employee.role}
                </Badge>
                <Badge className={employee.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                  {employee.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
              <div className="mt-2 space-y-1">
                <div className="flex items-center text-muted-foreground">
                  <Mail className="mr-2 h-4 w-4" />
                  {employee.email}
                </div>
                <div className="flex items-center text-muted-foreground">
                  <Phone className="mr-2 h-4 w-4" />
                  {employee.phone}
                </div>
                <div className="flex items-center text-muted-foreground">
                  <Calendar className="mr-2 h-4 w-4" />
                  {t('employees.joinedDate')}: {new Date(employee.createdAt).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>{t('employees.performanceMetrics')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-900">{employee.tasksCompleted || 0}</div>
                <div className="text-sm text-blue-600">{t('employees.tasksCompleted')}</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-900">{employee.tasksInProgress || 0}</div>
                <div className="text-sm text-orange-600">{t('employees.tasksInProgress')}</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg col-span-2">
                <div className="text-2xl font-bold text-green-900">{employee.completionRate || 0}%</div>
                <div className="text-sm text-green-600">{t('employees.completionRate')}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sales Performance */}
        {employee.salesData && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>{t('employees.salesPerformanceMetrics')}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">{t('employees.totalSales')}</span>
                  <span className="font-medium">{formatCurrency(employee.salesData.totalSales || 0)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">{t('employees.commission')}</span>
                  <span className="font-medium">{formatCurrency(employee.salesData.commission || 0)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">{t('employees.monthlyTarget')}</span>
                  <span className="font-medium">{formatCurrency(employee.salesData.monthlyTarget || 0)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">{t('employees.invoicesCount')}</span>
                  <span className="font-medium">{employee.salesData.invoicesCount || 0}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">{t('employees.conversionRate')}</span>
                  <span className="font-medium">{employee.salesData.conversionRate || 0}%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Document Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>{t('employees.documentStatus')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* ID Card */}
            {(employee.idCardNumber || employee.idCardExpiry) && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm font-medium">
                  <IdCard className="h-4 w-4 text-blue-600" />
                  <span>ID Card</span>
                </div>
                <div className="space-y-2">
                  {employee.idCardNumber && (
                    <div>
                      <div className="text-xs text-muted-foreground">Number</div>
                      <div className="font-medium">{employee.idCardNumber}</div>
                    </div>
                  )}
                  {employee.idCardExpiry && (
                    <div>
                      <div className="text-xs text-muted-foreground">Expiry Date</div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{new Date(employee.idCardExpiry).toLocaleDateString()}</span>
                        {(() => {
                          const daysUntil = getDaysUntilExpiry(employee.idCardExpiry)
                          const status = getExpiryStatus(daysUntil)
                          return (
                            <div className="flex items-center space-x-2">
                              <Badge className={`${status.bgColor} ${status.color} text-xs`}>
                                {status.label}
                              </Badge>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleCreateRenewalEvent('ID Card', employee.idCardExpiry!)}
                                className="text-xs h-6"
                              >
                                Set Reminder
                              </Button>
                            </div>
                          )
                        })()}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Passport */}
            {(employee.passportNumber || employee.passportExpiry) && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm font-medium">
                  <BookOpen className="h-4 w-4 text-green-600" />
                  <span>Passport</span>
                </div>
                <div className="space-y-2">
                  {employee.passportNumber && (
                    <div>
                      <div className="text-xs text-muted-foreground">Number</div>
                      <div className="font-medium">{employee.passportNumber}</div>
                    </div>
                  )}
                  {employee.passportExpiry && (
                    <div>
                      <div className="text-xs text-muted-foreground">Expiry Date</div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{new Date(employee.passportExpiry).toLocaleDateString()}</span>
                        {(() => {
                          const daysUntil = getDaysUntilExpiry(employee.passportExpiry)
                          const status = getExpiryStatus(daysUntil)
                          return (
                            <div className="flex items-center space-x-2">
                              <Badge className={`${status.bgColor} ${status.color} text-xs`}>
                                {status.label}
                              </Badge>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleCreateRenewalEvent('Passport', employee.passportExpiry!)}
                                className="text-xs h-6"
                              >
                                Set Reminder
                              </Button>
                            </div>
                          )
                        })()}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Visa */}
            {(employee.visaNumber || employee.visaExpiry) && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm font-medium">
                  <FileText className="h-4 w-4 text-purple-600" />
                  <span>Visa</span>
                </div>
                <div className="space-y-2">
                  {employee.visaNumber && (
                    <div>
                      <div className="text-xs text-muted-foreground">Number</div>
                      <div className="font-medium">{employee.visaNumber}</div>
                    </div>
                  )}
                  {employee.visaExpiry && (
                    <div>
                      <div className="text-xs text-muted-foreground">Expiry Date</div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{new Date(employee.visaExpiry).toLocaleDateString()}</span>
                        {(() => {
                          const daysUntil = getDaysUntilExpiry(employee.visaExpiry)
                          const status = getExpiryStatus(daysUntil)
                          return (
                            <div className="flex items-center space-x-2">
                              <Badge className={`${status.bgColor} ${status.color} text-xs`}>
                                {status.label}
                              </Badge>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleCreateRenewalEvent('Visa', employee.visaExpiry!)}
                                className="text-xs h-6"
                              >
                                Set Reminder
                              </Button>
                            </div>
                          )
                        })()}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* License */}
            {(employee.licenseNumber || employee.licenseExpiry) && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm font-medium">
                  <Car className="h-4 w-4 text-orange-600" />
                  <span>License</span>
                </div>
                <div className="space-y-2">
                  {employee.licenseNumber && (
                    <div>
                      <div className="text-xs text-muted-foreground">Number</div>
                      <div className="font-medium">{employee.licenseNumber}</div>
                    </div>
                  )}
                  {employee.licenseExpiry && (
                    <div>
                      <div className="text-xs text-muted-foreground">Expiry Date</div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{new Date(employee.licenseExpiry).toLocaleDateString()}</span>
                        {(() => {
                          const daysUntil = getDaysUntilExpiry(employee.licenseExpiry)
                          const status = getExpiryStatus(daysUntil)
                          return (
                            <div className="flex items-center space-x-2">
                              <Badge className={`${status.bgColor} ${status.color} text-xs`}>
                                {status.label}
                              </Badge>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleCreateRenewalEvent('License', employee.licenseExpiry!)}
                                className="text-xs h-6"
                              >
                                Set Reminder
                              </Button>
                            </div>
                          )
                        })()}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Contract */}
            {employee.contractExpiry && (
              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm font-medium">
                  <FileCheck className="h-4 w-4 text-indigo-600" />
                  <span>Contract</span>
                </div>
                <div className="space-y-2">
                  <div>
                    <div className="text-xs text-muted-foreground">Expiry Date</div>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{new Date(employee.contractExpiry).toLocaleDateString()}</span>
                      {(() => {
                        const daysUntil = getDaysUntilExpiry(employee.contractExpiry)
                        const status = getExpiryStatus(daysUntil)
                        return (
                          <div className="flex items-center space-x-2">
                            <Badge className={`${status.bgColor} ${status.color} text-xs`}>
                              {status.label}
                            </Badge>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCreateRenewalEvent('Contract', employee.contractExpiry!)}
                              className="text-xs h-6"
                            >
                              Set Reminder
                            </Button>
                          </div>
                        )
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* No Documents Message */}
          {!employee.idCardNumber && !employee.idCardExpiry && 
           !employee.passportNumber && !employee.passportExpiry &&
           !employee.visaNumber && !employee.visaExpiry &&
           !employee.licenseNumber && !employee.licenseExpiry &&
           !employee.contractExpiry && (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No document information available</p>
              <Button 
                variant="outline" 
                className="mt-2"
                onClick={() => router.push(`/dashboard/employees/${employee.id}/edit`)}
              >
                Add Document Information
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
