const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function createBasicData() {
  try {
    console.log('🌱 Creating basic data...')

    // Create a basic category
    let category = await prisma.category.findFirst({
      where: { name: 'General' }
    })

    if (!category) {
      category = await prisma.category.create({
        data: {
          name: 'General',
          nameAr: 'عام',
          description: 'General category for products and services',
          isActive: true,
        },
      })
    }

    // Create a basic supplier
    const supplier = await prisma.supplier.upsert({
      where: { mobile: '+968 9999 9999' },
      update: {},
      create: {
        name: 'General Supplier',
        nameAr: 'مورد عام',
        mobile: '+968 9999 9999',
        email: '<EMAIL>',
        company: 'General Supply Co.',
        address: 'Muscat, Oman',
        city: 'Muscat',
        country: 'Oman',
        isActive: true,
      },
    })

    // Create a basic customer
    const customer = await prisma.customer.upsert({
      where: { mobile: '+968 8888 8888' },
      update: {},
      create: {
        name: 'General Customer',
        nameAr: 'عميل عام',
        mobile: '+968 8888 8888',
        email: '<EMAIL>',
        company: 'General Company',
        address: 'Muscat, Oman',
        city: 'Muscat',
        country: 'Oman',
        isActive: true,
      },
    })

    // Create basic settings
    await prisma.setting.upsert({
      where: { key: 'company_name' },
      update: { value: 'Muscat Print & Design Center' },
      create: {
        key: 'company_name',
        value: 'Muscat Print & Design Center',
        description: 'Company name',
      },
    })

    await prisma.setting.upsert({
      where: { key: 'company_name_ar' },
      update: { value: 'مركز مسقط للطباعة والتصميم' },
      create: {
        key: 'company_name_ar',
        value: 'مركز مسقط للطباعة والتصميم',
        description: 'Company name in Arabic',
      },
    })

    await prisma.setting.upsert({
      where: { key: 'default_currency' },
      update: { value: 'OMR' },
      create: {
        key: 'default_currency',
        value: 'OMR',
        description: 'Default currency (Omani Rial)',
      },
    })

    console.log('✅ Basic data created successfully!')
    console.log('📂 Category:', category.name)
    console.log('🏢 Supplier:', supplier.name)
    console.log('👤 Customer:', customer.name)
    console.log('⚙️ Settings: Company name and currency configured')

  } catch (error) {
    console.error('❌ Error creating basic data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createBasicData()
