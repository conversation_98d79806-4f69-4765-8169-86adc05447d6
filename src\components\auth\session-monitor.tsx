"use client"

import { useEffect, useRef } from "react"
import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { sessionManager } from "@/lib/session-storage"

export function SessionMonitor() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const sessionCheckIntervalRef = useRef<NodeJS.Timeout>()
  const sessionManagerRef = useRef(sessionManager.getInstance())

  useEffect(() => {
    // Check if session is expired
    if (status === "unauthenticated") {
      // Clean up session manager
      sessionManagerRef.current.cleanup()

      // Redirect to login
      router.push("/auth/login")
    }
  }, [status, router])

  useEffect(() => {
    if (!session) return

    // Monitor for session expiration and activity
    const checkSession = async () => {
      try {
        // Check if session is expired due to inactivity
        if (sessionManagerRef.current.isSessionExpired()) {
          await signOut({ redirect: false })
          router.push("/auth/login")
          return
        }

        // Check if session is still valid on server
        const response = await fetch("/api/auth/session")
        if (!response.ok) {
          await signOut({ redirect: false })
          router.push("/auth/login")
          return
        }

        // Check if this is still a valid browser session
        if (!sessionManagerRef.current.isValidSession()) {
          await signOut({ redirect: false })
          router.push("/auth/login")
          return
        }
      } catch (error) {
        console.error("Session check failed:", error)
      }
    }

    // Check session every 2 minutes
    sessionCheckIntervalRef.current = setInterval(checkSession, 2 * 60 * 1000)

    return () => {
      if (sessionCheckIntervalRef.current) {
        clearInterval(sessionCheckIntervalRef.current)
      }
    }
  }, [session, router])

  // Monitor browser visibility and user activity
  useEffect(() => {
    if (!session) return

    const handleVisibilityChange = async () => {
      if (document.visibilityState === "visible") {
        sessionManagerRef.current.updateActivity()

        // Check session when user returns to tab
        try {
          const response = await fetch("/api/auth/session")
          if (!response.ok) {
            await signOut({ redirect: false })
            router.push("/auth/login")
          }
        } catch (error) {
          console.error("Session check on visibility change failed:", error)
        }
      }
    }

    // Track user activity
    const handleUserActivity = () => {
      sessionManagerRef.current.updateActivity()
    }

    // Events that indicate user activity
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']

    // Add event listeners
    document.addEventListener("visibilitychange", handleVisibilityChange)
    activityEvents.forEach(event => {
      document.addEventListener(event, handleUserActivity, { passive: true })
    })

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleUserActivity)
      })
    }
  }, [session, router])

  // Initialize session manager on mount
  useEffect(() => {
    if (session) {
      // Session manager is already initialized in the ref
      sessionManagerRef.current.updateActivity()
    }
  }, [session])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (sessionCheckIntervalRef.current) {
        clearInterval(sessionCheckIntervalRef.current)
      }
    }
  }, [])

  return null // This component doesn't render anything
}
