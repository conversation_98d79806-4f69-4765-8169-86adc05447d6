import { PrismaClient } from '@prisma/client'
import * as bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function createAdminUser() {
  try {
    console.log('🔐 Creating admin user...')

    // Check if admin already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingAdmin) {
      console.log('✅ Admin user already exists!')
      console.log('📧 Email: <EMAIL>')
      console.log('🔑 Password: admin123')
      return
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10)

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin User',
        role: 'ADMIN',
        phone: '+968 9123 4567',
        isActive: true,
        joinDate: new Date(),
        tasksCompleted: 0,
        tasksInProgress: 0,
        completionRate: 0,
      },
    })

    console.log('✅ Admin user created successfully!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: admin123')
    console.log('👤 Name:', adminUser.name)
    console.log('🎭 Role:', adminUser.role)

  } catch (error) {
    console.error('❌ Error creating admin user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the function
createAdminUser()
  .catch((error) => {
    console.error('❌ Failed to create admin user:', error)
    process.exit(1)
  })
