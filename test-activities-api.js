// Test script for activities and follow-ups API
const baseUrl = 'http://localhost:3004'

async function testActivitiesAPI() {
  try {
    console.log('Testing Activities API...')
    
    // Test GET activities for a lead
    const leadId = 'test-lead-id'
    const response = await fetch(`${baseUrl}/api/leads/${leadId}/activities`)
    
    if (response.ok) {
      const activities = await response.json()
      console.log('✅ Activities API working:', activities)
    } else {
      console.log('❌ Activities API error:', response.status, await response.text())
    }
  } catch (error) {
    console.log('❌ Activities API error:', error.message)
  }
}

async function testFollowUpsAPI() {
  try {
    console.log('Testing Follow-ups API...')
    
    // Test GET follow-ups for a lead
    const leadId = 'test-lead-id'
    const response = await fetch(`${baseUrl}/api/leads/${leadId}/followups`)
    
    if (response.ok) {
      const followUps = await response.json()
      console.log('✅ Follow-ups API working:', followUps)
    } else {
      console.log('❌ Follow-ups API error:', response.status, await response.text())
    }
  } catch (error) {
    console.log('❌ Follow-ups API error:', error.message)
  }
}

// Run tests
testActivitiesAPI()
testFollowUpsAPI() 