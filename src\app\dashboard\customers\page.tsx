"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Edit, Trash2, Phone, Mail, MoreHorizontal, Eye, BarChart3, Receipt, FileText } from "lucide-react"
import { useI18n } from "@/lib/i18n"
import { formatCurrency } from "@/lib/localization"
import { toast } from "sonner"

interface Customer {
  id: string
  name: string
  email?: string
  mobile?: string
  phone?: string
  company?: string
  taxNumber?: string
  address?: string
  isActive?: boolean
  createdAt?: string
  updatedAt?: string
  totalSpent?: number
  outstandingBalance?: number
  invoiceCount?: number
}

export default function CustomersPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    taxNumber: '',
    address: ''
  })

  // Fetch customers from API
  const fetchCustomers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/customers')
      if (response.ok) {
        const data = await response.json()
        setCustomers(data.customers || [])
      } else {
        console.error('Failed to fetch customers')
        toast.error('Failed to load customers')
      }
    } catch (error) {
      console.error('Error fetching customers:', error)
      toast.error('Error loading customers')
    } finally {
      setLoading(false)
    }
  }

  // Load customers on component mount
  useEffect(() => {
    fetchCustomers()
  }, [])

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (customer.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (customer.company || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      company: '',
      taxNumber: '',
      address: ''
    })
  }

  const populateForm = (customer: Customer) => {
    setFormData({
      name: customer.name || '',
      email: customer.email || '',
      phone: customer.mobile || customer.phone || '', // Use mobile if available, fallback to phone
      company: customer.company || '',
      taxNumber: customer.taxNumber || '',
      address: customer.address || ''
    })
  }

  const handleAddCustomer = () => {
    setEditingCustomer(null)
    resetForm()
    setIsDialogOpen(true)
  }

  const handleViewCustomer = (customer: Customer) => {
    router.push(`/dashboard/customers/${customer.id}`)
  }

  const handleEditCustomer = (customer: Customer) => {
    setEditingCustomer(customer)
    populateForm(customer)
    setIsDialogOpen(true)
  }

  const handleDeleteCustomer = async (customer: Customer) => {
    if (confirm(`${t('customers.confirmDelete')}\n\n${customer.name} - ${customer.company || ''}`)) {
      try {
        const response = await fetch(`/api/customers/${customer.id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          setCustomers(customers.filter(c => c.id !== customer.id))
          toast.success(`🗑️ ${t('customers.customerDeleted')}: ${customer.name}`)
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to delete customer')
        }
      } catch (error) {
        console.error('Error deleting customer:', error)
        toast.error('Error deleting customer')
      }
    }
  }

  const handleCreateInvoice = (customer: Customer) => {
    router.push(`/dashboard/invoices/create?customer=${customer.id}`)
  }

  const handleCreateQuotation = (customer: Customer) => {
    router.push(`/dashboard/quotations/create?customer=${customer.id}`)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSaveCustomer = async () => {
    if (!formData.name || !formData.phone) {
      toast.error(`${t('forms.required')}: ${t('customers.customerName')}, ${t('customers.customerPhone')}`)
      return
    }

    try {
      const method = editingCustomer ? 'PUT' : 'POST'
      const url = editingCustomer ? `/api/customers/${editingCustomer.id}` : '/api/customers'

      // Map phone field to mobile for API compatibility
      const payload = {
        ...formData,
        mobile: formData.phone // Map phone to mobile for backend
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (response.ok) {
        const savedCustomer = await response.json()

        if (editingCustomer) {
          // Update existing customer in state
          setCustomers(customers.map(customer =>
            customer.id === editingCustomer.id ? savedCustomer : customer
          ))
          toast.success(`✅ ${t('customers.customerUpdated')}: ${savedCustomer.name}`)
        } else {
          // Add new customer to state
          setCustomers([...customers, savedCustomer])
          toast.success(`✅ ${t('customers.customerAdded')}: ${savedCustomer.name}`)
        }

        handleCloseDialog()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to save customer')
      }
    } catch (error) {
      console.error('Error saving customer:', error)
      toast.error('Error saving customer')
    }
  }

  const handleCloseDialog = () => {
    setIsDialogOpen(false)
    setEditingCustomer(null)
    resetForm()
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('customers.title')}</h2>
          <p className="text-muted-foreground">
            {t('customers.manageCustomers')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/customers/dashboard')}>
            <BarChart3 className="mr-2 h-4 w-4" />
            {t('customers.dashboard')}
          </Button>
          <Button onClick={handleAddCustomer}>
            <Plus className="mr-2 h-4 w-4" />
            {t('customers.addCustomer')}
          </Button>
          <Dialog open={isDialogOpen} onOpenChange={handleCloseDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{editingCustomer ? t('customers.editCustomer') : t('customers.addCustomer')}</DialogTitle>
              <DialogDescription>
                {editingCustomer ? 'تحديث تفاصيل العميل أدناه.' : 'أدخل تفاصيل العميل أدناه.'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  {t('customers.customerName')} *
                </Label>
                <Input
                  id="name"
                  className="col-span-3"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t('customers.customerName')}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  {t('customers.customerEmail')}
                </Label>
                <Input
                  id="email"
                  type="email"
                  className="col-span-3"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone" className="text-right">
                  {t('customers.customerPhone')} *
                </Label>
                <Input
                  id="phone"
                  className="col-span-3"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+968 9123 4567"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="company" className="text-right">
                  {t('customers.customerCompany')}
                </Label>
                <Input
                  id="company"
                  className="col-span-3"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  placeholder={t('customers.customerCompany')}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="taxNumber" className="text-right">
                  {t('customers.taxNumber')}
                </Label>
                <Input
                  id="taxNumber"
                  className="col-span-3"
                  value={formData.taxNumber}
                  onChange={(e) => handleInputChange('taxNumber', e.target.value)}
                  placeholder="OM123456789"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="address" className="text-right">
                  {t('customers.customerAddress')}
                </Label>
                <Textarea
                  id="address"
                  className="col-span-3"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder={t('customers.customerAddress')}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={handleCloseDialog}>
                {t('common.cancel')}
              </Button>
              <Button type="submit" onClick={handleSaveCustomer}>
                {editingCustomer ? t('customers.editCustomer') : t('common.save')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('customers.searchCustomers')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="table-container">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="font-semibold text-foreground">{t('customers.customerName')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('common.contact')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('customers.totalSpent')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('customers.outstandingBalance')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('customers.invoices')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('common.status')}</TableHead>
              <TableHead className="text-right font-semibold text-foreground">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-12 text-muted-foreground">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="text-4xl">⏳</div>
                    <div className="text-lg font-medium">Loading customers...</div>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredCustomers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-12 text-muted-foreground">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="text-4xl">👥</div>
                    <div className="text-lg font-medium">{t('customers.noCustomersFound')}</div>
                    <div className="text-sm">{searchTerm ? t('customers.tryDifferentSearch') : 'ابدأ بإضافة عميل جديد'}</div>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredCustomers.map((customer) => (
                <TableRow key={customer.id} className="hover:bg-muted/30 transition-colors">
                  <TableCell>
                    <div>
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium text-primary hover:text-primary/80 hover:underline"
                        onClick={() => handleViewCustomer(customer)}
                      >
                        {customer.name}
                      </Button>
                      <div className="text-sm text-muted-foreground">{customer.company}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex items-center text-sm">
                        <Phone className="mr-1 h-3 w-3 text-muted-foreground" />
                        {customer.phone}
                      </div>
                      <div className="flex items-center text-sm">
                        <Mail className="mr-1 h-3 w-3 text-muted-foreground" />
                        {customer.email}
                      </div>
                    </div>
                  </TableCell>
                <TableCell className="font-medium text-green-700 dark:text-green-400 numeric-cell">
                  {formatCurrency(customer.totalSpent || 0)}
                </TableCell>
                <TableCell className="numeric-cell">
                  {(customer.outstandingBalance || 0) > 0 ? (
                    <span className="text-orange-600 font-medium bg-orange-50 dark:bg-orange-950 px-2 py-1 rounded-md">
                      {formatCurrency(customer.outstandingBalance || 0)}
                    </span>
                  ) : (
                    <span className="text-green-600 bg-green-50 dark:bg-green-950 px-2 py-1 rounded-md font-medium">{t('customers.paid')}</span>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800">
                    {customer.invoiceCount || 0} {t('customers.invoices')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={(customer.isActive !== false) ? "default" : "secondary"}>
                    {(customer.isActive !== false) ? t('common.active') : t('common.inactive')}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewCustomer(customer)}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t('customers.viewDetails')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditCustomer(customer)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('customers.editCustomer')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleCreateInvoice(customer)}>
                        <Receipt className="mr-2 h-4 w-4" />
                        {t('customers.createInvoice')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleCreateQuotation(customer)}>
                        <FileText className="mr-2 h-4 w-4" />
                        {t('customers.createQuotation')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteCustomer(customer)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('customers.deleteCustomer')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
