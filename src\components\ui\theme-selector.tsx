"use client"

import * as React from "react"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Moon, Sun, Monitor } from "lucide-react"
import { useI18n } from "@/lib/i18n"

export function ThemeSelector() {
  const { setTheme, theme } = useTheme()
  const { t, direction } = useI18n()
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  const getThemeIcon = () => {
    switch (theme) {
      case "light":
        return <Sun className="h-4 w-4" />
      case "dark":
        return <Moon className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  if (!mounted) {
    return (
      <Button variant="ghost" size="sm" disabled>
        <Monitor className="h-4 w-4" />
        <span className="sr-only">{t('common.theme')}</span>
      </Button>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" dir={direction}>
          {getThemeIcon()}
          <span className="sr-only">{t('common.theme')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={direction === 'rtl' ? 'start' : 'end'}>
        <DropdownMenuItem
          onClick={() => setTheme("light")}
          className={`${theme === 'light' ? 'bg-accent' : ''} ${direction === 'rtl' ? 'text-right' : 'text-left'}`}
        >
          <Sun className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
          {t('common.light')}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("dark")}
          className={`${theme === 'dark' ? 'bg-accent' : ''} ${direction === 'rtl' ? 'text-right' : 'text-left'}`}
        >
          <Moon className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
          {t('common.dark')}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("system")}
          className={`${theme === 'system' ? 'bg-accent' : ''} ${direction === 'rtl' ? 'text-right' : 'text-left'}`}
        >
          <Monitor className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
          {t('common.system')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
