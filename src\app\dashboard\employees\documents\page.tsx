"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  FileText,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
  CreditCard,
  BookOpen,
  IdCard,
  Car,
  FileCheck,
} from "lucide-react"
import { useI18n } from "@/lib/i18n"

interface Employee {
  id: string
  name: string
  email: string
  phone?: string
  avatar?: string
  isActive: boolean
  joinDate: string
  idCardNumber?: string
  idCardExpiry?: string
  passportNumber?: string
  passportExpiry?: string
  visaNumber?: string
  visaExpiry?: string
  licenseNumber?: string
  licenseExpiry?: string
  contractExpiry?: string
}

interface DocumentExpiration {
  type: string
  number?: string
  expiry: string
  daysUntil: number
  icon: any
  color: string
}

export default function EmployeeDocumentsPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [employees, setEmployees] = useState<Employee[]>([])
  const [statusFilter, setStatusFilter] = useState("all")
  const [expiryFilter, setExpiryFilter] = useState("all")
  const [loading, setLoading] = useState(true)

  // Load employees from API
  useEffect(() => {
    const loadEmployees = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/employees')
        if (response.ok) {
          const data = await response.json()
          // Handle both array and object with employees property
          const employeesList = Array.isArray(data) ? data : (data.employees || [])
          setEmployees(employeesList)
        }
      } catch (error) {
        console.error('Error loading employees:', error)
        setEmployees([])
      } finally {
        setLoading(false)
      }
    }

    loadEmployees()
  }, [])

  const getDocumentExpirations = (employee: Employee): DocumentExpiration[] => {
    const expirations: DocumentExpiration[] = []
    const now = new Date()

    const addExpiration = (type: string, number: string | undefined, expiry: string | undefined, icon: any, baseColor: string) => {
      if (expiry) {
        const expiryDate = new Date(expiry)
        const diffTime = expiryDate.getTime() - now.getTime()
        const daysUntil = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
        
        let color = baseColor
        if (daysUntil < 0) color = "text-red-600"
        else if (daysUntil <= 30) color = "text-red-500"
        else if (daysUntil <= 60) color = "text-orange-500"
        else if (daysUntil <= 90) color = "text-yellow-600"

        expirations.push({
          type,
          number,
          expiry,
          daysUntil,
          icon,
          color,
        })
      }
    }

    addExpiration("ID Card", employee.idCardNumber, employee.idCardExpiry, IdCard, "text-blue-600")
    addExpiration("Passport", employee.passportNumber, employee.passportExpiry, BookOpen, "text-green-600")
    addExpiration("Visa", employee.visaNumber, employee.visaExpiry, FileText, "text-purple-600")
    addExpiration("License", employee.licenseNumber, employee.licenseExpiry, Car, "text-orange-600")
    addExpiration("Contract", undefined, employee.contractExpiry, FileCheck, "text-indigo-600")

    return expirations.sort((a, b) => a.daysUntil - b.daysUntil)
  }

  const getExpiryStatus = (daysUntil: number) => {
    if (daysUntil < 0) return { label: "Expired", color: "bg-red-100 text-red-800" }
    if (daysUntil <= 7) return { label: "Critical", color: "bg-red-100 text-red-800" }
    if (daysUntil <= 30) return { label: "Warning", color: "bg-orange-100 text-orange-800" }
    if (daysUntil <= 90) return { label: "Caution", color: "bg-yellow-100 text-yellow-800" }
    return { label: "Valid", color: "bg-green-100 text-green-800" }
  }

  const getExpiryText = (daysUntil: number) => {
    if (daysUntil < 0) return `${Math.abs(daysUntil)} days ago`
    if (daysUntil === 0) return "Today"
    if (daysUntil === 1) return "Tomorrow"
    return `${daysUntil} days`
  }

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.email.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || 
      (statusFilter === "active" && employee.isActive) ||
      (statusFilter === "inactive" && !employee.isActive)

    if (expiryFilter === "all") return matchesSearch && matchesStatus

    const expirations = getDocumentExpirations(employee)
    const hasExpiring = expirations.some(exp => {
      if (expiryFilter === "expired") return exp.daysUntil < 0
      if (expiryFilter === "critical") return exp.daysUntil >= 0 && exp.daysUntil <= 7
      if (expiryFilter === "warning") return exp.daysUntil > 7 && exp.daysUntil <= 30
      if (expiryFilter === "caution") return exp.daysUntil > 30 && exp.daysUntil <= 90
      return false
    })

    return matchesSearch && matchesStatus && hasExpiring
  })

  const handleViewEmployee = (employee: Employee) => {
    router.push(`/dashboard/employees/${employee.id}`)
  }

  const handleEditEmployee = (employee: Employee) => {
    router.push(`/dashboard/employees/${employee.id}/edit`)
  }

  const handleCreateRenewalEvent = async (employee: Employee, docType: string, expiry: string) => {
    try {
      const eventData = {
        title: `${docType} Renewal - ${employee.name}`,
        titleAr: `تجديد ${docType} - ${employee.name}`,
        type: 'RENEWAL',
        category: 'HR',
        startDate: expiry,
        dueDate: expiry,
        priority: 'HIGH',
        relatedEntityType: 'employee',
        relatedEntityId: employee.id,
        assignedToId: employee.id,
        notes: `${docType} renewal reminder for ${employee.name}`,
        notifyBefore: 30,
      }

      const response = await fetch('/api/calendar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(eventData)
      })

      if (response.ok) {
        alert(`Renewal reminder created for ${employee.name}'s ${docType}`)
      } else {
        alert('Failed to create renewal reminder')
      }
    } catch (error) {
      console.error('Error creating renewal event:', error)
      alert('Failed to create renewal reminder')
    }
  }

  // Calculate statistics
  const totalEmployees = employees.length
  const activeEmployees = employees.filter(emp => emp.isActive).length
  const allExpirations = employees.flatMap(emp => getDocumentExpirations(emp))
  const expiredDocs = allExpirations.filter(exp => exp.daysUntil < 0).length
  const criticalDocs = allExpirations.filter(exp => exp.daysUntil >= 0 && exp.daysUntil <= 7).length
  const warningDocs = allExpirations.filter(exp => exp.daysUntil > 7 && exp.daysUntil <= 30).length

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Employee Documents</h2>
          <p className="text-muted-foreground">
            Track employee document expirations and renewals
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/calendar')}>
            <Calendar className="mr-2 h-4 w-4" />
            View Calendar
          </Button>
          <Button onClick={() => router.push('/dashboard/employees')}>
            <User className="mr-2 h-4 w-4" />
            All Employees
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-gradient-to-br from-blue-50 to-indigo-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">Total Employees</p>
              <p className="text-3xl font-bold text-blue-900">{totalEmployees}</p>
            </div>
            <div className="rounded-full bg-blue-100 p-3">
              <User className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <p className="text-xs text-blue-600 mt-2">{activeEmployees} active</p>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-red-50 to-pink-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-600">Expired Documents</p>
              <p className="text-3xl font-bold text-red-900">{expiredDocs}</p>
            </div>
            <div className="rounded-full bg-red-100 p-3">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
          <p className="text-xs text-red-600 mt-2">Require immediate action</p>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-orange-50 to-red-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-600">Critical (≤7 days)</p>
              <p className="text-3xl font-bold text-orange-900">{criticalDocs}</p>
            </div>
            <div className="rounded-full bg-orange-100 p-3">
              <Clock className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <p className="text-xs text-orange-600 mt-2">Expiring soon</p>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-yellow-50 to-orange-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-600">Warning (≤30 days)</p>
              <p className="text-3xl font-bold text-yellow-900">{warningDocs}</p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3">
              <FileText className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <p className="text-xs text-yellow-600 mt-2">Plan renewals</p>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search employees..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
        <Select value={expiryFilter} onValueChange={setExpiryFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Expiry Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Documents</SelectItem>
            <SelectItem value="expired">Expired</SelectItem>
            <SelectItem value="critical">Critical (≤7 days)</SelectItem>
            <SelectItem value="warning">Warning (≤30 days)</SelectItem>
            <SelectItem value="caution">Caution (≤90 days)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Employees Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Employee</TableHead>
              <TableHead>Document Status</TableHead>
              <TableHead>Next Expiry</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span>Loading employees...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredEmployees.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm ? 'No employees found matching your search.' : 'No employees found.'}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredEmployees.map((employee) => {
                const expirations = getDocumentExpirations(employee)
                const nextExpiry = expirations[0]
                const hasExpired = expirations.some(exp => exp.daysUntil < 0)
                const hasCritical = expirations.some(exp => exp.daysUntil >= 0 && exp.daysUntil <= 7)
                
                return (
                  <TableRow key={employee.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={employee.avatar} />
                          <AvatarFallback>
                            {employee.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium"
                            onClick={() => router.push(`/dashboard/employees/${employee.id}`)}
                          >
                            {employee.name}
                          </Button>
                          <div className="text-sm text-muted-foreground">{employee.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {expirations.slice(0, 3).map((exp, index) => {
                          const status = getExpiryStatus(exp.daysUntil)
                          return (
                            <Badge key={index} className={`text-xs ${status.color}`}>
                              {exp.type}: {status.label}
                            </Badge>
                          )
                        })}
                        {expirations.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{expirations.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {nextExpiry ? (
                        <div className="flex items-center space-x-2">
                          <nextExpiry.icon className={`h-4 w-4 ${nextExpiry.color}`} />
                          <div>
                            <div className="font-medium">{nextExpiry.type}</div>
                            <div className={`text-sm ${nextExpiry.color}`}>
                              {getExpiryText(nextExpiry.daysUntil)}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm text-muted-foreground">No documents</div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge className={employee.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}>
                        {employee.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewEmployee(employee)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditEmployee(employee)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Employee
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {nextExpiry && (
                            <DropdownMenuItem 
                              onClick={() => handleCreateRenewalEvent(employee, nextExpiry.type, nextExpiry.expiry)}
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                              Create Renewal Reminder
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
