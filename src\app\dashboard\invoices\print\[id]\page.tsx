"use client"

import { useEffect, useState } from "react"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { InvoiceTemplate, sampleInvoiceData } from "@/components/invoice/invoice-template"
import { ArrowLeft, Printer, Download, Eye, EyeOff } from "lucide-react"

export default function PrintInvoicePage() {
  const params = useParams()
  const router = useRouter()
  const [showImages, setShowImages] = useState(true)
  const [invoice, setInvoice] = useState(sampleInvoiceData)

  useEffect(() => {
    // In a real app, you would fetch the invoice data based on the ID
    const invoiceId = params.id
    console.log('Loading invoice:', invoiceId)
    
    // For demo purposes, we'll use sample data
    // In production, you would fetch from your API:
    // fetchInvoice(invoiceId).then(setInvoice)
  }, [params.id])

  const handlePrint = () => {
    window.print()
  }

  const handleDownloadPDF = () => {
    // In a real app, you would generate and download a PDF
    alert('PDF download functionality would be implemented here')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Print Controls - Hidden when printing */}
      <div className="bg-white border-b border-gray-200 p-4 print:hidden">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <h1 className="text-lg font-semibold">Invoice #{invoice.invoiceNumber}</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowImages(!showImages)}
            >
              {showImages ? (
                <>
                  <EyeOff className="mr-2 h-4 w-4" />
                  Hide Images
                </>
              ) : (
                <>
                  <Eye className="mr-2 h-4 w-4" />
                  Show Images
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadPDF}
            >
              <Download className="mr-2 h-4 w-4" />
              Download PDF
            </Button>
            <Button
              size="sm"
              onClick={handlePrint}
            >
              <Printer className="mr-2 h-4 w-4" />
              Print
            </Button>
          </div>
        </div>
      </div>

      {/* Invoice Content */}
      <div className="py-8 print:py-0">
        <InvoiceTemplate invoice={invoice} showImages={showImages} />
      </div>

      {/* Print Styles */}
      <style jsx global>{`
        @media print {
          body {
            margin: 0;
            padding: 0;
            background: white !important;
          }
          
          .print\\:hidden {
            display: none !important;
          }
          
          .print\\:py-0 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
          }
          
          /* Ensure images print properly */
          img {
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
          }
          
          /* Page break settings */
          .invoice-page {
            page-break-inside: avoid;
          }
          
          /* Table styling for print */
          table {
            border-collapse: collapse !important;
          }
          
          th, td {
            border: 1px solid #000 !important;
            padding: 8px !important;
          }
          
          /* Hide background colors in print */
          .bg-gray-50 {
            background: white !important;
          }
        }
      `}</style>
    </div>
  )
}
