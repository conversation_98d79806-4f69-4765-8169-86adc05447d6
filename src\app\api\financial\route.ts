import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '6months'

    // Calculate date range based on period
    const now = new Date()
    const startDate = new Date()
    
    switch (period) {
      case '1month':
        startDate.setMonth(now.getMonth() - 1)
        break
      case '3months':
        startDate.setMonth(now.getMonth() - 3)
        break
      case '6months':
        startDate.setMonth(now.getMonth() - 6)
        break
      case '1year':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setMonth(now.getMonth() - 6)
    }

    // Get invoices data for revenue
    const invoices = await prisma.invoice.findMany({
      where: {
        date: {
          gte: startDate,
          lte: now
        }
      },
      include: {
        items: true,
        customer: true
      }
    })

    // Get expenses data
    const expenses = await prisma.expense.findMany({
      where: {
        date: {
          gte: startDate,
          lte: now
        }
      },
      include: {
        expenseType: true
      }
    })

    // Calculate totals
    const totalRevenue = invoices.reduce((sum, invoice) => sum + Number(invoice.total), 0)
    const totalExpenses = expenses.reduce((sum, expense) => sum + Number(expense.amount), 0)
    const netProfit = totalRevenue - totalExpenses
    const grossMargin = totalRevenue > 0 ? ((netProfit / totalRevenue) * 100) : 0

    // Calculate monthly data
    const monthlyData = []
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
      
      const monthInvoices = invoices.filter(inv => {
        const invDate = new Date(inv.date)
        return invDate >= monthStart && invDate <= monthEnd
      })
      
      const monthExpenses = expenses.filter(exp => {
        const expDate = new Date(exp.date)
        return expDate >= monthStart && expDate <= monthEnd
      })
      
      const monthRevenue = monthInvoices.reduce((sum, inv) => sum + Number(inv.total), 0)
      const monthExpenseTotal = monthExpenses.reduce((sum, exp) => sum + Number(exp.amount), 0)
      const monthProfit = monthRevenue - monthExpenseTotal
      const monthMargin = monthRevenue > 0 ? ((monthProfit / monthRevenue) * 100) : 0
      
      monthlyData.push({
        month: monthStart.toLocaleDateString('en-US', { month: 'short' }),
        revenue: monthRevenue,
        expenses: monthExpenseTotal,
        profit: monthProfit,
        margin: monthMargin
      })
    }

    // Calculate revenue breakdown by product categories
    const revenueByCategory = new Map()
    invoices.forEach(invoice => {
      invoice.items.forEach(item => {
        const category = item.description.includes('Print') ? 'Printing Services' :
                        item.description.includes('Design') ? 'Design Services' :
                        item.description.includes('Office') ? 'Office Supplies' : 'Other Services'
        
        const current = revenueByCategory.get(category) || 0
        revenueByCategory.set(category, current + Number(item.total))
      })
    })

    const revenueBreakdown = Array.from(revenueByCategory.entries()).map(([category, amount]) => ({
      category,
      amount,
      percentage: totalRevenue > 0 ? ((amount / totalRevenue) * 100) : 0,
      color: category === 'Printing Services' ? '#3b82f6' :
             category === 'Design Services' ? '#10b981' :
             category === 'Office Supplies' ? '#f59e0b' : '#ef4444'
    }))

    // Calculate expense breakdown by type
    const expenseByType = new Map()
    expenses.forEach(expense => {
      const type = expense.expenseType?.name || 'Other'
      const current = expenseByType.get(type) || 0
      expenseByType.set(type, current + Number(expense.amount))
    })

    const expenseBreakdown = Array.from(expenseByType.entries()).map(([category, amount]) => ({
      category,
      amount,
      percentage: totalExpenses > 0 ? ((amount / totalExpenses) * 100) : 0,
      color: category === 'Materials & Supplies' ? '#3b82f6' :
             category === 'Salaries & Benefits' ? '#10b981' :
             category === 'Rent & Utilities' ? '#f59e0b' :
             category === 'Equipment & Maintenance' ? '#ef4444' : '#8b5cf6'
    }))

    // Calculate cash flow data (simplified)
    const cashFlowData = monthlyData.map(month => ({
      month: month.month,
      inflow: month.revenue,
      outflow: month.expenses,
      net: month.profit
    }))

    // Calculate KPI metrics
    const customerCount = new Set(invoices.map(inv => inv.customerId)).size
    const revenuePerCustomer = customerCount > 0 ? totalRevenue / customerCount : 0
    const averageOrderValue = invoices.length > 0 ? totalRevenue / invoices.length : 0

    const kpiMetrics = [
      {
        name: 'Revenue per Customer',
        value: revenuePerCustomer,
        target: 3000,
        unit: 'OMR',
        trend: 'up',
        change: 8.5
      },
      {
        name: 'Customer Acquisition Cost',
        value: 145,
        target: 120,
        unit: 'OMR',
        trend: 'down',
        change: -12.3
      },
      {
        name: 'Average Order Value',
        value: averageOrderValue,
        target: 500,
        unit: 'OMR',
        trend: 'up',
        change: 5.2
      },
      {
        name: 'Monthly Recurring Revenue',
        value: totalRevenue / 6, // Average monthly revenue
        target: 30000,
        unit: 'OMR',
        trend: 'up',
        change: 15.8
      }
    ]

    // Financial summary
    const financialSummary = {
      totalRevenue,
      totalExpenses,
      netProfit,
      grossMargin,
      operatingMargin: grossMargin * 0.85, // Simplified calculation
      netMargin: grossMargin * 0.75, // Simplified calculation
      revenueGrowth: 15.8, // This would need historical comparison
      expenseGrowth: 8.2,
      profitGrowth: 28.4
    }

    const response = {
      financialSummary,
      monthlyFinancials: monthlyData,
      revenueBreakdown,
      expenseBreakdown,
      cashFlowData,
      kpiMetrics,
      period,
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString()
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching financial data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch financial data' },
      { status: 500 }
    )
  }
}
