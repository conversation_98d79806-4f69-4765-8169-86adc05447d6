"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar,
  Clock,
  Bell,
  CalendarDays,
  AlertTriangle,
  CheckCircle,
  Plus,
  Eye
} from "lucide-react"

interface CalendarEvent {
  id: string
  title: string
  titleAr?: string
  type: string
  category: string
  startDate: string
  endDate?: string
  dueDate?: string
  status: string
  priority: string
  assignedTo?: {
    id: string
    name: string
    email: string
  }
}

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  IN_PROGRESS: "bg-blue-100 text-blue-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-gray-100 text-gray-800",
  OVERDUE: "bg-red-100 text-red-800",
}

const priorityColors = {
  LOW: "text-gray-600",
  MEDIUM: "text-blue-600",
  HIGH: "text-orange-600",
  URGENT: "text-red-600",
  CRITICAL: "text-red-700",
}

const typeIcons = {
  REMINDER: Bell,
  MEETING: Calendar,
  DEADLINE: Clock,
  RENEWAL: CalendarDays,
  EXPIRATION: AlertTriangle,
  TASK_DUE: CheckCircle,
  INVOICE_DUE: CheckCircle,
  PAYMENT_DUE: CheckCircle,
  HOLIDAY: Calendar,
  OTHER: Calendar,
}

export function CalendarWidget() {
  const router = useRouter()
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadUpcomingEvents = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/calendar')
        if (response.ok) {
          const data = await response.json()
          
          // Filter for upcoming events (next 7 days)
          const now = new Date()
          const nextWeek = new Date()
          nextWeek.setDate(now.getDate() + 7)
          
          const upcomingEvents = data
            .filter((event: CalendarEvent) => {
              const eventDate = new Date(event.dueDate || event.startDate)
              return eventDate >= now && eventDate <= nextWeek && event.status !== 'COMPLETED'
            })
            .sort((a: CalendarEvent, b: CalendarEvent) => {
              const dateA = new Date(a.dueDate || a.startDate)
              const dateB = new Date(b.dueDate || b.startDate)
              return dateA.getTime() - dateB.getTime()
            })
            .slice(0, 5) // Show only top 5 upcoming events
          
          setEvents(upcomingEvents)
        }
      } catch (error) {
        console.error('Error loading calendar events:', error)
      } finally {
        setLoading(false)
      }
    }

    loadUpcomingEvents()
  }, [])

  const getDaysUntil = (dateString: string) => {
    const now = new Date()
    const eventDate = new Date(dateString)
    const diffTime = eventDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return "Today"
    if (diffDays === 1) return "Tomorrow"
    if (diffDays < 0) return `${Math.abs(diffDays)} days overdue`
    return `${diffDays} days`
  }

  const getDaysUntilColor = (dateString: string) => {
    const now = new Date()
    const eventDate = new Date(dateString)
    const diffTime = eventDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays < 0) return "text-red-600"
    if (diffDays === 0) return "text-red-500"
    if (diffDays === 1) return "text-orange-500"
    if (diffDays <= 3) return "text-yellow-600"
    return "text-green-600"
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium flex items-center space-x-2">
          <Calendar className="h-4 w-4" />
          <span>Upcoming Events</span>
        </CardTitle>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/calendar/calendar-view')}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/calendar/create')}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        ) : events.length === 0 ? (
          <div className="text-center py-4">
            <Calendar className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">No upcoming events</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => router.push('/dashboard/calendar/create')}
            >
              <Plus className="mr-1 h-3 w-3" />
              Add Event
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {events.map((event) => {
              const TypeIcon = typeIcons[event.type as keyof typeof typeIcons] || Calendar
              const eventDate = event.dueDate || event.startDate
              
              return (
                <div
                  key={event.id}
                  className="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => router.push(`/dashboard/calendar/${event.id}`)}
                >
                  <div className={`mt-0.5 ${priorityColors[event.priority as keyof typeof priorityColors]}`}>
                    <TypeIcon className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium truncate">{event.title}</p>
                      <Badge className={statusColors[event.status as keyof typeof statusColors]} variant="secondary">
                        {event.status.replace('_', ' ')}
                      </Badge>
                    </div>
                    {event.titleAr && (
                      <p className="text-xs text-muted-foreground truncate" dir="rtl">
                        {event.titleAr}
                      </p>
                    )}
                    <div className="flex items-center justify-between mt-1">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {event.type.replace('_', ' ')}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {event.category}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span className={`text-xs font-medium ${getDaysUntilColor(eventDate)}`}>
                          {getDaysUntil(eventDate)}
                        </span>
                      </div>
                    </div>
                    {event.assignedTo && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Assigned to: {event.assignedTo.name}
                      </p>
                    )}
                  </div>
                </div>
              )
            })}
            
            <div className="pt-2 border-t">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => router.push('/dashboard/calendar')}
              >
                View All Events
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
