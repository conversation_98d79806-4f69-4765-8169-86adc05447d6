"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CustomerSelector } from "@/components/ui/customer-selector"
import { TeamMembersManager } from "@/components/projects/team-members-manager"
import { ProjectFinancialOverview } from "@/components/projects/project-financial-overview"
import { ProjectInvoicesManager } from "@/components/projects/project-invoices-manager"
import { ProjectExpensesManager } from "@/components/projects/project-expenses-manager"
import { ArrowLeft, Save, FolderO<PERSON>, Calendar, DollarSign } from "lucide-react"

interface Project {
  id: string
  code: string
  name: string
  nameAr?: string
  description?: string
  status: string
  priority: string
  startDate: string
  endDate?: string
  budget?: number
  actualCost?: number
  progress: number
  notes?: string
  clientId?: string
  managerId?: string
}

interface Employee {
  id: string
  name: string
  email: string
}

export default function EditProjectPage() {
  const router = useRouter()
  const params = useParams()
  const [saving, setSaving] = useState(false)
  const [loading, setLoading] = useState(true)
    const [employees, setEmployees] = useState<Employee[]>([])

  const [formData, setFormData] = useState({
    code: '',
    name: '',
    nameAr: '',
    description: '',
    status: 'PLANNING',
    priority: 'MEDIUM',
    startDate: '',
    endDate: '',
    budget: '',
    actualCost: '',
    progress: '0',
    notes: '',
    clientId: '',
    managerId: '',
  })

  // Load project data and related data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        
        // Load project data
        const projectResponse = await fetch(`/api/projects/${params.id}`)
        if (projectResponse.ok) {
          const project: Project = await projectResponse.json()
          setFormData({
            code: project.code,
            name: project.name,
            nameAr: project.nameAr || '',
            description: project.description || '',
            status: project.status,
            priority: project.priority,
            startDate: project.startDate ? new Date(project.startDate).toISOString().split('T')[0] : '',
            endDate: project.endDate ? new Date(project.endDate).toISOString().split('T')[0] : '',
            budget: project.budget?.toString() || '',
            actualCost: project.actualCost?.toString() || '',
            progress: project.progress.toString(),
            notes: project.notes || '',
            clientId: project.clientId || '',
            managerId: project.managerId || '',
          })
        } else {
          alert('Failed to load project data')
          router.push('/dashboard/projects')
          return
        }

        // Load employees
        const employeesResponse = await fetch('/api/employees')

        if (employeesResponse.ok) {
          const employeesData = await employeesResponse.json()
          setEmployees(Array.isArray(employeesData) ? employeesData : employeesData.employees || [])
        }
      } catch (error) {
        console.error('Error loading data:', error)
        alert('Failed to load data')
        router.push('/dashboard/projects')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      loadData()
    }
  }, [params.id, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      // Validate required fields
      if (!formData.name || !formData.code || !formData.startDate) {
        alert('Please fill in all required fields (Name, Code, Start Date)')
        setSaving(false)
        return
      }

      const updateData = {
        code: formData.code,
        name: formData.name,
        nameAr: formData.nameAr || null,
        description: formData.description || null,
        status: formData.status,
        priority: formData.priority,
        startDate: new Date(formData.startDate).toISOString(),
        endDate: formData.endDate ? new Date(formData.endDate).toISOString() : null,
        budget: formData.budget ? parseFloat(formData.budget) : null,
        actualCost: formData.actualCost ? parseFloat(formData.actualCost) : null,
        progress: parseInt(formData.progress),
        notes: formData.notes || null,
        clientId: formData.clientId || null,
        managerId: formData.managerId || null,
      }

      const response = await fetch(`/api/projects/${params.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })

      if (response.ok) {
        alert('Project updated successfully!')
        router.push(`/dashboard/projects/${params.id}`)
      } else {
        const error = await response.json()
        alert(`Failed to update project: ${error.error}`)
      }
    } catch (error) {
      console.error('Error updating project:', error)
      alert('Failed to update project')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Edit Project</h2>
          <p className="text-muted-foreground">
            Update project information and settings
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FolderOpen className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="code">Project Code *</Label>
                  <Input
                    id="code"
                    placeholder="PRJ-001"
                    value={formData.code}
                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name">Project Name *</Label>
                  <Input
                    id="name"
                    placeholder="Enter project name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nameAr">Project Name (Arabic)</Label>
                  <Input
                    id="nameAr"
                    placeholder="اسم المشروع"
                    value={formData.nameAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, nameAr: e.target.value }))}
                    dir="rtl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Project description..."
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PLANNING">Planning</SelectItem>
                      <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                      <SelectItem value="ON_HOLD">On Hold</SelectItem>
                      <SelectItem value="COMPLETED">Completed</SelectItem>
                      <SelectItem value="CANCELLED">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={formData.priority} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="LOW">Low</SelectItem>
                      <SelectItem value="MEDIUM">Medium</SelectItem>
                      <SelectItem value="HIGH">High</SelectItem>
                      <SelectItem value="URGENT">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="progress">Progress (%)</Label>
                  <Input
                    id="progress"
                    type="number"
                    min="0"
                    max="100"
                    placeholder="0"
                    className="bg-gray-50"
                    value={formData.progress}
                    readOnly
                    title="Progress is automatically calculated from completed tasks"
                  />
                  <p className="text-xs text-muted-foreground">
                    Automatically calculated from completed tasks
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientId">Client</Label>
                  <CustomerSelector
                    value={formData.clientId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, clientId: value }))}
                    placeholder="Search and select client..."
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="managerId">Project Manager</Label>
                  <Select value={formData.managerId || 'none'} onValueChange={(value) => setFormData(prev => ({ ...prev, managerId: value === 'none' ? '' : value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select manager" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Manager</SelectItem>
                      {employees.map((employee) => (
                        <SelectItem key={employee.id} value={employee.id}>
                          {employee.name} ({employee.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Members */}
        <Card>
          <CardContent className="pt-6">
            <TeamMembersManager projectId={params.id as string} isEditable={true} />
          </CardContent>
        </Card>

        {/* Timeline & Budget */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Timeline & Budget</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date *</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="budget">Budget (OMR)</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="budget"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      className="pl-8"
                      value={formData.budget}
                      onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="actualCost">Actual Cost (OMR)</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="actualCost"
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      className="pl-8 bg-gray-50"
                      value={formData.actualCost}
                      readOnly
                      title="Actual cost is automatically calculated from approved/paid expenses"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Automatically calculated from approved and paid expenses
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Financial Overview */}
        <Card>
          <CardContent className="pt-6">
            <ProjectFinancialOverview 
              projectId={params.id as string} 
              projectBudget={formData.budget ? parseFloat(formData.budget) : 0}
              projectActualCost={formData.actualCost ? parseFloat(formData.actualCost) : 0}
            />
          </CardContent>
        </Card>

        {/* Project Invoices */}
        <Card>
          <CardContent className="pt-6">
            <ProjectInvoicesManager projectId={params.id as string} isEditable={true} />
          </CardContent>
        </Card>

        {/* Project Expenses */}
        <Card>
          <CardContent className="pt-6">
            <ProjectExpensesManager projectId={params.id as string} isEditable={true} />
          </CardContent>
        </Card>

        {/* Notes */}
        <Card>
          <CardHeader>
            <CardTitle>Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="notes">Project Notes</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes about the project..."
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? 'Updating...' : 'Update Project'}
          </Button>
        </div>
      </form>
    </div>
  )
}
