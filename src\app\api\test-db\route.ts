import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Test basic database connectivity
    const result = await prisma.$queryRaw`SELECT 1 as test`
    
    // Test if Invoice table exists and get count
    const invoiceCount = await prisma.invoice.count()
    
    // Test if Expense table exists and get count
    const expenseCount = await prisma.expense.count()
    
    return NextResponse.json({
      status: 'success',
      message: 'Database connection successful',
      data: {
        connectivity: result,
        invoiceCount,
        expenseCount
      }
    })
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json({
      status: 'error',
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
