export const projects = {
  title: "Projects",
  addProject: "Add Project",
  editProject: "Edit Project",
  createProject: "Create Project",
  updateProject: "Update Project",
  deleteProject: "Delete Project",
  projectDetails: "Project Details",
  projectName: "Project Name",
  projectNameAr: "Project Name (Arabic)",
  projectCode: "Project Code",
  projectDescription: "Project Description",
  projectManager: "Project Manager",
  projectBudget: "Project Budget",
  actualCost: "Actual Cost",
  projectProgress: "Project Progress",
  projectStatus: "Project Status",
  projectPriority: "Project Priority",
  teamMembers: "Team Members",
  projectTasks: "Project Tasks",
  projectInvoices: "Project Invoices",
  projectExpenses: "Project Expenses",
  projectNotes: "Project Notes",
  searchPlaceholder: "Search projects, clients...",
  noProjectsFound: "No projects found",
  createFirstProject: "Create your first project!",
  noProjectsFoundSearch: "No projects found matching your search.",
  createFirstProjectAlt: "No projects found. Create your first project!",
  totalProjects: "Total Projects",
  activeProjects: "Active Projects",
  completedProjects: "Completed Projects",
  onHoldProjects: "On Hold Projects",
  cancelledProjects: "Cancelled Projects",
  totalBudget: "Total Budget",
  totalActualCost: "Total Actual Cost",
  averageProgress: "Average Progress",
  overdueProjects: "Overdue Projects",
  recentProjects: "Recent Projects",
  upcomingDeadlines: "Upcoming Deadlines",
  highPriorityProjects: "High Priority Projects",
  projectPerformance: "Project Performance",
  budgetUtilization: "Budget Utilization",
  timelineOverview: "Timeline Overview",
  financialOverview: "Financial Overview",
  addTeamMember: "Add Team Member",
  removeTeamMember: "Remove Team Member",
  assignManager: "Assign Manager",
  changeStatus: "Change Status",
  viewTasks: "View Tasks",
  addTask: "Add Task",
  viewInvoices: "View Invoices",
  addInvoice: "Add Invoice",
  viewExpenses: "View Expenses",
  addExpense: "Add Expense",
  projectManagement: "Project Management",
  manageDescription: "Manage your projects, track progress, and collaborate with your team",
  dashboard: "Dashboard",
  createProjectDescription: "Set up a new project with timeline, budget, and team assignments",
  projectInformation: "Project Information",
  searchAndSelectClient: "Search and select client...",
  searchAndSelectClientPlaceholder: "Search and select client...",
  selectProjectManager: "Select Project Manager",
  startDate: "Start Date",
  endDate: "End Date",
  dueDate: "Due Date",
  estimatedDuration: "Estimated Duration",
  actualDuration: "Actual Duration",
  client: "Client",
  clientName: "Client Name",
  projectDescriptionPlaceholder: "Describe the project goals, scope, and deliverables...",
  additionalNotes: "Additional Notes",
  additionalNotesPlaceholder: "Any additional notes or requirements...",
  projectNamePlaceholder: "Enter project name",
  projectCodePlaceholder: "Enter project code",
  budgetPlaceholder: "Enter project budget",
  selectClient: "Select Client",
  searchClients: "Search clients...",
  noClientFound: "No client found",
  projectCreatedSuccessfully: "Project created successfully!",
  failedToCreateProject: "Failed to create project",
  projectUpdatedSuccessfully: "Project updated successfully!",
  failedToUpdateProject: "Failed to update project",
  projectDeletedSuccessfully: "Project deleted successfully!",
  failedToDeleteProject: "Failed to delete project",
  confirmDeleteProject: "Are you sure you want to delete this project?",
  noClient: "No Client",
  noDueDate: "No Due Date",
  noManager: "No Manager",
  noDescription: "No Description",
  notStarted: "Not Started",
  archived: "Archived",
  statuses: {
    planning: "Planning",
    inProgress: "In Progress",
    onHold: "On Hold",
    completed: "Completed",
    cancelled: "Cancelled"
  },
  priorities: {
    low: "Low",
    medium: "Medium",
    high: "High",
    urgent: "Urgent"
  },
  back: "Back",
  dashboardTitle: "Project Dashboard",
  dashboardOverview: "Overview of all projects, progress, and key metrics",
  newProject: "New Project",
  completionRate: "Completion Rate",
  avgAcrossAll: "Average across all projects",
  spent: "spent",
  requireAttention: "Require immediate attention",
  statusDistribution: "Project Status Distribution"
} 