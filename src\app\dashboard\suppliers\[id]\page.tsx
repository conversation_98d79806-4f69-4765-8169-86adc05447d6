"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON>er, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  Building,
  Package,
  FileText,
  Receipt,
  MoreHorizontal,
  Trash2,
  Eye,
  Calendar,
  DollarSign,
  TrendingU<PERSON>,
  Clock,
  User
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"

interface Supplier {
  id: string
  name: string
  email?: string
  mobile?: string
  phone?: string
  address?: string
  company?: string
  contactPerson?: string
  taxNumber?: string
  notes?: string
  isActive: boolean
  createdAt: string
  totalPurchases: number
  outstandingPayments: number
  purchaseOrderCount: number
  productCount: number
  lastOrderDate?: string
  purchases?: any[]
}

interface PurchaseOrder {
  id: string
  number: string
  date: string
  dueDate: string
  status: 'DRAFT' | 'SENT' | 'RECEIVED' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  total: number
  amountPaid: number
  balance: number
}

interface Payment {
  id: string
  purchaseOrderNumber: string
  amount: number
  method: string
  date: string
  reference?: string
  notes?: string
}

interface Product {
  id: string
  name: string
  sku: string
  category: string
  unitPrice: number
  stock: number
  lastOrderDate: string
}

// Supplier data will be fetched from API

const mockPurchaseOrders: PurchaseOrder[] = [
  {
    id: "1",
    number: "PO-001",
    date: "2024-01-20",
    dueDate: "2024-02-20",
    status: "SENT",
    total: 4500,
    amountPaid: 0,
    balance: 4500
  },
  {
    id: "2",
    number: "PO-002",
    date: "2024-01-15",
    dueDate: "2024-02-15",
    status: "OVERDUE",
    total: 3200,
    amountPaid: 1200,
    balance: 2000
  },
  {
    id: "3",
    number: "PO-003",
    date: "2024-01-10",
    dueDate: "2024-02-10",
    status: "PAID",
    total: 5800,
    amountPaid: 5800,
    balance: 0
  },
  {
    id: "4",
    number: "PO-004",
    date: "2024-01-05",
    dueDate: "2024-02-05",
    status: "RECEIVED",
    total: 2900,
    amountPaid: 2900,
    balance: 0
  },
]

const mockPayments: Payment[] = [
  {
    id: "1",
    purchaseOrderNumber: "PO-003",
    amount: 5800,
    method: "Bank Transfer",
    date: "2024-01-12",
    reference: "TXN789012",
    notes: "Payment for office supplies"
  },
  {
    id: "2",
    purchaseOrderNumber: "PO-004",
    amount: 2900,
    method: "Check",
    date: "2024-01-08",
    reference: "CHK001234",
    notes: "Check payment processed"
  },
  {
    id: "3",
    purchaseOrderNumber: "PO-002",
    amount: 1200,
    method: "Bank Transfer",
    date: "2024-01-16",
    reference: "TXN345678",
    notes: "Partial payment"
  },
]

const mockProducts: Product[] = [
  {
    id: "1",
    name: "A4 Copy Paper",
    sku: "PAPER-A4-001",
    category: "Office Supplies",
    unitPrice: 12.5,
    stock: 500,
    lastOrderDate: "2024-01-20"
  },
  {
    id: "2",
    name: "Ballpoint Pens (Blue)",
    sku: "PEN-BLUE-001",
    category: "Writing Supplies",
    unitPrice: 0.75,
    stock: 1200,
    lastOrderDate: "2024-01-18"
  },
  {
    id: "3",
    name: "Stapler Heavy Duty",
    sku: "STAPLER-HD-001",
    category: "Office Equipment",
    unitPrice: 25.0,
    stock: 50,
    lastOrderDate: "2024-01-15"
  },
  {
    id: "4",
    name: "File Folders",
    sku: "FOLDER-001",
    category: "Filing Supplies",
    unitPrice: 1.25,
    stock: 800,
    lastOrderDate: "2024-01-12"
  },
]

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  SENT: "bg-blue-100 text-blue-800",
  RECEIVED: "bg-green-100 text-green-800",
  PAID: "bg-green-100 text-green-800",
  OVERDUE: "bg-red-100 text-red-800",
  CANCELLED: "bg-gray-100 text-gray-800",
}

export default function SupplierDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const [supplier, setSupplier] = useState<Supplier | null>(null)
  const [loading, setLoading] = useState(true)
  const { t } = useI18n()

  useEffect(() => {
    const fetchSupplier = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/suppliers/${params.id}`)
        if (response.ok) {
          const supplierData = await response.json()
          setSupplier(supplierData)
        } else {
          console.error('Failed to fetch supplier')
          setSupplier(null)
        }
      } catch (error) {
        console.error('Error fetching supplier:', error)
        setSupplier(null)
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchSupplier()
    }
  }, [params.id])

  const handleEdit = () => {
    router.push(`/dashboard/suppliers/${supplier?.id}/edit`)
  }

  const handleDelete = () => {
    if (confirm(t('suppliers.confirmDeleteSupplier'))) {
      console.log('Deleting supplier:', supplier?.name)
      alert('Delete functionality would be implemented here')
      router.push('/dashboard/suppliers')
    }
  }

  const handleCreatePurchaseOrder = () => {
    router.push(`/dashboard/purchases/create?supplier=${supplier?.id}`)
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('suppliers.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('suppliers.loading')}</h2>
          </div>
        </div>
      </div>
    )
  }

  if (!supplier) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('suppliers.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('suppliers.supplierNotFound')}</h2>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('suppliers.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{supplier.name}</h2>
            <p className="text-muted-foreground">
              {t('suppliers.supplierDetailsAndHistory')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge variant={supplier.isActive ? "default" : "secondary"}>
            {supplier.isActive ? t('suppliers.active') : t('suppliers.inactive')}
          </Badge>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                {t('suppliers.actions')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t('suppliers.editSupplier')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleCreatePurchaseOrder}>
                <Receipt className="mr-2 h-4 w-4" />
                {t('suppliers.createPurchaseOrder')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => router.push(`/dashboard/supplier-payouts/create?supplierId=${supplier.id}`)}>
                <DollarSign className="mr-2 h-4 w-4" />
                {t('suppliers.createPayout')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {t('suppliers.deleteSupplier')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('suppliers.supplierInformation')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('common.contactInformation')}</Label>
                    <div className="mt-2 space-y-2">
                      {supplier.mobile && (
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span><strong>{t('common.mobile')}:</strong> {supplier.mobile}</span>
                        </div>
                      )}
                      {supplier.phone && (
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span><strong>{t('common.phone')}:</strong> {supplier.phone}</span>
                        </div>
                      )}
                      {supplier.email && (
                        <div className="flex items-center space-x-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span><strong>{t('common.email')}:</strong> {supplier.email}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {supplier.company && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('common.company')}</Label>
                      <div className="mt-1 flex items-center space-x-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span>{supplier.company}</span>
                      </div>
                    </div>
                  )}

                  {supplier.contactPerson && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('suppliers.contactPerson')}</Label>
                      <div className="mt-1 flex items-center space-x-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span>{supplier.contactPerson}</span>
                      </div>
                    </div>
                  )}

                  {supplier.taxNumber && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('suppliers.taxNumber')}</Label>
                      <p className="text-base mt-1">{supplier.taxNumber}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {supplier.address && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('common.address')}</Label>
                      <div className="mt-1 flex items-start space-x-2">
                        <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                        <span className="text-sm">{supplier.address}</span>
                      </div>
                    </div>
                  )}

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('suppliers.supplierSince')}</Label>
                    <div className="mt-1 flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{new Date(supplier.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {supplier.lastOrderDate && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('suppliers.lastOrder')}</Label>
                      <div className="mt-1 flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{new Date(supplier.lastOrderDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {supplier.notes && (
                <div className="mt-6">
                  <Label className="text-sm font-medium text-muted-foreground">{t('common.notes')}</Label>
                  <p className="text-sm text-muted-foreground mt-1">{supplier.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tabs for Purchase Orders, Payments, Products */}
          <Card>
            <CardContent className="p-0">
              <Tabs defaultValue="orders" className="w-full">
                <div className="px-6 pt-6">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="orders">{t('suppliers.ordersTab')}</TabsTrigger>
                    <TabsTrigger value="payments">{t('suppliers.paymentsTab')}</TabsTrigger>
                    <TabsTrigger value="products">{t('suppliers.productsTab')}</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="orders" className="px-6 pb-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">{t('suppliers.purchaseOrders')}</h3>
                      <Button size="sm" onClick={handleCreatePurchaseOrder}>
                        <Receipt className="mr-2 h-4 w-4" />
                        {t('suppliers.createPurchaseOrder')}
                      </Button>
                    </div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t('suppliers.purchaseOrderNumber')}</TableHead>
                          <TableHead>{t('common.date')}</TableHead>
                          <TableHead>{t('suppliers.dueDate')}</TableHead>
                          <TableHead>{t('common.status')}</TableHead>
                          <TableHead className="text-right">{t('common.total')}</TableHead>
                          <TableHead className="text-right">{t('suppliers.balance')}</TableHead>
                          <TableHead className="text-right">{t('common.actions')}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {supplier.purchases && supplier.purchases.length > 0 ? (
                          supplier.purchases.map((purchase: any) => (
                            <TableRow key={purchase.id}>
                              <TableCell className="font-medium">
                                <Button
                                  variant="link"
                                  className="p-0 h-auto font-medium"
                                  onClick={() => router.push(`/dashboard/purchases/${purchase.id}`)}
                                >
                                  {purchase.number}
                                </Button>
                              </TableCell>
                              <TableCell>{new Date(purchase.date).toLocaleDateString()}</TableCell>
                              <TableCell>{purchase.expectedDate ? new Date(purchase.expectedDate).toLocaleDateString() : '-'}</TableCell>
                              <TableCell>
                                <Badge className={statusColors[purchase.status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"}>
                                  {purchase.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right font-medium">
                                {formatCurrency(purchase.total)}
                              </TableCell>
                              <TableCell className="text-right">
                                <span className="text-green-600">-</span>
                              </TableCell>
                              <TableCell className="text-right">
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                      <span className="sr-only">{t('suppliers.openMenu')}</span>
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => router.push(`/dashboard/purchases/${purchase.id}`)}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      {t('suppliers.viewPurchaseDetails')}
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => router.push(`/dashboard/purchases/${purchase.id}/edit`)}>
                                      <Edit className="mr-2 h-4 w-4" />
                                      {t('suppliers.editOrder')}
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem onClick={() => router.push(`/dashboard/supplier-payouts/create?supplierId=${supplier.id}&purchaseId=${purchase.id}`)}>
                                      <DollarSign className="mr-2 h-4 w-4" />
                                      {t('suppliers.createPayout')}
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-8">
                              <div className="text-muted-foreground">
                                {t('suppliers.noPurchaseOrdersFound')}
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>

                <TabsContent value="payments" className="px-6 pb-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">{t('suppliers.paymentHistory')}</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t('suppliers.purchaseOrderNumber')}</TableHead>
                          <TableHead>{t('common.date')}</TableHead>
                          <TableHead>{t('suppliers.method')}</TableHead>
                          <TableHead>{t('suppliers.reference')}</TableHead>
                          <TableHead className="text-right">{t('common.amount')}</TableHead>
                          <TableHead>{t('common.notes')}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockPayments.map((payment) => (
                          <TableRow key={payment.id}>
                            <TableCell className="font-medium">
                              <Button
                                variant="link"
                                className="p-0 h-auto font-medium"
                                onClick={() => router.push(`/dashboard/purchases?search=${payment.purchaseOrderNumber}`)}
                              >
                                {payment.purchaseOrderNumber}
                              </Button>
                            </TableCell>
                            <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{payment.method}</Badge>
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {payment.reference}
                            </TableCell>
                            <TableCell className="text-right font-medium text-green-600">
                              {formatCurrency(payment.amount)}
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {payment.notes}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>

                <TabsContent value="products" className="px-6 pb-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">{t('suppliers.supplierProducts')}</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t('common.product')}</TableHead>
                          <TableHead>{t('suppliers.sku')}</TableHead>
                          <TableHead>{t('common.category')}</TableHead>
                          <TableHead className="text-right">{t('suppliers.unitPrice')}</TableHead>
                          <TableHead className="text-right">{t('suppliers.stock')}</TableHead>
                          <TableHead>{t('suppliers.lastOrderDate')}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockProducts.map((product) => (
                          <TableRow key={product.id}>
                            <TableCell className="font-medium">{product.name}</TableCell>
                            <TableCell className="text-sm text-muted-foreground">{product.sku}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{product.category}</Badge>
                            </TableCell>
                            <TableCell className="text-right font-medium">
                              {formatCurrency(product.unitPrice)}
                            </TableCell>
                            <TableCell className="text-right">{product.stock}</TableCell>
                            <TableCell>{new Date(product.lastOrderDate).toLocaleDateString()}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Supplier Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t('suppliers.supplierSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('suppliers.totalPurchases')}:</span>
                <span className="font-medium">{formatCurrency(supplier.totalPurchases)}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('suppliers.outstandingPayments')}:</span>
                <span className={`font-medium ${supplier.outstandingPayments > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                  {formatCurrency(supplier.outstandingPayments)}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('suppliers.purchaseOrders')}:</span>
                <span className="font-medium">{supplier.purchaseOrderCount}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('suppliers.products')}:</span>
                <span className="font-medium">{supplier.productCount}</span>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('suppliers.status')}:</span>
                <Badge variant={supplier.isActive ? "default" : "secondary"}>
                  {supplier.isActive ? t('suppliers.active') : t('suppliers.inactive')}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('suppliers.quickActions')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" onClick={handleCreatePurchaseOrder}>
                <Receipt className="mr-2 h-4 w-4" />
                {t('suppliers.createPurchaseOrder')}
              </Button>

              <Button variant="outline" className="w-full" onClick={() => router.push(`/dashboard/supplier-payouts/create?supplierId=${supplier.id}`)}>
                <DollarSign className="mr-2 h-4 w-4" />
                {t('suppliers.createPayout')}
              </Button>

              <Button variant="outline" className="w-full" onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t('suppliers.editSupplier')}
              </Button>

              {supplier.email && (
                <Button variant="outline" className="w-full" onClick={() => window.open(`mailto:${supplier.email}`)}>
                  <Mail className="mr-2 h-4 w-4" />
                  {t('suppliers.sendEmail')}
                </Button>
              )}

              <Button variant="outline" className="w-full" onClick={() => window.open(`tel:${supplier.phone}`)}>
                <Phone className="mr-2 h-4 w-4" />
                {t('suppliers.callSupplier')}
              </Button>
            </CardContent>
          </Card>

          {/* Payment Status */}
          <Card>
            <CardHeader>
              <CardTitle>{t('suppliers.paymentStatus')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">{t('suppliers.paidOrders')}</span>
                </div>
                <span className="text-sm font-medium">2</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">{t('suppliers.receivedOrders')}</span>
                </div>
                <span className="text-sm font-medium">1</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">{t('suppliers.overdueOrders')}</span>
                </div>
                <span className="text-sm font-medium">1</span>
              </div>

              <Separator />

              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  {t('suppliers.lastPayment')}: {new Date('2024-01-16').toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}