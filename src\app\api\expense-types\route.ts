import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// GET /api/expense-types - Get all expense types
export async function GET() {
  try {
    const expenseTypes = await prisma.expenseType.findMany({
      include: {
        _count: {
          select: {
            expenses: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json(expenseTypes)
  } catch (error) {
    console.error('Error fetching expense types:', error)
    return NextResponse.json(
      { error: 'Failed to fetch expense types' },
      { status: 500 }
    )
  }
}

// POST /api/expense-types - Create new expense type
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, nameAr, description, isActive = true } = body

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Name is required' },
        { status: 400 }
      )
    }

    // Check if name already exists
    const existingType = await prisma.expenseType.findUnique({
      where: { name },
    })

    if (existingType) {
      return NextResponse.json(
        { error: 'Expense type name already exists' },
        { status: 400 }
      )
    }

    // Create expense type
    const expenseType = await prisma.expenseType.create({
      data: {
        name,
        nameAr,
        description,
        isActive,
      },
      include: {
        _count: {
          select: {
            expenses: true,
          },
        },
      },
    })

    return NextResponse.json(expenseType, { status: 201 })
  } catch (error) {
    console.error('Error creating expense type:', error)
    return NextResponse.json(
      { error: 'Failed to create expense type' },
      { status: 500 }
    )
  }
}

// PUT /api/expense-types - Update expense type
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, nameAr, description, isActive } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Expense type ID is required' },
        { status: 400 }
      )
    }

    // Check if expense type exists
    const existingType = await prisma.expenseType.findUnique({
      where: { id },
    })

    if (!existingType) {
      return NextResponse.json(
        { error: 'Expense type not found' },
        { status: 404 }
      )
    }

    // Check if name already exists for other expense types
    if (name && name !== existingType.name) {
      const nameExists = await prisma.expenseType.findFirst({
        where: {
          name,
          id: { not: id },
        },
      })

      if (nameExists) {
        return NextResponse.json(
          { error: 'Expense type name already exists' },
          { status: 400 }
        )
      }
    }

    // Update expense type
    const updatedType = await prisma.expenseType.update({
      where: { id },
      data: {
        name,
        nameAr,
        description,
        isActive,
        updatedAt: new Date(),
      },
      include: {
        _count: {
          select: {
            expenses: true,
          },
        },
      },
    })

    return NextResponse.json(updatedType)
  } catch (error) {
    console.error('Error updating expense type:', error)
    return NextResponse.json(
      { error: 'Failed to update expense type' },
      { status: 500 }
    )
  }
}
