import { PrismaClient } from '@prisma/client'
import { getPrismaOptions, initializeDatabase, databaseConfig } from './database-config'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

// Initialize database configuration (similar to PHP Capsule setup)
initializeDatabase()

// Get Prisma options with performance optimizations
const prismaOptions = getPrismaOptions()

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    ...prismaOptions,
    // Additional Prisma-specific optimizations
    errorFormat: process.env.NODE_ENV === 'development' ? 'pretty' : 'minimal',
  })

// Development: Enable query logging for debugging (similar to PHP development setup)
if (process.env.NODE_ENV === 'development' && process.env.ENABLE_QUERY_LOG !== 'false') {
  // Note: Event logging is configured through prismaOptions.log
  // This provides similar functionality to PHP's query logging
  console.log('🔍 Development mode: Query logging enabled')
}

// Production: Prevent lazy loading to catch N+1 queries (similar to PHP Eloquent setup)
if (process.env.CI_ENVIRONMENT === 'production' || process.env.NODE_ENV === 'production') {
  // Note: Prisma doesn't have direct equivalent to Eloquent's preventLazyLoading
  // But we can add connection pool optimizations and monitoring
  console.log('🚀 Production mode: Database optimizations enabled')
}

// Global Prisma instance for development hot reloading
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma
}

// Export database configuration for use in other parts of the application
export { databaseConfig } from './database-config'

// Graceful shutdown handler
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})

process.on('SIGINT', async () => {
  await prisma.$disconnect()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  await prisma.$disconnect()
  process.exit(0)
})
