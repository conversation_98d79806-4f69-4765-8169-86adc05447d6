"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Plus,
  Trash2,
  ArrowLeft,
  Save,
  Send,
  FileText,
  Calculator,
  Search,
  Check,
  ChevronsUpDown,
  UserPlus,
  Package,
  Printer,
  Eye,
  Download,
  Edit,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  MoreHorizontal,
  Copy,
  Archive,
  CreditCard
} from "lucide-react"
import { cn } from "@/lib/utils"
import { getStorageItem, STORAGE_KEYS } from "@/lib/storage"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

interface InvoiceItem {
  id: string
  description: string
  productId: string
  quantity: number
  unitPrice: number
  total: number
  image?: string
}

interface Product {
  id: string
  name: string
  nameAr?: string
  price: number
  unit: string
  category?: {
    id: string
    name: string
    nameAr?: string
  }
  image?: string
  type: 'PHYSICAL' | 'SERVICE'
  stock?: number
  sku?: string
}

interface Customer {
  id: string
  name: string
  mobile: string
  email?: string
}

interface Task {
  id: string
  title: string
  description: string
  customer: string
  status: string
}

interface Quotation {
  id: string
  number: string
  customer: string
  status: string
  total: number
  items: InvoiceItem[]
}

// Tasks will be fetched from API

const mockQuotations: Quotation[] = [
  {
    id: "1",
    number: "QUO-001",
    customer: "ABC Corporation",
    status: "approved",
    total: 150.00,
    items: [
      { id: "1", description: "Business Cards", productId: "cards", quantity: 5, unitPrice: 2.00, total: 10.00 },
      { id: "2", description: "Design Services", productId: "design", quantity: 2, unitPrice: 50.00, total: 100.00 }
    ]
  },
  {
    id: "2",
    number: "QUO-002",
    customer: "XYZ Enterprises",
    status: "pending",
    total: 75.50,
    items: [
      { id: "1", description: "Brochure Design", productId: "brochure", quantity: 1, unitPrice: 75.00, total: 75.00 }
    ]
  }
]

export default function CreateInvoicePage() {
  const router = useRouter()
  const { t, formatCurrency } = useI18n()
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([
    {
      id: "1",
      description: "",
      productId: "",
      quantity: 1,
      unitPrice: 0,
      total: 0
    }
  ])

  // Check for quotation conversion and projectId on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const quotationId = urlParams.get('quotation')
    const projectId = urlParams.get('projectId')

    if (quotationId) {
      fetch(`/api/quotations/${quotationId}`)
        .then(res => res.json())
        .then(data => {
          if (data && !data.error && data.customer) {
            const subtotal = Number(data.subtotal) || 0;
            const discount = Number(data.discount) || 0;
            const taxAmount = Number(data.taxAmount) || 0;
            const denominator = subtotal - discount;
            const taxRate = taxAmount > 0 && denominator > 0 ? (taxAmount / denominator) * 100 : 5;

            setFormData(prev => ({
              ...prev,
              quotationId: data.id,
              quotationNumber: data.number,
              customerId: data.customer.id,
              customerName: data.customer.name,
              customerMobile: data.customer.mobile,
              notes: data.notes || '',
              discount: discount,
              taxRate: taxRate,
            }))
            setCustomerSearchValue(`${data.customer.name} (${data.customer.mobile})`)
            setInvoiceItems(data.items.map((item: any) => ({
              id: item.id,
              description: item.description,
              productId: item.productId,
              quantity: Number(item.quantity) || 0,
              unitPrice: Number(item.unitPrice) || 0,
              total: Number(item.total) || 0,
            })))
          } else {
            toast.error("Failed to load quotation data or quotation is missing customer information.")
          }
        })
        .catch(() => toast.error("Error fetching quotation data."))
    }

    // Store projectId for redirect purposes and fetch project data
    if (projectId) {
      setFormData(prev => ({ ...prev, projectId }))
      
      // Fetch project data to auto-populate customer
      fetch(`/api/projects/${projectId}`)
        .then(response => response.json())
        .then(project => {
          if (project && project.client) {
            setFormData(prev => ({
              ...prev,
              customerId: project.client.id,
              customerName: project.client.name,
              customerMobile: project.client.mobile || project.client.phone
            }))
            setCustomerSearchValue(`${project.client.name} (${project.client.mobile || project.client.phone})`)
          }
        })
        .catch(error => {
          console.error('Error fetching project data:', error)
        })
    }
  }, [])

  const [formData, setFormData] = useState({
    customerId: "",
    customerName: "",
    customerMobile: "",
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    quotationId: "",
    quotationNumber: "",
    taskId: "",
    projectId: "", // Add projectId field
    taxRate: 5, // Default VAT rate for Oman
    discount: 0,
    discountType: "amount", // "amount" or "percentage"
    notes: "Thank you for your business!",
    status: "draft",
    // Payment settings
    paymentTerms: "net30",
    paymentMethod: "bank_transfer",
    recordPayment: false,
    paymentAmount: 0
  })

  // Settings for VAT (this would come from your settings/config)
  const [vatSettings, setVatSettings] = useState({
    enabled: true,
    rate: 5, // 5% VAT for Oman
    label: "VAT"
  })

  const [totals, setTotals] = useState({
    subtotal: 0,
    discountAmount: 0,
    taxAmount: 0,
    total: 0
  })

  // UI State
  const [customers, setCustomers] = useState<Customer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [productsLoading, setProductsLoading] = useState(true)
  const [customersLoading, setCustomersLoading] = useState(true)
  const [tasksLoading, setTasksLoading] = useState(true)
  const [customerSearchOpen, setCustomerSearchOpen] = useState(false)
  const [customerSearchValue, setCustomerSearchValue] = useState("")
  const [taskSearchOpen, setTaskSearchOpen] = useState(false)
  const [taskSearchValue, setTaskSearchValue] = useState("")
  const [showNewCustomerDialog, setShowNewCustomerDialog] = useState(false)
  const [newCustomerData, setNewCustomerData] = useState({ name: "", mobile: "", email: "" })
  const [productSearchStates, setProductSearchStates] = useState<{[key: string]: {open: boolean, value: string}}>({})
  const [mobileCheckResult, setMobileCheckResult] = useState<Customer | null>(null)

  const [companyData, setCompanyData] = useState({
    companyName: 'Your Company Name',
    companyNameAr: 'اسم شركتك',
    companyAddress: 'Your Company Address',
    companyAddressAr: 'عنوان شركتك',
    companyPhone: '+968 XXXX XXXX',
    companyEmail: '<EMAIL>',
    taxNumber: '',
    logo: null as string | null
  })

  // Load company data from settings
  const loadCompanyData = () => {
    try {
      const savedLogo = getStorageItem(STORAGE_KEYS.COMPANY_LOGO)
      const savedCompanyData = getStorageItem(STORAGE_KEYS.COMPANY_DATA)

      if (savedCompanyData) {
        const parsedData = JSON.parse(savedCompanyData)
        setCompanyData(prev => ({
          ...prev,
          companyName: parsedData.companyName || prev.companyName,
          companyNameAr: parsedData.companyNameAr || prev.companyNameAr,
          companyAddress: parsedData.companyAddress || prev.companyAddress,
          companyAddressAr: parsedData.companyAddressAr || prev.companyAddressAr,
          companyPhone: parsedData.companyPhone || prev.companyPhone,
          companyEmail: parsedData.companyEmail || prev.companyEmail,
          taxNumber: parsedData.taxNumber || prev.taxNumber,
        }))
      }

      if (savedLogo) {
        setCompanyData(prev => ({ ...prev, logo: savedLogo }))
      }
    } catch (error) {
      console.error('Error loading company data:', error)
    }
  }

  // Single, consolidated useEffect for all initial data loading
  useEffect(() => {
    const initializePage = async () => {
      // Load company data from local storage
      loadCompanyData();
      
      // Fetch base data (customers, products, etc.)
    try {
      setCustomersLoading(true)
        const customersResponse = await fetch('/api/customers?limit=1000')
        if (customersResponse.ok) {
          const data = await customersResponse.json()
        setCustomers(data.customers || [])
      }
    } catch (error) {
      console.error('Error fetching customers:', error)
    } finally {
      setCustomersLoading(false)
    }

      try {
        setProductsLoading(true)
        const productsResponse = await fetch('/api/products?limit=1000')
        if (productsResponse.ok) {
          const data = await productsResponse.json()
          setProducts(data.products || [])
        }
      } catch (error) {
        console.error('Error fetching products:', error)
      } finally {
        setProductsLoading(false)
      }

      try {
        setTasksLoading(true);
        const tasksResponse = await fetch('/api/tasks?limit=1000&status=all');
        if (tasksResponse.ok) {
          const data = await tasksResponse.json();
        const mappedTasks = (data.tasks || []).map((task: any) => ({
          id: task.id,
          title: task.title,
          description: task.description,
          customer: task.customer?.name || 'Unknown Customer',
          status: task.status
          }));
          setTasks(mappedTasks);
      } else {
           console.error('Failed to fetch tasks');
      }
    } catch (error) {
        console.error('Error fetching tasks:', error);
    } finally {
        setTasksLoading(false);
      }

      // Check URL for quotation to convert
      const urlParams = new URLSearchParams(window.location.search);
      const quotationId = urlParams.get('quotation');
      if (quotationId) {
        try {
          const res = await fetch(`/api/quotations/${quotationId}`);
          const data = await res.json();
          if (data && !data.error && data.customer) {
            const subtotal = Number(data.subtotal) || 0;
            const discount = Number(data.discount) || 0;
            const taxAmount = Number(data.taxAmount) || 0;
            const denominator = subtotal - discount;
            const taxRate = taxAmount > 0 && denominator > 0 ? (taxAmount / denominator) * 100 : 5;

            setFormData(prev => ({
              ...prev,
              quotationId: data.id,
              quotationNumber: data.number,
              customerId: data.customer.id,
              customerName: data.customer.name,
              customerMobile: data.customer.mobile,
              notes: data.notes || '',
              discount: discount,
              taxRate: taxRate,
            }));
            setCustomerSearchValue(`${data.customer.name} (${data.customer.mobile})`);
            setInvoiceItems(data.items.map((item: any) => ({
              id: item.id,
              description: item.description,
              productId: item.productId,
              quantity: Number(item.quantity) || 0,
              unitPrice: Number(item.unitPrice) || 0,
              total: Number(item.total) || 0,
            })));
          } else {
            toast.error("Failed to load quotation data or quotation is missing customer information.");
          }
        } catch {
          toast.error("Error fetching quotation data.");
        }
      }
    };

    initializePage();
  }, []);

  // Initialize VAT rate from settings
  useEffect(() => {
    if (vatSettings.enabled) {
      setFormData(prev => ({ ...prev, taxRate: vatSettings.rate }))
    }
  }, [vatSettings])

  // Calculate totals whenever items or rates change
  useEffect(() => {
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0)
    const discountAmount = formData.discountType === "percentage"
      ? (subtotal * formData.discount) / 100
      : formData.discount
    const taxableAmount = subtotal - discountAmount
    const taxAmount = vatSettings.enabled ? (taxableAmount * formData.taxRate) / 100 : 0
    const total = taxableAmount + taxAmount

    setTotals({
      subtotal,
      discountAmount,
      taxAmount,
      total
    })
  }, [invoiceItems, formData.taxRate, formData.discount, formData.discountType, vatSettings.enabled])

  const addInvoiceItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      description: "",
      productId: "",
      quantity: 1,
      unitPrice: 0,
      total: 0
    }
    setInvoiceItems([...invoiceItems, newItem])
  }

  const removeInvoiceItem = (id: string) => {
    if (invoiceItems.length > 1) {
      setInvoiceItems(invoiceItems.filter(item => item.id !== id))
    }
  }

  const updateInvoiceItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setInvoiceItems(items => items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value }

        // Auto-fill product details when product is selected
        if (field === 'productId' && value) {
          const product = products.find(p => p.id === value)
          if (product) {
            updatedItem.unitPrice = product.price
            updatedItem.description = product.name
            updatedItem.image = product.image
            // Calculate total immediately when product is selected
            updatedItem.total = Number((updatedItem.quantity * product.price).toFixed(2))
          }
        }

        // Recalculate total when quantity or price changes
        if (field === 'quantity' || field === 'unitPrice') {
          updatedItem.total = Number((updatedItem.quantity * updatedItem.unitPrice).toFixed(2))
        }

        return updatedItem
      }
      return item
    }))
  }

  // Customer management functions
  const checkMobileExists = (mobile: string) => {
    const existingCustomer = customers.find(c => c.mobile === mobile)
    setMobileCheckResult(existingCustomer || null)
    return existingCustomer
  }

  const handleCustomerSelect = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customerId: customer.id,
        customerName: customer.name,
        customerMobile: customer.mobile
      }))
      setCustomerSearchValue(`${customer.name} (${customer.mobile})`)
    }
  }

  const handleNewCustomerSave = async () => {
    if (!newCustomerData.name || !newCustomerData.mobile) {
      toast.error(t('pleaseEnterNameMobile'))
      return
    }

    const existingCustomer = checkMobileExists(newCustomerData.mobile)
    if (existingCustomer) {
      toast.warning(t('customerMobileExists'))
      return
    }

    try {
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newCustomerData.name,
          mobile: newCustomerData.mobile,
          email: newCustomerData.email || ""
        }),
      })

      if (response.ok) {
        const newCustomer = await response.json()
        setCustomers(prev => [...prev, newCustomer])
        handleCustomerSelect(newCustomer.id);
        setShowNewCustomerDialog(false)
        setNewCustomerData({ name: "", mobile: "", email: "" })
        setMobileCheckResult(null)
        toast.success(`${t('customerAddedSuccessfully')}: ${newCustomer.name}`)
      } else {
        const error = await response.json()
        toast.error(`${t('failedToCreateCustomer')}: ${error.error || ''}`)
      }
    } catch (error) {
      console.error('Error creating customer:', error)
      toast.error(t('failedToCreateCustomer'))
    }
  }

  // Product search functions
  const setProductSearchState = (itemId: string, state: {open?: boolean, value?: string}) => {
    setProductSearchStates(prev => ({
      ...prev,
      [itemId]: { ...prev[itemId], ...state }
    }))
  }

  const getProductSearchState = (itemId: string) => {
    return productSearchStates[itemId] || { open: false, value: "" }
  }

  const handleTaskSelect = (taskId: string) => {
    const task = tasks.find((t: Task) => t.id === taskId)
    if (task && invoiceItems.length === 1 && !invoiceItems[0].description) {
      updateInvoiceItem(invoiceItems[0].id, 'description', task.description)
    }
    setFormData(prev => ({ ...prev, taskId }))
  }

  const handlePrint = () => {
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      toast.warning(t('pleaseAllowPopups'))
      return
    }

    const printContent = generatePrintHTML()
    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.focus()
    printWindow.print()
  }

  const generatePrintHTML = () => {
    const customer = customers.find(c => c.id === formData.customerId)
    const currentDate = new Date().toLocaleDateString()

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Invoice Preview</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              color: #333;
              line-height: 1.4;
            }
            .invoice-header {
              display: flex;
              align-items: flex-start;
              justify-content: space-between;
              margin-bottom: 30px;
              border-bottom: 2px solid #333;
              padding-bottom: 20px;
            }
            .company-info {
              flex: 1;
            }
            .company-logo {
              max-height: 80px;
              max-width: 200px;
              margin-bottom: 15px;
              object-contain: contain;
            }
            .company-name {
              font-size: 28px;
              font-weight: bold;
              color: #2563eb;
              margin-bottom: 5px;
            }
            .company-details {
              color: #666;
              font-size: 14px;
              line-height: 1.6;
            }
            .invoice-title {
              font-size: 32px;
              font-weight: bold;
              margin: 20px 0;
              text-align: center;
              color: #2563eb;
            }
            .invoice-info {
              display: flex;
              justify-content: space-between;
              margin-bottom: 30px;
            }
            .info-section {
              flex: 1;
              margin-right: 20px;
            }
            .info-section:last-child {
              margin-right: 0;
            }
            .info-title {
              font-weight: bold;
              font-size: 16px;
              margin-bottom: 10px;
              color: #2563eb;
            }
            .items-table {
              width: 100%;
              border-collapse: collapse;
              margin: 20px 0;
            }
            .items-table th, .items-table td {
              border: 1px solid #ddd;
              padding: 12px;
              text-align: left;
            }
            .items-table th {
              background-color: #f8f9fa;
              font-weight: bold;
            }
            .items-table .number {
              text-align: right;
            }
            .totals {
              margin-top: 30px;
              text-align: right;
            }
            .totals-table {
              margin-left: auto;
              border-collapse: collapse;
              min-width: 300px;
            }
            .totals-table td {
              padding: 8px 15px;
              border-bottom: 1px solid #eee;
            }
            .totals-table .total-row {
              font-weight: bold;
              font-size: 18px;
              border-top: 2px solid #333;
              border-bottom: 2px solid #333;
            }
            .notes {
              margin-top: 30px;
              padding: 15px;
              background-color: #f8f9fa;
              border-left: 4px solid #2563eb;
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="invoice-header">
            <div class="company-info">
              ${companyData.logo ? `<img src="${companyData.logo}" alt="Company Logo" class="company-logo" />` : ''}
              <div class="company-name">${companyData.companyName}</div>
              <div class="company-details">
                <div>${companyData.companyAddress}</div>
                <div>Phone: ${companyData.companyPhone} | Email: ${companyData.companyEmail}</div>
                ${companyData.taxNumber ? `<div>Tax Number: ${companyData.taxNumber}</div>` : ''}
              </div>
            </div>
          </div>

          <div class="invoice-title">INVOICE</div>

          <div class="invoice-info">
            <div class="info-section">
              <div class="info-title">Bill To:</div>
              <div><strong>${customer?.name || 'Customer Name'}</strong></div>
              <div>${customer?.mobile || 'Phone Number'}</div>
              <div>${customer?.email || ''}</div>
            </div>
            <div class="info-section">
              <div class="info-title">Invoice Details:</div>
              <div><strong>Date:</strong> ${currentDate}</div>
              <div><strong>Due Date:</strong> ${new Date(formData.dueDate).toLocaleDateString()}</div>
              <div><strong>Status:</strong> ${formData.status.toUpperCase()}</div>
              ${formData.quotationNumber ? `<div><strong>From Quotation:</strong> ${formData.quotationNumber}</div>` : ''}
            </div>
          </div>

          <table class="items-table">
            <thead>
              <tr>
                <th style="width: 50px;">#</th>
                <th>Description</th>
                <th style="width: 80px;">Qty</th>
                <th style="width: 120px;">Unit Price</th>
                <th style="width: 120px;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${invoiceItems.map((item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${item.description || 'Item description'}</td>
                  <td class="number">${item.quantity}</td>
                  <td class="number">${formatCurrency(item.unitPrice)}</td>
                  <td class="number">${formatCurrency(item.total)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="totals">
            <table class="totals-table">
              <tr>
                <td>Subtotal:</td>
                <td class="number">${formatCurrency(totals.subtotal)}</td>
              </tr>
              ${formData.discount > 0 ? `
                <tr>
                  <td>Discount (${formData.discountType === 'percentage' ? formData.discount + '%' : formatCurrency(formData.discount)}):</td>
                  <td class="number">-${formatCurrency(totals.discountAmount)}</td>
                </tr>
              ` : ''}
              ${vatSettings.enabled ? `
                <tr>
                  <td>${vatSettings.label} (${formData.taxRate}%):</td>
                  <td class="number">${formatCurrency(totals.taxAmount)}</td>
                </tr>
              ` : ''}
              <tr class="total-row">
                <td>Total:</td>
                <td class="number">${formatCurrency(totals.total)}</td>
              </tr>
            </table>
          </div>

          ${formData.notes ? `
            <div class="notes">
              <strong>Notes:</strong><br>
              ${formData.notes}
            </div>
          ` : ''}
        </body>
      </html>
    `
  }

  const handleSave = async (status: string) => {
    if (!formData.customerId) {
      toast.error(t('pleaseSelectCustomer'))
      return
    }

    if (invoiceItems.length === 0 || invoiceItems.every(item => !item.description && !item.productId)) {
      toast.error(t('pleaseAddInvoiceItem'))
      return
    }

    const invalidItems = invoiceItems.filter(item =>
      (item.description || item.productId) && (item.quantity <= 0 || item.unitPrice < 0)
    )

    if (invalidItems.length > 0) {
      toast.error(t('checkItemQuantitiesPrices'))
      return
    }

    try {
      const invoiceItemsFormatted = invoiceItems
        .filter(item => item.description || item.productId)
        .map(item => ({
          description: item.description,
          quantity: item.quantity.toString(),
          unitPrice: item.unitPrice.toString(),
          productId: item.productId !== 'custom' ? item.productId : null
        }))

      const invoiceData = {
        customerId: formData.customerId,
        taskId: formData.taskId || null,
        projectId: formData.projectId || null,
        dueDate: formData.dueDate,
        items: invoiceItemsFormatted,
        taxAmount: totals.taxAmount,
        discount: totals.discountAmount,
        notes: formData.notes
      }

      console.log('Saving invoice to database:', invoiceData)

      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceData),
      })

      if (response.ok) {
        const savedInvoice = await response.json()
        
        if (formData['recordPayment'] && formData['paymentAmount'] > 0) {
          try {
            const paymentData = {
              invoiceId: savedInvoice.id,
              amount: formData['paymentAmount'],
              method: formData['paymentMethod'] || 'BANK_TRANSFER',
              date: new Date().toISOString().split('T')[0],
              notes: `Payment recorded during invoice creation`
            }

            const paymentResponse = await fetch('/api/payments', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(paymentData),
            })

            if (paymentResponse.ok) {
              toast.success(`${t('invoiceCreatedSuccessfully')}: ${savedInvoice.number} ${status === 'draft' ? 'saved as draft' : 'created'} with payment recorded!`)
            } else {
              toast.warning(`Invoice created: ${savedInvoice.number}, but failed to record payment. You can record payment manually.`)
            }
          } catch (error) {
            console.error('Error recording payment:', error)
            toast.warning(`Invoice created: ${savedInvoice.number}, but failed to record payment. You can record payment manually.`)
          }
        } else {
          toast.success(`${t('invoiceCreatedSuccessfully')}: ${savedInvoice.number} ${status === 'draft' ? 'saved as draft' : 'created'}!`)
        }

        if (formData.projectId) {
          router.push(`/dashboard/projects/${formData.projectId}/edit`)
        } else {
          router.push('/dashboard/invoices')
        }
      } else {
        const error = await response.json()
        toast.error(`${t('failedToCreateInvoice')}: ${error.error || ''}`)
      }
    } catch (error) {
      console.error('Error saving invoice:', error)
      toast.error(t('failedToSaveInvoice'))
    }
  }

  const handleSaveAndPrint = async (status: string) => {
    await handleSave(status)
    setTimeout(() => {
      handlePrint()
    }, 500)
  }

  return (
    <div className="flex flex-col h-full">
      <header className="bg-background border-b p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-semibold">{t('invoices.createInvoiceTitle')}</h1>
          </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => handleSave('draft')}>
            <Save className="h-4 w-4 mr-2" />
            {t('invoices.saveDraft')}
          </Button>
          <Button onClick={() => handleSave('pending')}>
            <Send className="h-4 w-4 mr-2" />
            {t('invoices.createInvoice')}
          </Button>
          <Button variant="secondary" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            {t('invoices.print')}
          </Button>
        </div>
      </header>

      <main className="flex-1 overflow-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
        <Card>
          <CardHeader>
                <CardTitle>{t('invoices.customerInformation')}</CardTitle>
          </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="customer">{t('invoices.customer')}</Label>
                  <Popover open={customerSearchOpen} onOpenChange={setCustomerSearchOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={customerSearchOpen}
                          className="w-full justify-between"
                      >
                          {formData.customerId
                            ? customers.find((c) => c.id === formData.customerId)?.name
                            : t('invoices.selectCustomer')}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                      <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                      <Command>
                          <CommandInput placeholder={t('invoices.searchCustomer')} />
                        <CommandList>
                            <CommandEmpty>{t('invoices.noCustomerFound')}</CommandEmpty>
                          <CommandGroup>
                            {customers.map((customer) => (
                              <CommandItem
                                key={customer.id}
                                  value={customer.id}
                                  onSelect={(currentValue) => {
                                    handleCustomerSelect(currentValue)
                                  setCustomerSearchOpen(false)
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    formData.customerId === customer.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                  {customer.name} ({customer.mobile})
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
                  <div className="flex items-end">
                    <Button onClick={() => setShowNewCustomerDialog(true)} variant="outline">
                      <UserPlus className="h-4 w-4 mr-2" /> {t('invoices.newCustomer')}
                    </Button>
                              </div>
              </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
                <CardTitle>{t('invoices.invoiceItems')}</CardTitle>
          </CardHeader>
          <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-1/2">{t('invoices.item')}</TableHead>
                      <TableHead>{t('invoices.quantity')}</TableHead>
                      <TableHead>{t('invoices.unitPrice')}</TableHead>
                      <TableHead>{t('invoices.total')}</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoiceItems.map((item, index) => (
                      <TableRow key={item.id}>
                        <TableCell>
                           <Popover open={getProductSearchState(item.id).open} onOpenChange={(open) => setProductSearchState(item.id, { open })}>
                            <PopoverTrigger asChild>
                              <Button variant="outline" className="w-full justify-between">
                                {item.productId ? products.find(p => p.id === item.productId)?.name : t('invoices.selectProduct')}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                              <Command>
                                <CommandInput placeholder={t('invoices.searchProducts')} />
                                <CommandList>
                                  <CommandEmpty>{t('invoices.noProductFound')}</CommandEmpty>
                                  <CommandGroup>
                                    {products.map((product) => (
                                      <CommandItem
                                        key={product.id}
                                        value={product.id}
                                        onSelect={(currentValue) => {
                                          updateInvoiceItem(item.id, 'productId', currentValue);
                                          setProductSearchState(item.id, { open: false });
                                        }}
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            item.productId === product.id ? "opacity-100" : "opacity-0"
                                          )}
                                        />
                                        {product.name}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <Textarea
                            placeholder={t('invoices.itemDescription')}
                            value={item.description}
                            onChange={(e) => updateInvoiceItem(item.id, 'description', e.target.value)}
                            className="mt-2"
                          />
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            value={item.quantity}
                            onChange={(e) => updateInvoiceItem(item.id, 'quantity', parseFloat(e.target.value))}
                          />
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            value={item.unitPrice}
                            onChange={(e) => updateInvoiceItem(item.id, 'unitPrice', parseFloat(e.target.value))}
                          />
                        </TableCell>
                        <TableCell>{formatCurrency(item.total)}</TableCell>
                        <TableCell>
                          <Button variant="ghost" size="icon" onClick={() => removeInvoiceItem(item.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <Button onClick={addInvoiceItem} variant="outline" className="mt-4">
                  <Plus className="h-4 w-4 mr-2" /> {t('invoices.addItem')}
                </Button>
          </CardContent>
        </Card>
          </div>

          <div className="space-y-6">
        <Card>
          <CardHeader>
                <CardTitle>{t('invoices.invoiceDetailsTitle')}</CardTitle>
          </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="dueDate">{t('invoices.dueDate')}</Label>
                  <Input
                    id="dueDate"
                    type="date"
                    value={formData.dueDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="status">{t('invoices.status')}</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('invoices.selectStatus')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">{t('invoices.draft')}</SelectItem>
                      <SelectItem value="pending">{t('invoices.pendingPayment')}</SelectItem>
                      <SelectItem value="paid">{t('invoices.paid')}</SelectItem>
                    </SelectContent>
                  </Select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
                <CardTitle>{t('invoices.invoiceSummary')}</CardTitle>
          </CardHeader>
          <CardContent>
              <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>{t('invoices.subtotal')}</span>
                    <span>{formatCurrency(totals.subtotal)}</span>
              </div>
                  <div className="flex justify-between items-center">
                     <span>{t('invoices.discount')}</span>
                    <div className="flex gap-2 w-1/2">
                       <Input 
                         type="number"
                         value={formData.discount}
                         onChange={(e) => setFormData(prev => ({ ...prev, discount: Number(e.target.value) }))}
                       />
                       <Select value={formData.discountType} onValueChange={(v) => setFormData(prev => ({ ...prev, discountType: v as "amount" | "percentage" }))}>
                         <SelectTrigger><SelectValue/></SelectTrigger>
                  <SelectContent>
                           <SelectItem value="amount">OMR</SelectItem>
                           <SelectItem value="percentage">%</SelectItem>
                  </SelectContent>
                </Select>
              </div>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('invoices.tax')} ({formData.taxRate}%)</span>
                    <span>{formatCurrency(totals.taxAmount)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg">
                    <span>{t('invoices.total')}</span>
                    <span>{formatCurrency(totals.total)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
                 <CardTitle>{t('invoices.notes')}</CardTitle>
          </CardHeader>
          <CardContent>
                 <Textarea
                   value={formData.notes}
                   onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                   placeholder={t('invoices.addNotesPlaceholder')}
                 />
          </CardContent>
        </Card>
      </div>
        </div>
      </main>

      {/* New Customer Dialog */}
      <Dialog open={showNewCustomerDialog} onOpenChange={setShowNewCustomerDialog}>
         <DialogContent>
          <DialogHeader>
             <DialogTitle>{t('invoices.addNewCustomer')}</DialogTitle>
             <DialogDescription>{t('invoices.addNewCustomerDesc')}</DialogDescription>
          </DialogHeader>
           <div className="space-y-4">
             <div>
               <Label htmlFor="new-customer-name">{t('invoices.customerName')}</Label>
               <Input id="new-customer-name" value={newCustomerData.name} onChange={(e) => setNewCustomerData(prev => ({ ...prev, name: e.target.value }))} />
            </div>
             <div>
               <Label htmlFor="new-customer-mobile">{t('invoices.mobileNumber')}</Label>
               <Input id="new-customer-mobile" value={newCustomerData.mobile} onChange={(e) => setNewCustomerData(prev => ({ ...prev, mobile: e.target.value }))} />
                </div>
             <div>
               <Label htmlFor="new-customer-email">{t('invoices.emailOptional')}</Label>
               <Input id="new-customer-email" type="email" value={newCustomerData.email} onChange={(e) => setNewCustomerData(prev => ({ ...prev, email: e.target.value }))} />
            </div>
          </div>
          <DialogFooter>
             <Button variant="outline" onClick={() => setShowNewCustomerDialog(false)}>{t('invoices.cancel')}</Button>
             <Button onClick={handleNewCustomerSave}>{t('invoices.addCustomer')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
