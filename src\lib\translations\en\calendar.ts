export const calendar = {
  title: "Calendar & Reminders",
  calendarAndReminders: "Calendar & Reminders",
  createEvent: "Create Event",
  addEvent: "Add Event",
  editEvent: "Edit Event",
  deleteEvent: "Delete Event",
  eventInformation: "Event Information",
  eventTitle: "Event Title",
  eventTitleAr: "Event Title (Arabic)",
  eventTitlePlaceholder: "Enter event title",
  allDayEvent: "All Day Event",
  startDate: "Start Date",
  endDate: "End Date",
  dueDate: "Due Date",
  assignTo: "Assign To",
  selectAssignee: "Select assignee",
  unassigned: "Unassigned",
  eventType: "Event Type",
  selectEventType: "Select event type",
  category: "Category",
  selectCategory: "Select category",
  priority: "Priority",
  selectPriority: "Select priority",
  notificationSettings: "Notification Settings",
  notifyBefore: "Notify Before (days)",
  recurringEvent: "Recurring Event",
  selectFrequency: "Select frequency",
  completedEvents: "Completed Events",
  overDueEvents: "Overdue Events",
  overdueEvents: "Overdue Events",
  upcomingEvents: "Upcoming Events",
  totalEvents: "Total Events",
  allStatus: "All Status",
  allCategories: "All Categories",
  allTypes: "All Types",
  searchPlaceholder: "Search events...",
  eventPriority: "Event Priority",
  eventCategory: "Event Category",
  createEventDescription: "Manage appointments, meetings, deadlines, and important dates",
  manageDescription: "Manage document renewals, deadlines, and important dates",
  calendarView: "Calendar View",
  listView: "List View",
  today: "Today",
  noEventsFound: "No events found. Create your first event!",
  eventCreatedSuccessfully: "Event created successfully",
  failedToCreateEvent: "Failed to create event",
  eventDeletedSuccessfully: "Event deleted successfully",
  failedToDeleteEvent: "Failed to delete event",
  description: "Description",
  eventDescriptionPlaceholder: "Event description...",
  additionalNotes: "Additional Notes",
  additionalNotesPlaceholder: "Any additional notes...",
  cancel: "Cancel",
  creating: "Creating...",
  
  // Event Types
  types: {
    reminder: "Reminder",
    meeting: "Meeting",
    deadline: "Deadline",
    renewal: "Renewal",
    expiration: "Expiration",
    taskDue: "Task Due",
    invoiceDue: "Invoice Due",
    paymentDue: "Payment Due",
    holiday: "Holiday",
    other: "Other"
  },
  
  // Categories
  categories: {
    document: "Document",
    financial: "Financial",
    legal: "Legal",
    hr: "HR",
    business: "Business",
    personal: "Personal",
    system: "System",
    other: "Other"
  },
  
  // Priorities
  priorities: {
    low: "Low",
    medium: "Medium",
    high: "High",
    urgent: "Urgent",
    critical: "Critical"
  },
  
  // Recurring Types
  recurringTypes: {
    daily: "Daily",
    weekly: "Weekly",
    monthly: "Monthly",
    quarterly: "Quarterly",
    yearly: "Yearly",
    custom: "Custom"
  },
  
  // Week Days
  weekDays: {
    sun: "Sun",
    mon: "Mon",
    tue: "Tue",
    wed: "Wed",
    thu: "Thu",
    fri: "Fri",
    sat: "Sat"
  },
  
  // Other
  back: "Back",
  calendarViewTitle: "Calendar View",
  calendarViewDescription: "Visual calendar with events and reminders",
  month: "Month",
  week: "Week",
  day: "Day",
  more: "More",
  legend: "Legend",
  status: "Status",
  eventTypes: {
    reminder: "Reminder",
    meeting: "Meeting",
    deadline: "Deadline",
    renewal: "Renewal",
    expiration: "Expiration",
    taskDue: "Task Due",
    invoiceDue: "Invoice Due",
    paymentDue: "Payment Due",
    holiday: "Holiday",
    other: "Other"
  },
  quickActions: "Quick Actions"
} as const; 