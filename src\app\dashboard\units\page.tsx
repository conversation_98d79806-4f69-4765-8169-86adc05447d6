"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Edit, Trash2, Ruler, ArrowLeft } from "lucide-react"
import { useI18n } from "@/lib/i18n"
import { ScrollableTable, useTableKeyboardNavigation } from "@/components/ui/scrollable-table"

export default function UnitsPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingUnit, setEditingUnit] = useState(null)
  const [units, setUnits] = useState([])
  const [loading, setLoading] = useState(true)
  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    symbol: '',
    symbolAr: '',
    description: '',
    descriptionAr: ''
  })

  // Enable keyboard navigation for table
  useTableKeyboardNavigation()

  // Fetch units from API
  const fetchUnits = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/units')

      if (response.ok) {
        const data = await response.json()
        setUnits(data.units || [])
      } else {
        console.error('Failed to fetch units, status:', response.status)
        const errorData = await response.json().catch(() => ({}))
        console.error('Error details:', errorData)
      }
    } catch (error) {
      console.error('Error fetching units:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUnits()
  }, [])

  const filteredUnits = units.filter(unit =>
    unit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (unit.nameAr && unit.nameAr.includes(searchTerm)) ||
    unit.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (unit.description && unit.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const resetForm = () => {
    setFormData({
      name: '',
      nameAr: '',
      symbol: '',
      symbolAr: '',
      description: '',
      descriptionAr: ''
    })
  }

  const populateForm = (unit) => {
    setFormData({
      name: unit.name || '',
      nameAr: unit.nameAr || '',
      symbol: unit.symbol || '',
      symbolAr: unit.symbolAr || '',
      description: unit.description || '',
      descriptionAr: unit.descriptionAr || ''
    })
  }

  const handleAddUnit = () => {
    setEditingUnit(null)
    resetForm()
    setIsDialogOpen(true)
  }

  const handleEditUnit = (unit) => {
    setEditingUnit(unit)
    populateForm(unit)
    setIsDialogOpen(true)
  }

  const handleDeleteUnit = async (unit) => {
    if (confirm(t('units.confirmDelete'))) {
      try {
        const response = await fetch(`/api/units/${unit.id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          alert(`✅ ${t('units.unitDeleted')}: ${unit.name}`)
          await fetchUnits() // Refresh the list
        } else {
          const error = await response.json()
          alert(`Error: ${error.error || 'Failed to delete unit'}`)
        }
      } catch (error) {
        console.error('Error deleting unit:', error)
        alert('Error deleting unit')
      }
    }
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSaveUnit = async () => {
    if (!formData.name || !formData.symbol) {
      alert(t('forms.required'))
      return
    }

    try {
      const method = editingUnit ? 'PUT' : 'POST'
      const url = editingUnit ? `/api/units/${editingUnit.id}` : '/api/units'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          nameAr: formData.nameAr,
          symbol: formData.symbol,
          symbolAr: formData.symbolAr,
          description: formData.description,
          descriptionAr: formData.descriptionAr,
        }),
      })

      if (response.ok) {
        const unit = await response.json()
        alert(`✅ ${editingUnit ? t('units.unitUpdated') : t('units.unitAdded')}: ${unit.name}`)
        await fetchUnits() // Refresh the list
        handleCloseDialog()
      } else {
        const error = await response.json()
        alert(`Error: ${error.error || 'Failed to save unit'}`)
      }
    } catch (error) {
      console.error('Error saving unit:', error)
      alert('Error saving unit')
    }
  }

  const handleCloseDialog = () => {
    setIsDialogOpen(false)
    setEditingUnit(null)
    resetForm()
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('units.title')}</h2>
          <p className="text-muted-foreground">
            {t('nav.units')} - إدارة وحدات القياس للمنتجات والخدمات
          </p>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <Button onClick={handleAddUnit}>
          <Plus className="mr-2 h-4 w-4" />
          {t('units.addUnit')}
        </Button>
        <Dialog open={isDialogOpen} onOpenChange={handleCloseDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{editingUnit ? t('units.editUnit') : t('units.addUnit')}</DialogTitle>
              <DialogDescription>
                {editingUnit ? 'تحديث تفاصيل الوحدة أدناه.' : 'إنشاء وحدة قياس جديدة للمنتجات والخدمات.'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('units.unitName')} *</Label>
                  <Input
                    id="name"
                    placeholder={t('units.unitName')}
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nameAr">{t('units.unitNameAr')}</Label>
                  <Input
                    id="nameAr"
                    placeholder="اسم الوحدة"
                    dir="rtl"
                    value={formData.nameAr}
                    onChange={(e) => handleInputChange('nameAr', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="symbol">{t('units.unitSymbol')} *</Label>
                  <Input
                    id="symbol"
                    placeholder={t('units.unitSymbol')}
                    value={formData.symbol}
                    onChange={(e) => handleInputChange('symbol', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="symbolAr">{t('units.unitSymbolAr')}</Label>
                  <Input
                    id="symbolAr"
                    placeholder="رمز الوحدة"
                    dir="rtl"
                    value={formData.symbolAr}
                    onChange={(e) => handleInputChange('symbolAr', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  placeholder="Unit description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="descriptionAr">Description (Arabic)</Label>
                <Input
                  id="descriptionAr"
                  placeholder="وصف الوحدة"
                  dir="rtl"
                  value={formData.descriptionAr}
                  onChange={(e) => handleInputChange('descriptionAr', e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={handleCloseDialog}>
                {t('common.cancel')}
              </Button>
              <Button type="submit" onClick={handleSaveUnit}>
                {editingUnit ? t('units.editUnit') : t('units.addUnit')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('units.searchUnits')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
      </div>

      <div className="table-container">
        <ScrollableTable>
          <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('units.unitName')}</TableHead>
              <TableHead>{t('units.unitSymbol')}</TableHead>
              <TableHead>{t('common.description')}</TableHead>
              <TableHead>{t('products.title')}</TableHead>
              <TableHead>{t('common.status')}</TableHead>
              <TableHead className="text-right">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span>Loading units...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredUnits.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <div className="flex flex-col items-center space-y-2">
                    <Ruler className="h-8 w-8 text-gray-400" />
                    <p className="text-gray-500">
                      {searchTerm ? 'No units found matching your search.' : 'No units found. Create your first unit to get started.'}
                    </p>
                    {!searchTerm && (
                      <Button onClick={handleAddUnit} className="mt-2">
                        <Plus className="mr-2 h-4 w-4" />
                        {t('units.addUnit')}
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredUnits.map((unit) => (
                <TableRow key={unit.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <Ruler className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <button className="font-medium text-blue-600 hover:text-blue-800 text-left">
                          {unit.name}
                        </button>
                        <div className="text-sm text-muted-foreground" dir="rtl">
                          {unit.nameAr}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-mono text-sm font-medium">{unit.symbol}</div>
                      <div className="font-mono text-xs text-muted-foreground" dir="rtl">
                        {unit.symbolAr}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-sm">{unit.description}</div>
                      <div className="text-xs text-muted-foreground mt-1" dir="rtl">
                        {unit.descriptionAr}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {unit._count?.products || 0} {t('products.title')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={unit.isActive ? "default" : "secondary"}>
                      {unit.isActive ? t('common.active') : t('common.inactive')}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditUnit(unit)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteUnit(unit)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
          </Table>
        </ScrollableTable>
      </div>
    </div>
  )
}
