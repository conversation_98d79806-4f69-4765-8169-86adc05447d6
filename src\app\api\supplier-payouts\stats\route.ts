import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'all' // all, today, week, month, year

    let dateFilter = {}
    const now = new Date()

    switch (period) {
      case 'today':
        const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate())
        const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
        dateFilter = {
          date: {
            gte: startOfDay,
            lt: endOfDay,
          },
        }
        break
      case 'week':
        const startOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay())
        dateFilter = {
          date: {
            gte: startOfWeek,
          },
        }
        break
      case 'month':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
        dateFilter = {
          date: {
            gte: startOfMonth,
          },
        }
        break
      case 'year':
        const startOfYear = new Date(now.getFullYear(), 0, 1)
        dateFilter = {
          date: {
            gte: startOfYear,
          },
        }
        break
    }

    // Get all payouts with date filter
    const payouts = await prisma.supplierPayout.findMany({
      where: dateFilter,
      include: {
        supplier: true,
      },
    })

    // Calculate statistics
    const totalPayouts = payouts.length
    const totalAmount = payouts.reduce((sum, payout) => sum + Number(payout.amount), 0)

    const statusCounts = {
      PENDING: payouts.filter(p => p.status === 'PENDING').length,
      APPROVED: payouts.filter(p => p.status === 'APPROVED').length,
      PAID: payouts.filter(p => p.status === 'PAID').length,
      CANCELLED: payouts.filter(p => p.status === 'CANCELLED').length,
      REJECTED: payouts.filter(p => p.status === 'REJECTED').length,
    }

    const statusAmounts = {
      PENDING: payouts.filter(p => p.status === 'PENDING').reduce((sum, p) => sum + Number(p.amount), 0),
      APPROVED: payouts.filter(p => p.status === 'APPROVED').reduce((sum, p) => sum + Number(p.amount), 0),
      PAID: payouts.filter(p => p.status === 'PAID').reduce((sum, p) => sum + Number(p.amount), 0),
      CANCELLED: payouts.filter(p => p.status === 'CANCELLED').reduce((sum, p) => sum + Number(p.amount), 0),
      REJECTED: payouts.filter(p => p.status === 'REJECTED').reduce((sum, p) => sum + Number(p.amount), 0),
    }

    const methodCounts = {
      CASH: payouts.filter(p => p.method === 'CASH').length,
      CARD: payouts.filter(p => p.method === 'CARD').length,
      BANK_TRANSFER: payouts.filter(p => p.method === 'BANK_TRANSFER').length,
      CHECK: payouts.filter(p => p.method === 'CHECK').length,
      OTHER: payouts.filter(p => p.method === 'OTHER').length,
    }

    // Get overdue payouts
    const overduePayouts = payouts.filter(p => 
      p.dueDate && 
      p.dueDate < now && 
      p.status !== 'PAID' && 
      p.status !== 'CANCELLED' && 
      p.status !== 'REJECTED'
    ).length

    // Get upcoming payouts (due in next 7 days)
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    const upcomingPayouts = payouts.filter(p => 
      p.dueDate && 
      p.dueDate >= now && 
      p.dueDate <= nextWeek && 
      p.status !== 'PAID' && 
      p.status !== 'CANCELLED' && 
      p.status !== 'REJECTED'
    ).length

    // Get top suppliers by payout amount
    const supplierPayouts = payouts.reduce((acc, payout) => {
      const supplierId = payout.supplierId
      if (!acc[supplierId]) {
        acc[supplierId] = {
          supplier: payout.supplier,
          totalAmount: 0,
          payoutCount: 0,
        }
      }
      acc[supplierId].totalAmount += Number(payout.amount)
      acc[supplierId].payoutCount += 1
      return acc
    }, {} as Record<string, any>)

    const topSuppliers = Object.values(supplierPayouts)
      .sort((a: any, b: any) => b.totalAmount - a.totalAmount)
      .slice(0, 5)

    // Calculate average payout amount
    const averageAmount = totalPayouts > 0 ? totalAmount / totalPayouts : 0

    return NextResponse.json({
      totalPayouts,
      totalAmount,
      averageAmount,
      statusCounts,
      statusAmounts,
      methodCounts,
      overduePayouts,
      upcomingPayouts,
      topSuppliers,
      period,
    })
  } catch (error) {
    console.error('Error fetching payout statistics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
