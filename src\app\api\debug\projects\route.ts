import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    // Get all projects with their raw data
    const projects = await prisma.project.findMany({
      select: {
        id: true,
        name: true,
        budget: true,
        actualCost: true,
        progress: true,
        status: true
      }
    })

    // Get all expenses for each project
    const projectsWithExpenses = await Promise.all(
      projects.map(async (project) => {
        const expenses = await prisma.expense.findMany({
          where: { 
            projectId: project.id,
            status: { in: ['APPROVED', 'PAID'] }
          },
          select: { amount: true, status: true }
        })

        const tasks = await prisma.task.findMany({
          where: { projectId: project.id },
          select: { status: true }
        })

        const totalExpenses = expenses.reduce((sum, exp) => sum + Number(exp.amount), 0)
        const completedTasks = tasks.filter(task => task.status === 'COMPLETED').length
        const calculatedProgress = tasks.length > 0 ? Math.round((completedTasks / tasks.length) * 100) : 0

        return {
          id: project.id,
          name: project.name,
          budget: {
            raw: project.budget,
            asNumber: Number(project.budget),
            type: typeof project.budget
          },
          actualCost: {
            raw: project.actualCost,
            asNumber: Number(project.actualCost),
            type: typeof project.actualCost
          },
          calculatedActualCost: totalExpenses,
          progress: {
            stored: project.progress,
            calculated: calculatedProgress
          },
          status: project.status,
          expenses: expenses.length,
          tasks: {
            total: tasks.length,
            completed: completedTasks
          }
        }
      })
    )

    // Calculate totals
    const totalBudget = projects.reduce((sum, p) => sum + Number(p.budget || 0), 0)
    const totalActualCost = projects.reduce((sum, p) => sum + Number(p.actualCost || 0), 0)

    return NextResponse.json({
      summary: {
        totalProjects: projects.length,
        totalBudget,
        totalActualCost,
        rawBudgetSum: projects.reduce((sum, p) => sum + (p.budget || 0), 0)
      },
      projects: projectsWithExpenses
    })
  } catch (error) {
    console.error('Debug error:', error)
    return NextResponse.json({ error: 'Debug failed', details: error }, { status: 500 })
  }
} 