"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Plus, Trash2, Calculator } from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"

interface InvoiceItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  total: number
  productId?: string
}

interface CreateInvoiceDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function CreateInvoiceDialog({ open, onOpenChange, onSuccess }: CreateInvoiceDialogProps) {
  const { t } = useI18n()
  const [loading, setLoading] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [tasks, setTasks] = useState<any[]>([])
  
  // Form state
  const [customerId, setCustomerId] = useState("")
  const [taskId, setTaskId] = useState("")
  const [dueDate, setDueDate] = useState("")
  const [notes, setNotes] = useState("")
  const [items, setItems] = useState<InvoiceItem[]>([
    { id: "1", description: "", quantity: 1, unitPrice: 0, total: 0 }
  ])
  const [taxRate, setTaxRate] = useState(5) // 5% VAT for Oman
  const [discount, setDiscount] = useState(0)

  // Load data
  useEffect(() => {
    if (open) {
      loadCustomers()
      loadProducts()
      loadTasks()
      
      // Set default due date (30 days from now)
      const defaultDueDate = new Date()
      defaultDueDate.setDate(defaultDueDate.getDate() + 30)
      setDueDate(defaultDueDate.toISOString().split('T')[0])
    }
  }, [open])

  const loadCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      if (response.ok) {
        const data = await response.json()
        setCustomers(Array.isArray(data) ? data : data.customers || [])
      }
    } catch (error) {
      console.error('Error loading customers:', error)
    }
  }

  const loadProducts = async () => {
    try {
      const response = await fetch('/api/products')
      if (response.ok) {
        const data = await response.json()
        setProducts(Array.isArray(data) ? data : data.products || [])
      }
    } catch (error) {
      console.error('Error loading products:', error)
    }
  }

  const loadTasks = async () => {
    try {
      const response = await fetch('/api/tasks')
      if (response.ok) {
        const data = await response.json()
        setTasks(Array.isArray(data) ? data : data.tasks || [])
      }
    } catch (error) {
      console.error('Error loading tasks:', error)
    }
  }

  const addItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      description: "",
      quantity: 1,
      unitPrice: 0,
      total: 0
    }
    setItems([...items, newItem])
  }

  const removeItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter(item => item.id !== id))
    }
  }

  const updateItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value }
        
        // Recalculate total when quantity or unitPrice changes
        if (field === 'quantity' || field === 'unitPrice') {
          updatedItem.total = updatedItem.quantity * updatedItem.unitPrice
        }
        
        return updatedItem
      }
      return item
    }))
  }

  const selectProduct = (itemId: string, productId: string) => {
    const product = products.find(p => p.id === productId)
    if (product) {
      updateItem(itemId, 'description', product.name)
      updateItem(itemId, 'unitPrice', product.price)
      updateItem(itemId, 'productId', productId)
    }
  }

  // Calculate totals
  const subtotal = items.reduce((sum, item) => sum + item.total, 0)
  const discountAmount = (subtotal * discount) / 100
  const taxableAmount = subtotal - discountAmount
  const taxAmount = (taxableAmount * taxRate) / 100
  const total = taxableAmount + taxAmount

  const handleSubmit = async () => {
    if (!customerId || items.some(item => !item.description || item.quantity <= 0 || item.unitPrice <= 0)) {
      alert('Please fill in all required fields')
      return
    }

    setLoading(true)
    try {
      const invoiceData = {
        customerId,
        taskId: taskId || null,
        dueDate,
        items: items.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          productId: item.productId || null
        })),
        taxAmount,
        discount: discountAmount,
        notes
      }

      // Replace with actual API call
      // const response = await fetch('/api/invoices', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(invoiceData)
      // })

      console.log('Creating invoice:', invoiceData)
      
      // Reset form
      setCustomerId("")
      setTaskId("")
      setNotes("")
      setItems([{ id: "1", description: "", quantity: 1, unitPrice: 0, total: 0 }])
      setDiscount(0)
      
      onSuccess()
      onOpenChange(false)
    } catch (error) {
      console.error('Error creating invoice:', error)
      alert('Error creating invoice')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('invoices.createInvoiceTitle')}</DialogTitle>
          <DialogDescription>
            {t('invoices.createInvoiceDescription')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Customer and Date Information */}
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="customer">{t('invoices.customer')} *</Label>
              <Select value={customerId} onValueChange={setCustomerId}>
                <SelectTrigger>
                  <SelectValue placeholder={t('invoices.selectCustomer')} />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer: any) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="dueDate">{t('invoices.dueDate')}</Label>
              <Input
                id="dueDate"
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="task">{t('invoices.linkToTask')}</Label>
              <Select value={taskId} onValueChange={setTaskId}>
                <SelectTrigger>
                  <SelectValue placeholder={t('invoices.selectTask')} />
                </SelectTrigger>
                <SelectContent>
                  {tasks.map((task: any) => (
                    <SelectItem key={task.id} value={task.id}>
                      {task.title}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Invoice Items */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">{t('invoices.invoiceItems')}</Label>
              <Button type="button" variant="outline" size="sm" onClick={addItem}>
                <Plus className="mr-2 h-4 w-4" />
                {t('invoices.addItem')}
              </Button>
            </div>
            
            <div className="border rounded-lg p-4 space-y-4">
              <div className="grid grid-cols-12 gap-3 text-sm font-medium text-muted-foreground">
                <div className="col-span-4">{t('invoices.itemDescription')}</div>
                <div className="col-span-2">{t('invoices.productService')}</div>
                <div className="col-span-2">{t('invoices.quantity')}</div>
                <div className="col-span-2">{t('invoices.unitPrice')}</div>
                <div className="col-span-1">{t('invoices.total')}</div>
                <div className="col-span-1">{t('invoices.action')}</div>
              </div>
              
              {items.map((item) => (
                <div key={item.id} className="grid grid-cols-12 gap-3 items-center">
                  <Input
                    placeholder={t('invoices.itemDescriptionPlaceholder')}
                    value={item.description}
                    onChange={(e) => updateItem(item.id, 'description', e.target.value)}
                    className="col-span-4"
                  />
                  
                  <Select onValueChange={(value) => selectProduct(item.id, value)}>
                    <SelectTrigger className="col-span-2">
                      <SelectValue placeholder={t('invoices.selectProduct')} />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map((product: any) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={item.quantity}
                    onChange={(e) => updateItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                    className="col-span-2"
                  />
                  
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={item.unitPrice}
                    onChange={(e) => updateItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                    className="col-span-2"
                  />
                  
                  <div className="col-span-1 text-sm font-medium">
                    {formatCurrency(item.total)}
                  </div>
                  
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeItem(item.id)}
                    disabled={items.length === 1}
                    className="col-span-1 h-9"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Tax and Discount */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="taxRate">{t('invoices.taxRate')} (%)</Label>
              <Input
                id="taxRate"
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={taxRate}
                onChange={(e) => setTaxRate(parseFloat(e.target.value) || 0)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="discount">{t('invoices.discount')} (%)</Label>
              <Input
                id="discount"
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={discount}
                onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
              />
            </div>
          </div>

          {/* Totals Summary */}
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">{t('invoices.invoiceSummary')}</span>
              <Calculator className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>{t('invoices.subtotal')}:</span>
                <span>{formatCurrency(subtotal)}</span>
              </div>
              {discount > 0 && (
                <div className="flex justify-between">
                  <span>{t('invoices.discount')} ({discount}%):</span>
                  <span>-{formatCurrency(discountAmount)}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span>{t('invoices.tax')} ({taxRate}%):</span>
                <span>{formatCurrency(taxAmount)}</span>
              </div>
              <div className="flex justify-between font-medium text-base border-t pt-1">
                <span>{t('invoices.total')}:</span>
                <span>{formatCurrency(total)}</span>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">{t('invoices.notesOptional')}</Label>
            <Textarea
              id="notes"
              placeholder={t('invoices.additionalNotesPlaceholder')}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('invoices.cancel')}
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? t('invoices.creating') : t('invoices.createInvoice')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
