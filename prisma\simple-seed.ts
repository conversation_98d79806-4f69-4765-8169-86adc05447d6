import { PrismaClient } from '@prisma/client'
import * as bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database with basic data...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10)

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Admin User',
      role: 'ADMIN',
      phone: '+968 24123456',
      isActive: true,
    },
  })

  // Create categories
  const printingCategory = await prisma.category.create({
    data: {
      name: 'Printing Services',
      nameAr: 'خدمات الطباعة',
      description: 'All printing related services',
    },
  })

  const suppliesCategory = await prisma.category.create({
    data: {
      name: 'Office Supplies',
      nameAr: 'المستلزمات المكتبية',
      description: 'Office supplies and materials',
    },
  })

  // Create suppliers
  const supplier1 = await prisma.supplier.create({
    data: {
      name: 'Al Warak Trading LLC',
      email: '<EMAIL>',
      mobile: '+968 24123456',
      phone: '+968 24123456',
      company: 'Al Warak Trading LLC',
      address: 'Ruwi Commercial District, Muscat, Oman',
      contactPerson: 'Ahmed Al Rashid',
      taxNumber: 'OM1234567890',
      notes: 'Main paper and office supplies distributor',
    },
  })

  // Create products
  const product1 = await prisma.product.create({
    data: {
      name: 'A4 Paper - Premium',
      nameAr: 'ورق A4 - ممتاز',
      description: 'High quality A4 printing paper 80gsm',
      sku: 'A4-PAPER-001',
      type: 'PHYSICAL',
      price: 2.500,
      costPrice: 1.800,
      currentStock: 1000,
      minStock: 100,
      unit: 'ream',
      categoryId: suppliesCategory.id,
      supplierId: supplier1.id,
    },
  })

  const product2 = await prisma.product.create({
    data: {
      name: 'Business Card Printing',
      nameAr: 'طباعة بطاقات العمل',
      description: 'Professional business card printing service (500 cards)',
      sku: 'BC-PRINT-001',
      type: 'SERVICE',
      price: 15.000,
      costPrice: 8.000,
      currentStock: 0,
      minStock: 0,
      unit: 'set',
      categoryId: printingCategory.id,
    },
  })

  // Create customers
  const customer1 = await prisma.customer.create({
    data: {
      name: 'Muscat Trading Company LLC',
      email: '<EMAIL>',
      mobile: '+968 24111222',
      phone: '+968 24111222',
      company: 'Muscat Trading Company LLC',
      address: 'Al Khuwair, Way 3018, Building 145, Muscat, Oman',
      taxNumber: 'OM1001234567',
      notes: 'Regular corporate customer, bulk printing orders',
    },
  })

  const customer2 = await prisma.customer.create({
    data: {
      name: 'Ahmed Al Kindi',
      email: '<EMAIL>',
      mobile: '+968 99123456',
      phone: '+968 99123456',
      company: 'Individual',
      address: 'Al Mawaleh, Muscat, Oman',
      notes: 'Individual customer, occasional printing needs',
    },
  })

  // Create settings
  await prisma.setting.upsert({
    where: { key: 'company_name' },
    update: { value: 'Muscat Print & Design Center' },
    create: {
      key: 'company_name',
      value: 'Muscat Print & Design Center',
      description: 'Company name',
    },
  })

  await prisma.setting.upsert({
    where: { key: 'default_currency' },
    update: { value: 'OMR' },
    create: {
      key: 'default_currency',
      value: 'OMR',
      description: 'Default currency (Omani Rial)',
    },
  })

  console.log('✅ Basic database seeded successfully!')
  console.log('👤 Admin user: <EMAIL> / admin123')
  console.log('📊 Created: 2 categories, 1 supplier, 2 products, 2 customers')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
