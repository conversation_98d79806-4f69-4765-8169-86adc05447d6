"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  ArrowL<PERSON><PERSON>,
  Plus,
  Trash2,
  Check,
  ChevronsUpDown,
  UserPlus,
  Save,
  Send,
  FileText,
  Calculator,
  Package,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { addQuotation } from "@/lib/quotation-storage"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

interface Customer {
  id: string
  name: string
  mobile: string
  email?: string
}

interface Product {
  id: string
  name: string
  price: number
  unit: string
  category: { name: string } | string;
}

interface QuotationItem {
  id: string
  description: string
  productId: string
  quantity: number
  unitPrice: number
  total: number
}

export default function CreateQuotationPage() {
  const router = useRouter()
  const { t, formatCurrency } = useI18n()
  const [products, setProducts] = useState<Product[]>([])
  const [quotationItems, setQuotationItems] = useState<QuotationItem[]>([
    {
      id: "1",
      description: "",
      productId: "",
      quantity: 1,
      unitPrice: 0,
      total: 0
    }
  ])

  const [formData, setFormData] = useState({
    customerId: "",
    customerName: "",
    customerMobile: "",
    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    taxRate: 5, // Default VAT rate for Oman
    discount: 0,
    discountType: "amount", // "amount" or "percentage"
    notes: t('quotations.create.infoSection.notesPlaceholder'),
    status: "draft"
  })

  // Settings for VAT (this would come from your settings/config)
  const [vatSettings, setVatSettings] = useState({
    enabled: true,
    rate: 5, // 5% VAT for Oman
    label: "VAT"
  })

  const [totals, setTotals] = useState({
    subtotal: 0,
    discountAmount: 0,
    taxAmount: 0,
    total: 0
  })

  const [customers, setCustomers] = useState<Customer[]>([])
  const [customerSearchOpen, setCustomerSearchOpen] = useState(false)
  const [customerSearchValue, setCustomerSearchValue] = useState("")
  const [showNewCustomerDialog, setShowNewCustomerDialog] = useState(false)
  const [newCustomerData, setNewCustomerData] = useState({ name: "", mobile: "", email: "" })
  const [mobileCheckResult, setMobileCheckResult] = useState<Customer | null>(null)

  // Load customers and products on component mount
  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    try {
      const [customersRes, productsRes] = await Promise.all([
        fetch('/api/customers'),
        fetch('/api/products')
      ])
      if (customersRes.ok) {
        const data = await customersRes.json()
        setCustomers(Array.isArray(data) ? data : data.customers || [])
      }
      if (productsRes.ok) {
        const data = await productsRes.json()
        setProducts(data.products || [])
      }
    } catch (error) {
      console.error('Error loading initial data:', error)
      toast.error("Failed to load customers and products.")
    }
  }
  
  // Product search states
  const [productSearchStates, setProductSearchStates] = useState<{[key: string]: {open: boolean, value: string}}>({})

  // Initialize VAT rate from settings
  useEffect(() => {
    if (vatSettings.enabled) {
      setFormData(prev => ({ ...prev, taxRate: vatSettings.rate }))
    }
  }, [vatSettings])

  // Calculate totals whenever items or rates change
  useEffect(() => {
    const subtotal = quotationItems.reduce((sum, item) => sum + item.total, 0)
    const discountAmount = formData.discountType === "percentage"
      ? (subtotal * formData.discount) / 100
      : formData.discount
    const taxableAmount = subtotal - discountAmount
    const taxAmount = vatSettings.enabled ? (taxableAmount * formData.taxRate) / 100 : 0
    const total = taxableAmount + taxAmount

    setTotals({
      subtotal,
      discountAmount,
      taxAmount,
      total
    })
  }, [quotationItems, formData.taxRate, formData.discount, formData.discountType, vatSettings.enabled])

  const updateQuotationItem = (id: string, field: keyof QuotationItem, value: any) => {
    setQuotationItems(items => items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value }

        if (field === 'productId' && value) {
          const product = products.find(p => p.id === value)
          if (product) {
            updatedItem.unitPrice = product.price
            updatedItem.description = product.name
            updatedItem.total = Number((updatedItem.quantity * product.price).toFixed(2))
          }
        }

        if (field === 'quantity' || field === 'unitPrice') {
          updatedItem.total = Number((updatedItem.quantity * updatedItem.unitPrice).toFixed(2))
        }

        return updatedItem
      }
      return item
    }))
  }

  const addQuotationItem = () => {
    const newItem: QuotationItem = {
      id: Date.now().toString(),
      description: "",
      productId: "",
      quantity: 1,
      unitPrice: 0,
      total: 0
    }
    setQuotationItems(prev => [...prev, newItem])
  }

  const removeQuotationItem = (id: string) => {
    if (quotationItems.length > 1) {
      setQuotationItems(prev => prev.filter(item => item.id !== id))
    }
  }

  const setProductSearchState = (itemId: string, state: {open?: boolean, value?: string}) => {
    setProductSearchStates(prev => ({
      ...prev,
      [itemId]: { ...prev[itemId], ...state }
    }))
  }

  const getProductSearchState = (itemId: string) => {
    return productSearchStates[itemId] || { open: false, value: "" }
  }

  const checkMobileExists = (mobile: string) => {
    const existing = customers.find(c => c.mobile === mobile)
    setMobileCheckResult(existing || null)
    return existing
  }

  const handleCustomerSelect = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customerId: customer.id,
        customerName: customer.name,
        customerMobile: customer.mobile
      }))
      setCustomerSearchValue(`${customer.name} (${customer.mobile})`)
    }
  }

  const handleNewCustomerSave = async () => {
    if (!newCustomerData.name || !newCustomerData.mobile) {
      toast.error(t('quotations.create.alerts.nameAndMobileRequired'))
      return
    }

    const existingCustomer = checkMobileExists(newCustomerData.mobile)
    if (existingCustomer) {
      toast.error(t('quotations.create.alerts.customerExists'))
      return
    }

    try {
      const newCustomer: Customer = {
        id: Date.now().toString(),
        name: newCustomerData.name,
        mobile: newCustomerData.mobile,
        email: newCustomerData.email || ""
      }

      setCustomers(prev => [...prev, newCustomer])
      handleCustomerSelect(newCustomer.id)

      setShowNewCustomerDialog(false)
      setNewCustomerData({ name: "", mobile: "", email: "" })
      setMobileCheckResult(null)

      toast.success(t('quotations.create.alerts.customerCreated'))
    } catch (error) {
      console.error('Error creating customer:', error)
      toast.error(t('quotations.create.alerts.customerCreateFailed'))
    }
  }

  const handleSave = async (status: string) => {
    if (!formData.customerId) {
      toast.error(t('quotations.create.alerts.selectCustomer'))
      return
    }

    if (quotationItems.length === 0 || quotationItems.every(item => !item.description && !item.productId)) {
      toast.error(t('quotations.create.alerts.addOneItem'))
      return
    }

    const invalidItems = quotationItems.filter(item =>
      (item.description || item.productId) && (item.quantity <= 0 || item.unitPrice < 0)
    )

    if (invalidItems.length > 0) {
      toast.error(t('quotations.create.alerts.checkItems'))
      return
    }

    try {
      const quotationData = {
        validUntil: formData.validUntil,
        customerId: formData.customerId,
        status: status === 'draft' ? 'PENDING' : 'PENDING' as const,
        subtotal: totals.subtotal,
        taxAmount: totals.taxAmount,
        discount: totals.discountAmount,
        total: totals.total,
        items: quotationItems
          .filter(item => item.description || item.productId)
          .map(item => ({
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            total: item.total,
            productId: item.productId,
          })),
        notes: formData.notes,
      }

      console.log('Saving quotation:', quotationData)
      const savedQuotation = await addQuotation(quotationData)

      if (savedQuotation) {
        const successMessage = status === 'draft'
          ? t('quotations.create.alerts.savedAsDraft').replace('{number}', savedQuotation.number)
          : t('quotations.create.alerts.createdSuccessfully').replace('{number}', savedQuotation.number)
        toast.success(successMessage)

        router.push('/dashboard/quotations')
      } else {
        toast.error(t('quotations.create.alerts.saveFailed'))
      }

    } catch (error) {
      console.error('Error saving quotation:', error)
      toast.error(t('quotations.create.alerts.saveFailed'))
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/dashboard/quotations')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('quotations.create.backButton')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('quotations.create.title')}</h2>
            <p className="text-muted-foreground">
              {t('quotations.create.description')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => handleSave('draft')}
          >
            <Save className="mr-2 h-4 w-4" />
            {t('quotations.create.saveDraft')}
          </Button>
          <Button onClick={() => handleSave('final')}>
            <Send className="mr-2 h-4 w-4" />
            {t('quotations.create.createFinal')}
          </Button>
        </div>
      </div>

      <div className="max-w-6xl mx-auto space-y-6">

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              {t('quotations.create.infoSection.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="customer">{t('quotations.create.customerSection.label')}</Label>
                <div className="flex gap-2">
                  <Popover open={customerSearchOpen} onOpenChange={setCustomerSearchOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={customerSearchOpen}
                        className="flex-1 justify-between min-w-0"
                      >
                        <span className="truncate">{customerSearchValue || t('quotations.create.customerSection.placeholder')}</span>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[400px] p-0">
                      <Command>
                        <CommandInput placeholder={t('quotations.create.customerSection.searchPlaceholder')} />
                        <CommandList>
                          <CommandEmpty>
                            <div className="p-4 text-center">
                              <p className="text-sm text-muted-foreground mb-2">{t('quotations.create.customerSection.notFound')}</p>
                              <Button
                                size="sm"
                                onClick={() => {
                                  setShowNewCustomerDialog(true)
                                  setCustomerSearchOpen(false)
                                }}
                              >
                                <UserPlus className="mr-2 h-4 w-4" />
                                {t('quotations.create.customerSection.addNew')}
                              </Button>
                            </div>
                          </CommandEmpty>
                          <CommandGroup>
                            {customers.map((customer) => (
                              <CommandItem
                                key={customer.id}
                                value={`${customer.name} ${customer.mobile} ${customer.email || ''}`}
                                keywords={[customer.name, customer.mobile, customer.email || '']}
                                onSelect={() => {
                                  handleCustomerSelect(customer.id)
                                  setCustomerSearchOpen(false)
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    formData.customerId === customer.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                <div className="flex flex-col">
                                  <span className="font-medium">{customer.name}</span>
                                  <span className="text-sm text-muted-foreground">{customer.mobile}</span>
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setShowNewCustomerDialog(true)}
                    title={t('quotations.create.customerSection.addNew')}
                  >
                    <UserPlus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="validUntil">{t('quotations.create.infoSection.validUntil')}</Label>
                <Input
                  id="validUntil"
                  type="date"
                  value={formData.validUntil}
                  onChange={(e) => setFormData({...formData, validUntil: e.target.value})}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="status">{t('quotations.create.infoSection.status')}</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData({...formData, status: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">{t('quotations.create.infoSection.statusOptions.draft')}</SelectItem>
                    <SelectItem value="final">{t('quotations.create.infoSection.statusOptions.final')}</SelectItem>
                    <SelectItem value="sent">{t('quotations.create.infoSection.statusOptions.sent')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">{t('quotations.create.infoSection.notes')}</Label>
                <Textarea
                  id="notes"
                  placeholder={t('quotations.create.infoSection.notesPlaceholder')}
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  className="min-h-[60px]"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                {t('quotations.create.itemsSection.title')}
              </CardTitle>
              <Button onClick={addQuotationItem} size="sm">
                <Plus className="mr-2 h-4 w-4" />
                {t('quotations.create.itemsSection.addItem')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <Table className="min-w-full">
                  <TableHeader>
                    <TableRow className="border-b">
                      <TableHead className="min-w-[300px]">{t('quotations.create.itemsSection.headers.description')}</TableHead>
                      <TableHead className="min-w-[200px]">{t('quotations.create.itemsSection.headers.product')}</TableHead>
                      <TableHead className="w-[120px] text-center">{t('quotations.create.itemsSection.headers.quantity')}</TableHead>
                      <TableHead className="w-[140px] text-right">{t('quotations.create.itemsSection.headers.unitPrice')}</TableHead>
                      <TableHead className="w-[140px] text-right">{t('quotations.create.itemsSection.headers.total')}</TableHead>
                      <TableHead className="w-[100px] text-center">{t('quotations.create.itemsSection.headers.action')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {quotationItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Textarea
                            placeholder={t('quotations.create.itemsSection.descriptionPlaceholder')}
                            value={item.description}
                            onChange={(e) => updateQuotationItem(item.id, 'description', e.target.value)}
                            className="min-h-[60px] resize-none"
                          />
                        </TableCell>
                        <TableCell>
                          <Popover
                            open={getProductSearchState(item.id).open}
                            onOpenChange={(open) => setProductSearchState(item.id, { open })}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                role="combobox"
                                className="w-full justify-between"
                              >
                                {item.productId ?
                                  products.find(p => p.id === item.productId)?.name || t('quotations.create.itemsSection.productPlaceholder')
                                  : t('quotations.create.itemsSection.productPlaceholder')
                                }
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[300px] p-0">
                              <Command>
                                <CommandInput placeholder={t('quotations.create.itemsSection.productSearchPlaceholder')} />
                                <CommandList>
                                  <CommandEmpty>{t('quotations.create.itemsSection.productNotFound')}</CommandEmpty>
                                  <CommandGroup heading={t('quotations.create.itemsSection.productsGroup')}>
                                    {products.map((product) => (
                                      <CommandItem
                                        key={product.id}
                                        value={`${product.name} ${typeof product.category === 'object' ? product.category.name : product.category} ${product.unit}`}
                                        keywords={[product.name, typeof product.category === 'object' ? product.category.name : product.category, product.unit]}
                                        onSelect={() => {
                                          updateQuotationItem(item.id, 'productId', product.id)
                                          setProductSearchState(item.id, { open: false })
                                        }}
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            item.productId === product.id ? "opacity-100" : "opacity-0"
                                          )}
                                        />
                                        <div className="flex flex-col">
                                          <span className="font-medium">{product.name}</span>
                                          <span className="text-sm text-muted-foreground">
                                            {formatCurrency(Number(product.price))} {product.unit} • {typeof product.category === 'object' ? product.category.name : product.category}
                                          </span>
                                        </div>
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                        </TableCell>
                        <TableCell className="text-center">
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.quantity}
                            onChange={(e) => updateQuotationItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                            className="w-full text-center"
                            placeholder="1"
                          />
                        </TableCell>
                        <TableCell className="text-right">
                          <Input
                            type="number"
                            min="0"
                            step="0.001"
                            value={item.unitPrice}
                            onChange={(e) => updateQuotationItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                            className="w-full text-right"
                            placeholder="0.000"
                          />
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="font-medium text-lg">
                            {formatCurrency(Number(item.total))}
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeQuotationItem(item.id)}
                            disabled={quotationItems.length === 1}
                            className="mx-auto"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="mr-2 h-5 w-5" />
              {t('quotations.create.settingsSection.title').replace('{label}', vatSettings.label)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {vatSettings.enabled && (
                <div className="space-y-2">
                  <Label htmlFor="taxRate">{t('quotations.create.settingsSection.rateLabel').replace('{label}', vatSettings.label)}</Label>
                  <Input
                    id="taxRate"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={formData.taxRate}
                    onChange={(e) => setFormData({...formData, taxRate: parseFloat(e.target.value) || 0})}
                    disabled={!vatSettings.enabled}
                  />
                  <p className="text-xs text-muted-foreground">
                    {t('quotations.create.settingsSection.defaultRate').replace('{rate}', String(vatSettings.rate))}
                  </p>
                </div>
              )}
              <div className="space-y-2">
                <Label htmlFor="discount">{t('quotations.create.settingsSection.discount')}</Label>
                <div className="flex gap-2">
                  <Select
                    value={formData.discountType}
                    onValueChange={(value: "amount" | "percentage") => setFormData({...formData, discountType: value as "amount" | "percentage"})}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="amount">{t('quotations.create.settingsSection.amount')}</SelectItem>
                      <SelectItem value="percentage">{t('quotations.create.settingsSection.percentage')}</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    id="discount"
                    type="number"
                    min="0"
                    max={formData.discountType === "percentage" ? "100" : undefined}
                    step="0.01"
                    value={formData.discount}
                    onChange={(e) => setFormData({...formData, discount: parseFloat(e.target.value) || 0})}
                    placeholder={formData.discountType === "percentage" ? "0.00%" : "0.00 OMR"}
                    className="flex-1"
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  {formData.discountType === "percentage"
                    ? t('quotations.create.settingsSection.percentageHint')
                    : t('quotations.create.settingsSection.amountHint')
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="mr-2 h-5 w-5" />
              {t('quotations.create.summarySection.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between text-lg">
                    <span>{t('quotations.create.summarySection.subtotal')}</span>
                    <span className="font-medium">{formatCurrency(Number(totals.subtotal))}</span>
                  </div>

                  {formData.discount > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>
                        {t('quotations.create.summarySection.discount').replace('{details}', formData.discountType === "percentage"
                          ? `${formData.discount}%`
                          : formatCurrency(Number(formData.discount)))}
                      </span>
                      <span className="font-medium">-{formatCurrency(Number(totals.discountAmount))}</span>
                    </div>
                  )}

                  {vatSettings.enabled && (
                    <div className="flex justify-between text-lg">
                      <span>{t('quotations.create.summarySection.tax').replace('{label}', vatSettings.label).replace('{rate}', String(formData.taxRate))}</span>
                      <span className="font-medium">{formatCurrency(Number(totals.taxAmount))}</span>
                    </div>
                  )}

                  <Separator />

                  <div className="flex justify-between text-2xl font-bold text-primary">
                    <span>{t('quotations.create.summarySection.total')}</span>
                    <span>{formatCurrency(Number(totals.total))}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-muted/50 rounded-lg text-center">
                    <div className="text-2xl font-bold text-primary">{quotationItems.length}</div>
                    <div className="text-sm text-muted-foreground">{t('quotations.create.summarySection.items')}</div>
                  </div>
                  <div className="p-4 bg-muted/50 rounded-lg text-center">
                    <div className="text-2xl font-bold text-primary capitalize">{t(`quotations.create.infoSection.statusOptions.${formData.status}`)}</div>
                    <div className="text-sm text-muted-foreground">{t('quotations.create.summarySection.status')}</div>
                  </div>
                </div>

                <div className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => handleSave('draft')}
                    size="lg"
                  >
                    <Save className="mr-2 h-4 w-4" />
                    {t('quotations.create.saveDraft')}
                  </Button>
                  <Button
                    className="w-full"
                    onClick={() => handleSave('final')}
                    size="lg"
                  >
                    <Send className="mr-2 h-4 w-4" />
                    {t('quotations.create.createFinal')}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Dialog open={showNewCustomerDialog} onOpenChange={setShowNewCustomerDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t('quotations.create.newCustomerDialog.title')}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="customerName">{t('quotations.create.newCustomerDialog.nameLabel')}</Label>
              <Input
                id="customerName"
                placeholder={t('quotations.create.newCustomerDialog.namePlaceholder')}
                value={newCustomerData.name}
                onChange={(e) => setNewCustomerData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="customerMobile">{t('quotations.create.newCustomerDialog.mobileLabel')}</Label>
              <Input
                id="customerMobile"
                placeholder={t('quotations.create.newCustomerDialog.mobilePlaceholder')}
                value={newCustomerData.mobile}
                onChange={(e) => {
                  const mobile = e.target.value
                  setNewCustomerData(prev => ({ ...prev, mobile }))
                  if (mobile.length >= 8) {
                    checkMobileExists(mobile)
                  } else {
                    setMobileCheckResult(null)
                  }
                }}
              />
              {mobileCheckResult && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-sm text-yellow-800">
                    <strong>{t('quotations.create.newCustomerDialog.customerExists').replace('{name}', mobileCheckResult.name)}</strong>
                  </p>
                  <p className="text-xs text-yellow-600 mt-1">
                    {t('quotations.create.newCustomerDialog.customerExistsHint')}
                  </p>
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="customerEmail">{t('quotations.create.newCustomerDialog.emailLabel')}</Label>
              <Input
                id="customerEmail"
                type="email"
                placeholder={t('quotations.create.newCustomerDialog.emailPlaceholder')}
                value={newCustomerData.email}
                onChange={(e) => setNewCustomerData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                setShowNewCustomerDialog(false)
                setNewCustomerData({ name: "", mobile: "", email: "" })
                setMobileCheckResult(null)
              }}
            >
              {t('quotations.create.newCustomerDialog.cancel')}
            </Button>
            <Button
              onClick={handleNewCustomerSave}
              disabled={!newCustomerData.name || !newCustomerData.mobile || !!mobileCheckResult}
            >
              {t('quotations.create.newCustomerDialog.add')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
