"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Package,
  Search,
  Plus,
  Minus,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Archive,
  RefreshCw,
  FileText,
  Calendar,
  DollarSign,
  Target,
  Activity
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"

export default function StockManagementPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [stockData, setStockData] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [isAdjustmentDialogOpen, setIsAdjustmentDialogOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)
  const [adjustmentData, setAdjustmentData] = useState({
    type: "IN",
    quantity: 0,
    reason: "",
    notes: ""
  })

  useEffect(() => {
    loadStockData()
  }, [])

  const loadStockData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/products/stock')
      if (response.ok) {
        const data = await response.json()
        setStockData(data)
      }
    } catch (error) {
      console.error('Error loading stock data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">{t('stock.loadingStockData') || 'Loading stock data...'}</p>
        </div>
      </div>
    )
  }

  if (!stockData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-muted-foreground">{t('stock.noStockDataAvailable') || 'No stock data available'}</p>
        </div>
      </div>
    )
  }

  const { products, statistics, topSellingProducts, needRestockProducts } = stockData

  // Filter products based on search and status
  const filteredProducts = products.filter((product: any) => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = filterStatus === "all" ||
                         (filterStatus === "low" && product.stockStatus === "LOW_STOCK") ||
                         (filterStatus === "out" && product.stockStatus === "OUT_OF_STOCK") ||
                         (filterStatus === "in" && product.stockStatus === "IN_STOCK")

    return matchesSearch && matchesStatus
  })

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'OUT_OF_STOCK': return 'text-red-600 bg-red-50'
      case 'LOW_STOCK': return 'text-yellow-600 bg-yellow-50'
      case 'IN_STOCK': return 'text-green-600 bg-green-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getStockStatusText = (status: string) => {
    switch (status) {
      case 'OUT_OF_STOCK': return t('stock.outOfStock') || 'Out of Stock'
      case 'LOW_STOCK': return t('stock.lowStock') || 'Low Stock'
      case 'IN_STOCK': return t('stock.inStock') || 'In Stock'
      default: return 'Unknown'
    }
  }

// Mock data for demonstration (keeping for fallback)
const mockProducts = [
  {
    id: "1",
    name: "Wireless Headphones",
    nameAr: "سماعات لاسلكية",
    sku: "WH-001",
    category: { name: "Electronics" },
    currentStock: 15,
    minStock: 20,
    maxStock: 100,
    unit: "piece",
    price: 45.500,
    costPrice: 30.000,
    location: "A1-B2",
    lastRestocked: "2024-01-15",
    supplier: { name: "Tech Supplier Co." },
    movements: [
      { type: "IN", quantity: 50, date: "2024-01-15", reason: "Purchase Order #PO-001" },
      { type: "OUT", quantity: 35, date: "2024-01-20", reason: "Sales" },
    ]
  },
  {
    id: "2",
    name: "Office Chair",
    nameAr: "كرسي مكتب",
    sku: "OC-002",
    category: { name: "Furniture" },
    currentStock: 8,
    minStock: 10,
    maxStock: 50,
    unit: "piece",
    price: 125.000,
    costPrice: 85.000,
    location: "B2-C1",
    lastRestocked: "2024-01-10",
    supplier: { name: "Furniture Plus" },
    movements: [
      { type: "IN", quantity: 20, date: "2024-01-10", reason: "Purchase Order #PO-002" },
      { type: "OUT", quantity: 12, date: "2024-01-18", reason: "Sales" },
    ]
  },
  {
    id: "3",
    name: "Laptop Stand",
    nameAr: "حامل لابتوب",
    sku: "LS-003",
    category: { name: "Accessories" },
    currentStock: 25,
    minStock: 15,
    maxStock: 80,
    unit: "piece",
    price: 22.750,
    costPrice: 15.000,
    location: "A3-B1",
    lastRestocked: "2024-01-12",
    supplier: { name: "Office Supplies Ltd" },
    movements: [
      { type: "IN", quantity: 40, date: "2024-01-12", reason: "Purchase Order #PO-003" },
      { type: "OUT", quantity: 15, date: "2024-01-22", reason: "Sales" },
    ]
  }
]

  const handleStockAdjustment = (product: any) => {
    setSelectedProduct(product)
    setAdjustmentData({
      type: "IN",
      quantity: 0,
      reason: "",
      notes: ""
    })
    setIsAdjustmentDialogOpen(true)
  }

  const handleSaveAdjustment = async () => {
    if (!selectedProduct || !adjustmentData.quantity || !adjustmentData.reason) {
      alert(t('stock.fillAllRequiredFields') || 'Please fill in all required fields')
      return
    }

    try {
      // This would integrate with the API to update stock
      console.log('Saving stock adjustment:', {
        productId: selectedProduct.id,
        ...adjustmentData
      })

      // For now, just close the dialog and reload data
      setIsAdjustmentDialogOpen(false)
      await loadStockData()
      const actionText = adjustmentData.type === "IN" ? (t('stock.stockIncreased') || "increased") : (t('stock.stockDecreased') || "decreased")
      alert(`Stock ${actionText} for ${selectedProduct.name}`)
    } catch (error) {
      console.error('Error saving stock adjustment:', error)
      alert(t('stock.failedToSaveStockAdjustment') || 'Failed to save stock adjustment')
    }
  }

  const getStockStatus = (product: any) => {
    if (product.currentStock === 0) return { status: t('stock.outOfStock') || "Out of Stock", color: "bg-red-100 text-red-800" }
    if (product.currentStock <= product.minStock) return { status: t('stock.lowStock') || "Low Stock", color: "bg-yellow-100 text-yellow-800" }
    return { status: t('stock.inStock') || "In Stock", color: "bg-green-100 text-green-800" }
  }

  const getStockPercentage = (product: any) => {
    return Math.min((product.currentStock / product.maxStock) * 100, 100)
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('stock.title') || 'Stock Management'}</h2>
          <p className="text-muted-foreground">
            {t('stock.description') || 'Monitor and manage your inventory levels'}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/products')}>
            <Package className="mr-2 h-4 w-4" />
            {t('stock.products') || 'Products'}
          </Button>
          <Button variant="outline">
            <FileText className="mr-2 h-4 w-4" />
            {t('stock.stockReport') || 'Stock Report'}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">{t('stock.totalProducts') || 'Total Products'}</p>
                <p className="text-3xl font-bold text-blue-900">{statistics.totalProducts}</p>
              </div>
              <div className="rounded-full bg-blue-100 p-3">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <p className="text-xs text-blue-600 mt-2">{t('stock.activeInventoryItems') || 'Active inventory items'}</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-orange-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Low Stock Alert</p>
                <p className="text-3xl font-bold text-yellow-900">{statistics.lowStockProducts}</p>
              </div>
              <div className="rounded-full bg-yellow-100 p-3">
                <AlertTriangle className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
            <p className="text-xs text-yellow-600 mt-2">Items need restocking</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Stock Value</p>
                <p className="text-2xl font-bold text-green-900">{formatCurrency(statistics.totalStockValue)}</p>
              </div>
              <div className="rounded-full bg-green-100 p-3">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <p className="text-xs text-green-600 mt-2">Total inventory cost</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-pink-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">Potential Profit</p>
                <p className="text-2xl font-bold text-purple-900">{formatCurrency(statistics.totalStockValue - statistics.totalCostValue)}</p>
              </div>
              <div className="rounded-full bg-purple-100 p-3">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <p className="text-xs text-purple-600 mt-2">If all stock sold</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products, SKU, or category..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Products</SelectItem>
            <SelectItem value="in_stock">In Stock</SelectItem>
            <SelectItem value="low_stock">Low Stock</SelectItem>
            <SelectItem value="out_of_stock">Out of Stock</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Stock Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead>SKU</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Current Stock</TableHead>
              <TableHead>Min/Max Stock</TableHead>
              <TableHead>Stock Level</TableHead>
              <TableHead>Value</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-center">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProducts.map((product) => {
              const stockStatus = getStockStatus(product)
              const stockPercentage = getStockPercentage(product)

              return (
                <TableRow key={product.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{product.name}</div>
                      <div className="text-sm text-muted-foreground" dir="rtl">{product.nameAr}</div>
                    </div>
                  </TableCell>
                  <TableCell className="font-mono text-sm">{product.sku}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{product.category.name}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {product.currentStock <= product.minStock && (
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                      )}
                      <span className={`font-medium ${product.currentStock <= product.minStock ? 'text-orange-600' : ''}`}>
                        {product.currentStock} {product.unit}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>Min: {product.minStock}</div>
                      <div>Max: {product.maxStock}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            stockPercentage <= 20 ? 'bg-red-500' :
                            stockPercentage <= 40 ? 'bg-yellow-500' :
                            'bg-green-500'
                          }`}
                          style={{ width: `${stockPercentage}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-center">{stockPercentage.toFixed(0)}%</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div className="font-medium">{formatCurrency(product.currentStock * product.price)}</div>
                      <div className="text-muted-foreground">Cost: {formatCurrency(product.currentStock * product.costPrice)}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-sm">{product.location}</TableCell>
                  <TableCell>
                    <Badge className={stockStatus.color}>
                      {stockStatus.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStockAdjustment(product)}
                    >
                      <RefreshCw className="h-4 w-4 mr-1" />
                      Adjust
                    </Button>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>

      {/* Stock Adjustment Dialog */}
      <Dialog open={isAdjustmentDialogOpen} onOpenChange={setIsAdjustmentDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Stock Adjustment</DialogTitle>
            <DialogDescription>
              Adjust stock levels for {selectedProduct?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label>Current Stock</Label>
              <div className="text-lg font-medium">{selectedProduct?.currentStock} {selectedProduct?.unit}</div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">Adjustment Type</Label>
              <Select value={adjustmentData.type} onValueChange={(value) => setAdjustmentData({...adjustmentData, type: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="IN">Stock In (+)</SelectItem>
                  <SelectItem value="OUT">Stock Out (-)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity</Label>
              <Input
                id="quantity"
                type="number"
                min="1"
                value={adjustmentData.quantity}
                onChange={(e) => setAdjustmentData({...adjustmentData, quantity: parseInt(e.target.value) || 0})}
                placeholder="Enter quantity"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="reason">Reason *</Label>
              <Select value={adjustmentData.reason} onValueChange={(value) => setAdjustmentData({...adjustmentData, reason: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Purchase Order">Purchase Order</SelectItem>
                  <SelectItem value="Sales">Sales</SelectItem>
                  <SelectItem value="Damaged">Damaged</SelectItem>
                  <SelectItem value="Lost">Lost</SelectItem>
                  <SelectItem value="Returned">Returned</SelectItem>
                  <SelectItem value="Transfer">Transfer</SelectItem>
                  <SelectItem value="Adjustment">Manual Adjustment</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Input
                id="notes"
                value={adjustmentData.notes}
                onChange={(e) => setAdjustmentData({...adjustmentData, notes: e.target.value})}
                placeholder="Additional notes..."
              />
            </div>
            {adjustmentData.quantity > 0 && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="text-sm">
                  <strong>New Stock Level:</strong> {
                    adjustmentData.type === "IN"
                      ? (selectedProduct?.currentStock || 0) + adjustmentData.quantity
                      : Math.max(0, (selectedProduct?.currentStock || 0) - adjustmentData.quantity)
                  } {selectedProduct?.unit}
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAdjustmentDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveAdjustment}>
              Save Adjustment
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
