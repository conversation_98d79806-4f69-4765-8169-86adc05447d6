"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useI18n } from "@/lib/i18n"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import {
  DollarSign,
  Users,
  FileText,
  Package,
  TrendingUp,
  Clock,
  AlertTriangle,
  CheckCircle,
  Quote,
  Receipt,
  ShoppingCart,
  CreditCard,
  Calendar,
  Target,
  Truck,
  AlertOctagon,
  CalendarIcon,
  Filter
} from "lucide-react"
import { formatCurrency, formatDateTime, getCurrentMuscatTime, isBusinessHours } from "@/lib/localization"
import { PrayerTimesWidget } from "@/components/notifications/prayer-times-banner"
import { UpcomingRenewalsWidget } from "@/components/dashboard/upcoming-renewals-widget"
import { CalendarWidget } from "@/components/dashboard/calendar-widget"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

export default function Dashboard() {
  const router = useRouter()
  const { t, formatCurrency: formatCurrencyI18n, direction } = useI18n()
  const currentTime = getCurrentMuscatTime()
  const businessHours = isBusinessHours()

  // Date filtering state
  const [dateFilter, setDateFilter] = useState('thisMonth')
  const [customDateFrom, setCustomDateFrom] = useState<Date>()
  const [customDateTo, setCustomDateTo] = useState<Date>()
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  // Fetch dashboard data based on date filter
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true)
      try {
        let period = '30' // default to 30 days

        switch (dateFilter) {
          case 'today':
            period = '1'
            break
          case 'thisWeek':
            period = '7'
            break
          case 'thisMonth':
            period = '30'
            break
          case 'last6Months':
            period = '180'
            break
          case 'thisYear':
            period = '365'
            break
          case 'custom':
            // Handle custom date range
            if (customDateFrom && customDateTo) {
              const diffTime = Math.abs(customDateTo.getTime() - customDateFrom.getTime())
              period = Math.ceil(diffTime / (1000 * 60 * 60 * 24)).toString()
            }
            break
        }

        const response = await fetch(`/api/dashboard/stats?period=${period}`)
        if (response.ok) {
          const data = await response.json()
          setDashboardData(data)
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchDashboardData()
  }, [dateFilter, customDateFrom, customDateTo])

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('dashboard.title')}</h2>
          <p className="text-sm text-muted-foreground">
            {formatDateTime(currentTime)} • Muscat Time
            {!businessHours && (
              <Badge variant="secondary" className={direction === 'rtl' ? 'mr-2' : 'ml-2'}>
                {t('dashboard.outsideBusinessHours')}
              </Badge>
            )}
          </p>
        </div>

        {/* Date Filter */}
        <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger className="w-[180px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder={t('dashboard.selectPeriod')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">{t('dashboard.today')}</SelectItem>
              <SelectItem value="thisWeek">{t('dashboard.thisWeek')}</SelectItem>
              <SelectItem value="thisMonth">{t('dashboard.thisMonth')}</SelectItem>
              <SelectItem value="last6Months">{t('dashboard.last6Months')}</SelectItem>
              <SelectItem value="thisYear">{t('dashboard.thisYear')}</SelectItem>
              <SelectItem value="custom">{t('dashboard.customDate')}</SelectItem>
            </SelectContent>
          </Select>

          {dateFilter === 'custom' && (
            <>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[140px] justify-start text-left font-normal",
                      !customDateFrom && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {customDateFrom ? format(customDateFrom, "PPP") : t('dashboard.fromDate')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={customDateFrom}
                    onSelect={setCustomDateFrom}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-[140px] justify-start text-left font-normal",
                      !customDateTo && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {customDateTo ? format(customDateTo, "PPP") : t('dashboard.toDate')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={customDateTo}
                    onSelect={setCustomDateTo}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('dashboard.totalRevenue')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {loading ? '...' : formatCurrencyI18n(dashboardData?.summary?.totalRevenue || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.selectedPeriod')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('dashboard.pendingTasks')}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {loading ? '...' : dashboardData?.summary?.pendingTasks || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.activeTasks')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('dashboard.overdueInvoices')}</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {loading ? '...' : dashboardData?.summary?.overdueInvoices || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.needsAttention')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('dashboard.lowStockItems')}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {loading ? '...' : dashboardData?.summary?.lowStockItems || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.needsRestocking')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('dashboard.totalCustomers')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {loading ? '...' : dashboardData?.topCustomers?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.activeCustomers')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('dashboard.totalProducts')}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              {loading ? '...' : dashboardData?.topProducts?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('dashboard.activeProducts')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Business Overview Grid */}
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-3">
        {/* Tasks & Deadlines */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">{t('dashboard.tasksAndDeadlines')}</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dashboard/tasks')}
              >
                {t('dashboard.viewAll')}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span>Print Business Cards</span>
              </div>
              <Badge className="bg-red-100 text-red-800 text-xs">{t('dashboard.dueToday')}</Badge>
            </div>
            <div className="flex items-center justify-between text-sm">
              <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                <span>Design Brochure</span>
              </div>
              <Badge className="bg-yellow-100 text-yellow-800 text-xs">{t('dashboard.tomorrow')}</Badge>
            </div>
            <div className="flex items-center justify-between text-sm">
              <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Copy Documents</span>
              </div>
              <Badge className="bg-green-100 text-green-800 text-xs">{t('dashboard.completed')}</Badge>
            </div>
            <div className="pt-2 border-t">
              <div className="text-xs text-muted-foreground">
                5 {t('dashboard.tasksDueThisWeek')}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Expenses Overview */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">{t('dashboard.recentExpenses')}</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/dashboard/expenses')}
              >
                {t('dashboard.viewAll')}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <div>
                <p className="font-medium">{t('dashboard.officeSupplies')}</p>
                <p className="text-xs text-muted-foreground">EXP-001</p>
              </div>
              <div className={direction === 'rtl' ? 'text-left' : 'text-right'}>
                <p className="font-medium">{formatCurrencyI18n(125.50)}</p>
                <Badge className="bg-green-100 text-green-800 text-xs">{t('dashboard.approved')}</Badge>
              </div>
            </div>
            <div className="flex items-center justify-between text-sm">
              <div>
                <p className="font-medium">{t('dashboard.fuel')}</p>
                <p className="text-xs text-muted-foreground">EXP-002</p>
              </div>
              <div className={direction === 'rtl' ? 'text-left' : 'text-right'}>
                <p className="font-medium">{formatCurrencyI18n(85.00)}</p>
                <Badge className="bg-yellow-100 text-yellow-800 text-xs">{t('dashboard.pending')}</Badge>
              </div>
            </div>
            <div className="flex items-center justify-between text-sm">
              <div>
                <p className="font-medium">{t('dashboard.internetBill')}</p>
                <p className="text-xs text-muted-foreground">EXP-003</p>
              </div>
              <div className={direction === 'rtl' ? 'text-left' : 'text-right'}>
                <p className="font-medium">{formatCurrencyI18n(45.00)}</p>
                <Badge className="bg-blue-100 text-blue-800 text-xs">{t('dashboard.paid')}</Badge>
              </div>
            </div>
            <div className="pt-2 border-t">
              <div className="text-xs text-muted-foreground">
                {formatCurrencyI18n(1285.50)} {t('dashboard.thisMonth')}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Renewals */}
        <UpcomingRenewalsWidget />

        {/* Calendar Widget */}
        <CalendarWidget />

      </div>

      {/* Recent Activities */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>{t('dashboard.recentActivities')}</CardTitle>
          </CardHeader>
          <CardContent className={direction === 'rtl' ? 'pr-2' : 'pl-2'}>
            <div className="space-y-4">
              <div className="flex items-center">
                <CheckCircle className={`h-4 w-4 text-green-500 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
                <div className={`${direction === 'rtl' ? 'mr-4' : 'ml-4'} space-y-1`}>
                  <p className="text-sm font-medium leading-none">
                    {t('dashboard.taskCompleted').replace('{task}', 'Print 100 Brochures')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {t('dashboard.assignedTo').replace('{name}', 'John Doe').replace('{time}', '2 hours')}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <Quote className={`h-4 w-4 text-green-500 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
                <div className={`${direction === 'rtl' ? 'mr-4' : 'ml-4'} space-y-1`}>
                  <p className="text-sm font-medium leading-none">
                    {t('dashboard.quotationApproved').replace('{number}', 'QUO-003')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    ABC Corporation - {formatCurrencyI18n(2450.00)} - {t('dashboard.readyToConvert')}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <ShoppingCart className={`h-4 w-4 text-blue-500 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
                <div className={`${direction === 'rtl' ? 'mr-4' : 'ml-4'} space-y-1`}>
                  <p className="text-sm font-medium leading-none">
                    {t('dashboard.purchaseOrderReceived').replace('{number}', 'PO-004')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Paper Supply Co. - {formatCurrencyI18n(525.00)} - {t('dashboard.stockUpdated')}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <CreditCard className={`h-4 w-4 text-purple-500 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
                <div className={`${direction === 'rtl' ? 'mr-4' : 'ml-4'} space-y-1`}>
                  <p className="text-sm font-medium leading-none">
                    {t('dashboard.expenseApproved').replace('{number}', 'EXP-005')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {t('dashboard.equipmentMaintenance')} - {formatCurrencyI18n(180.00)}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <Target className={`h-4 w-4 text-green-500 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
                <div className={`${direction === 'rtl' ? 'mr-4' : 'ml-4'} space-y-1`}>
                  <p className="text-sm font-medium leading-none">
                    {t('dashboard.taskCompleted').replace('{task}', 'Print 100 Brochures')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {t('dashboard.assignedTo').replace('{name}', 'John Doe').replace('{time}', '2 hours')}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <AlertOctagon className={`h-4 w-4 text-red-500 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
                <div className={`${direction === 'rtl' ? 'mr-4' : 'ml-4'} space-y-1`}>
                  <p className="text-sm font-medium leading-none">
                    {t('dashboard.criticalStockAlert').replace('{item}', t('dashboard.a4Paper'))}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    50 {t('dashboard.sheetsLeft')} - {t('dashboard.reorderImmediately')}
                  </p>
                </div>
              </div>

              <div className="flex items-center">
                <Receipt className={`h-4 w-4 text-blue-500 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
                <div className={`${direction === 'rtl' ? 'mr-4' : 'ml-4'} space-y-1`}>
                  <p className="text-sm font-medium leading-none">
                    {t('dashboard.invoicePaymentReceived').replace('{number}', 'INV-001')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    ABC Company - {formatCurrencyI18n(1250.00)} - {t('dashboard.paidInFull')}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="col-span-3 space-y-4">
          <PrayerTimesWidget />
        </div>
      </div>
    </div>
  )
}
