/**
 * Robust storage utility that persists data across port changes
 * Uses multiple storage strategies to ensure data persistence
 */

// Storage keys
const STORAGE_KEYS = {
  COMPANY_LOGO: 'companyLogo',
  COMPANY_NAME: 'companyName',
  COMPANY_DATA: 'companyData',
} as const

// Storage strategies
class StorageManager {
  private static instance: StorageManager
  private storageKey = 'office_manager_data'

  static getInstance(): StorageManager {
    if (!StorageManager.instance) {
      StorageManager.instance = new StorageManager()
    }
    return StorageManager.instance
  }

  /**
   * Save data using multiple storage strategies
   */
  setItem(key: string, value: string): void {
    try {
      // Strategy 1: Standard localStorage
      localStorage.setItem(key, value)
      
      // Strategy 2: Store in a consolidated object with domain-agnostic key
      this.saveToConsolidatedStorage(key, value)
      
      // Strategy 3: Store with hostname-based key (ignores port)
      const hostnameKey = `${this.getHostnameKey()}_${key}`
      localStorage.setItem(hostnameKey, value)
      
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  }

  /**
   * Get data using multiple storage strategies with fallback
   */
  getItem(key: string): string | null {
    try {
      // Strategy 1: Try standard localStorage first
      let value = localStorage.getItem(key)
      if (value) return value

      // Strategy 2: Try consolidated storage
      value = this.getFromConsolidatedStorage(key)
      if (value) {
        // Restore to standard localStorage for future access
        localStorage.setItem(key, value)
        return value
      }

      // Strategy 3: Try hostname-based key
      const hostnameKey = `${this.getHostnameKey()}_${key}`
      value = localStorage.getItem(hostnameKey)
      if (value) {
        // Restore to standard localStorage for future access
        localStorage.setItem(key, value)
        return value
      }

      // Strategy 4: Try to find any key containing our target key (port-agnostic search)
      value = this.searchForKey(key)
      if (value) {
        // Restore to standard localStorage for future access
        localStorage.setItem(key, value)
        return value
      }

      return null
    } catch (error) {
      console.warn('Failed to read from localStorage:', error)
      return null
    }
  }

  /**
   * Remove data from all storage strategies
   */
  removeItem(key: string): void {
    try {
      // Remove from standard localStorage
      localStorage.removeItem(key)
      
      // Remove from consolidated storage
      this.removeFromConsolidatedStorage(key)
      
      // Remove from hostname-based storage
      const hostnameKey = `${this.getHostnameKey()}_${key}`
      localStorage.removeItem(hostnameKey)
      
    } catch (error) {
      console.warn('Failed to remove from localStorage:', error)
    }
  }

  /**
   * Get hostname without port for consistent storage across port changes
   */
  private getHostnameKey(): string {
    if (typeof window === 'undefined') return 'server'
    
    // Use hostname without port
    const hostname = window.location.hostname || 'localhost'
    return `office_manager_${hostname}`
  }

  /**
   * Save to consolidated storage object
   */
  private saveToConsolidatedStorage(key: string, value: string): void {
    try {
      const consolidatedKey = this.storageKey
      const existing = localStorage.getItem(consolidatedKey)
      const data = existing ? JSON.parse(existing) : {}
      
      data[key] = value
      data.lastUpdated = new Date().toISOString()
      
      localStorage.setItem(consolidatedKey, JSON.stringify(data))
    } catch (error) {
      console.warn('Failed to save to consolidated storage:', error)
    }
  }

  /**
   * Get from consolidated storage object
   */
  private getFromConsolidatedStorage(key: string): string | null {
    try {
      const consolidatedKey = this.storageKey
      const existing = localStorage.getItem(consolidatedKey)
      if (!existing) return null
      
      const data = JSON.parse(existing)
      return data[key] || null
    } catch (error) {
      console.warn('Failed to read from consolidated storage:', error)
      return null
    }
  }

  /**
   * Remove from consolidated storage object
   */
  private removeFromConsolidatedStorage(key: string): void {
    try {
      const consolidatedKey = this.storageKey
      const existing = localStorage.getItem(consolidatedKey)
      if (!existing) return
      
      const data = JSON.parse(existing)
      delete data[key]
      data.lastUpdated = new Date().toISOString()
      
      localStorage.setItem(consolidatedKey, JSON.stringify(data))
    } catch (error) {
      console.warn('Failed to remove from consolidated storage:', error)
    }
  }

  /**
   * Search for key across all localStorage items (port-agnostic)
   */
  private searchForKey(targetKey: string): string | null {
    try {
      // Look for keys that contain our target key with different port numbers
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (!key) continue
        
        // Check if this key matches our pattern with different port
        if (key.includes(targetKey) && key.includes('localhost')) {
          const value = localStorage.getItem(key)
          if (value) return value
        }
        
        // Check consolidated storage from different ports
        if (key.startsWith('office_manager_') && key.includes('localhost')) {
          try {
            const data = JSON.parse(localStorage.getItem(key) || '{}')
            if (data[targetKey]) return data[targetKey]
          } catch (e) {
            // Ignore invalid JSON
          }
        }
      }
      
      return null
    } catch (error) {
      console.warn('Failed to search for key:', error)
      return null
    }
  }

  /**
   * Migrate data from old storage to new storage format
   */
  migrateOldData(): void {
    try {
      // List of keys to migrate
      const keysToMigrate = [
        STORAGE_KEYS.COMPANY_LOGO,
        STORAGE_KEYS.COMPANY_NAME,
        STORAGE_KEYS.COMPANY_DATA,
      ]

      keysToMigrate.forEach(key => {
        const value = this.searchForKey(key)
        if (value && !localStorage.getItem(key)) {
          // Migrate to current storage
          this.setItem(key, value)
        }
      })
    } catch (error) {
      console.warn('Failed to migrate old data:', error)
    }
  }
}

// Export singleton instance
export const storage = StorageManager.getInstance()

// Convenience functions
export const setStorageItem = (key: string, value: string) => storage.setItem(key, value)
export const getStorageItem = (key: string) => storage.getItem(key)
export const removeStorageItem = (key: string) => storage.removeItem(key)
export const migrateStorageData = () => storage.migrateOldData()

// Export storage keys for consistency
export { STORAGE_KEYS }
