export const settings = {
  // Page titles and descriptions
  title: "Settings",
  saveSettings: "Save Settings",
  generalSettings: "General Settings",
  userSettings: "User Settings",
  languageSettings: "Language Settings",

  // Company settings
  companyLogo: "Company Logo",
  companyLogoDescription: "Company Logo - will appear in the sidebar navigation",
  companyInformation: "Company Information",
  companyInformationDescription: "Update company details and contact information",
  companyName: "Company Name",
  companyNameAr: "Company Name (Arabic)",
  addressEnglish: "Address (English)",
  addressArabic: "Address (Arabic)",
  phone: "Phone",
  email: "Email",
  vatRate: "VAT Rate (%)",
  currency: "Currency",
  taxRegistrationNumber: "Tax Registration Number",
  commercialRegistration: "Commercial Registration",
  establishmentCard: "Establishment Card",

  // Logo upload
  changeLogo: "Change Logo",
  uploadLogo: "Upload Logo",
  logoRecommendation: "Recommended: PNG or SVG format, max 2MB. Logo will be displayed without background.",

  // Invoice settings
  invoiceSettings: "Invoice Settings",
  invoiceSettingsDescription: "Configure terms & conditions, signature, and stamp for invoices.",
  termsConditions: "Terms & Conditions",
  termsConditionsEnglish: "Terms & Conditions (English)",
  termsConditionsArabic: "Terms & Conditions (Arabic)",
  termsConditionsEnglishPlaceholder: "Enter terms and conditions in English...",
  termsConditionsArabicPlaceholder: "Enter terms and conditions in Arabic...",
  authorizedSignature: "Authorized Signature",
  signature: "Signature",
  changeSignature: "Change Signature",
  uploadSignature: "Upload Signature",
  signatureRecommendation: "PNG or JPG format, max 2MB. Authorized signature for invoices.",
  companyStamp: "Company Stamp",
  stamp: "Stamp",
  changeStamp: "Change Stamp",
  uploadStamp: "Upload Stamp",
  stampRecommendation: "PNG or JPG format, max 2MB. Official company stamp for invoices.",

  // WhatsApp settings
  whatsappIntegration: "WhatsApp Integration",
  whatsappIntegrationDescription: "Configure WhatsApp API settings for automated notifications.",
  enableWhatsappNotifications: "Enable WhatsApp Notifications",
  apiUrl: "API URL",
  accountKey: "Account Key",
  secretKey: "Secret Key",
  accountKeyPlaceholder: "Your Textcloud account key",
  secretKeyPlaceholder: "Your Textcloud secret key",
  testConnection: "Test Connection",
  notificationSettings: "Notification Settings",
  taskAssignmentNotifications: "Task Assignment Notifications",
  taskStartedNotifications: "Task Started Notifications",
  taskCompletionNotifications: "Task Completion Notifications",
  invoiceNotifications: "Invoice Notifications",
  quotationNotifications: "Quotation Notifications",

  // User management
  userManagement: "User Management",
  userManagementDescription: "Manage user accounts and permissions.",
  addNewUser: "Add New User",
  userManagementPlaceholder: "User management functionality will be implemented here. This includes creating, editing, and managing user roles and permissions.",

  // Localization settings
  localizationSettings: "Localization Settings",
  localizationSettingsDescription: "Configure language and regional settings.",
  defaultLanguage: "Default Language",
  english: "English",
  arabic: "العربية (Arabic)",
  dateFormat: "Date Format",
  timeFormat: "Time Format",
  hour12: "12 Hour (AM/PM)",
  hour24: "24 Hour",
  timezone: "Timezone",
  utc: "UTC",
  easternTime: "Eastern Time",
  pacificTime: "Pacific Time",
  london: "London",
  dubai: "Dubai",
  enableRtlSupport: "Enable RTL Support for Arabic",

  // Alert messages
  settingsSavedSuccessfully: "Settings saved successfully!",
  failedToSaveSettings: "Failed to save settings. Please try again."
};