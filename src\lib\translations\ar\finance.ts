export const finance = {
  // Page titles and descriptions
  title: "المالية",
  description: "إدارة وتقارير مالية شاملة",
  financialDashboard: "لوحة القيادة المالية",
  financialReports: "التقارير المالية",
  
  // Main sections
  accounting: "Accounting",
  budgeting: "Budgeting",
  cashFlow: "Cash Flow",
  profitLoss: "Profit & Loss",
  balanceSheet: "Balance Sheet",
  trialBalance: "Trial Balance",
  generalLedger: "General Ledger",
  journalEntries: "Journal Entries",
  
  // Financial metrics
  revenue: "Revenue",
  income: "Income",
  expenses: "Expenses",
  profit: "Profit",
  loss: "Loss",
  netIncome: "Net Income",
  grossProfit: "Gross Profit",
  grossLoss: "Gross Loss",
  operatingProfit: "Operating Profit",
  operatingLoss: "Operating Loss",
  
  // Assets and liabilities
  assets: "Assets",
  liabilities: "Liabilities",
  equity: "Equity",
  currentAssets: "Current Assets",
  fixedAssets: "Fixed Assets",
  currentLiabilities: "Current Liabilities",
  longTermLiabilities: "Long-term Liabilities",
  
  // Account types
  accountTypes: "Account Types",
  cashAccount: "Cash Account",
  bankAccount: "Bank Account",
  creditCardAccount: "Credit Card Account",
  receivablesAccount: "Accounts Receivable",
  payablesAccount: "Accounts Payable",
  inventoryAccount: "Inventory Account",
  
  // Transactions
  transaction: "Transaction",
  transactions: "Transactions",
  transactionDate: "Transaction Date",
  transactionType: "Transaction Type",
  transactionAmount: "Transaction Amount",
  transactionDescription: "Transaction Description",
  reference: "Reference",
  debit: "Debit",
  credit: "Credit",
  
  // Transaction types
  incomeTransaction: "Income",
  expenseTransaction: "Expense",
  transfer: "Transfer",
  adjustment: "Adjustment",
  deposit: "Deposit",
  withdrawal: "Withdrawal",
  payment: "Payment",
  receipt: "Receipt",
  
  // Budgeting
  budget: "Budget",
  budgets: "Budgets",
  budgetPeriod: "Budget Period",
  budgetAmount: "Budget Amount",
  actualAmount: "Actual Amount",
  variance: "Variance",
  budgetVsActual: "Budget vs Actual",
  
  // Cash flow
  cashInflow: "Cash Inflow",
  cashOutflow: "Cash Outflow",
  netCashFlow: "Net Cash Flow",
  cashPosition: "Cash Position",
  cashFlowForecast: "Cash Flow Forecast",
  operatingCashFlow: "Operating Cash Flow",
  investingCashFlow: "Investing Cash Flow",
  financingCashFlow: "Financing Cash Flow",
  
  // Financial ratios
  ratios: "Financial Ratios",
  profitMargin: "Profit Margin",
  currentRatio: "Current Ratio",
  quickRatio: "Quick Ratio",
  debtToEquity: "Debt to Equity",
  returnOnAssets: "Return on Assets",
  returnOnEquity: "Return on Equity",
  
  // Periods
  daily: "Daily",
  weekly: "Weekly",
  monthly: "Monthly",
  quarterly: "Quarterly",
  yearly: "Yearly",
  
  // Date ranges
  thisMonth: "This Month",
  lastMonth: "Last Month",
  thisQuarter: "This Quarter",
  lastQuarter: "Last Quarter",
  thisYear: "This Year",
  lastYear: "Last Year",
  custom: "Custom",
  
  // Actions
  addTransaction: "Add Transaction",
  editTransaction: "Edit Transaction",
  deleteTransaction: "Delete Transaction",
  reconcile: "Reconcile",
  generateReport: "Generate Report",
  exportData: "Export Data",
  importData: "Import Data",
  
  // Bank reconciliation
  bankReconciliation: "Bank Reconciliation",
  reconcileAccount: "Reconcile Account",
  reconciledTransactions: "Reconciled Transactions",
  unreconciledTransactions: "Unreconciled Transactions",
  reconciliationDate: "Reconciliation Date",
  bankStatement: "Bank Statement",
  
  // Financial reports
  incomeStatement: "Income Statement",
  cashFlowStatement: "Cash Flow Statement",
  financialSummary: "Financial Summary",
  expenseReport: "Expense Report",
  revenueReport: "Revenue Report",
  profitabilityReport: "Profitability Report",
  
  // Tax and compliance
  tax: "Tax",
  taxes: "Taxes",
  taxRate: "Tax Rate",
  taxAmount: "Tax Amount",
  taxReport: "Tax Report",
  vatReport: "VAT Report",
  taxPeriod: "Tax Period",
  
  // Currency and formatting
  currency: "Currency",
  amount: "Amount",
  percentage: "Percentage",
  total: "Total",
  subtotal: "Subtotal",
  grandTotal: "Grand Total",
  
  // Status
  active: "Active",
  inactive: "Inactive",
  pending: "Pending",
  completed: "Completed",
  approved: "Approved",
  rejected: "Rejected",
  
  // Search and filters
  searchTransactions: "Search transactions...",
  filterByAccount: "Filter by Account",
  filterByType: "Filter by Type",
  filterByPeriod: "Filter by Period",
  allAccounts: "All Accounts",
  allTypes: "All Types",
  
  // Messages
  transactionCreatedSuccessfully: "Transaction created successfully",
  transactionUpdatedSuccessfully: "Transaction updated successfully",
  transactionDeletedSuccessfully: "Transaction deleted successfully",
  reportGeneratedSuccessfully: "Report generated successfully",
  dataExportedSuccessfully: "Data exported successfully",
  reconciliationCompletedSuccessfully: "Reconciliation completed successfully",
  
  // Error messages
  failedToCreateTransaction: "Failed to create transaction",
  failedToUpdateTransaction: "Failed to update transaction",
  failedToDeleteTransaction: "Failed to delete transaction",
  failedToGenerateReport: "Failed to generate report",
  failedToExportData: "Failed to export data",
  
  // Validation
  accountRequired: "Account is required",
  amountRequired: "Amount is required",
  dateRequired: "Date is required",
  descriptionRequired: "Description is required",
  invalidAmount: "Amount must be a valid number",
  invalidDate: "Please enter a valid date",
  
  // Empty states
  noTransactionsFound: "No transactions found",
  noAccountsConfigured: "No accounts configured",
  noDataAvailable: "No data available for the selected period",
  
  // Loading states
  loadingTransactions: "Loading transactions...",
  loadingReports: "Loading reports...",
  generatingReport: "Generating report...",
  exportingData: "Exporting data...",
  
  // Charts and analytics
  charts: "Charts",
  analytics: "Analytics",
  trends: "Trends",
  comparison: "Comparison",
  breakdown: "Breakdown",
  summary: "Summary",
  
  // Account management
  accounts: "Accounts",
  accountName: "Account Name",
  accountNumber: "Account Number",
  accountBalance: "Account Balance",
  openingBalance: "Opening Balance",
  closingBalance: "Closing Balance",
  
  // Financial planning
  forecasting: "Forecasting",
  projections: "Projections",
  scenarios: "Scenarios",
  planning: "Financial Planning",
  
  // Notifications
  lowCashAlert: "Low Cash Alert",
  budgetExceeded: "Budget Exceeded",
  overduePayments: "Overdue Payments",
  reconciliationDue: "Reconciliation Due",

  // Financial Dashboard & Reports (nested structure)
  financial: {
    title: "لوحة المعلومات المالية",
    subtitle: "تقارير مالية شاملة وتحليلات الأداء",
    period: {
      lastMonth: "الشهر الماضي",
      last3Months: "آخر 3 أشهر",
      last6Months: "آخر 6 أشهر",
      lastYear: "السنة الماضية",
      ytd: "من بداية العام",
      current: "الفترة الحالية",
      previous: "الفترة السابقة",
      quarterly: "ربع سنوي",
      annual: "سنوي"
    },
    buttons: {
      reports: "التقارير",
      plStatement: "قائمة الأرباح والخسائر",
      back: "رجوع",
      generate: "إنشاء",
      view: "عرض",
      download: "تحميل",
      print: "طباعة"
    },
    cards: {
      profitMargin: "هامش الربح",
      netProfit: "صافي الربح",
      totalExpenses: "إجمالي المصروفات",
      totalRevenue: "إجمالي الإيرادات",
      healthyMargin: "هامش صحي",
      fromLastPeriod: "من الفترة السابقة",
      paid: "مدفوع",
      approved: "معتمد",
      pending: "قيد الانتظار",
      rejected: "مرفوض",
      revenueBreakdown: "تفصيل الإيرادات",
      expenseBreakdown: "تفصيل المصروفات",
      cashFlowAnalysis: "تحليل التدفق النقدي",
      cashInflow: "التدفق الداخل",
      cashOutflow: "التدفق الخارج",
      netCashFlow: "صافي التدفق النقدي",
      inflow: "التدفق الداخل",
      outflow: "التدفق الخارج",
      net: "صافي",
      keyMetrics: "المؤشرات الرئيسية",
      target: "الهدف"
    },
    charts: {
      profitMarginTrend: "اتجاه هامش الربح",
      revenueVsExpensesTrend: "اتجاه الإيرادات مقابل المصروفات",
      cashFlow: "التدفق النقدي",
      revenueBreakdown: "تفصيل الإيرادات",
      expenseBreakdown: "تفصيل المصروفات",
      revenue: "الإيرادات",
      expenses: "المصروفات",
      profit: "الربح",
      profitMargin: "هامش الربح"
    },
    kpis: {
      revenuePerCustomer: "الإيرادات لكل عميل",
      customerAcquisitionCost: "تكلفة اكتساب العملاء",
      averageOrderValue: "متوسط قيمة الطلب",
      monthlyRecurringRevenue: "الإيرادات الشهرية المتكررة"
    },
    loading: "جاري تحميل البيانات المالية...",
    noData: "لا توجد بيانات مالية متاحة",
    
    // Financial Reports Page
    reports: {
      title: "التقارير المالية",
      subtitle: "إنشاء والوصول إلى تقارير مالية شاملة وتحليلات",
      categories: {
        allReports: "جميع التقارير",
        financialStatements: "القوائم المالية",
        performanceReports: "تقارير الأداء",
        budgetReports: "تقارير الميزانية",
        analysisReports: "تقارير التحليل",
        complianceReports: "تقارير الامتثال"
      },
      stats: {
        availableReports: "التقارير المتاحة",
        monthlyReports: "التقارير الشهرية",
        weeklyReports: "التقارير الأسبوعية",
        quarterlyReports: "التقارير الربع سنوية"
      },
      table: {
        reportName: "اسم التقرير",
        category: "الفئة",
        generated: "تم إنشاؤه",
        frequency: "التكرار",
        status: "الحالة",
        actions: "الإجراءات",
        available: "متاح"
      },
      lastGenerated: "آخر إنشاء:",
      recentReports: "التقارير الحديثة",
      frequencies: {
        monthly: "شهري",
        weekly: "أسبوعي",
        quarterly: "ربع سنوي",
        annual: "سنوي"
      },
      reportTypes: {
        profitloss: {
          name: "قائمة الأرباح والخسائر",
          description: "بيان الدخل الشامل الذي يوضح الإيرادات والمصروفات وصافي الربح"
        },
        balancesheet: {
          name: "الميزانية العمومية",
          description: "بيان المركز المالي الذي يوضح الأصول والخصوم وحقوق الملكية"
        },
        cashflow: {
          name: "بيان التدفق النقدي",
          description: "تحليل التدفقات النقدية الداخلة والخارجة من العمليات والاستثمار والتمويل"
        },
        revenueanalysis: {
          name: "تحليل الإيرادات",
          description: "تفصيل مفصل للإيرادات حسب الخدمة والعميل والفترة الزمنية"
        },
        expenseanalysis: {
          name: "تحليل المصروفات",
          description: "تحليل شامل للمصروفات حسب الفئة والقسم"
        },
        customerprofitability: {
          name: "ربحية العملاء",
          description: "تحليل هوامش الربح ومساهمة الإيرادات حسب العميل"
        },
        budgetvariance: {
          name: "الميزانية مقابل الفعلي",
          description: "مقارنة الأداء الفعلي مع الأرقام المدرجة في الميزانية"
        },
        financialratios: {
          name: "النسب المالية",
          description: "تحليل النسب المالية الرئيسية ومؤشرات الأداء"
        },
        taxsummary: {
          name: "ملخص الضرائب",
          description: "ملخص الالتزامات الضريبية والمدفوعات لتقارير الامتثال"
        }
      }
    },
    profitLoss: {
      title: "قائمة الأرباح والخسائر",
      comprehensiveIncome: "بيان الدخل الشامل",
      netIncome: "صافي الدخل",
      operatingIncome: "الدخل التشغيلي",
      grossProfit: "إجمالي الربح",
      totalRevenue: "إجمالي الإيرادات",
      comparison: {
        vsLastPeriod: "مقارنة بالفترة السابقة"
      },
      revenue: {
        title: "الإيرادات",
        printingServices: "خدمات الطباعة",
        designServices: "خدمات التصميم",
        officeSupplies: "اللوازم المكتبية",
        consulting: "الاستشارات",
        total: "إجمالي الإيرادات"
      },
      costOfGoodsSold: {
        title: "تكلفة البضائع المباعة",
        materials: "المواد والخامات",
        directLabor: "العمالة المباشرة",
        manufacturingOverhead: "المصاريف الصناعية العامة",
        total: "إجمالي تكلفة البضائع المباعة"
      },
      operatingExpenses: {
        title: "المصروفات التشغيلية",
        salariesAndBenefits: "الرواتب والمزايا",
        rentAndUtilities: "الإيجار والمرافق",
        equipmentMaintenance: "صيانة المعدات",
        marketingAdvertising: "التسويق والإعلان",
        insurance: "التأمين",
        professionalServices: "الخدمات المهنية",
        officeExpenses: "مصاريف المكتب",
        depreciation: "الاستهلاك",
        total: "إجمالي المصروفات التشغيلية"
      },
      otherIncomeExpenses: {
        title: "الإيرادات والمصروفات الأخرى",
        interestIncome: "إيرادات الفوائد",
        interestExpense: "مصروفات الفوائد",
        otherIncome: "إيرادات أخرى",
        total: "صافي الإيرادات الأخرى"
      },
      netIncomeBeforeTax: "صافي الدخل قبل الضريبة",
      incomeTax: "ضريبة الدخل",
      margins: {
        title: "الهوامش",
        grossMargin: "هامش الربح الإجمالي",
        operatingMargin: "هامش الربح التشغيلي",
        netMargin: "هامش الربح الصافي"
      }
    }
  }
}; 