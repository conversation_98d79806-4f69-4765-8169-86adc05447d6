# Translation Implementation Roadmap

## Current Status
- ✅ **Modular System**: Created and implemented
- ✅ **Common Module**: 100+ translations completed
- ✅ **Customer Module**: 60+ translations completed
- ✅ **Navigation Module**: 20+ translations completed
- ✅ **Supplier Detail**: Fixed missing translations

## Remaining Tasks (773 → ~600 remaining)

### 🔥 IMMEDIATE PRIORITY (Complete Today - 2-3 hours)

#### Task 1: Dashboard Module (47 missing)
**File**: `src/lib/translations/ar/dashboard.ts`
**Time**: 1 hour

```typescript
export const dashboard = {
  // Time periods
  outsideBusinessHours: "خارج ساعات العمل",
  selectPeriod: "اختر الفترة",
  today: "اليوم",
  thisWeek: "هذا الأسبوع", 
  thisMonth: "هذا الشهر",
  last6Months: "آخر 6 أشهر",
  thisYear: "هذا العام",
  customDate: "تاريخ مخصص",
  fromDate: "من تاريخ",
  toDate: "إلى تاريخ",
  selectedPeriod: "الفترة المحددة",
  
  // Statistics
  totalRevenue: "إجمالي الإيرادات",
  activeTasks: "المهام النشطة",
  needsAttention: "تحتاج انتباه",
  lowStockItems: "العناصر منخفضة المخزون",
  needsRestocking: "تحتاج إعادة تخزين",
  totalCustomers: "إجمالي العملاء",
  activeCustomers: "العملاء النشطين",
  totalProducts: "إجمالي المنتجات",
  activeProducts: "المنتجات النشطة",
  
  // Tasks and deadlines
  tasksAndDeadlines: "المهام والمواعيد النهائية",
  viewAll: "عرض الكل",
  dueToday: "مستحق اليوم",
  tomorrow: "غداً",
  completed: "مكتمل",
  tasksDueThisWeek: "المهام المستحقة هذا الأسبوع",
  
  // Recent activities
  recentExpenses: "المصروفات الأخيرة",
  officeSupplies: "مستلزمات المكتب",
  approved: "موافق عليه",
  fuel: "وقود",
  pending: "معلق",
  internetBill: "فاتورة الإنترنت",
  paid: "مدفوع",
  
  // Activity feed
  taskCompleted: "تم إكمال المهمة",
  assignedTo: "مُكلف إلى",
  quotationApproved: "تم الموافقة على عرض السعر",
  readyToConvert: "جاهز للتحويل",
  purchaseOrderReceived: "تم استلام أمر الشراء",
  stockUpdated: "تم تحديث المخزون",
  expenseApproved: "تم الموافقة على المصروف",
  equipmentMaintenance: "صيانة المعدات",
  
  // Alerts
  criticalStockAlert: "تنبيه مخزون حرج",
  a4Paper: "ورق A4",
  sheetsLeft: "ورقة متبقية",
  reorderImmediately: "أعد الطلب فوراً",
  invoicePaymentReceived: "تم استلام دفعة الفاتورة",
  paidInFull: "مدفوع بالكامل",
};
```

#### Task 2: Forms Module (Missing forms.required)
**File**: `src/lib/translations/ar/forms.ts`
**Time**: 15 minutes

```typescript
export const forms = {
  required: "مطلوب",
  optional: "اختياري",
  // ... rest of form translations
};
```

#### Task 3: Categories & Units (32 missing)
**File**: `src/lib/translations/ar/categories.ts` & `src/lib/translations/ar/units.ts`
**Time**: 30 minutes

### 🟡 HIGH PRIORITY (Complete Tomorrow - 3-4 hours)

#### Task 4: Products Module (80 missing)
**File**: `src/lib/translations/ar/products.ts`
**Time**: 2 hours

#### Task 5: Invoices Module (23 missing)  
**File**: `src/lib/translations/ar/invoices.ts`
**Time**: 1 hour

#### Task 6: Employees Module (38 missing)
**File**: `src/lib/translations/ar/employees.ts`
**Time**: 1 hour

### 🟢 MEDIUM PRIORITY (Complete This Week)

#### Task 7: Calendar Module (40 missing)
#### Task 8: Quotations Module (18 missing)
#### Task 9: Expenses Module (34 missing)
#### Task 10: Settings Module (11 missing)

### 🔵 LOW PRIORITY (Complete Next Week)

#### Task 11: Error Messages & API Responses (258 missing)
#### Task 12: Leads, Projects, Reports modules

## Implementation Instructions

### Step 1: Create Module Files
```bash
# Create missing module files
touch src/lib/translations/ar/dashboard.ts
touch src/lib/translations/ar/forms.ts
touch src/lib/translations/ar/categories.ts
touch src/lib/translations/ar/units.ts
touch src/lib/translations/ar/products.ts
touch src/lib/translations/ar/invoices.ts
touch src/lib/translations/ar/employees.ts
```

### Step 2: Update Main Translation File
Add imports to `src/lib/translations/ar.ts`:
```typescript
import { dashboard } from './ar/dashboard';
import { forms } from './ar/forms';
import { categories } from './ar/categories';
// ... etc

export const ar = {
  common,
  customers,
  navigation,
  dashboard,
  forms,
  categories,
  // ... etc
};
```

### Step 3: Test Each Module
After completing each module:
1. Run `npm run dev`
2. Navigate to the relevant pages
3. Switch to Arabic language
4. Verify all text is translated
5. Check for any remaining English text

### Step 4: Update Components
Some components may need to be updated to use the new translation keys:
- Replace hardcoded English text with `t('module.key')`
- Use common translations where appropriate
- Ensure consistent terminology

## Success Metrics

### Daily Targets
- **Day 1**: Dashboard, Forms, Categories, Units (100+ translations)
- **Day 2**: Products, Invoices, Employees (140+ translations)  
- **Day 3**: Calendar, Quotations, Expenses (90+ translations)
- **Day 4**: Settings, Leads, Projects (50+ translations)
- **Day 5**: Error messages, API responses (200+ translations)

### Weekly Target
- **Week 1**: 100% translation coverage for all user-facing text
- **Zero English text** showing in Arabic mode
- **Consistent terminology** across all modules
- **Proper RTL layout** and formatting

## Quality Assurance

### Translation Standards
- Use formal Arabic (فصحى)
- Consistent terminology across modules
- Proper RTL text direction
- Cultural appropriateness for Oman/Gulf region
- Business terminology accuracy

### Testing Checklist
- [ ] All navigation items translated
- [ ] All form labels and buttons translated
- [ ] All table headers translated
- [ ] All status messages translated
- [ ] All error messages translated
- [ ] All confirmation dialogs translated
- [ ] All empty states translated
- [ ] All tooltips and help text translated

## Maintenance Plan

### Regular Tasks
- Weekly translation audits using the audit script
- New feature translation requirements
- User feedback incorporation
- Translation quality reviews

### Automation
- Run `node scripts/audit-translations.js` before each release
- Add translation coverage to CI/CD pipeline
- Create translation key extraction for new features

## Emergency Fixes

If you need to quickly fix specific missing translations:

1. **Find the component**: Use the audit script output to locate the file
2. **Identify the translation key**: Look for `t('...')` calls
3. **Add to appropriate module**: Add the translation to the correct module file
4. **Test immediately**: Refresh the page to see the change

## Contact & Support

For questions about:
- **Arabic translations**: Verify with native Arabic speakers
- **Technical implementation**: Check the modular translation system
- **Business terminology**: Confirm with domain experts
- **RTL layout issues**: Test with Arabic content

---

**Remember**: The goal is 100% Arabic translation coverage with zero English text showing in Arabic mode. Focus on user-facing text first, then error messages and API responses. 