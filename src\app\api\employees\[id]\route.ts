import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import bcrypt from 'bcryptjs'

// GET /api/employees/[id] - Get employee by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const employee = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        isActive: true,
        createdAt: true,
        idCardNumber: true,
        idCardExpiry: true,
        passportNumber: true,
        passportExpiry: true,
        visaNumber: true,
        visaExpiry: true,
        licenseNumber: true,
        licenseExpiry: true,
        contractExpiry: true,
        salesData: true,
      },
    })

    if (!employee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 })
    }

    return NextResponse.json(employee)
  } catch (error) {
    console.error('Error fetching employee:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/employees/[id] - Update employee
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const {
      name,
      email,
      phone,
      role,
      password,
      isActive,
      idCardNumber,
      idCardExpiry,
      passportNumber,
      passportExpiry,
      visaNumber,
      visaExpiry,
      licenseNumber,
      licenseExpiry,
      contractExpiry,
    } = body

    // Check if employee exists
    const existingEmployee = await prisma.user.findUnique({
      where: { id },
    })

    if (!existingEmployee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 })
    }

    // Check if email is being changed and if it already exists
    if (email && email !== existingEmployee.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email },
      })

      if (emailExists) {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 400 }
        )
      }
    }

    // Prepare update data
    const updateData: any = {}

    if (name !== undefined) updateData.name = name
    if (email !== undefined) updateData.email = email
    if (phone !== undefined) updateData.phone = phone
    if (role !== undefined) updateData.role = role
    if (isActive !== undefined) updateData.isActive = isActive

    // Document fields
    if (idCardNumber !== undefined) updateData.idCardNumber = idCardNumber || null
    if (idCardExpiry !== undefined) updateData.idCardExpiry = idCardExpiry ? new Date(idCardExpiry) : null
    if (passportNumber !== undefined) updateData.passportNumber = passportNumber || null
    if (passportExpiry !== undefined) updateData.passportExpiry = passportExpiry ? new Date(passportExpiry) : null
    if (visaNumber !== undefined) updateData.visaNumber = visaNumber || null
    if (visaExpiry !== undefined) updateData.visaExpiry = visaExpiry ? new Date(visaExpiry) : null
    if (licenseNumber !== undefined) updateData.licenseNumber = licenseNumber || null
    if (licenseExpiry !== undefined) updateData.licenseExpiry = licenseExpiry ? new Date(licenseExpiry) : null
    if (contractExpiry !== undefined) updateData.contractExpiry = contractExpiry ? new Date(contractExpiry) : null

    // Hash password if provided
    if (password) {
      updateData.password = await bcrypt.hash(password, 12)
    }

    const employee = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        isActive: true,
        createdAt: true,
        idCardNumber: true,
        idCardExpiry: true,
        passportNumber: true,
        passportExpiry: true,
        visaNumber: true,
        visaExpiry: true,
        licenseNumber: true,
        licenseExpiry: true,
        contractExpiry: true,
        salesData: true,
      },
    })

    return NextResponse.json(employee)
  } catch (error) {
    console.error('Error updating employee:', error)
    return NextResponse.json(
      { error: 'Failed to update employee' },
      { status: 500 }
    )
  }
}

// DELETE /api/employees/[id] - Delete employee
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    // Check if employee exists
    const existingEmployee = await prisma.user.findUnique({
      where: { id },
    })

    if (!existingEmployee) {
      return NextResponse.json({ error: 'Employee not found' }, { status: 404 })
    }

    // Check for related records that would prevent deletion
    const relatedRecords = await prisma.$transaction(async (tx) => {
      const [tasks, invoices, quotations, purchases, expenses] = await Promise.all([
        tx.task.count({ where: { OR: [{ assignedToId: id }, { createdById: id }] } }),
        tx.invoice.count({ where: { userId: id } }),
        tx.quotation.count({ where: { userId: id } }),
        tx.purchase.count({ where: { userId: id } }),
        tx.expense.count({ where: { createdById: id } })
      ])

      return { tasks, invoices, quotations, purchases, expenses }
    })

    // If there are related records, prevent deletion and inform user
    const totalRelated = Object.values(relatedRecords).reduce((sum, count) => sum + count, 0)
    if (totalRelated > 0) {
      const relatedTypes = []
      if (relatedRecords.tasks > 0) relatedTypes.push(`${relatedRecords.tasks} task(s)`)
      if (relatedRecords.invoices > 0) relatedTypes.push(`${relatedRecords.invoices} invoice(s)`)
      if (relatedRecords.quotations > 0) relatedTypes.push(`${relatedRecords.quotations} quotation(s)`)
      if (relatedRecords.purchases > 0) relatedTypes.push(`${relatedRecords.purchases} purchase(s)`)
      if (relatedRecords.expenses > 0) relatedTypes.push(`${relatedRecords.expenses} expense(s)`)

      return NextResponse.json(
        {
          error: `Cannot delete employee. They have related records: ${relatedTypes.join(', ')}. Please reassign or delete these records first.`
        },
        { status: 400 }
      )
    }

    // Delete employee (this will cascade delete sales data)
    await prisma.user.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Employee deleted successfully' })
  } catch (error) {
    console.error('Error deleting employee:', error)
    return NextResponse.json(
      { error: 'Failed to delete employee' },
      { status: 500 }
    )
  }
}
