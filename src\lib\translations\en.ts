// Import modular translations
import { common } from './en/common';
import { customers } from './en/customers';
import { navigation } from './en/navigation';
import { dashboard } from './en/dashboard';
import { forms } from './en/forms';
import { categories } from './en/categories';
import { units } from './en/units';
import { products } from './en/products';
import { stock } from './en/stock';
import { tasks } from './en/tasks';
import { invoices } from './en/invoices';
import { auth } from './en/auth';
import { system } from './en/system';
import { expenses } from './en/expenses';
import { employees } from './en/employees';
import { quotations } from './en/quotations';
import { calendar } from './en/calendar';
import { settings } from './en/settings';
import { projects } from './en/projects';
import { leads } from './en/leads';
import { suppliers } from './en/suppliers';
import { maintenance } from './en/maintenance';
import { purchases } from './en/purchases';
import { finance } from './en/finance';
import { pos } from './en/pos';
import { reports } from './en/reports';

// Export combined translations
export const en = {
  common,
  customers,
  navigation,
  dashboard,
  forms,
  categories,
  units,
  products,
  stock,
  tasks,
  invoices,
  auth,
  system,
  expenses,
  employees,
  quotations,
  calendar,
  settings,
  projects,
  leads,
  suppliers,
  maintenance,
  purchases,
  finance,
  pos,
  reports
};

export type TranslationKeys = typeof en
