"use client"

import { useState, useEffect } from "react"
import { useRouter, useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ArrowLeft, Loader2, Plus, Trash2 } from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

interface PurchaseItem {
  id?: string
  description: string
  quantity: number
  unitPrice: number
  total: number
  productId?: string
}

interface Purchase {
  id: string
  number: string
  date: string
  expectedDate?: string
  status: string
  subtotal: number
  taxAmount: number
  discountAmount: number
  total: number
  notes?: string
  supplierId: string
  supplier?: any
  items: PurchaseItem[]
}

export default function EditPurchasePage() {
  const router = useRouter()
  const params = useParams()
  const { t } = useI18n()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [purchase, setPurchase] = useState<Purchase | null>(null)
  const [suppliers, setSuppliers] = useState<any[]>([])
  
  const [formData, setFormData] = useState({
    supplierId: "",
    date: "",
    expectedDate: "",
    status: "PENDING",
    notes: "",
    items: [] as PurchaseItem[],
    taxAmount: 0,
    discountAmount: 0,
  })

  // Load purchase data and suppliers
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      try {
        // Load purchase
        const purchaseResponse = await fetch(`/api/purchases/${params.id}`)
        if (purchaseResponse.ok) {
          const purchaseData = await purchaseResponse.json()
          setPurchase(purchaseData)
          
          setFormData({
            supplierId: purchaseData.supplierId,
            date: purchaseData.date.split('T')[0],
            expectedDate: purchaseData.expectedDate ? purchaseData.expectedDate.split('T')[0] : "",
            status: purchaseData.status,
            notes: purchaseData.notes || "",
            items: purchaseData.items || [],
            taxAmount: Number(purchaseData.taxAmount),
            discountAmount: Number(purchaseData.discountAmount),
          })
        }

        // Load suppliers
        const suppliersResponse = await fetch('/api/suppliers')
        if (suppliersResponse.ok) {
          const suppliersData = await suppliersResponse.json()
          setSuppliers(suppliersData.suppliers || [])
        }
      } catch (error) {
        console.error('Error loading data:', error)
      } finally {
        setLoading(false)
      }
    }
    
    if (params.id) {
      loadData()
    }
  }, [params.id])

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleItemChange = (index: number, field: keyof PurchaseItem, value: any) => {
    const newItems = [...formData.items]
    newItems[index] = {
      ...newItems[index],
      [field]: value
    }
    
    // Recalculate total for this item
    if (field === 'quantity' || field === 'unitPrice') {
      newItems[index].total = newItems[index].quantity * newItems[index].unitPrice
    }
    
    setFormData(prev => ({
      ...prev,
      items: newItems
    }))
  }

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, {
        description: "",
        quantity: 1,
        unitPrice: 0,
        total: 0
      }]
    }))
  }

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  const calculateTotals = () => {
    const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0)
    const total = subtotal + formData.taxAmount - formData.discountAmount
    return { subtotal, total }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.supplierId) {
      alert('Please select a supplier')
      return
    }

    if (formData.items.length === 0) {
      alert('Please add at least one item')
      return
    }

    setSaving(true)
    try {
      const { subtotal, total } = calculateTotals()
      
      const response = await fetch(`/api/purchases/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          supplierId: formData.supplierId,
          date: formData.date,
          expectedDate: formData.expectedDate || null,
          status: formData.status,
          notes: formData.notes,
          items: formData.items,
          subtotal,
          taxAmount: formData.taxAmount,
          discountAmount: formData.discountAmount,
          total,
        }),
      })

      if (response.ok) {
        alert('Purchase updated successfully!')
        router.push(`/dashboard/purchases/${params.id}`)
      } else {
        const error = await response.json()
        alert(`Failed to update purchase: ${error.error}`)
      }
    } catch (error) {
      console.error('Error updating purchase:', error)
      alert('Failed to update purchase')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('purchases.loading')}</h2>
          </div>
        </div>
      </div>
    )
  }

  if (!purchase) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('purchases.notFound')}</h2>
          </div>
        </div>
      </div>
    )
  }

  const { subtotal, total } = calculateTotals()

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('purchases.editPurchase')}</h2>
            <p className="text-muted-foreground">
              {t('purchases.updatePurchaseOrder')} {purchase.number}
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Purchase Details */}
            <Card>
              <CardHeader>
                <CardTitle>{t('purchases.purchaseDetails')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="supplier">{t('purchases.supplier')}</Label>
                    <Select
                      value={formData.supplierId}
                      onValueChange={(value) => handleInputChange('supplierId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={t('purchases.selectSupplier')} />
                      </SelectTrigger>
                      <SelectContent>
                        {suppliers.map((supplier) => (
                          <SelectItem key={supplier.id} value={supplier.id}>
                            {supplier.name} {supplier.company && `(${supplier.company})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">{t('purchases.status')}</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) => handleInputChange('status', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PENDING">{t('purchases.pending')}</SelectItem>
                        <SelectItem value="ORDERED">{t('purchases.ordered')}</SelectItem>
                        <SelectItem value="RECEIVED">{t('purchases.received')}</SelectItem>
                        <SelectItem value="CANCELLED">{t('purchases.cancelled')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">{t('purchases.purchaseDate')}</Label>
                    <Input
                      id="date"
                      type="date"
                      value={formData.date}
                      onChange={(e) => handleInputChange('date', e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="expectedDate">{t('purchases.expectedDate')}</Label>
                    <Input
                      id="expectedDate"
                      type="date"
                      value={formData.expectedDate}
                      onChange={(e) => handleInputChange('expectedDate', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">{t('purchases.notes')}</Label>
                  <Textarea
                    id="notes"
                    placeholder={t('purchases.additionalNotesOptional')}
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    className="min-h-[80px]"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Items */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>{t('purchases.items')}</CardTitle>
                  <Button type="button" variant="outline" size="sm" onClick={addItem}>
                    <Plus className="mr-2 h-4 w-4" />
                    {t('purchases.addItem')}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.items.map((item, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-end">
                    <div className="col-span-5">
                      <Label>{t('purchases.description')}</Label>
                      <Input
                        placeholder={t('purchases.itemDescription')}
                        value={item.description}
                        onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Label>{t('purchases.quantity')}</Label>
                      <Input
                        type="number"
                        min="1"
                        value={item.quantity}
                        onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Label>{t('purchases.unitPrice')}</Label>
                      <Input
                        type="number"
                        min="0"
                        step="0.001"
                        value={item.unitPrice}
                        onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                      />
                    </div>
                    <div className="col-span-2">
                      <Label>{t('purchases.total')}</Label>
                      <Input
                        value={formatCurrency(item.total)}
                        disabled
                      />
                    </div>
                    <div className="col-span-1">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeItem(index)}
                        disabled={formData.items.length === 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Summary */}
            <Card>
              <CardHeader>
                <CardTitle>{t('purchases.summary')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="taxAmount">{t('purchases.taxAmount')}</Label>
                  <Input
                    id="taxAmount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.taxAmount}
                    onChange={(e) => handleInputChange('taxAmount', parseFloat(e.target.value) || 0)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="discountAmount">{t('purchases.discountAmount')}</Label>
                  <Input
                    id="discountAmount"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.discountAmount}
                    onChange={(e) => handleInputChange('discountAmount', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2 pt-4 border-t">
                  <div className="flex justify-between">
                    <span>{t('purchases.subtotal')}:</span>
                    <span className="font-medium">{formatCurrency(subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('purchases.tax')}:</span>
                    <span>{formatCurrency(formData.taxAmount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('purchases.discount')}:</span>
                    <span>-{formatCurrency(formData.discountAmount)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold">
                    <span>{t('purchases.total')}:</span>
                    <span>{formatCurrency(total)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>{t('purchases.actions')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('purchases.updating')}
                    </>
                  ) : (
                    t('purchases.updatePurchase')
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push(`/dashboard/purchases/${params.id}`)}
                  disabled={saving}
                >
                  {t('purchases.cancel')}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  )
}
