import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current date for calculations
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)

    // Get total customers
    const totalCustomers = await prisma.customer.count()

    // Get active customers (customers with invoices in last 6 months)
    const sixMonthsAgo = new Date()
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)
    
    const activeCustomers = await prisma.customer.count({
      where: {
        invoices: {
          some: {
            date: {
              gte: sixMonthsAgo
            }
          }
        }
      }
    })

    // Get new customers this month
    const newCustomersThisMonth = await prisma.customer.count({
      where: {
        createdAt: {
          gte: startOfMonth
        }
      }
    })

    // Get total revenue from all customers
    const totalRevenueResult = await prisma.invoice.aggregate({
      _sum: {
        total: true
      },
      where: {
        status: 'PAID'
      }
    })

    const totalRevenue = totalRevenueResult._sum?.total || 0

    // Get average order value
    const avgOrderValueResult = await prisma.invoice.aggregate({
      _avg: {
        total: true
      },
      where: {
        status: 'PAID'
      }
    })

    const avgOrderValue = avgOrderValueResult._avg?.total || 0

    // Get repeat customers (customers with more than 1 invoice)
    const repeatCustomersResult = await prisma.customer.findMany({
      where: {
        invoices: {
          some: {}
        }
      },
      include: {
        _count: {
          select: {
            invoices: true
          }
        }
      }
    })

    const repeatCustomers = repeatCustomersResult.filter(customer => customer._count.invoices > 1).length

    // Get outstanding balance
    const outstandingBalanceResult = await prisma.invoice.aggregate({
      _sum: {
        total: true
      },
      where: {
        status: {
          in: ['UNPAID', 'OVERDUE', 'PARTIAL']
        }
      }
    })

    const outstandingBalance = outstandingBalanceResult._sum?.total || 0

    // Get recent customers
    const recentCustomers = await prisma.customer.findMany({
      take: 10,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        _count: {
          select: {
            invoices: true
          }
        },
        invoices: {
          where: {
            status: 'PAID'
          },
          select: {
            total: true
          }
        }
      }
    })

    // Calculate total spent for each customer
    const recentCustomersWithStats = recentCustomers.map(customer => ({
      id: customer.id,
      name: customer.name,
      nameAr: customer.nameAr,
      email: customer.email,
      company: customer.company,
      createdAt: customer.createdAt,
      totalInvoices: customer._count.invoices,
      totalSpent: customer.invoices.reduce((sum, invoice) => sum + Number(invoice.total || 0), 0)
    }))

    const stats = {
      total: totalCustomers,
      active: activeCustomers,
      inactive: totalCustomers - activeCustomers,
      newThisMonth: newCustomersThisMonth,
      totalRevenue: Number(totalRevenue),
      avgOrderValue: Number(avgOrderValue),
      repeatCustomers: repeatCustomers,
      outstandingBalance: Number(outstandingBalance)
    }

    return NextResponse.json({
      stats,
      recentCustomers: recentCustomersWithStats
    })

  } catch (error) {
    console.error('Error fetching customer dashboard data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch customer dashboard data' },
      { status: 500 }
    )
  }
} 