"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { CustomerSelector } from "@/components/ui/customer-selector"
import { TeamMembersManager } from "@/components/projects/team-members-manager"
import { ArrowLeft, Save, Calendar } from "lucide-react"
import { useI18n } from "@/lib/i18n"

export default function CreateProjectPage() {
  const router = useRouter()
  const { t, direction } = useI18n()
  const [saving, setSaving] = useState(false)
  const [users, setUsers] = useState<any[]>([])

  // Translated options
  const PROJECT_STATUSES = [
    { value: 'PLANNING', label: t('projects.statuses.planning') || 'Planning' },
    { value: 'IN_PROGRESS', label: t('projects.statuses.inProgress') || 'In Progress' },
    { value: 'ON_HOLD', label: t('projects.statuses.onHold') || 'On Hold' },
    { value: 'COMPLETED', label: t('projects.statuses.completed') || 'Completed' },
    { value: 'CANCELLED', label: t('projects.statuses.cancelled') || 'Cancelled' },
  ]

  const PROJECT_PRIORITIES = [
    { value: 'LOW', label: t('projects.priorities.low') || 'Low' },
    { value: 'MEDIUM', label: t('projects.priorities.medium') || 'Medium' },
    { value: 'HIGH', label: t('projects.priorities.high') || 'High' },
    { value: 'URGENT', label: t('projects.priorities.urgent') || 'Urgent' },
  ]

  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    description: '',
    status: 'PLANNING',
    priority: 'MEDIUM',
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    budget: '',
    clientId: '',
    managerId: '',
    notes: '',
  })

  // Load users
  useEffect(() => {
    const loadData = async () => {
      try {
        const usersRes = await fetch('/api/users')
        
        if (usersRes.ok) {
          const usersData = await usersRes.json()
          setUsers(usersData.users || usersData)
        }
      } catch (error) {
        console.error('Error loading data:', error)
      }
    }

    loadData()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const projectData = {
        ...formData,
        budget: formData.budget ? parseFloat(formData.budget) : null,
        clientId: formData.clientId || null,
        managerId: formData.managerId || null,
        endDate: formData.endDate || null,
      }

      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData)
      })

      if (response.ok) {
        alert(t('projects.projectCreatedSuccessfully') || 'Project created successfully!')
        router.push('/dashboard/projects')
      } else {
        const error = await response.json()
        alert(`${t('projects.failedToCreateProject') || 'Failed to create project'}: ${error.error}`)
      }
    } catch (error) {
      console.error('Error creating project:', error)
      alert(t('projects.failedToCreateProject') || 'Failed to create project')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className={`flex-1 space-y-6 p-8 pt-6 ${direction === 'rtl' ? 'font-arabic' : ''}`} dir={direction}>
      <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{t('common.back') || 'Back'}</span>
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('projects.createProject') || 'Create New Project'}</h2>
          <p className="text-muted-foreground">
            {t('projects.createProjectDescription') || 'Set up a new project with timeline, budget, and team assignments'}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('projects.projectInformation') || 'Project Information'}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('projects.projectName') || 'Project Name'} *</Label>
                  <Input
                    id="name"
                    placeholder={t('projects.projectName') || 'Enter project name'}
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                    dir={direction}
                    className={direction === 'rtl' ? 'text-right' : 'text-left'}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nameAr">{t('projects.projectNameAr') || 'Project Name (Arabic)'}</Label>
                  <Input
                    id="nameAr"
                    placeholder="اسم المشروع"
                    dir="rtl"
                    value={formData.nameAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, nameAr: e.target.value }))}
                    className="text-right"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="clientId">{t('common.client') || 'Client'}</Label>
                  <CustomerSelector
                    value={formData.clientId}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, clientId: value }))}
                    placeholder={t('projects.searchAndSelectClient') || 'Search and select client...'}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="managerId">{t('projects.projectManager') || 'Project Manager'}</Label>
                  <Select value={formData.managerId || 'none'} onValueChange={(value) => setFormData(prev => ({ ...prev, managerId: value === 'none' ? '' : value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('projects.selectProjectManager') || 'Select project manager'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">{t('common.unassigned') || 'Unassigned'}</SelectItem>
                      {users.map(user => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.name} ({user.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="status">{t('common.status') || 'Status'}</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={`${t('common.select') || 'Select'} ${t('common.status') || 'Status'}`} />
                    </SelectTrigger>
                    <SelectContent>
                      {PROJECT_STATUSES.map(status => (
                        <SelectItem key={status.value} value={status.value}>{status.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="priority">{t('common.priority') || 'Priority'}</Label>
                  <Select value={formData.priority} onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={`${t('common.select') || 'Select'} ${t('common.priority') || 'Priority'}`} />
                    </SelectTrigger>
                    <SelectContent>
                      {PROJECT_PRIORITIES.map(priority => (
                        <SelectItem key={priority.value} value={priority.value}>{priority.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="startDate">{t('projects.startDate') || 'Start Date'} *</Label>
                  <div className="relative">
                    <Calendar className={`absolute top-2.5 h-4 w-4 text-muted-foreground ${direction === 'rtl' ? 'right-2' : 'left-2'}`} />
                    <Input
                      id="startDate"
                      type="date"
                      className={direction === 'rtl' ? 'pr-8' : 'pl-8'}
                      value={formData.startDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">{t('projects.endDate') || 'End Date'}</Label>
                  <div className="relative">
                    <Calendar className={`absolute top-2.5 h-4 w-4 text-muted-foreground ${direction === 'rtl' ? 'right-2' : 'left-2'}`} />
                    <Input
                      id="endDate"
                      type="date"
                      className={direction === 'rtl' ? 'pr-8' : 'pl-8'}
                      value={formData.endDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget">{t('projects.projectBudget') || 'Budget'} (OMR)</Label>
                  <div className="relative">
                    <span className={`absolute top-2.5 text-sm text-muted-foreground font-medium ${direction === 'rtl' ? 'right-2' : 'left-2'}`}>OMR</span>
                    <Input
                      id="budget"
                      type="number"
                      step="0.001"
                      placeholder="0.000"
                      className={direction === 'rtl' ? 'pr-12' : 'pl-12'}
                      value={formData.budget}
                      onChange={(e) => setFormData(prev => ({ ...prev, budget: e.target.value }))}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">{t('projects.projectDescription') || 'Project Description'}</Label>
              <Textarea
                id="description"
                placeholder={t('projects.projectDescriptionPlaceholder') || 'Describe the project objectives, scope, and deliverables...'}
                rows={4}
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                dir={direction}
                className={direction === 'rtl' ? 'text-right' : 'text-left'}
              />
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">{t('projects.additionalNotes') || 'Additional Notes'}</Label>
              <Textarea
                id="notes"
                placeholder={t('projects.additionalNotesPlaceholder') || 'Any additional notes or requirements...'}
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                dir={direction}
                className={direction === 'rtl' ? 'text-right' : 'text-left'}
              />
            </div>

            {/* Action Buttons */}
            <div className={`flex items-center justify-end pt-6 ${direction === 'rtl' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                {t('common.cancel') || 'Cancel'}
              </Button>
              <Button type="submit" disabled={saving}>
                <Save className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
                {saving ? (t('common.loading') || 'Creating...') : (t('projects.createProject') || 'Create Project')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
