import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/purchases/[id] - Get purchase by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const purchase = await prisma.purchase.findUnique({
      where: { id },
      include: {
        supplier: true,
        user: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    if (!purchase) {
      return NextResponse.json(
        { error: 'Purchase not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(purchase)
  } catch (error) {
    console.error('Error fetching purchase:', error)
    return NextResponse.json(
      { error: 'Failed to fetch purchase' },
      { status: 500 }
    )
  }
}

// PUT /api/purchases/[id] - Update purchase
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const {
      supplierId,
      date,
      expectedDate,
      status,
      notes,
      items,
      subtotal,
      taxAmount,
      discountAmount,
      total,
    } = body

    // Check if purchase exists
    const existingPurchase = await prisma.purchase.findUnique({
      where: { id },
      include: { items: true },
    })

    if (!existingPurchase) {
      return NextResponse.json(
        { error: 'Purchase not found' },
        { status: 404 }
      )
    }

    // Update purchase with transaction
    const updatedPurchase = await prisma.$transaction(async (tx) => {
      // Delete existing items
      await tx.purchaseItem.deleteMany({
        where: { purchaseId: id },
      })

      // Update purchase
      const purchase = await tx.purchase.update({
        where: { id },
        data: {
          supplierId,
          date: new Date(date),
          expectedDate: expectedDate ? new Date(expectedDate) : null,
          status,
          notes,
          subtotal: parseFloat(subtotal),
          taxAmount: parseFloat(taxAmount),
          discountAmount: parseFloat(discountAmount),
          total: parseFloat(total),
          updatedAt: new Date(),
        },
      })

      // Create new items
      if (items && items.length > 0) {
        await tx.purchaseItem.createMany({
          data: items.map((item: any) => ({
            purchaseId: id,
            description: item.description,
            quantity: parseInt(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            total: parseFloat(item.total),
            productId: item.productId || null,
          })),
        })
      }

      return purchase
    })

    // Fetch updated purchase with relations
    const finalPurchase = await prisma.purchase.findUnique({
      where: { id },
      include: {
        supplier: true,
        user: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    return NextResponse.json(finalPurchase)
  } catch (error) {
    console.error('Error updating purchase:', error)
    return NextResponse.json(
      { error: 'Failed to update purchase' },
      { status: 500 }
    )
  }
}

// DELETE /api/purchases/[id] - Delete purchase
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if purchase exists
    const existingPurchase = await prisma.purchase.findUnique({
      where: { id },
    })

    if (!existingPurchase) {
      return NextResponse.json(
        { error: 'Purchase not found' },
        { status: 404 }
      )
    }

    // Delete purchase (this will cascade delete items)
    await prisma.purchase.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Purchase deleted successfully' })
  } catch (error) {
    console.error('Error deleting purchase:', error)
    return NextResponse.json(
      { error: 'Failed to delete purchase' },
      { status: 500 }
    )
  }
}
