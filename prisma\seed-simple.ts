import { PrismaClient } from '@prisma/client'
import * as bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting simple database seeding...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10)

  const adminUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Admin User',
      role: 'ADMIN',
      phone: '+968 9123 4567',
      isActive: true,
      joinDate: new Date('2023-01-01'),
      tasksCompleted: 50,
      tasksInProgress: 2,
      completionRate: 96.0,
    },
  })

  // Create employee user
  const employeeUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: '<PERSON>',
      role: 'EMPLOYEE',
      phone: '+968 9876 5432',
      isActive: true,
      joinDate: new Date('2023-06-15'),
      tasksCompleted: 45,
      tasksInProgress: 3,
      completionRate: 95.0,
    },
  })

  // Create sales data
  await prisma.salesData.create({
    data: {
      userId: employeeUser.id,
      totalSales: 125600.000,
      monthlyTarget: 100000.000,
      invoicesCount: 28,
      avgOrderValue: 4485.000,
      conversionRate: 78.0,
      commission: 6280.000,
      rank: 1,
      growth: 15.8,
    },
  })

  // Create categories
  const electronicsCategory = await prisma.category.create({
    data: {
      name: 'Electronics',
      nameAr: 'الإلكترونيات',
      description: 'Electronic devices and accessories',
    },
  })

  const furnitureCategory = await prisma.category.create({
    data: {
      name: 'Furniture',
      nameAr: 'الأثاث',
      description: 'Office and home furniture',
    },
  })

  // Create units
  const pieceUnit = await prisma.unit.create({
    data: {
      name: 'Piece',
      nameAr: 'قطعة',
      symbol: 'pcs',
    },
  })

  // Create suppliers
  const supplier1 = await prisma.supplier.create({
    data: {
      name: 'Tech Supplier Co.',
      nameAr: 'شركة المورد التقني',
      email: '<EMAIL>',
      mobile: '+968 9123 4567',
      phone: '+968 2456 7890',
      address: 'Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      company: 'Tech Supplier Co.',
      contactPerson: 'Ahmed Al-Rashid',
    },
  })

  // Create products
  const product1 = await prisma.product.create({
    data: {
      name: 'Wireless Headphones',
      nameAr: 'سماعات لاسلكية',
      description: 'High-quality wireless headphones',
      sku: 'WH-001',
      barcode: '1234567890123',
      price: 45.500,
      costPrice: 30.000,
      currentStock: 15,
      minStock: 20,
      maxStock: 100,
      location: 'A1-B2',
      categoryId: electronicsCategory.id,
      unitId: pieceUnit.id,
      supplierId: supplier1.id,
      lastRestocked: new Date('2024-01-15'),
    },
  })

  const product2 = await prisma.product.create({
    data: {
      name: 'Office Chair',
      nameAr: 'كرسي مكتب',
      description: 'Ergonomic office chair',
      sku: 'OC-002',
      barcode: '1234567890124',
      price: 125.000,
      costPrice: 85.000,
      currentStock: 8,
      minStock: 10,
      maxStock: 50,
      location: 'B2-C1',
      categoryId: furnitureCategory.id,
      unitId: pieceUnit.id,
      supplierId: supplier1.id,
      lastRestocked: new Date('2024-01-10'),
    },
  })

  // Create stock movements
  await prisma.stockMovement.create({
    data: {
      productId: product1.id,
      type: 'IN',
      quantity: 50,
      reason: 'Purchase Order #PO-001',
      date: new Date('2024-01-15'),
    },
  })

  await prisma.stockMovement.create({
    data: {
      productId: product1.id,
      type: 'OUT',
      quantity: 35,
      reason: 'Sales',
      date: new Date('2024-01-20'),
    },
  })

  // Create customers
  const customer1 = await prisma.customer.create({
    data: {
      name: 'Ahmed Al-Balushi',
      nameAr: 'أحمد البلوشي',
      email: '<EMAIL>',
      mobile: '+968 9111 2222',
      phone: '+968 2444 5555',
      address: 'Ruwi, Muscat',
      city: 'Muscat',
      company: 'Al-Balushi Trading',
      taxNumber: 'TAX123456',
    },
  })

  const customer2 = await prisma.customer.create({
    data: {
      name: 'Fatima Al-Zahra',
      nameAr: 'فاطمة الزهراء',
      email: '<EMAIL>',
      mobile: '+968 9333 4444',
      address: 'Sohar Industrial Area',
      city: 'Sohar',
      company: 'Zahra Enterprises',
    },
  })

  // Create settings
  await prisma.setting.create({
    data: {
      key: 'company_name',
      value: 'Office Sales & Services Management',
      description: 'Company name in English',
    },
  })

  await prisma.setting.create({
    data: {
      key: 'company_name_ar',
      value: 'نظام إدارة المبيعات والخدمات المكتبية',
      description: 'Company name in Arabic',
    },
  })

  await prisma.setting.create({
    data: {
      key: 'tax_rate',
      value: '5',
      description: 'Default tax rate percentage',
    },
  })

  console.log('✅ Simple database seeding completed successfully!')
  console.log('📊 Created:')
  console.log('   - 2 Users with Sales Data')
  console.log('   - 2 Categories')
  console.log('   - 1 Unit')
  console.log('   - 1 Supplier')
  console.log('   - 2 Products')
  console.log('   - 2 Stock Movements')
  console.log('   - 2 Customers')
  console.log('   - 3 Settings')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
