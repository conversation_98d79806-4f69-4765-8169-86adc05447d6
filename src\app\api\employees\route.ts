import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import bcrypt from 'bcryptjs'

// GET /api/employees - Get all employees
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const skip = (page - 1) * limit

    const [employees, total] = await Promise.all([
      prisma.user.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          role: true,
          isActive: true,
          createdAt: true,
          idCardNumber: true,
          idCardExpiry: true,
          passportNumber: true,
          passportExpiry: true,
          visaNumber: true,
          visaExpiry: true,
          licenseNumber: true,
          licenseExpiry: true,
          contractExpiry: true,
          salesData: true,
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.user.count(),
    ])

    return NextResponse.json({
      employees,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching employees:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/employees - Create new employee
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      email,
      phone,
      role = 'EMPLOYEE',
      password,
      idCardNumber,
      idCardExpiry,
      passportNumber,
      passportExpiry,
      visaNumber,
      visaExpiry,
      licenseNumber,
      licenseExpiry,
      contractExpiry,
    } = body

    // Validate required fields
    if (!name || !email || !phone || !password) {
      return NextResponse.json(
        { error: 'Name, email, phone, and password are required' },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    const employee = await prisma.user.create({
      data: {
        name,
        email,
        phone,
        role,
        password: hashedPassword,
        isActive: true,
        joinDate: new Date(),
        tasksCompleted: 0,
        tasksInProgress: 0,
        completionRate: 0,
        // Document fields
        idCardNumber: idCardNumber || null,
        idCardExpiry: idCardExpiry ? new Date(idCardExpiry) : null,
        passportNumber: passportNumber || null,
        passportExpiry: passportExpiry ? new Date(passportExpiry) : null,
        visaNumber: visaNumber || null,
        visaExpiry: visaExpiry ? new Date(visaExpiry) : null,
        licenseNumber: licenseNumber || null,
        licenseExpiry: licenseExpiry ? new Date(licenseExpiry) : null,
        contractExpiry: contractExpiry ? new Date(contractExpiry) : null,
        salesData: {
          create: {
            totalSales: 0,
            monthlyTarget: 50000,
            invoicesCount: 0,
            avgOrderValue: 0,
            conversionRate: 0,
            commission: 0,
            rank: 0,
            growth: 0,
          },
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        isActive: true,
        createdAt: true,
        idCardNumber: true,
        idCardExpiry: true,
        passportNumber: true,
        passportExpiry: true,
        visaNumber: true,
        visaExpiry: true,
        licenseNumber: true,
        licenseExpiry: true,
        contractExpiry: true,
        salesData: true,
      },
    })

    return NextResponse.json(employee, { status: 201 })
  } catch (error) {
    console.error('Error creating employee:', error)
    return NextResponse.json(
      { error: 'Failed to create employee' },
      { status: 500 }
    )
  }
}
