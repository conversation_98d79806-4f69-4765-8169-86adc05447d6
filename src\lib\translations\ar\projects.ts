export const projects = {
  title: "المشاريع",
  addProject: "إضافة مشروع",
  editProject: "تعديل مشروع",
  createProject: "إنشاء مشروع",
  updateProject: "تحديث مشروع",
  deleteProject: "حذف مشروع",
  projectDetails: "تفاصيل المشروع",
  projectName: "اسم المشروع",
  projectNameAr: "اسم المشروع (عربي)",
  projectCode: "رمز المشروع",
  projectDescription: "وصف المشروع",
  projectManager: "مدير المشروع",
  projectBudget: "ميزانية المشروع",
  actualCost: "التكلفة الفعلية",
  projectProgress: "تقدم المشروع",
  projectStatus: "حالة المشروع",
  projectPriority: "أولوية المشروع",
  teamMembers: "أعضاء الفريق",
  projectTasks: "مهام المشروع",
  projectInvoices: "فواتير المشروع",
  projectExpenses: "مصروفات المشروع",
  projectNotes: "ملاحظات المشروع",
  searchPlaceholder: "البحث في المشاريع، العملاء...",
  noProjectsFound: "لم يتم العثور على مشاريع",
  createFirstProject: "أنشئ أول مشروع!",
  noProjectsFoundSearch: "لم يتم العثور على مشاريع تطابق بحثك.",
  createFirstProjectAlt: "لم يتم العثور على مشاريع. أنشئ أول مشروع!",
  totalProjects: "إجمالي المشاريع",
  activeProjects: "المشاريع النشطة",
  completedProjects: "المشاريع المكتملة",
  onHoldProjects: "المشاريع المعلقة",
  cancelledProjects: "المشاريع الملغية",
  totalBudget: "إجمالي الميزانية",
  totalActualCost: "إجمالي التكلفة الفعلية",
  averageProgress: "متوسط التقدم",
  overdueProjects: "المشاريع المتأخرة",
  recentProjects: "المشاريع الحديثة",
  upcomingDeadlines: "المواعيد النهائية القادمة",
  highPriorityProjects: "المشاريع عالية الأولوية",
  projectPerformance: "أداء المشاريع",
  budgetUtilization: "استخدام الميزانية",
  timelineOverview: "نظرة عامة على الجدول الزمني",
  financialOverview: "النظرة المالية العامة",
  addTeamMember: "إضافة عضو فريق",
  removeTeamMember: "إزالة عضو فريق",
  assignManager: "تعيين مدير",
  changeStatus: "تغيير الحالة",
  viewTasks: "عرض المهام",
  addTask: "إضافة مهمة",
  viewInvoices: "عرض الفواتير",
  addInvoice: "إضافة فاتورة",
  viewExpenses: "عرض المصروفات",
  addExpense: "إضافة مصروف",
  projectManagement: "إدارة المشاريع",
  manageDescription: "إدارة مشاريعك، تتبع التقدم، والتعاون مع فريقك",
  dashboard: "لوحة التحكم",
  createProjectDescription: "إعداد مشروع جديد مع الجدول الزمني والميزانية وتعيينات الفريق",
  projectInformation: "معلومات المشروع",
  searchAndSelectClient: "البحث واختيار العميل...",
  searchAndSelectClientPlaceholder: "البحث واختيار العميل...",
  selectProjectManager: "اختيار مدير المشروع",
  startDate: "تاريخ البداية",
  endDate: "تاريخ النهاية",
  dueDate: "تاريخ الاستحقاق",
  estimatedDuration: "المدة المقدرة",
  actualDuration: "المدة الفعلية",
  client: "العميل",
  clientName: "اسم العميل",
  projectDescriptionPlaceholder: "وصف أهداف المشروع والنطاق والمخرجات...",
  additionalNotes: "ملاحظات إضافية",
  additionalNotesPlaceholder: "أي ملاحظات أو متطلبات إضافية...",
  projectNamePlaceholder: "أدخل اسم المشروع",
  projectCodePlaceholder: "أدخل رمز المشروع",
  budgetPlaceholder: "أدخل ميزانية المشروع",
  selectClient: "اختر العميل",
  searchClients: "البحث عن العملاء...",
  noClientFound: "لم يتم العثور على عميل",
  projectCreatedSuccessfully: "تم إنشاء المشروع بنجاح!",
  failedToCreateProject: "فشل في إنشاء المشروع",
  projectUpdatedSuccessfully: "تم تحديث المشروع بنجاح!",
  failedToUpdateProject: "فشل في تحديث المشروع",
  projectDeletedSuccessfully: "تم حذف المشروع بنجاح!",
  failedToDeleteProject: "فشل في حذف المشروع",
  confirmDeleteProject: "هل أنت متأكد من أنك تريد حذف هذا المشروع؟",
  noClient: "لا يوجد عميل",
  noDueDate: "لا يوجد موعد استحقاق",
  noManager: "لا يوجد مدير",
  noDescription: "لا يوجد وصف",
  notStarted: "لم يبدأ",
  archived: "مؤرشف",
  statuses: {
    planning: "التخطيط",
    inProgress: "قيد التنفيذ",
    onHold: "معلق",
    completed: "مكتمل",
    cancelled: "ملغي"
  },
  priorities: {
    low: "منخفض",
    medium: "متوسط",
    high: "عالي",
    urgent: "عاجل"
  },
  back: "رجوع",
  dashboardTitle: "لوحة تحكم المشاريع",
  dashboardOverview: "نظرة عامة على جميع المشاريع والتقدم والبيانات الرئيسية",
  newProject: "مشروع جديد",
  completionRate: "معدل الإنجاز",
  avgAcrossAll: "المتوسط عبر جميع المشاريع",
  spent: "تم إنفاقها",
  requireAttention: "تتطلب انتباهاً فورياً",
  statusDistribution: "توزيع حالة المشاريع"
} 