"use client"

import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, DollarSign, Package, TrendingUp, Hash } from "lucide-react"
import { useI18n } from '@/lib/i18n'
import { useEffect, useState } from 'react'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  LineChart,
  Line,
} from 'recharts'
import { formatCurrency, formatDate } from '@/lib/localization'

interface RevenueStats {
  overview: {
    totalRevenue: number;
    totalInvoices: number;
    avgRevenuePerInvoice: number;
    topProduct: { name: string; nameAr: string; amount: number } | null;
  };
  revenueByProduct: { name: string; nameAr: string; amount: number }[];
  revenueByCustomer: { name: string; nameAr: string; amount: number }[];
  revenueByMonth: { month: string; amount: number }[];
  recentInvoices: any[];
}

export default function RevenueAnalysis() {
  const router = useRouter()
  const { t, language } = useI18n()
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<RevenueStats | null>(null)
  const [period, setPeriod] = useState('90d')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true)
        setError(null)
        const res = await fetch(`/api/financial-reports/revenue-analysis?period=${period}`)
        if (!res.ok) {
          const errorData = await res.json()
          throw new Error(errorData.details || 'Failed to load revenue stats')
        }
        const data: RevenueStats = await res.json()
        setStats(data)
      } catch (err: any) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }
    fetchStats()
  }, [period])
  
  const getName = (item: { name: string, nameAr: string }) => {
    return language === 'ar' ? (item.nameAr || item.name) : item.name
  }

  if (loading) {
    return <div className="flex items-center justify-center h-64 text-muted-foreground">{t('finance.loadingReports') || 'جاري تحميل التقرير...'}</div>
  }

  if (error) {
    return <div className="flex items-center justify-center h-64 text-red-500"><h3>{t('common.error')}</h3><p>{error}</p></div>
  }

  if (!stats) return null

  const { overview, revenueByProduct, revenueByMonth, recentInvoices } = stats
  const topProductName = overview.topProduct ? getName(overview.topProduct) : 'N/A'

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => router.back()}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                {t('finance.financial.buttons.back') || 'رجوع'}
            </Button>
            <h2 className="text-3xl font-bold tracking-tight">
                {t('finance.financial.reports.reportTypes.revenueanalysis.name') || 'تحليل الإيرادات'}
            </h2>
        </div>
        <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-40"><SelectValue /></SelectTrigger>
            <SelectContent>
                <SelectItem value="30d">آخر 30 يومًا</SelectItem>
                <SelectItem value="90d">آخر 90 يومًا</SelectItem>
                <SelectItem value="ytd">منذ بداية العام</SelectItem>
                <SelectItem value="all">كل الأوقات</SelectItem>
            </SelectContent>
        </Select>
      </div>
      
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card><CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">إجمالي الإيرادات</CardTitle><DollarSign className="h-4 w-4 text-green-500" /></CardHeader><CardContent><div className="text-2xl font-bold text-green-600">{formatCurrency(overview.totalRevenue)}</div></CardContent></Card>
        <Card><CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">عدد الفواتير</CardTitle><Hash className="h-4 w-4 text-blue-500" /></CardHeader><CardContent><div className="text-2xl font-bold">{overview.totalInvoices}</div></CardContent></Card>
        <Card><CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">متوسط الإيراد للفاتورة</CardTitle><TrendingUp className="h-4 w-4 text-orange-500" /></CardHeader><CardContent><div className="text-2xl font-bold">{formatCurrency(overview.avgRevenuePerInvoice)}</div></CardContent></Card>
        <Card><CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2"><CardTitle className="text-sm font-medium">المنتج الأكثر مبيعًا</CardTitle><Package className="h-4 w-4 text-purple-500" /></CardHeader><CardContent><div className="text-lg font-bold">{topProductName}</div><p className="text-xs text-muted-foreground">{overview.topProduct ? formatCurrency(overview.topProduct.amount) : ''}</p></CardContent></Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card><CardHeader><CardTitle>اتجاه الإيرادات الشهري</CardTitle></CardHeader><CardContent><ResponsiveContainer width="100%" height={300}><LineChart data={revenueByMonth}><XAxis dataKey="month" /><YAxis /><Tooltip formatter={(value) => formatCurrency(value as number)} /><Line type="monotone" dataKey="amount" stroke="#10b981" strokeWidth={2} /></LineChart></ResponsiveContainer></CardContent></Card>
        <Card><CardHeader><CardTitle>الإيرادات حسب المنتج</CardTitle></CardHeader><CardContent><ResponsiveContainer width="100%" height={300}><BarChart data={revenueByProduct.slice(0, 10).map(p => ({...p, name: getName(p)}))}><XAxis dataKey="name" angle={-45} textAnchor="end" height={80} /><YAxis /><Tooltip formatter={(value) => formatCurrency(value as number)} /><Bar dataKey="amount" fill="#8b5cf6" /></BarChart></ResponsiveContainer></CardContent></Card>
      </div>

      {/* Recent Invoices Table */}
      <Card><CardHeader><CardTitle>أحدث الفواتير</CardTitle></CardHeader><CardContent><Table><TableHeader><TableRow><TableHead>التاريخ</TableHead><TableHead>رقم الفاتورة</TableHead><TableHead>العميل</TableHead><TableHead className="text-right">المبلغ</TableHead></TableRow></TableHeader><TableBody>{recentInvoices.map((inv) => (<TableRow key={inv.id}><TableCell>{formatDate(inv.date, language)}</TableCell><TableCell>{inv.number}</TableCell><TableCell>{language === 'ar' ? inv.customerNameAr : inv.customerName}</TableCell><TableCell className="text-right">{formatCurrency(inv.total)}</TableCell></TableRow>))}</TableBody></Table></CardContent></Card>
    </div>
  )
} 