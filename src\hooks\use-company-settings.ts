import { useState, useEffect } from 'react'
import { getSettings } from '@/lib/settings-storage'

interface CompanySettings {
  logo: string | null
  companyName: string
  companyNameAr: string
  loading: boolean
}

export function useCompanySettings(): CompanySettings {
  const [settings, setSettings] = useState<CompanySettings>({
    logo: null,
    companyName: 'Office Services Pro',
    companyNameAr: 'خدمات المكاتب المحترفة',
    loading: true,
  })

  useEffect(() => {
    const loadCompanySettings = async () => {
      try {
        const { settingsObject } = await getSettings()
        
        setSettings({
          logo: settingsObject.company_logo?.value || null,
          companyName: settingsObject.company_name?.value || 'Office Services Pro',
          companyNameAr: settingsObject.company_name_ar?.value || 'خدمات المكاتب المحترفة',
          loading: false,
        })
      } catch (error) {
        console.error('Error loading company settings:', error)
        setSettings(prev => ({ ...prev, loading: false }))
      }
    }

    loadCompanySettings()
  }, [])

  return settings
}
