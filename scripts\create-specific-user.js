const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createSpecificUser() {
  try {
    console.log('🔐 Creating user with specific session ID...')

    const hashedPassword = await bcrypt.hash('password123', 10)

    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: hashedPassword,
        name: 'BZ Point User',
        role: 'ADMIN',
        phone: '+968 9123 4567',
        isActive: true,
      },
      create: {
        id: 'cmbesht5j00008cxsm36r7lg0',
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'BZ Point User',
        role: 'ADMIN',
        phone: '+968 9123 4567',
        isActive: true,
        joinDate: new Date(),
        tasksCompleted: 0,
        tasksInProgress: 0,
        completionRate: 0,
      },
    })

    console.log('✅ User created/updated successfully!')
    console.log('📧 Email:', user.email)
    console.log('👤 Name:', user.name)
    console.log('🎭 Role:', user.role)
    console.log('🆔 ID:', user.id)

  } catch (error) {
    console.error('❌ Error creating user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createSpecificUser() 