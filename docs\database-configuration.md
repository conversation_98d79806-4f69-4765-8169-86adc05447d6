# Database Configuration

This document describes the database configuration setup that mirrors the PHP Capsule configuration you provided.

## Environment Variables

Create a `.env.local` file in your project root with the following configuration:

```env
# Database Configuration
# Main database connection URL (for Prisma)
DATABASE_URL="mysql://root:@localhost:3306/print_next_db"

# Structured database configuration (similar to PHP Capsule setup)
DB_DRIVER=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=print_next_db
DB_USERNAME=root
DB_PASSWORD=
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_PREFIX=

# Database Performance Settings
DB_PERSISTENT_CONNECTIONS=true
DB_CONNECTION_TIMEOUT=30
DB_PREFETCH_SIZE=1000
DB_BUFFER_SIZE=1048576
DB_USE_BUFFERED_QUERY=true

# Database SQL Mode (Production optimized)
DB_SQL_MODE="STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"

# Environment
NODE_ENV=development
CI_ENVIRONMENT=development

# NextAuth Configuration
NEXTAUTH_SECRET=your-secret-key-here-change-in-production
NEXTAUTH_URL=http://localhost:3000

# Application Settings
APP_NAME="Print Next"
APP_URL=http://localhost:3000
APP_TIMEZONE="Asia/Muscat"
APP_LOCALE=en
APP_FALLBACK_LOCALE=en

# Logging
LOG_LEVEL=info
ENABLE_QUERY_LOG=true
```

## Configuration Structure

The database configuration is structured similar to your PHP Capsule setup:

### PHP Capsule (Original)
```php
$capsule->addConnection([
    'driver'    => 'mysql',
    'host'      => env('database.default.hostname'),
    'database'  => env('database.default.database'),
    'username'  => env('database.default.username'),
    'password'  => env('database.default.password'),
    'charset'   => env('database.default.charset'),
    'collation' => env('database.default.collation'),
    'prefix'    => env('database.default.DBPrefix'),
    'strict'    => false,
    'engine'    => null,
    'options'   => [
        \PDO::ATTR_PERSISTENT => true,
        \PDO::ATTR_CASE => \PDO::CASE_NATURAL,
        \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
        // ... other PDO options
    ],
]);
```

### Next.js/Prisma (New)
```typescript
export const databaseConfig: DatabaseConfig = {
  driver: env('DB_DRIVER', 'mysql'),
  host: env('DB_HOST', 'localhost'),
  port: env('DB_PORT', 3306),
  database: env('DB_DATABASE', 'print_next_db'),
  username: env('DB_USERNAME', 'root'),
  password: env('DB_PASSWORD', ''),
  charset: env('DB_CHARSET', 'utf8mb4'),
  collation: env('DB_COLLATION', 'utf8mb4_unicode_ci'),
  prefix: env('DB_PREFIX', ''),
  strict: false,
  engine: null,
  options: {
    persistent: env('DB_PERSISTENT_CONNECTIONS', true),
    connectionTimeout: env('DB_CONNECTION_TIMEOUT', 30),
    prefetchSize: env('DB_PREFETCH_SIZE', 1000),
    bufferSize: env('DB_BUFFER_SIZE', 1024 * 1024),
    useBufferedQuery: env('DB_USE_BUFFERED_QUERY', true),
    sqlMode: env('DB_SQL_MODE', 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'),
  },
};
```

## Performance Optimizations

### Development Environment
- **Query Logging**: Enabled by default for debugging
- **Pretty Error Format**: Enhanced error messages for development
- **Connection Monitoring**: Detailed logging of database operations

### Production Environment
- **Connection Pooling**: Optimized connection pool settings
- **Persistent Connections**: Reduced connection overhead
- **Buffer Optimization**: 1MB buffer for improved performance
- **SQL Mode**: Strict mode for data integrity
- **Minimal Error Format**: Reduced error verbosity

## Features Comparison

| Feature | PHP Capsule | Next.js/Prisma | Status |
|---------|-------------|----------------|---------|
| Environment-based config | ✅ | ✅ | ✅ |
| Persistent connections | ✅ | ✅ | ✅ |
| Query logging (dev) | ✅ | ✅ | ✅ |
| Connection pooling | ✅ | ✅ | ✅ |
| Lazy loading prevention | ✅ | ⚠️ | Prisma handles differently |
| Buffer optimization | ✅ | ✅ | ✅ |
| SQL mode configuration | ✅ | ✅ | ✅ |
| Graceful shutdown | ✅ | ✅ | ✅ |

## Usage

### Basic Usage
```typescript
import { prisma, databaseConfig } from '@/lib/prisma'

// Use Prisma client as normal
const users = await prisma.user.findMany()

// Access configuration
console.log('Database:', databaseConfig.database)
```

### Health Check
```typescript
import { checkDatabaseConnection } from '@/lib/database-config'

const isHealthy = await checkDatabaseConnection()
if (!isHealthy) {
  console.error('Database connection failed!')
}
```

### Configuration Access
```typescript
import { databaseConfig } from '@/lib/database-config'

console.log('Current database configuration:', databaseConfig)
```

## Migration from PHP

If you're migrating from the PHP setup, here's the mapping:

| PHP Capsule | Next.js/Prisma |
|-------------|----------------|
| `env('database.default.hostname')` | `env('DB_HOST')` |
| `env('database.default.database')` | `env('DB_DATABASE')` |
| `env('database.default.username')` | `env('DB_USERNAME')` |
| `env('database.default.password')` | `env('DB_PASSWORD')` |
| `env('database.default.charset')` | `env('DB_CHARSET')` |
| `env('database.default.collation')` | `env('DB_COLLATION')` |
| `env('database.default.DBPrefix')` | `env('DB_PREFIX')` |

## Troubleshooting

### Connection Issues
1. Verify environment variables are set correctly
2. Check database server is running
3. Ensure user has proper permissions
4. Test connection with health check function

### Performance Issues
1. Enable query logging to identify slow queries
2. Check connection pool settings
3. Monitor buffer usage
4. Review SQL mode configuration

### Development vs Production
- Development: Query logging enabled, pretty errors
- Production: Optimized for performance, minimal logging

## Security Considerations

1. **Environment Variables**: Never commit `.env.local` to version control
2. **Database Credentials**: Use strong passwords and limited permissions
3. **SQL Mode**: Strict mode prevents data integrity issues
4. **Connection Limits**: Prevent connection exhaustion attacks

## Next Steps

1. Copy the environment variables to your `.env.local` file
2. Update the values to match your database setup
3. Restart your development server
4. Verify the configuration is working with the health check

The database configuration is now structured similarly to your PHP Capsule setup with equivalent performance optimizations and environment-based configuration. 