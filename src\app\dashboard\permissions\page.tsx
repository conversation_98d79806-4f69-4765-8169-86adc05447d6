"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Search, Shield, Eye, Plus } from "lucide-react"
import { useI18n } from "@/lib/i18n"

interface Permission {
  id: string
  name: string
  nameAr?: string
  module: string
  action: string
  resource?: string
  description?: string
  isActive: boolean
  createdAt: string
}

export default function PermissionsPage() {
  const { direction } = useI18n()
  const router = useRouter()
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedModule, setSelectedModule] = useState<string>("")

  useEffect(() => {
    loadPermissions()
  }, [])

  const loadPermissions = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/permissions')
      if (response.ok) {
        const data = await response.json()
        setPermissions(data)
      }
    } catch (error) {
      console.error('Error loading permissions:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (permission.nameAr && permission.nameAr.includes(searchTerm)) ||
      permission.module.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.action.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesModule = !selectedModule || permission.module === selectedModule
    
    return matchesSearch && matchesModule
  })

  const modules = [...new Set(permissions.map(p => p.module))].sort()

  const getModuleDisplayName = (module: string) => {
    const moduleNames: Record<string, { en: string; ar: string }> = {
      dashboard: { en: 'Dashboard', ar: 'لوحة التحكم' },
      customers: { en: 'Customers', ar: 'العملاء' },
      suppliers: { en: 'Suppliers', ar: 'الموردين' },
      products: { en: 'Products', ar: 'المنتجات' },
      categories: { en: 'Categories', ar: 'الفئات' },
      units: { en: 'Units', ar: 'الوحدات' },
      tasks: { en: 'Tasks', ar: 'المهام' },
      invoices: { en: 'Invoices', ar: 'الفواتير' },
      quotations: { en: 'Quotations', ar: 'عروض الأسعار' },
      purchases: { en: 'Purchases', ar: 'المشتريات' },
      expenses: { en: 'Expenses', ar: 'المصروفات' },
      financial: { en: 'Financial', ar: 'المالية' },
      employees: { en: 'Employees', ar: 'الموظفين' },
      reports: { en: 'Reports', ar: 'التقارير' },
      settings: { en: 'Settings', ar: 'الإعدادات' },
      users: { en: 'Users', ar: 'المستخدمين' },
      roles: { en: 'Roles', ar: 'الأدوار' },
      pos: { en: 'POS', ar: 'نقطة البيع' },
      leads: { en: 'Leads', ar: 'العملاء المحتملين' },
    }

    return direction === 'rtl' 
      ? moduleNames[module]?.ar || module 
      : moduleNames[module]?.en || module
  }

  const getActionDisplayName = (action: string) => {
    const actionNames: Record<string, { en: string; ar: string }> = {
      view: { en: 'View', ar: 'عرض' },
      create: { en: 'Create', ar: 'إنشاء' },
      edit: { en: 'Edit', ar: 'تعديل' },
      delete: { en: 'Delete', ar: 'حذف' },
      export: { en: 'Export', ar: 'تصدير' },
      print: { en: 'Print', ar: 'طباعة' },
      approve: { en: 'Approve', ar: 'موافقة' },
      assign: { en: 'Assign', ar: 'تعيين' },
      manage: { en: 'Manage', ar: 'إدارة' },
      access: { en: 'Access', ar: 'وصول' },
      sell: { en: 'Sell', ar: 'بيع' },
      convert: { en: 'Convert', ar: 'تحويل' },
      stock: { en: 'Stock', ar: 'مخزون' },
    }

    return direction === 'rtl' 
      ? actionNames[action]?.ar || action 
      : actionNames[action]?.en || action
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading permissions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex-1 space-y-4 p-8 pt-6 ${direction === 'rtl' ? 'rtl' : 'ltr'}`} dir={direction}>
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            {direction === 'rtl' ? 'إدارة الصلاحيات' : 'Permissions Management'}
          </h2>
          <p className="text-muted-foreground">
            {direction === 'rtl' 
              ? 'عرض وإدارة صلاحيات النظام' 
              : 'View and manage system permissions'
            }
          </p>
        </div>
        <Button onClick={() => router.push('/dashboard/permissions/add')}>
          <Plus className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
          {direction === 'rtl' ? 'إضافة صلاحية' : 'Add Permission'}
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {direction === 'rtl' ? 'إجمالي الصلاحيات' : 'Total Permissions'}
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{permissions.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {direction === 'rtl' ? 'الصلاحيات النشطة' : 'Active Permissions'}
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {permissions.filter(p => p.isActive).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {direction === 'rtl' ? 'الوحدات' : 'Modules'}
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{modules.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {direction === 'rtl' ? 'الصلاحيات المفلترة' : 'Filtered Permissions'}
            </CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredPermissions.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>{direction === 'rtl' ? 'البحث والفلترة' : 'Search & Filter'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className={`absolute top-2.5 h-4 w-4 text-muted-foreground ${direction === 'rtl' ? 'right-2' : 'left-2'}`} />
              <Input
                placeholder={direction === 'rtl' ? 'البحث في الصلاحيات...' : 'Search permissions...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={direction === 'rtl' ? 'pr-8' : 'pl-8'}
              />
            </div>
            <select
              value={selectedModule}
              onChange={(e) => setSelectedModule(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">
                {direction === 'rtl' ? 'جميع الوحدات' : 'All Modules'}
              </option>
              {modules.map(module => (
                <option key={module} value={module}>
                  {getModuleDisplayName(module)}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Table */}
      <Card>
        <CardHeader>
          <CardTitle>{direction === 'rtl' ? 'قائمة الصلاحيات' : 'Permissions List'}</CardTitle>
          <CardDescription>
            {direction === 'rtl' 
              ? `إجمالي ${filteredPermissions.length} صلاحية` 
              : `Total ${filteredPermissions.length} permissions`
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{direction === 'rtl' ? 'اسم الصلاحية' : 'Permission Name'}</TableHead>
                <TableHead>{direction === 'rtl' ? 'الوحدة' : 'Module'}</TableHead>
                <TableHead>{direction === 'rtl' ? 'الإجراء' : 'Action'}</TableHead>
                <TableHead>{direction === 'rtl' ? 'المورد' : 'Resource'}</TableHead>
                <TableHead>{direction === 'rtl' ? 'الوصف' : 'Description'}</TableHead>
                <TableHead>{direction === 'rtl' ? 'الحالة' : 'Status'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPermissions.map((permission) => (
                <TableRow key={permission.id}>
                  <TableCell className="font-medium">
                    <div>
                      <div>{permission.name}</div>
                      {permission.nameAr && (
                        <div className="text-sm text-muted-foreground">{permission.nameAr}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getModuleDisplayName(permission.module)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {getActionDisplayName(permission.action)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {permission.resource ? (
                      <Badge variant="outline">{permission.resource}</Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs truncate">
                      {permission.description || '-'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={permission.isActive ? "default" : "secondary"}>
                      {permission.isActive 
                        ? (direction === 'rtl' ? 'نشط' : 'Active')
                        : (direction === 'rtl' ? 'غير نشط' : 'Inactive')
                      }
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
