# Translation Tasks - Systematic Implementation

## Audit Results Summary
- **Total missing translations: 773**
- **Current coverage: ~65%**
- **Target: 100% coverage**

## Phase 1: HIGH PRIORITY (Week 1)

### Task 1.1: Common Module (10 translations) ⭐ CRITICAL
**File:** `src/lib/translations/ar/common.ts`
**Status:** 🔴 Not Started
**Estimated Time:** 30 minutes

Missing translations:
- common.download: "تحميل"
- common.theme: "المظهر"
- common.light: "فاتح"
- common.dark: "داكن"
- common.system: "النظام"
- common.settings: "الإعدادات"
- common.priority: "الأولوية"
- common.contactInformation: "معلومات الاتصال"
- common.product: "المنتج"
- common.category: "الفئة"

### Task 1.2: Customer Module (14 translations) ⭐ CRITICAL
**File:** `src/lib/translations/ar/customers.ts`
**Status:** 🔴 Not Started
**Estimated Time:** 45 minutes

Missing translations:
- customers.customerDeleted: "تم حذف العميل"
- customers.customerPhone: "هاتف العميل"
- customers.customerUpdated: "تم تحديث العميل"
- customers.customerAdded: "تم إضافة العميل"
- customers.manageCustomers: "إدارة العملاء"
- customers.dashboard: "لوحة تحكم العملاء"
- customers.customerEmail: "بريد العميل الإلكتروني"
- customers.customerCompany: "شركة العميل"
- customers.customerAddress: "عنوان العميل"
- customers.searchCustomers: "البحث في العملاء"
- customers.noCustomersFound: "لم يتم العثور على عملاء"
- customers.tryDifferentSearch: "جرب بحث مختلف"
- customers.viewDetails: "عرض التفاصيل"
- customers.deleteCustomer: "حذف العميل"

### Task 1.3: Supplier Module (3 translations) ⭐ CRITICAL
**File:** `src/lib/translations/ar/suppliers.ts`
**Status:** 🔴 Not Started
**Estimated Time:** 15 minutes

Missing translations:
- suppliers.ordersTab: "الطلبات"
- suppliers.paymentsTab: "المدفوعات"
- suppliers.productsTab: "المنتجات"

### Task 1.4: Navigation Module (2 translations) ⭐ CRITICAL
**File:** `src/lib/translations/ar/navigation.ts`
**Status:** 🔴 Not Started
**Estimated Time:** 15 minutes

Missing translations:
- nav.categories: "الفئات"
- nav.units: "الوحدات"

## Phase 2: MEDIUM PRIORITY (Week 2)

### Task 2.1: Dashboard Module (47 translations) 🟡
**File:** `src/lib/translations/ar/dashboard.ts`
**Status:** 🔴 Not Started
**Estimated Time:** 2 hours

Key missing translations:
- Dashboard statistics and widgets
- Date/time selectors
- Activity feeds
- Performance metrics

### Task 2.2: Products Module (80 translations) 🟡
**File:** `src/lib/translations/ar/products.ts`
**Status:** 🔴 Not Started
**Estimated Time:** 3 hours

Key missing translations:
- Product creation forms
- Category management
- Stock management
- Product details

### Task 2.3: Invoices Module (19 translations) 🟡
**File:** `src/lib/translations/ar/invoices.ts`
**Status:** 🔴 Not Started
**Estimated Time:** 1 hour

Key missing translations:
- Invoice creation/editing
- Payment processing
- Invoice actions

## Phase 3: LOW PRIORITY (Week 3-4)

### Task 3.1: Calendar Module (40 translations) 🟢
**File:** `src/lib/translations/ar/calendar.ts`
**Status:** 🔴 Not Started
**Estimated Time:** 2 hours

### Task 3.2: Employees Module (27 translations) 🟢
**File:** `src/lib/translations/ar/employees.ts`
**Status:** 🔴 Not Started
**Estimated Time:** 1.5 hours

### Task 3.3: Other Modules (Various) 🟢
- Quotations (12 translations)
- Settings (7 translations)
- Reports, Financial, etc.

## Implementation Strategy

### Step 1: Create Modular Structure
```bash
mkdir -p src/lib/translations/ar
mkdir -p src/lib/translations/en
```

### Step 2: Start with Common Module
1. Create `src/lib/translations/ar/common.ts`
2. Add all missing common translations
3. Update components to use common translations
4. Test thoroughly

### Step 3: Continue with Customer/Supplier
1. Create module-specific translation files
2. Move existing translations from main file
3. Add missing translations
4. Update component imports
5. Test each module

### Step 4: Systematic Testing
- Test each module after completion
- Verify RTL layout
- Check for missing translations
- Validate Arabic text quality

## Quick Start Commands

### Run Translation Audit
```bash
node scripts/audit-translations.js
```

### Create Module Structure
```bash
# Create common translations first
touch src/lib/translations/ar/common.ts
touch src/lib/translations/en/common.ts

# Create customer translations
touch src/lib/translations/ar/customers.ts
touch src/lib/translations/en/customers.ts

# Create supplier translations
touch src/lib/translations/ar/suppliers.ts
touch src/lib/translations/en/suppliers.ts
```

## Success Metrics

### Week 1 Target
- ✅ Common module: 100% translated
- ✅ Customer module: 100% translated  
- ✅ Supplier module: 100% translated
- ✅ Navigation: 100% translated
- **Target Coverage: 80%**

### Week 2 Target
- ✅ Dashboard module: 100% translated
- ✅ Products module: 100% translated
- ✅ Invoices module: 100% translated
- **Target Coverage: 95%**

### Week 3-4 Target
- ✅ All modules: 100% translated
- ✅ Zero English text in Arabic mode
- ✅ Consistent terminology
- **Target Coverage: 100%**

## Notes
- Focus on user-facing text first
- API error messages can be lower priority
- Test each module immediately after translation
- Use consistent Arabic terminology across modules
- Prioritize pages that users see most often 