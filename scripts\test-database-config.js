#!/usr/bin/env node

/**
 * Database Configuration Test Script
 * Tests the database configuration similar to PHP Capsule setup
 */

// Test database configuration
const { PrismaClient } = require('@prisma/client');

async function testDatabaseConfig() {
  try {
    console.log('🔍 Testing database configuration...\n');

    // Test environment variables
    console.log('📋 Environment Variables:');
    console.log(`DATABASE_URL: ${process.env.DATABASE_URL ? '✅ Set' : '❌ Not set'}`);
    console.log(`NODE_ENV: ${process.env.NODE_ENV || 'development'}`);
    console.log();

    // Test Prisma connection
    try {
      const prisma = new PrismaClient({
        log: ['error', 'warn'],
      });
      
      console.log('🔌 Testing Prisma connection...');
      await prisma.$connect();
      console.log('✅ Prisma connection successful');
      
      // Test a simple query
      const result = await prisma.$queryRaw`SELECT 1 as test`;
      console.log('✅ Database query test successful');
      console.log(`Query result:`, result);
      
      // Test database info
      const dbInfo = await prisma.$queryRaw`SELECT DATABASE() as current_db, VERSION() as version`;
      console.log('✅ Database info retrieved:');
      console.log(`Current Database: ${dbInfo[0]?.current_db || 'Unknown'}`);
      console.log(`MySQL Version: ${dbInfo[0]?.version || 'Unknown'}`);
      
      // Test connection pool settings
      console.log('\n⚙️  Connection Settings:');
      console.log('Prisma client configured with optimized settings');
      
      await prisma.$disconnect();
      console.log('✅ Prisma disconnection successful');
      
    } catch (error) {
      console.error('❌ Prisma connection error:', error.message);
      if (error.code) {
        console.error(`Error code: ${error.code}`);
      }
      if (error.meta) {
        console.error(`Error details:`, error.meta);
      }
    }

    console.log('\n🎉 Database configuration test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testDatabaseConfig(); 