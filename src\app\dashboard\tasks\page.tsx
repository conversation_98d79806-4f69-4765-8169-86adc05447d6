"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Clock,
  User,
  AlertCircle,
  MoreHorizontal,
  Eye,
  Play,
  Pause,
  CheckCircle,
  XCircle
} from "lucide-react"
import { useI18n } from "@/lib/i18n"



const statusColors = {
  NEW: "bg-blue-100 text-blue-800",
  IN_PROGRESS: "bg-yellow-100 text-yellow-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
}

const priorityColors = {
  LOW: "bg-gray-100 text-gray-800",
  MEDIUM: "bg-orange-100 text-orange-800",
  HIGH: "bg-red-100 text-red-800",
  URGENT: "bg-purple-100 text-purple-800",
}

export default function TasksPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    total: 0,
    new: 0,
    inProgress: 0,
    completed: 0,
  })

  // Fetch tasks from API
  const fetchTasks = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/tasks')
      if (response.ok) {
        const data = await response.json()
        setTasks(data.tasks || [])

        // Calculate stats
        const taskList = data.tasks || []
        setStats({
          total: taskList.length,
          new: taskList.filter(t => t.status === 'NEW').length,
          inProgress: taskList.filter(t => t.status === 'IN_PROGRESS').length,
          completed: taskList.filter(t => t.status === 'COMPLETED').length,
        })
      } else {
        console.error('Failed to fetch tasks')
      }
    } catch (error) {
      console.error('Error fetching tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTasks()
  }, [])

  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (task.customer?.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (task.assignedTo?.name || '').toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || task.status === statusFilter
    const matchesPriority = priorityFilter === "all" || task.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesPriority
  })

  const handleViewTask = (task: any) => {
    router.push(`/dashboard/tasks/${task.id}`)
  }

  const handleEditTask = (task: any) => {
    router.push(`/dashboard/tasks/${task.id}/edit`)
  }

  const handleStartTask = async (task: any) => {
    try {
      const response = await fetch(`/api/tasks/${task.id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'IN_PROGRESS' }),
      })

      if (response.ok) {
        alert(`✅ ${t('tasks.taskStartedSuccessfully') || 'Task started successfully'}: "${task.title}"`)
        await fetchTasks()
      } else {
        alert(t('tasks.failedToStartTask') || 'Failed to start task')
      }
    } catch (error) {
      console.error('Error starting task:', error)
      alert(t('tasks.errorStartingTask') || 'Error starting task')
    }
  }

  const handlePauseTask = async (task: any) => {
    try {
      const response = await fetch(`/api/tasks/${task.id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'NEW' }),
      })

      if (response.ok) {
        alert(`✅ ${t('tasks.taskPausedSuccessfully') || 'Task paused successfully'}: "${task.title}"`)
        await fetchTasks()
      } else {
        alert(t('tasks.failedToPauseTask') || 'Failed to pause task')
      }
    } catch (error) {
      console.error('Error pausing task:', error)
      alert(t('tasks.errorPausingTask') || 'Error pausing task')
    }
  }

  const handleCompleteTask = (task: any) => {
    // Navigate to completion page with file upload
    router.push(`/dashboard/tasks/${task.id}/complete`)
  }

  const handleCancelTask = async (task: any) => {
    if (confirm(`${t('tasks.confirmCancelTask') || 'Are you sure you want to cancel task'} "${task.title}"?`)) {
      try {
        const response = await fetch(`/api/tasks/${task.id}/status`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status: 'CANCELLED' }),
        })

        if (response.ok) {
          alert(`✅ ${t('tasks.taskCancelledSuccessfully') || 'Task cancelled successfully'}: "${task.title}"`)
          await fetchTasks()
        } else {
          alert(t('tasks.failedToCancelTask') || 'Failed to cancel task')
        }
      } catch (error) {
        console.error('Error cancelling task:', error)
        alert(t('tasks.errorCancellingTask') || 'Error cancelling task')
      }
    }
  }

  const handleDeleteTask = async (task: any) => {
    if (confirm(`${t('tasks.confirmDeleteTask') || 'Are you sure you want to delete task'} "${task.title}"?`)) {
      try {
        const response = await fetch(`/api/tasks/${task.id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          alert(`✅ ${t('tasks.taskDeletedSuccessfully') || 'Task deleted successfully'}: "${task.title}"`)
          await fetchTasks()
        } else {
          alert(t('tasks.failedToDeleteTask') || 'Failed to delete task')
        }
      } catch (error) {
        console.error('Error deleting task:', error)
        alert(t('tasks.errorDeletingTask') || 'Error deleting task')
      }
    }
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('tasks.title') || 'Tasks'}</h2>
          <p className="text-muted-foreground">
            {t('tasks.description') || 'Manage and track work assignments'}
          </p>
        </div>
        <Button onClick={() => router.push('/dashboard/tasks/create')}>
          <Plus className="mr-2 h-4 w-4" />
          {t('tasks.addTask') || 'Add Task'}
        </Button>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('tasks.searchTasks') || 'Search tasks...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder={t('tasks.status') || 'Status'} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('tasks.allStatus') || 'All Status'}</SelectItem>
            <SelectItem value="NEW">{t('tasks.new') || 'New'}</SelectItem>
            <SelectItem value="IN_PROGRESS">{t('tasks.inProgress') || 'In Progress'}</SelectItem>
            <SelectItem value="COMPLETED">{t('tasks.completed') || 'Completed'}</SelectItem>
            <SelectItem value="CANCELLED">{t('tasks.cancelled') || 'Cancelled'}</SelectItem>
          </SelectContent>
        </Select>

        <Select value={priorityFilter} onValueChange={setPriorityFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder={t('tasks.priority') || 'Priority'} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('tasks.allPriority') || 'All Priority'}</SelectItem>
            <SelectItem value="LOW">{t('tasks.low') || 'Low'}</SelectItem>
            <SelectItem value="MEDIUM">{t('tasks.medium') || 'Medium'}</SelectItem>
            <SelectItem value="HIGH">{t('tasks.high') || 'High'}</SelectItem>
            <SelectItem value="URGENT">{t('tasks.urgent') || 'Urgent'}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <div className="rounded-lg border p-6 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-blue-700">{stats.total}</div>
              <div className="text-sm text-blue-600 font-medium">{t('tasks.totalTasks') || 'Total Tasks'}</div>
            </div>
            <div className="p-3 bg-blue-500 rounded-full">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border p-6 bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-yellow-700">{stats.new}</div>
              <div className="text-sm text-yellow-600 font-medium">{t('tasks.newTasks') || 'New Tasks'}</div>
            </div>
            <div className="p-3 bg-yellow-500 rounded-full">
              <Plus className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border p-6 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-orange-700">{stats.inProgress}</div>
              <div className="text-sm text-orange-600 font-medium">{t('tasks.inProgress') || 'In Progress'}</div>
            </div>
            <div className="p-3 bg-orange-500 rounded-full">
              <Play className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-green-700">{stats.completed}</div>
              <div className="text-sm text-green-600 font-medium">{t('tasks.completed') || 'Completed'}</div>
            </div>
            <div className="p-3 bg-green-500 rounded-full">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      <div className="table-container">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('tasks.task') || 'Task'}</TableHead>
              <TableHead>{t('tasks.customer') || 'Customer'}</TableHead>
              <TableHead>{t('tasks.assignedTo') || 'Assigned To'}</TableHead>
              <TableHead>{t('tasks.priority') || 'Priority'}</TableHead>
              <TableHead>{t('tasks.status') || 'Status'}</TableHead>
              <TableHead>{t('tasks.estHours') || 'Est. Hours'}</TableHead>
              <TableHead className="text-right">{t('tasks.actions') || 'Actions'}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTasks.map((task) => (
              <TableRow key={task.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{task.title}</div>
                    <div className="text-sm text-muted-foreground">
                      {task.description}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{task.customer?.name || (t('tasks.noCustomer') || 'No Customer')}</TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <User className="mr-1 h-3 w-3" />
                    {task.assignedTo?.name || (t('tasks.unassigned') || 'Unassigned')}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={priorityColors[task.priority as keyof typeof priorityColors]}>
                    {task.priority}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge className={statusColors[task.status as keyof typeof statusColors]}>
                    {task.status.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Clock className="mr-1 h-3 w-3" />
                    {task.estimatedHours}h
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewTask(task)}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t('tasks.viewDetails') || 'View Details'}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditTask(task)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('tasks.editTask') || 'Edit Task'}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {task.status === 'NEW' && (
                        <DropdownMenuItem
                          onClick={() => handleStartTask(task)}
                          className="text-green-600 focus:text-green-600"
                        >
                          <Play className="mr-2 h-4 w-4" />
                          {t('tasks.startTask') || 'Start Task'}
                        </DropdownMenuItem>
                      )}
                      {task.status === 'IN_PROGRESS' && (
                        <>
                          <DropdownMenuItem
                            onClick={() => handlePauseTask(task)}
                            className="text-yellow-600 focus:text-yellow-600"
                          >
                            <Pause className="mr-2 h-4 w-4" />
                            {t('tasks.pauseTask') || 'Pause Task'}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleCompleteTask(task)}
                            className="text-green-600 focus:text-green-600"
                          >
                            <CheckCircle className="mr-2 h-4 w-4" />
                            {t('tasks.completeTask') || 'Complete Task'}
                          </DropdownMenuItem>
                        </>
                      )}
                      {(task.status === 'NEW' || task.status === 'IN_PROGRESS') && (
                        <DropdownMenuItem
                          onClick={() => handleCancelTask(task)}
                          className="text-orange-600 focus:text-orange-600"
                        >
                          <XCircle className="mr-2 h-4 w-4" />
                          {t('tasks.cancelTask') || 'Cancel Task'}
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteTask(task)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('tasks.delete') || 'Delete'}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
