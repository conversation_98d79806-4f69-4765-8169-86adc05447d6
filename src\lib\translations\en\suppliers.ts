export const suppliers = {
  // Page titles and descriptions
  title: "Suppliers",
  dashboard: "Supplier Dashboard",
  dashboardDescription: "Overview of your supplier network and procurement metrics",
  viewAllSuppliers: "View All Suppliers",
  
  // Supplier management
  addSupplier: "Add Supplier",
  editSupplier: "Edit Supplier",
  createSupplier: "Create Supplier",
  updateSupplier: "Update Supplier",
  deleteSupplier: "Delete Supplier",
  confirmDelete: "Are you sure you want to delete this supplier?",
  confirmDeleteSupplier: "Are you sure you want to delete this supplier?",
  
  // Supplier information
  supplierName: "Supplier Name",
  supplierEmail: "Supplier Email",
  supplierPhone: "Supplier Phone",
  supplierAddress: "Supplier Address",
  supplierCompany: "Supplier Company",
  supplierDetails: "Supplier Details",
  supplierInformation: "Supplier Information",
  supplierNotFound: "Supplier not found",
  supplierDetailsAndHistory: "Supplier Details and Purchase History",
  supplierSince: "Supplier Since",
  lastOrder: "Last Order",
  lastOrderDate: "Last Order Date",
  contactPerson: "Contact Person",
  taxNumber: "Tax Number",
  
  // Search and filters
  searchSuppliers: "Search suppliers...",
  noSuppliers: "No suppliers found",
  noSuppliersFound: "No suppliers found",
  
  // Financial information
  totalSuppliers: "Total Suppliers",
  activeSuppliers: "Active Suppliers",
  totalPurchases: "Total Purchases",
  outstandingPayments: "Outstanding Payments",
  supplierSummary: "Supplier Summary",
  thisMonth: "this month",
  fromLastMonth: "from last month",
  avgOrderValue: "Avg. Order Value",
  reliableSuppliers: "Reliable Suppliers",
  inactiveSuppliers: "Inactive Suppliers",
  needReEngagement: "Need re-engagement",
  
  // Purchase orders
  purchaseOrders: "Purchase Orders",
  purchaseOrderNumber: "Purchase Order Number",
  createPurchaseOrder: "Create Purchase Order",
  noPurchaseOrdersFound: "No purchase orders found for this supplier",
  viewPurchaseDetails: "View Purchase Details",
  editOrder: "Edit Order",
  totalOrders: "Total Orders",
  paidOrders: "Paid Orders",
  receivedOrders: "Received Orders",
  pendingOrders: "Pending Orders",
  overdueOrders: "Overdue Orders",
  
  // Payments
  paymentHistory: "Payment History",
  createPayout: "Create Payout",
  lastPayment: "Last Payment",
  method: "Method",
  reference: "Reference",
  noPaymentsFound: "No payments found for this supplier",
  paymentStatus: "Payment Status",
  
  // Products
  supplierProducts: "Supplier Products",
  products: "Products",
  noProductsFound: "No products found for this supplier",
  sku: "SKU",
  unitPrice: "Unit Price",
  stock: "Stock",
  
  // Actions
  quickActions: "Quick Actions",
  sendEmail: "Send Email",
  callSupplier: "Call Supplier",
  viewDetails: "View Details",
  actions: "Actions",
  
  // Tabs
  ordersTab: "Orders",
  paymentsTab: "Payments",
  productsTab: "Products",
  
  // Other fields
  dueDate: "Due Date",
  balance: "Balance",
  status: "Status",
  active: "Active",
  inactive: "Inactive",
  
  // UI elements
  openMenu: "Open Menu",
  back: "Back",
  loading: "Loading...",
  
  // Messages
  supplierAdded: "Supplier added successfully",
  supplierUpdated: "Supplier updated successfully",
  supplierDeleted: "Supplier deleted successfully"
} as const; 