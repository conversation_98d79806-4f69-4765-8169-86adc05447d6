"use client"

import { useRout<PERSON> } from "next/navigation"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  FunnelChart,
  Funnel,
} from "recharts"
import {
  Users,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  UserPlus,
  Plus,
  Eye,
  Star,
  Phone,
  Mail,
  Calendar
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

// Lead metrics will be fetched from API

const leadStatusData = [
  { name: 'New', value: 45, color: '#3b82f6' },
  { name: 'Qualified', value: 32, color: '#10b981' },
  { name: 'Proposal', value: 18, color: '#f59e0b' },
  { name: 'Negotiation', value: 12, color: '#f97316' },
  { name: 'Won', value: 29, color: '#22c55e' },
  { name: 'Lost', value: 20, color: '#ef4444' },
]

const leadSourceData = [
  { source: 'Website', leads: 45, conversions: 12, value: 125000 },
  { source: 'Referral', leads: 32, conversions: 8, value: 89000 },
  { source: 'Cold Call', leads: 28, conversions: 5, value: 67000 },
  { source: 'Social Media', leads: 25, conversions: 6, value: 78000 },
  { source: 'Trade Show', leads: 18, conversions: 4, value: 56000 },
  { source: 'Email Campaign', leads: 8, conversions: 2, value: 23000 },
]

const monthlyData = [
  { month: 'Sep', leads: 134, conversions: 18, value: 385000 },
  { month: 'Oct', leads: 142, conversions: 22, value: 420000 },
  { month: 'Nov', leads: 148, conversions: 25, value: 445000 },
  { month: 'Dec', leads: 152, conversions: 27, value: 465000 },
  { month: 'Jan', leads: 156, conversions: 29, value: 485600 },
]

const salesFunnelData = [
  { name: 'Leads', value: 156, fill: '#3b82f6' },
  { name: 'Qualified', value: 89, fill: '#10b981' },
  { name: 'Proposal', value: 42, fill: '#f59e0b' },
  { name: 'Negotiation', value: 29, fill: '#f97316' },
  { name: 'Won', value: 18, fill: '#22c55e' },
]

const topLeads = [
  {
    id: '3',
    name: 'عبدالله راشد البلوشي',
    nameEn: 'Abdullah Rashid Al-Balushi',
    company: 'مجموعة البلوشي',
    status: 'PROPOSAL',
    priority: 'HIGH',
    estimatedValue: 25000,
    leadScore: 91,
    assignedTo: 'Omar Al-Hinai',
    nextFollowUp: '2024-01-23',
  },
  {
    id: '4',
    name: 'سارة محمد الزدجالية',
    nameEn: 'Sara Mohammed Al-Zadjali',
    company: 'شركة الزدجالي للخدمات',
    status: 'NEGOTIATION',
    priority: 'HIGH',
    estimatedValue: 12000,
    leadScore: 88,
    assignedTo: 'Fatima Al-Zahra',
    nextFollowUp: '2024-01-21',
  },
  {
    id: '6',
    name: 'أمل يوسف الريامية',
    nameEn: 'Amal Yousuf Al-Riyami',
    company: 'شركة الريامي للتطوير',
    status: 'WON',
    priority: 'HIGH',
    estimatedValue: 18000,
    leadScore: 95,
    assignedTo: 'Sarah Al-Balushi',
    nextFollowUp: null,
  },
  {
    id: '1',
    name: 'محمد أحمد الكندي',
    nameEn: 'Mohammed Ahmed Al-Kindi',
    company: 'شركة الكندي للتجارة',
    status: 'NEW',
    priority: 'HIGH',
    estimatedValue: 15000,
    leadScore: 85,
    assignedTo: 'Sarah Al-Balushi',
    nextFollowUp: '2024-01-22',
  },
  {
    id: '2',
    name: 'فاطمة سالم الهنائية',
    nameEn: 'Fatima Salem Al-Hinai',
    company: 'مؤسسة الهنائي للاستشارات',
    status: 'QUALIFIED',
    priority: 'MEDIUM',
    estimatedValue: 8500,
    leadScore: 72,
    assignedTo: 'Ahmed Al-Rashid',
    nextFollowUp: '2024-01-24',
  },
]

const upcomingFollowUps = [
  {
    id: '4',
    name: 'سارة محمد الزدجالية',
    nameEn: 'Sara Mohammed Al-Zadjali',
    company: 'شركة الزدجالي للخدمات',
    followUpDate: '2024-01-21',
    followUpTime: '10:00 AM',
    type: 'Call',
    assignedTo: 'Fatima Al-Zahra',
  },
  {
    id: '1',
    name: 'محمد أحمد الكندي',
    nameEn: 'Mohammed Ahmed Al-Kindi',
    company: 'شركة الكندي للتجارة',
    followUpDate: '2024-01-22',
    followUpTime: '2:00 PM',
    type: 'Meeting',
    assignedTo: 'Sarah Al-Balushi',
  },
  {
    id: '3',
    name: 'عبدالله راشد البلوشي',
    nameEn: 'Abdullah Rashid Al-Balushi',
    company: 'مجموعة البلوشي',
    followUpDate: '2024-01-23',
    followUpTime: '11:30 AM',
    type: 'Proposal Review',
    assignedTo: 'Omar Al-Hinai',
  },
  {
    id: '2',
    name: 'فاطمة سالم الهنائية',
    nameEn: 'Fatima Salem Al-Hinai',
    company: 'مؤسسة الهنائي للاستشارات',
    followUpDate: '2024-01-24',
    followUpTime: '9:00 AM',
    type: 'Call',
    assignedTo: 'Ahmed Al-Rashid',
  },
]

export default function LeadsDashboard() {
  const router = useRouter()
  const { t } = useI18n()
  const [leadMetrics, setLeadMetrics] = useState({
    totalLeads: 0,
    totalLeadsGrowth: 0,
    activeLeads: 0,
    activeLeadsGrowth: 0,
    conversionRate: 0,
    conversionRateGrowth: 0,
    pipelineValue: 0,
    pipelineValueGrowth: 0,
  })
  const [recentLeads, setRecentLeads] = useState([])
  const [upcomingFollowUps, setUpcomingFollowUps] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchLeadData()
  }, [])

  const fetchLeadData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/leads/dashboard')
      if (response.ok) {
        const data = await response.json()
        setLeadMetrics(data.metrics || leadMetrics)
        setRecentLeads(data.recentLeads || [])
        setUpcomingFollowUps(data.upcomingFollowUps || [])
      }
    } catch (error) {
      console.error('Error fetching lead data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    const colors = {
      NEW: "bg-blue-100 text-blue-800",
      QUALIFIED: "bg-green-100 text-green-800",
      PROPOSAL: "bg-yellow-100 text-yellow-800",
      NEGOTIATION: "bg-orange-100 text-orange-800",
      WON: "bg-emerald-100 text-emerald-800",
      LOST: "bg-red-100 text-red-800"
    }
    return colors[status] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('leads.dashboardTitle')}</h2>
          <p className="text-muted-foreground">
            {t('leads.dashboardOverview')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/leads')}>
            <Users className="mr-2 h-4 w-4" />
            {t('leads.viewAllLeads')}
          </Button>
          <Button onClick={() => router.push('/dashboard/leads/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('leads.addLead')}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('leads.totalLeads')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{leadMetrics.totalLeads}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              +{leadMetrics.totalLeadsGrowth} {t('leads.fromLastMonth')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('leads.activeLeads')}</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{leadMetrics.activeLeads}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              +{leadMetrics.activeLeadsGrowth} {t('leads.fromLastMonth')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('leads.conversionRate')}</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{leadMetrics.conversionRate}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              +{leadMetrics.conversionRateGrowth}% {t('leads.fromLastMonth')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('leads.pipelineValue')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(leadMetrics.pipelineValue)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              +{leadMetrics.pipelineValueGrowth}% {t('leads.fromLastMonth')}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Monthly Performance */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>{t('leads.monthlyPerformance')}</CardTitle>
          </CardHeader>
          <CardContent className="pl-2">
            <ResponsiveContainer width="100%" height={350}>
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip
                  formatter={(value, name) => [
                    name === 'value' ? formatCurrency(value as number) : value,
                    name === 'value' ? t('leads.pipelineValue') : name === 'conversions' ? t('leads.conversions') : t('leads.leads')
                  ]}
                />
                <Bar yAxisId="left" dataKey="leads" fill="#3b82f6" name={t('leads.leads')} />
                <Line yAxisId="left" type="monotone" dataKey="conversions" stroke="#10b981" strokeWidth={2} name={t('leads.conversions')} />
                <Line yAxisId="right" type="monotone" dataKey="value" stroke="#f59e0b" strokeWidth={2} name={t('leads.pipelineValue')} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Lead Status Distribution */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>{t('leads.leadStatusDistribution')}</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={350}>
              <PieChart>
                <Pie
                  data={leadStatusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${t('leads.' + name.toLowerCase())}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {leadStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Tables Section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Top Leads */}
        <Card>
          <CardHeader>
            <CardTitle>{t('leads.topLeads')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('leads.lead')}</TableHead>
                  <TableHead>{t('leads.status')}</TableHead>
                  <TableHead>{t('leads.value')}</TableHead>
                  <TableHead>{t('leads.score')}</TableHead>
                  <TableHead className="text-right">{t('leads.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {topLeads.map((lead) => (
                  <TableRow key={lead.id}>
                    <TableCell>
                      <div>
                        <Button
                          variant="link"
                          className="p-0 h-auto font-medium"
                          onClick={() => router.push(`/dashboard/leads/${lead.id}`)}
                        >
                          {lead.name}
                        </Button>
                        <div className="text-sm text-muted-foreground">{lead.nameEn}</div>
                        <div className="text-sm text-muted-foreground">{lead.company}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(lead.status)}>
                        {lead.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium text-green-600">
                      {formatCurrency(lead.estimatedValue)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Star className={`h-4 w-4 ${lead.leadScore >= 80 ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`} />
                        <span className="text-sm font-medium">{lead.leadScore}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/dashboard/leads/${lead.id}`)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Upcoming Follow-ups */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-blue-500" />
              <span>{t('leads.upcomingFollowUps')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('leads.lead')}</TableHead>
                  <TableHead>{t('leads.dateTime')}</TableHead>
                  <TableHead>{t('leads.type')}</TableHead>
                  <TableHead className="text-right">{t('leads.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {upcomingFollowUps.map((followUp) => (
                  <TableRow key={followUp.id}>
                    <TableCell>
                      <div>
                        <Button
                          variant="link"
                          className="p-0 h-auto font-medium"
                          onClick={() => router.push(`/dashboard/leads/${followUp.id}`)}
                        >
                          {followUp.name}
                        </Button>
                        <div className="text-sm text-muted-foreground">{followUp.nameEn}</div>
                        <div className="text-sm text-muted-foreground">{followUp.company}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">{new Date(followUp.followUpDate).toLocaleDateString()}</div>
                        <div className="text-muted-foreground">{followUp.followUpTime}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{followUp.type}</Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/dashboard/leads/${followUp.id}`)}
                      >
                        {followUp.type === 'Call' ? <Phone className="h-4 w-4" /> : <Calendar className="h-4 w-4" />}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Lead Source Performance */}
      <Card>
        <CardHeader>
          <CardTitle>{t('leads.leadSourcePerformance')}</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={leadSourceData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="source" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip
                formatter={(value, name) => [
                  name === 'value' ? formatCurrency(value as number) : value,
                  name === 'value' ? t('leads.pipelineValue') : name === 'conversions' ? t('leads.conversions') : t('leads.leads')
                ]}
              />
              <Bar yAxisId="left" dataKey="leads" fill="#3b82f6" name={t('leads.leads')} />
              <Bar yAxisId="left" dataKey="conversions" fill="#10b981" name={t('leads.conversions')} />
              <Bar yAxisId="right" dataKey="value" fill="#f59e0b" name={t('leads.pipelineValue')} />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  )
}
