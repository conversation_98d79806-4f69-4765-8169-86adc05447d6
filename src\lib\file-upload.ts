import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { randomUUID } from 'crypto'

export interface UploadResult {
  url: string
  path: string
  filename: string
}

export async function uploadFile(file: File, projectId: string): Promise<UploadResult> {
  try {
    // Convert File to ArrayBuffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Generate unique filename
    const fileExtension = file.name.split('.').pop() || ''
    const uniqueFilename = `${randomUUID()}.${fileExtension}`
    
    // Create directory structure: public/uploads/projects/{projectId}/
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'projects', projectId)
    await mkdir(uploadDir, { recursive: true })
    
    // Save file
    const filePath = join(uploadDir, uniqueFilename)
    await writeFile(filePath, buffer)
    
    // Generate public URL
    const publicUrl = `/uploads/projects/${projectId}/${uniqueFilename}`
    
    return {
      url: publicUrl,
      path: filePath,
      filename: uniqueFilename
    }
  } catch (error) {
    console.error('Error uploading file:', error)
    throw new Error('Failed to upload file')
  }
}

export function getFileTypeFromMimeType(mimeType: string): string {
  const mimeToExtension: { [key: string]: string } = {
    'application/pdf': 'pdf',
    'application/msword': 'doc',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'application/vnd.ms-excel': 'xls',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'application/vnd.ms-powerpoint': 'ppt',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
    'text/plain': 'txt',
    'text/html': 'html',
    'text/css': 'css',
    'application/javascript': 'js',
    'application/json': 'json',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/bmp': 'bmp',
    'image/webp': 'webp',
    'image/svg+xml': 'svg'
  }
  
  return mimeToExtension[mimeType] || 'file'
} 