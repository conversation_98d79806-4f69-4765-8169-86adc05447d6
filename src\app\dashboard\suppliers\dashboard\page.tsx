"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts"
import {
  Truck,
  DollarSign,
  TrendingUp,
  Calendar,
  Plus,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Package,
  FileText,
  Clock,
  CheckCircle,
  Building
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

// Supplier stats will be fetched from API

// Chart data will be fetched from API
const monthlyData = []
const supplierTypeData = []
const paymentStatusData = []

export default function SupplierDashboard() {
  const router = useRouter()
  const { t } = useI18n()
  const [supplierStats, setSupplierStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    newThisMonth: 0,
    totalPurchases: 0,
    avgOrderValue: 0,
    topSuppliers: 0,
    reliableSuppliers: 0
  })
  const [recentSuppliers, setRecentSuppliers] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchSupplierData()
  }, [])

  const fetchSupplierData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/suppliers/dashboard')
      if (response.ok) {
        const data = await response.json()
        setSupplierStats(data.stats || supplierStats)
        setRecentSuppliers(data.recentSuppliers || [])
      }
    } catch (error) {
      console.error('Error fetching supplier data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('suppliers.dashboard')}</h2>
          <p className="text-muted-foreground">
            {t('suppliers.dashboardDescription')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/suppliers')}>
            <Truck className="mr-2 h-4 w-4" />
            {t('suppliers.viewAllSuppliers')}
          </Button>
          <Button onClick={() => router.push('/dashboard/suppliers/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('suppliers.addSupplier')}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('suppliers.totalSuppliers')}</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{supplierStats.total}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +{supplierStats.newThisMonth} {t('suppliers.thisMonth')}
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('suppliers.totalPurchases')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(supplierStats.totalPurchases)}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +18% {t('suppliers.fromLastMonth')}
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('suppliers.avgOrderValue')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(supplierStats.avgOrderValue)}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +12% {t('suppliers.fromLastMonth')}
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('suppliers.reliableSuppliers')}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{supplierStats.reliableSuppliers}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-gray-600 flex items-center">
                <Minus className="h-3 w-3 mr-1" />
                {Math.round((supplierStats.reliableSuppliers / supplierStats.total) * 100)}% of total
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Supplier Status Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('suppliers.activeSuppliers')}</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{supplierStats.active}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((supplierStats.active / supplierStats.total) * 100)}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('suppliers.inactiveSuppliers')}</CardTitle>
            <Clock className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{supplierStats.inactive}</div>
            <p className="text-xs text-muted-foreground">
              {t('suppliers.needReEngagement')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Suppliers</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{supplierStats.topSuppliers}</div>
            <p className="text-xs text-muted-foreground">
              High-volume suppliers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding Payments</CardTitle>
            <Package className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{formatCurrency(24600)}</div>
            <p className="text-xs text-muted-foreground">
              To 8 suppliers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Monthly Procurement Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Procurement Trend</CardTitle>
            <CardDescription>
              New suppliers and purchase volume over the last 5 months
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip
                  formatter={(value, name) => [
                    name === 'suppliers' ? value : formatCurrency(value as number),
                    name === 'suppliers' ? 'New Suppliers' : 'Purchases'
                  ]}
                />
                <Bar yAxisId="left" dataKey="suppliers" fill="#3b82f6" />
                <Line yAxisId="right" type="monotone" dataKey="purchases" stroke="#10b981" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Supplier Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Supplier Type Distribution</CardTitle>
            <CardDescription>
              Breakdown of suppliers by location and type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={supplierTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {supplierTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Suppliers */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Suppliers</CardTitle>
              <CardDescription>
                Latest suppliers and their procurement status
              </CardDescription>
            </div>
            <Button variant="outline" onClick={() => router.push('/dashboard/suppliers')}>
              <Eye className="mr-2 h-4 w-4" />
              View All
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Supplier</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Total Purchases</TableHead>
                <TableHead>Purchase Orders</TableHead>
                <TableHead>Outstanding</TableHead>
                <TableHead>Last Order</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentSuppliers.map((supplier) => (
                <TableRow key={supplier.id}>
                  <TableCell>
                    <div>
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium"
                        onClick={() => router.push(`/dashboard/suppliers/${supplier.id}`)}
                      >
                        {supplier.name}
                      </Button>
                      <div className="text-sm text-muted-foreground">{supplier.company}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{supplier.contactPerson}</div>
                      <div className="text-sm text-muted-foreground">{supplier.phone}</div>
                      <div className="text-sm text-muted-foreground">{supplier.email}</div>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {formatCurrency(supplier.totalPurchases)}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{supplier.purchaseOrderCount} orders</Badge>
                  </TableCell>
                  <TableCell>
                    {supplier.outstandingPayments > 0 ? (
                      <span className="text-orange-600 font-medium">
                        {formatCurrency(supplier.outstandingPayments)}
                      </span>
                    ) : (
                      <span className="text-green-600">Paid</span>
                    )}
                  </TableCell>
                  <TableCell>{new Date(supplier.lastOrder).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Badge
                      variant={supplier.status === 'active' ? 'default' : 'secondary'}
                      className={supplier.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                    >
                      {supplier.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/dashboard/suppliers/${supplier.id}`)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}