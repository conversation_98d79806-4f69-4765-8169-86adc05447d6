"use client"

import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, CreditCard, ShoppingCart, TrendingUp, Hash } from "lucide-react"
import { useI18n } from '@/lib/i18n'
import { useEffect, useState } from 'react'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  LineChart,
  Line,
} from 'recharts'
import { formatCurrency, formatDate } from '@/lib/localization'

// Define a comprehensive interface for the stats payload
interface ExpenseStats {
  overview: {
    totalExpenses: number;
    totalAmount: number;
    avgExpenseAmount: number;
    topCategory: { name: string; nameAr: string; amount: number } | null;
  };
  expensesByType: { name: string; nameAr: string; amount: number }[];
  expensesByMonth: { month: string; amount: number }[];
  recentExpenses: any[];
}

export default function ExpenseAnalysis() {
  const router = useRouter()
  const { t, language } = useI18n()
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<ExpenseStats | null>(null)
  const [period, setPeriod] = useState('90d')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true)
        setError(null)
        const res = await fetch(`/api/expenses/stats?period=${period}`)
        if (!res.ok) {
          const errorData = await res.json()
          throw new Error(errorData.details || 'Failed to load expense stats')
        }
        const data: ExpenseStats = await res.json()
        setStats(data)
      } catch (err: any) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }
    fetchStats()
  }, [period])

  const getCategoryName = (item: { name: string, nameAr: string }) => {
    return language === 'ar' ? (item.nameAr || item.name) : item.name
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        {t('finance.loadingReports') || 'جاري تحميل التقرير...'}
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64 text-red-500">
        <h3>{t('common.error')}</h3>
        <p>{error}</p>
      </div>
    )
  }

  if (!stats) return null

  const { overview, expensesByType, expensesByMonth, recentExpenses } = stats
  const topCategoryName = overview.topCategory ? getCategoryName(overview.topCategory) : 'N/A'

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back') || 'رجوع'}
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {t('finance.financial.reports.reportTypes.expenseanalysis.name') || 'تحليل المصروفات'}
          </h2>
        </div>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="30d">آخر 30 يومًا</SelectItem>
            <SelectItem value="90d">آخر 90 يومًا</SelectItem>
            <SelectItem value="ytd">منذ بداية العام</SelectItem>
            <SelectItem value="all">كل الأوقات</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المصروفات</CardTitle>
            <CreditCard className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{formatCurrency(overview.totalAmount)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">عدد المعاملات</CardTitle>
            <Hash className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.totalExpenses}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط المصروف</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(overview.avgExpenseAmount)}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">أعلى فئة إنفاق</CardTitle>
            <ShoppingCart className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{topCategoryName}</div>
            <p className="text-xs text-muted-foreground">
              {overview.topCategory ? formatCurrency(overview.topCategory.amount) : ''}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>اتجاه المصروفات الشهري</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={expensesByMonth}>
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
                <Line type="monotone" dataKey="amount" stroke="#ef4444" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>المصروفات حسب الفئة</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={expensesByType.map(e => ({...e, name: getCategoryName(e)}))}>
                <XAxis dataKey="name" angle={-45} textAnchor="end" height={60} />
                <YAxis />
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
                <Bar dataKey="amount" fill="#8b5cf6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Expenses Table */}
      <Card>
        <CardHeader>
          <CardTitle>أحدث المصروفات</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>التاريخ</TableHead>
                <TableHead>الوصف</TableHead>
                <TableHead>الفئة</TableHead>
                <TableHead>المشروع</TableHead>
                <TableHead className="text-right">المبلغ</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentExpenses.map((exp) => (
                <TableRow key={exp.id}>
                  <TableCell>{formatDate(exp.createdAt, language)}</TableCell>
                  <TableCell>{exp.description}</TableCell>
                  <TableCell>{language === 'ar' ? exp.expenseType.nameAr : exp.expenseType.name}</TableCell>
                  <TableCell>{exp.project?.name || '-'}</TableCell>
                  <TableCell className="text-right">{formatCurrency(exp.amount)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
} 