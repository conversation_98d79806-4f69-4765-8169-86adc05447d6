import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/permissions - Get all permissions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const moduleParam = searchParams.get('module')
    const groupByModule = searchParams.get('groupByModule') === 'true'

    const where = {
      isActive: true,
      ...(moduleParam && { module: moduleParam }),
    }

    const permissions = await prisma.permission.findMany({
      where,
      orderBy: [
        { module: 'asc' },
        { action: 'asc' },
      ],
    })

    if (groupByModule) {
      // Group permissions by module
      const groupedPermissions = permissions.reduce((acc, permission) => {
        if (!acc[permission.module]) {
          acc[permission.module] = []
        }
        acc[permission.module].push(permission)
        return acc
      }, {} as Record<string, typeof permissions>)

      return NextResponse.json(groupedPermissions)
    }

    return NextResponse.json(permissions)
  } catch (error) {
    console.error('Error fetching permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch permissions' },
      { status: 500 }
    )
  }
}

// POST /api/permissions - Create new permission
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, nameAr, module, action, resource, description } = body

    // Validate required fields
    if (!name || !module || !action) {
      return NextResponse.json(
        { error: 'Name, module, and action are required' },
        { status: 400 }
      )
    }

    // Check if permission already exists
    const existingPermission = await prisma.permission.findFirst({
      where: {
        module,
        action,
        resource: resource || null,
      },
    })

    if (existingPermission) {
      return NextResponse.json(
        { error: 'Permission already exists for this module/action/resource combination' },
        { status: 400 }
      )
    }

    // Create permission
    const permission = await prisma.permission.create({
      data: {
        name,
        nameAr,
        module,
        action,
        resource,
        description,
      },
    })

    return NextResponse.json(permission, { status: 201 })
  } catch (error) {
    console.error('Error creating permission:', error)
    return NextResponse.json(
      { error: 'Failed to create permission' },
      { status: 500 }
    )
  }
}

// PUT /api/permissions - Update permission
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, nameAr, description, isActive } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Permission ID is required' },
        { status: 400 }
      )
    }

    // Check if permission exists
    const existingPermission = await prisma.permission.findUnique({
      where: { id },
    })

    if (!existingPermission) {
      return NextResponse.json(
        { error: 'Permission not found' },
        { status: 404 }
      )
    }

    // Update permission (module, action, resource should not be changed)
    const permission = await prisma.permission.update({
      where: { id },
      data: {
        name,
        nameAr,
        description,
        isActive,
      },
    })

    return NextResponse.json(permission)
  } catch (error) {
    console.error('Error updating permission:', error)
    return NextResponse.json(
      { error: 'Failed to update permission' },
      { status: 500 }
    )
  }
}
