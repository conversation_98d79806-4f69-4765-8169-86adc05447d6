/**
 * Quotation API utility - replaces localStorage with database API calls
 */

export interface Quotation {
  id: string
  number: string
  date: string
  validUntil: string
  customer: string
  customerData?: any
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED'
  subtotal: number
  taxAmount: number
  total: number
  items: QuotationItem[]
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface QuotationItem {
  id: string
  name: string
  nameAr?: string
  quantity: number
  price: number
  total: number
  unit?: string
}

// Get all quotations from API
export const getQuotations = async (): Promise<Quotation[]> => {
  try {
    const response = await fetch('/api/quotations')
    if (!response.ok) {
      throw new Error('Failed to fetch quotations')
    }
    const data = await response.json()
    // API returns { quotations: [], pagination: {} }, we need just the quotations array
    return data.quotations || []
  } catch (error) {
    console.error('Error loading quotations:', error)
    return []
  }
}

// Save quotation via API
export const saveQuotation = async (quotation: any): Promise<Quotation | null> => {
  try {
    const response = await fetch('/api/quotations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(quotation),
    })

    if (!response.ok) {
      throw new Error('Failed to save quotation')
    }

    return await response.json()
  } catch (error) {
    console.error('Error saving quotation:', error)
    return null
  }
}

// Add new quotation (alias for saveQuotation)
export const addQuotation = async (quotation: any): Promise<Quotation | null> => {
  return await saveQuotation(quotation)
}

// Update quotation via API
export const updateQuotation = async (id: string, updates: Partial<Quotation>): Promise<Quotation | null> => {
  try {
    const response = await fetch(`/api/quotations/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      throw new Error('Failed to update quotation')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating quotation:', error)
    return null
  }
}

// Delete quotation via API
export const deleteQuotation = async (id: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/quotations/${id}`, {
      method: 'DELETE',
    })

    return response.ok
  } catch (error) {
    console.error('Error deleting quotation:', error)
    return false
  }
}

// Get quotation by ID via API
export const getQuotationById = async (id: string): Promise<Quotation | null> => {
  try {
    const response = await fetch(`/api/quotations/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch quotation')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading quotation:', error)
    return null
  }
}

// Generate unique quotation ID
const generateQuotationId = (): string => {
  return `quot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Generate quotation number
const generateQuotationNumber = (sequence: number): string => {
  return `QUO-${sequence.toString().padStart(3, '0')}`
}

// Default quotations for demo
const getDefaultQuotations = (): Quotation[] => {
  return [
    {
      id: "1",
      number: "QUO-001",
      date: "2024-01-15",
      validUntil: "2024-02-15",
      customer: "ABC Corporation",
      status: "PENDING",
      subtotal: 1000.00,
      taxAmount: 150.00,
      total: 1150.00,
      items: [
        { id: "1", name: "Business Cards", quantity: 500, price: 2.00, total: 1000.00, unit: "pieces" }
      ],
      notes: "Thank you for considering our services!",
      createdAt: "2024-01-15T10:00:00.000Z",
      updatedAt: "2024-01-15T10:00:00.000Z"
    },
    {
      id: "2",
      number: "QUO-002",
      date: "2024-01-16",
      validUntil: "2024-02-16",
      customer: "XYZ Enterprises",
      status: "APPROVED",
      subtotal: 750.00,
      taxAmount: 112.50,
      total: 862.50,
      items: [
        { id: "1", name: "Brochure Design", quantity: 10, price: 75.00, total: 750.00, unit: "designs" }
      ],
      notes: "Professional brochure design service",
      createdAt: "2024-01-16T10:00:00.000Z",
      updatedAt: "2024-01-16T10:00:00.000Z"
    },
    {
      id: "3",
      number: "QUO-003",
      date: "2024-01-17",
      validUntil: "2024-02-17",
      customer: "Legal Associates",
      status: "CONVERTED",
      subtotal: 500.00,
      taxAmount: 75.00,
      total: 575.00,
      items: [
        { id: "1", name: "Document Binding", quantity: 333, price: 1.50, total: 500.00, unit: "documents" }
      ],
      notes: "Converted to invoice INV-003",
      createdAt: "2024-01-17T10:00:00.000Z",
      updatedAt: "2024-01-17T10:00:00.000Z"
    }
  ]
}
