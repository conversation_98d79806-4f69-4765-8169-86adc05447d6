"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  BarChart3, 
  TrendingUp, 
  DollarSign, 
  Users, 
  Package, 
  FileText,
  Download,
  Calendar
} from "lucide-react"
import { useI18n } from '@/lib/i18n'

// Report data will be fetched from API

// Top customers data will be fetched from API

export default function ReportsPage() {
  const { t } = useI18n()
  const [dateRange, setDateRange] = useState("last30days")
  const [reportsData, setReportsData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadReportsData()
  }, [dateRange])

  const loadReportsData = async () => {
    try {
      setLoading(true)
      const period = dateRange === 'last7days' ? '1month' :
                   dateRange === 'last30days' ? '1month' :
                   dateRange === 'last3months' ? '3months' :
                   dateRange === 'last6months' ? '6months' : '1year'

      const response = await fetch(`/api/reports?period=${period}`)
      if (response.ok) {
        const data = await response.json()
        setReportsData(data)
      }
    } catch (error) {
      console.error('Error loading reports data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading reports data...</p>
        </div>
      </div>
    )
  }

  if (!reportsData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-muted-foreground">No reports data available</p>
        </div>
      </div>
    )
  }

  const { salesData, topProducts, topCustomers, summary } = reportsData

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">{t('reports.title')}</h2>
        <div className="flex items-center space-x-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t('reports.selectPeriod')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last7days">{t('reports.last7days')}</SelectItem>
              <SelectItem value="last30days">{t('reports.last30days')}</SelectItem>
              <SelectItem value="last3months">{t('reports.last3months')}</SelectItem>
              <SelectItem value="last6months">{t('reports.last6months')}</SelectItem>
              <SelectItem value="lastyear">{t('reports.lastyear')}</SelectItem>
            </SelectContent>
          </Select>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            {t('reports.export')}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('reports.totalRevenue')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summary.totalSales)}</div>
            <p className="text-xs text-muted-foreground">
              {summary.profitMargin.toFixed(1)}% profit margin
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('reports.totalOrders')}</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalInvoices}</div>
            <p className="text-xs text-muted-foreground">
              {t('reports.totalInvoicesGenerated')}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('reports.activeCustomers')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              {t('reports.customersInPeriod')}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('reports.avgOrderValue')}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summary.averageOrderValue)}</div>
            <p className="text-xs text-muted-foreground">
              {t('reports.perInvoiceAverage')}
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="sales" className="space-y-4">
        <TabsList>
          <TabsTrigger value="sales">
            <TrendingUp className="mr-2 h-4 w-4" />
            {t('reports.salesAnalytics')}
          </TabsTrigger>
          <TabsTrigger value="products">
            <Package className="mr-2 h-4 w-4" />
            {t('reports.productPerformance')}
          </TabsTrigger>
          <TabsTrigger value="customers">
            <Users className="mr-2 h-4 w-4" />
            {t('reports.customerAnalytics')}
          </TabsTrigger>
          <TabsTrigger value="tasks">
            <BarChart3 className="mr-2 h-4 w-4" />
            {t('reports.taskPerformance')}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sales" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>{t('reports.salesOverview')}</CardTitle>
                <CardDescription>{t('reports.monthlySalesPerformance')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {salesData.map((item: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.month}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm">{formatCurrency(item.sales)}</span>
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${(item.sales / 25000) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('reports.invoiceStatistics')}</CardTitle>
                <CardDescription>{t('reports.invoiceStatusBreakdown')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{t('reports.paidInvoices')}</span>
                    <span className="text-sm font-medium text-green-600">245 (68%)</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{t('reports.pendingInvoices')}</span>
                    <span className="text-sm font-medium text-yellow-600">89 (25%)</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{t('reports.overdueInvoices')}</span>
                    <span className="text-sm font-medium text-red-600">27 (7%)</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('reports.products.topSellingProducts')}</CardTitle>
              <CardDescription>{t('reports.products.bestPerformingItems')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topProducts.map((product: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{product.name}</div>
                      <div className="text-sm text-muted-foreground">{product.sales} {t('reports.products.unitsSold')}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(product.revenue)}</div>
                      <div className="text-sm text-muted-foreground">{t('reports.revenue')}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('reports.customers.topCustomers')}</CardTitle>
              <CardDescription>{t('reports.customers.highestValueCustomers')}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topCustomers.map((customer: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{customer.name}</div>
                      <div className="text-sm text-muted-foreground">{customer.orders} {t('reports.customers.orders')}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(customer.revenue)}</div>
                      <div className="text-sm text-muted-foreground">{t('reports.customers.totalSpent')}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>{t('reports.tasks.taskCompletionRate')}</CardTitle>
                <CardDescription>{t('reports.tasks.employeePerformanceMetrics')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">John Doe</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">95%</span>
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '95%' }}></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Jane Smith</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">88%</span>
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '88%' }}></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Mike Johnson</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">92%</span>
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('reports.tasks.taskStatusOverview')}</CardTitle>
                <CardDescription>{t('reports.tasks.currentTaskDistribution')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{t('reports.tasks.completedTasks')}</span>
                    <span className="text-sm font-medium text-green-600">156</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{t('reports.tasks.inProgress')}</span>
                    <span className="text-sm font-medium text-blue-600">23</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{t('reports.tasks.pending')}</span>
                    <span className="text-sm font-medium text-yellow-600">12</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{t('reports.tasks.overdue')}</span>
                    <span className="text-sm font-medium text-red-600">3</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
