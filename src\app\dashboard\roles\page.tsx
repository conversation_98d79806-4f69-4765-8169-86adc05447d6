"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Plus, Search, MoreHorizontal, Users, Shield, Edit, Trash2 } from "lucide-react"
// import { PermissionGuard } from "@/components/auth/permission-guard" // Temporarily disabled for setup

import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

interface Role {
  id: string
  name: string
  nameAr?: string
  description?: string
  isActive: boolean
  isSystem: boolean
  createdAt: string
  _count: {
    users: number
  }
}

export default function RolesPage() {
  const { t, direction } = useI18n()
  const router = useRouter()
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")


  useEffect(() => {
    loadRoles()
  }, [])

  const loadRoles = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/roles')
      if (response.ok) {
        const data = await response.json()
        setRoles(data)
      }
    } catch (error) {
      console.error('Error loading roles:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (role.nameAr && role.nameAr.includes(searchTerm))
  )

  const handleAddRole = () => {
    router.push('/dashboard/roles/add')
  }

  const handleEditRole = (roleId: string) => {
    router.push(`/dashboard/roles/${roleId}/edit`)
  }

  const handleDeleteRole = async (roleId: string) => {
    const confirmMessage = direction === 'rtl'
      ? 'هل أنت متأكد من حذف هذا الدور؟'
      : 'Are you sure you want to delete this role?'

    if (!confirm(confirmMessage)) return

    try {
      const response = await fetch(`/api/roles/${roleId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setRoles(roles.filter(role => role.id !== roleId))
        toast.success(
          direction === 'rtl' ? 'تم حذف الدور بنجاح' : 'Role deleted successfully'
        )
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to delete role')
      }
    } catch (error) {
      console.error('Error deleting role:', error)
      toast.error('Failed to delete role')
    }
  }



  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading roles...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex-1 space-y-4 p-8 pt-6 ${direction === 'rtl' ? 'rtl' : 'ltr'}`} dir={direction}>
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            {direction === 'rtl' ? 'إدارة الأدوار' : 'Roles Management'}
          </h2>
          <p className="text-muted-foreground">
            {direction === 'rtl' 
              ? 'إدارة أدوار المستخدمين والصلاحيات' 
              : 'Manage user roles and permissions'
            }
          </p>
        </div>
        <Button onClick={handleAddRole}>
          <Plus className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
          {direction === 'rtl' ? 'إضافة دور' : 'Add Role'}
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {direction === 'rtl' ? 'إجمالي الأدوار' : 'Total Roles'}
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{roles.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {direction === 'rtl' ? 'الأدوار النشطة' : 'Active Roles'}
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.filter(role => role.isActive).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {direction === 'rtl' ? 'أدوار النظام' : 'System Roles'}
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.filter(role => role.isSystem).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>{direction === 'rtl' ? 'البحث والفلترة' : 'Search & Filter'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className={`absolute top-2.5 h-4 w-4 text-muted-foreground ${direction === 'rtl' ? 'right-2' : 'left-2'}`} />
              <Input
                placeholder={direction === 'rtl' ? 'البحث في الأدوار...' : 'Search roles...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={direction === 'rtl' ? 'pr-8' : 'pl-8'}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Roles Table */}
      <Card>
        <CardHeader>
          <CardTitle>{direction === 'rtl' ? 'قائمة الأدوار' : 'Roles List'}</CardTitle>
          <CardDescription>
            {direction === 'rtl' 
              ? `إجمالي ${filteredRoles.length} دور` 
              : `Total ${filteredRoles.length} roles`
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{direction === 'rtl' ? 'اسم الدور' : 'Role Name'}</TableHead>
                <TableHead>{direction === 'rtl' ? 'الوصف' : 'Description'}</TableHead>
                <TableHead>{direction === 'rtl' ? 'المستخدمين' : 'Users'}</TableHead>
                <TableHead>{direction === 'rtl' ? 'النوع' : 'Type'}</TableHead>
                <TableHead>{direction === 'rtl' ? 'الحالة' : 'Status'}</TableHead>
                <TableHead className="text-right">{direction === 'rtl' ? 'الإجراءات' : 'Actions'}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRoles.map((role) => (
                <TableRow key={role.id}>
                  <TableCell className="font-medium">
                    <div>
                      <div>{role.name}</div>
                      {role.nameAr && (
                        <div className="text-sm text-muted-foreground">{role.nameAr}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs truncate">
                      {role.description || '-'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {role._count.users}
                    </div>
                  </TableCell>
                  <TableCell>
                    {role.isSystem ? (
                      <Badge variant="secondary">
                        {direction === 'rtl' ? 'نظام' : 'System'}
                      </Badge>
                    ) : (
                      <Badge variant="outline">
                        {direction === 'rtl' ? 'مخصص' : 'Custom'}
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant={role.isActive ? "default" : "secondary"}>
                      {role.isActive 
                        ? (direction === 'rtl' ? 'نشط' : 'Active')
                        : (direction === 'rtl' ? 'غير نشط' : 'Inactive')
                      }
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditRole(role.id)}>
                          <Edit className="mr-2 h-4 w-4" />
                          {direction === 'rtl' ? 'عرض التفاصيل' : 'View Details'}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditRole(role.id)}>
                          <Edit className="mr-2 h-4 w-4" />
                          {direction === 'rtl' ? 'تعديل' : 'Edit'}
                        </DropdownMenuItem>
                        {!role.isSystem && (
                          <DropdownMenuItem
                            onClick={() => handleDeleteRole(role.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            {direction === 'rtl' ? 'حذف' : 'Delete'}
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>


    </div>
  )
}
