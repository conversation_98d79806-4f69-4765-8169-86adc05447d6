const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAdmin() {
  try {
    console.log('🔐 Creating admin user...')

    // Hash the password
    const hashedPassword = await bcrypt.hash('admin123', 10)

    // Create or update admin user
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: hashedPassword,
        name: 'Admin User',
        role: 'ADMIN',
        phone: '+968 9123 4567',
        isActive: true,
      },
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin User',
        role: 'ADMIN',
        phone: '+968 9123 4567',
        isActive: true,
        joinDate: new Date(),
        tasksCompleted: 0,
        tasksInProgress: 0,
        completionRate: 0,
      },
    })

    console.log('✅ Admin user created successfully!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: admin123')
    console.log('👤 Name:', adminUser.name)
    console.log('🎭 Role:', adminUser.role)

  } catch (error) {
    console.error('❌ Error creating admin user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the function
createAdmin()
  .catch((error) => {
    console.error('❌ Failed to create admin user:', error)
    process.exit(1)
  })
