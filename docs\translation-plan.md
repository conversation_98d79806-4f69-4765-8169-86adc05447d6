# Translation System Restructuring Plan

## Current Issues
- Translations scattered across multiple files (`messages/ar.json`, `src/lib/translations/ar.ts`)
- Many missing translations showing as English text
- Duplicate translation keys causing linter errors
- No systematic approach to handle module-specific translations

## Proposed Solution: Modular Translation System

### 1. File Structure
```
src/lib/translations/
├── ar/
│   ├── index.ts          # Main export file
│   ├── common.ts         # Common UI elements
│   ├── navigation.ts     # Navigation & menu items
│   ├── dashboard.ts      # Dashboard module
│   ├── customers.ts      # Customer module
│   ├── suppliers.ts      # Supplier module
│   ├── products.ts       # Products module
│   ├── invoices.ts       # Invoices module
│   ├── quotations.ts     # Quotations module
│   ├── tasks.ts          # Tasks module
│   ├── projects.ts       # Projects module
│   ├── calendar.ts       # Calendar module
│   ├── employees.ts      # Employees module
│   ├── reports.ts        # Reports module
│   ├── settings.ts       # Settings module
│   └── financial.ts     # Financial module
├── en/
│   └── [same structure as ar/]
└── index.ts             # Main translation loader
```

### 2. Implementation Tasks

#### Task 1: Create Common Translations (Priority: HIGH)
**File:** `src/lib/translations/ar/common.ts`
**Missing translations to add:**
- contact: "جهة الاتصال"
- contactInformation: "معلومات الاتصال"
- actions: "الإجراءات"
- status: "الحالة"
- date: "التاريخ"
- total: "المجموع"
- amount: "المبلغ"
- notes: "الملاحظات"
- product: "المنتج"
- category: "الفئة"
- All form labels, buttons, and UI elements

#### Task 2: Customer Module Translations (Priority: HIGH)
**File:** `src/lib/translations/ar/customers.ts`
**Missing translations to add:**
- customerDetailsAndHistory: "تفاصيل العميل وسجل المعاملات"
- customerSummary: "ملخص العميل"
- totalInvoices: "إجمالي الفواتير"
- totalQuotations: "إجمالي عروض الأسعار"
- quickActions: "الإجراءات السريعة"
- customerInformation: "معلومات العميل"
- paymentStatus: "حالة الدفع"
- All customer detail page elements

#### Task 3: Supplier Module Translations (Priority: HIGH)
**File:** `src/lib/translations/ar/suppliers.ts`
**Missing translations to add:**
- supplierDetailsAndHistory: "تفاصيل المورد وسجل المشتريات"
- supplierSummary: "ملخص المورد"
- supplierInformation: "معلومات المورد"
- purchaseOrders: "أوامر الشراء"
- paymentHistory: "سجل المدفوعات"
- All supplier detail page elements

#### Task 4: Navigation Translations (Priority: HIGH)
**File:** `src/lib/translations/ar/navigation.ts`
**Missing translations to add:**
- All sidebar menu items
- Breadcrumb navigation
- Page titles and descriptions

#### Task 5: Dashboard Translations (Priority: MEDIUM)
**File:** `src/lib/translations/ar/dashboard.ts`
- All dashboard widgets
- Statistics cards
- Chart labels
- Quick action buttons

#### Task 6: Products Module (Priority: MEDIUM)
**File:** `src/lib/translations/ar/products.ts`
- Product forms
- Category management
- Stock management
- Product detail pages

#### Task 7: Invoices Module (Priority: MEDIUM)
**File:** `src/lib/translations/ar/invoices.ts`
- Invoice creation/editing
- Invoice status labels
- Payment tracking
- Invoice templates

#### Task 8: Other Modules (Priority: LOW)
- Quotations, Tasks, Projects, Calendar, Employees, Reports, Settings, Financial

### 3. Implementation Steps

#### Step 1: Create Base Structure
1. Create modular translation files
2. Move existing translations to appropriate modules
3. Remove duplicate translations
4. Update import statements

#### Step 2: Audit Missing Translations
1. Scan all components for `t('...')` calls
2. Identify missing translation keys
3. Create comprehensive translation lists
4. Prioritize by module usage

#### Step 3: Implement Translations Module by Module
1. Start with Common (used everywhere)
2. Then Navigation (visible on all pages)
3. Then Customer & Supplier (main business modules)
4. Continue with other modules

#### Step 4: Testing & Validation
1. Test each module after translation
2. Verify RTL layout works correctly
3. Check for missing translations
4. Validate Arabic text quality

### 4. Translation Guidelines

#### Arabic Translation Standards
- Use formal Arabic (فصحى)
- Consistent terminology across modules
- Proper RTL text direction
- Cultural appropriateness for Oman/Gulf region

#### Technical Standards
- Use TypeScript for type safety
- Consistent key naming convention
- Modular exports for tree-shaking
- Fallback to English if translation missing

### 5. Maintenance Plan

#### Regular Tasks
- Weekly translation audits
- New feature translation requirements
- User feedback incorporation
- Translation quality reviews

#### Tools & Automation
- Translation key extraction scripts
- Missing translation detection
- Automated testing for translation coverage
- CI/CD integration for translation validation

## Next Steps

1. **Immediate (Today):** Create modular file structure
2. **Week 1:** Implement Common + Navigation + Customer modules
3. **Week 2:** Implement Supplier + Dashboard + Products modules
4. **Week 3:** Implement remaining modules
5. **Week 4:** Testing, validation, and refinement

## Success Metrics

- 100% translation coverage for all UI text
- Zero English text showing in Arabic mode
- Consistent terminology across all modules
- Proper RTL layout and formatting
- User satisfaction with Arabic interface 