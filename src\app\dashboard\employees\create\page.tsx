"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, Save, User, FileText, Calendar, IdCard, BookOpen, Car, FileCheck } from "lucide-react"
import { useI18n } from "@/lib/i18n"

export default function CreateEmployeePage() {
  const router = useRouter()
  const { t } = useI18n()
  const [saving, setSaving] = useState(false)
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'EMPLOYEE',
    password: '',
    confirmPassword: '',
    // Document Information
    idCardNumber: '',
    idCardExpiry: '',
    passportNumber: '',
    passportExpiry: '',
    visaNumber: '',
    visaExpiry: '',
    licenseNumber: '',
    licenseExpiry: '',
    contractExpiry: '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      // Validate required fields
      if (!formData.name || !formData.email || !formData.phone) {
        alert('Please fill in all required fields (Name, Email, Phone)')
        setSaving(false)
        return
      }

      if (formData.password !== formData.confirmPassword) {
        alert('Passwords do not match')
        setSaving(false)
        return
      }

      const employeeData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        role: formData.role,
        password: formData.password,
        // Document fields
        idCardNumber: formData.idCardNumber || null,
        idCardExpiry: formData.idCardExpiry ? new Date(formData.idCardExpiry).toISOString() : null,
        passportNumber: formData.passportNumber || null,
        passportExpiry: formData.passportExpiry ? new Date(formData.passportExpiry).toISOString() : null,
        visaNumber: formData.visaNumber || null,
        visaExpiry: formData.visaExpiry ? new Date(formData.visaExpiry).toISOString() : null,
        licenseNumber: formData.licenseNumber || null,
        licenseExpiry: formData.licenseExpiry ? new Date(formData.licenseExpiry).toISOString() : null,
        contractExpiry: formData.contractExpiry ? new Date(formData.contractExpiry).toISOString() : null,
      }

      const response = await fetch('/api/employees', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(employeeData)
      })

      if (response.ok) {
        alert('Employee created successfully!')
        router.push('/dashboard/employees')
      } else {
        const error = await response.json()
        alert(`Failed to create employee: ${error.error}`)
      }
    } catch (error) {
      console.error('Error creating employee:', error)
      alert('Failed to create employee')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Add New Employee</h2>
          <p className="text-muted-foreground">
            Create a new employee account with document tracking
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    placeholder="Enter full name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    placeholder="+968 9123 4567"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    required
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Select value={formData.role} onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="EMPLOYEE">Employee</SelectItem>
                      <SelectItem value="MANAGER">Manager</SelectItem>
                      <SelectItem value="ADMIN">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password *</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm Password *</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="Confirm password"
                    value={formData.confirmPassword}
                    onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    required
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Document Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Document Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* ID Card */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 text-sm font-medium text-muted-foreground">
                  <IdCard className="h-4 w-4" />
                  <span>ID Card Information</span>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="idCardNumber">ID Card Number</Label>
                  <Input
                    id="idCardNumber"
                    placeholder="Enter ID card number"
                    value={formData.idCardNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, idCardNumber: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="idCardExpiry">ID Card Expiry Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="idCardExpiry"
                      type="date"
                      className="pl-8"
                      value={formData.idCardExpiry}
                      onChange={(e) => setFormData(prev => ({ ...prev, idCardExpiry: e.target.value }))}
                    />
                  </div>
                </div>

                <Separator />

                {/* Passport */}
                <div className="flex items-center space-x-2 text-sm font-medium text-muted-foreground">
                  <BookOpen className="h-4 w-4" />
                  <span>Passport Information</span>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passportNumber">Passport Number</Label>
                  <Input
                    id="passportNumber"
                    placeholder="Enter passport number"
                    value={formData.passportNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, passportNumber: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passportExpiry">Passport Expiry Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="passportExpiry"
                      type="date"
                      className="pl-8"
                      value={formData.passportExpiry}
                      onChange={(e) => setFormData(prev => ({ ...prev, passportExpiry: e.target.value }))}
                    />
                  </div>
                </div>
              </div>

              {/* Visa and License */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 text-sm font-medium text-muted-foreground">
                  <FileText className="h-4 w-4" />
                  <span>Visa Information</span>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="visaNumber">Visa Number</Label>
                  <Input
                    id="visaNumber"
                    placeholder="Enter visa number"
                    value={formData.visaNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, visaNumber: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="visaExpiry">Visa Expiry Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="visaExpiry"
                      type="date"
                      className="pl-8"
                      value={formData.visaExpiry}
                      onChange={(e) => setFormData(prev => ({ ...prev, visaExpiry: e.target.value }))}
                    />
                  </div>
                </div>

                <Separator />

                {/* License */}
                <div className="flex items-center space-x-2 text-sm font-medium text-muted-foreground">
                  <Car className="h-4 w-4" />
                  <span>License Information</span>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="licenseNumber">License Number</Label>
                  <Input
                    id="licenseNumber"
                    placeholder="Enter license number"
                    value={formData.licenseNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, licenseNumber: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="licenseExpiry">License Expiry Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="licenseExpiry"
                      type="date"
                      className="pl-8"
                      value={formData.licenseExpiry}
                      onChange={(e) => setFormData(prev => ({ ...prev, licenseExpiry: e.target.value }))}
                    />
                  </div>
                </div>

                <Separator />

                {/* Contract */}
                <div className="flex items-center space-x-2 text-sm font-medium text-muted-foreground">
                  <FileCheck className="h-4 w-4" />
                  <span>Contract Information</span>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contractExpiry">Contract Expiry Date</Label>
                  <div className="relative">
                    <Calendar className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="contractExpiry"
                      type="date"
                      className="pl-8"
                      value={formData.contractExpiry}
                      onChange={(e) => setFormData(prev => ({ ...prev, contractExpiry: e.target.value }))}
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? 'Creating...' : 'Create Employee'}
          </Button>
        </div>
      </form>
    </div>
  )
}
