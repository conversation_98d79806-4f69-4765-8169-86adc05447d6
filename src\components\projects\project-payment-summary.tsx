"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  CreditCard, 
  Receipt, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import Link from "next/link"

interface PaymentData {
  invoices: {
    total: number
    totalAmount: number
    paidAmount: number
    unpaidAmount: number
    overdueAmount: number
    partialAmount: number
    recentPayments: Array<{
      id: string
      amount: number
      date: string
      method: string
      invoiceNumber: string
      reference?: string
    }>
  }
  expenses: {
    total: number
    totalAmount: number
    paidAmount: number
    pendingAmount: number
    approvedAmount: number
  }
  cashFlow: {
    netIncome: number
    profitMargin: number
    collectionRate: number
    avgPaymentTime: number
  }
}

interface ProjectPaymentSummaryProps {
  projectId: string
}

export function ProjectPaymentSummary({ projectId }: ProjectPaymentSummaryProps) {
  const [paymentData, setPaymentData] = useState<PaymentData>({
    invoices: {
      total: 0,
      totalAmount: 0,
      paidAmount: 0,
      unpaidAmount: 0,
      overdueAmount: 0,
      partialAmount: 0,
      recentPayments: []
    },
    expenses: {
      total: 0,
      totalAmount: 0,
      paidAmount: 0,
      pendingAmount: 0,
      approvedAmount: 0
    },
    cashFlow: {
      netIncome: 0,
      profitMargin: 0,
      collectionRate: 0,
      avgPaymentTime: 0
    }
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPaymentData()
  }, [projectId])

  const fetchPaymentData = async () => {
    try {
      setLoading(true)
      
      // Fetch invoices and expenses data
      const [invoicesResponse, expensesResponse] = await Promise.all([
        fetch(`/api/invoices?projectId=${projectId}&limit=1000`),
        fetch(`/api/expenses?projectId=${projectId}&limit=1000`)
      ])

      const invoicesData = invoicesResponse.ok ? await invoicesResponse.json() : { invoices: [] }
      const expensesData = expensesResponse.ok ? await expensesResponse.json() : { expenses: [] }

      const invoices = invoicesData.invoices || []
      const expenses = expensesData.expenses || []

      // Calculate invoice stats
      const totalInvoiceAmount = invoices.reduce((sum: number, inv: any) => sum + Number(inv.total), 0)
      const paidInvoiceAmount = invoices
        .filter((inv: any) => inv.status === 'PAID')
        .reduce((sum: number, inv: any) => sum + Number(inv.total), 0)
      const unpaidInvoiceAmount = invoices
        .filter((inv: any) => inv.status === 'UNPAID')
        .reduce((sum: number, inv: any) => sum + Number(inv.total), 0)
      const overdueInvoiceAmount = invoices
        .filter((inv: any) => inv.status === 'OVERDUE')
        .reduce((sum: number, inv: any) => sum + Number(inv.total), 0)
      const partialInvoiceAmount = invoices
        .filter((inv: any) => inv.status === 'PARTIAL')
        .reduce((sum: number, inv: any) => sum + Number(inv.total), 0)

      // Calculate expense stats
      const totalExpenseAmount = expenses.reduce((sum: number, exp: any) => sum + Number(exp.amount), 0)
      const paidExpenseAmount = expenses
        .filter((exp: any) => exp.status === 'PAID')
        .reduce((sum: number, exp: any) => sum + Number(exp.amount), 0)
      const pendingExpenseAmount = expenses
        .filter((exp: any) => exp.status === 'PENDING')
        .reduce((sum: number, exp: any) => sum + Number(exp.amount), 0)
      const approvedExpenseAmount = expenses
        .filter((exp: any) => exp.status === 'APPROVED')
        .reduce((sum: number, exp: any) => sum + Number(exp.amount), 0)

      // Get recent payments (mock data for now - would come from payments API)
      const recentPayments = invoices
        .filter((inv: any) => inv.status === 'PAID')
        .slice(0, 5)
        .map((inv: any) => ({
          id: inv.id,
          amount: inv.total,
          date: inv.date,
          method: 'Bank Transfer', // This would come from actual payment data
          invoiceNumber: inv.number,
          reference: `PAY-${inv.number}`
        }))

      // Calculate cash flow metrics
      const netIncome = totalInvoiceAmount - totalExpenseAmount
      const profitMargin = totalInvoiceAmount > 0 ? (netIncome / totalInvoiceAmount) * 100 : 0
      const collectionRate = totalInvoiceAmount > 0 ? (paidInvoiceAmount / totalInvoiceAmount) * 100 : 0
      const avgPaymentTime = 15 // This would be calculated from actual payment data

      setPaymentData({
        invoices: {
          total: invoices.length,
          totalAmount: totalInvoiceAmount,
          paidAmount: paidInvoiceAmount,
          unpaidAmount: unpaidInvoiceAmount,
          overdueAmount: overdueInvoiceAmount,
          partialAmount: partialInvoiceAmount,
          recentPayments
        },
        expenses: {
          total: expenses.length,
          totalAmount: totalExpenseAmount,
          paidAmount: paidExpenseAmount,
          pendingAmount: pendingExpenseAmount,
          approvedAmount: approvedExpenseAmount
        },
        cashFlow: {
          netIncome,
          profitMargin,
          collectionRate,
          avgPaymentTime
        }
      })
    } catch (error) {
      console.error('Error fetching payment data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getPaymentHealthStatus = () => {
    const { collectionRate, profitMargin } = paymentData.cashFlow
    if (collectionRate >= 90 && profitMargin >= 20) return { color: "text-green-600", bg: "bg-green-50", status: "Excellent" }
    if (collectionRate >= 75 && profitMargin >= 10) return { color: "text-blue-600", bg: "bg-blue-50", status: "Good" }
    if (collectionRate >= 60 && profitMargin >= 0) return { color: "text-yellow-600", bg: "bg-yellow-50", status: "Fair" }
    return { color: "text-red-600", bg: "bg-red-50", status: "Poor" }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  const healthStatus = getPaymentHealthStatus()

  return (
    <div className="space-y-6">
      {/* Payment Health Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Payment Health Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className={`p-4 rounded-lg ${healthStatus.bg}`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${healthStatus.color}`}>Overall Health</p>
                <p className={`text-2xl font-bold ${healthStatus.color}`}>
                  {healthStatus.status}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Based on collection rate and profitability
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-muted-foreground">Collection Rate</p>
                <p className={`text-lg font-bold ${healthStatus.color}`}>
                  {paymentData.cashFlow.collectionRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Revenue & Payment Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Revenue */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(paymentData.invoices.totalAmount)}
            </div>
            <p className="text-xs text-muted-foreground">
              {paymentData.invoices.total} invoice(s)
            </p>
          </CardContent>
        </Card>

        {/* Paid Amount */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collected</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(paymentData.invoices.paidAmount)}
            </div>
            <p className="text-xs text-muted-foreground">
              {paymentData.cashFlow.collectionRate.toFixed(1)}% collection rate
            </p>
          </CardContent>
        </Card>

        {/* Outstanding Amount */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {formatCurrency(paymentData.invoices.unpaidAmount + paymentData.invoices.partialAmount)}
            </div>
            <p className="text-xs text-muted-foreground">
              Pending payments
            </p>
          </CardContent>
        </Card>

        {/* Overdue Amount */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(paymentData.invoices.overdueAmount)}
            </div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Payment Status Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Status Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Collection Progress */}
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Collection Progress</span>
                <span>{paymentData.cashFlow.collectionRate.toFixed(1)}%</span>
              </div>
              <Progress value={paymentData.cashFlow.collectionRate} className="h-3" />
            </div>

            {/* Payment Status Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-900">
                  {formatCurrency(paymentData.invoices.paidAmount)}
                </div>
                <div className="text-xs text-green-600">Paid</div>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded-lg">
                <div className="text-lg font-bold text-yellow-900">
                  {formatCurrency(paymentData.invoices.partialAmount)}
                </div>
                <div className="text-xs text-yellow-600">Partial</div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <div className="text-lg font-bold text-orange-900">
                  {formatCurrency(paymentData.invoices.unpaidAmount)}
                </div>
                <div className="text-xs text-orange-600">Unpaid</div>
              </div>
              <div className="text-center p-3 bg-red-50 rounded-lg">
                <div className="text-lg font-bold text-red-900">
                  {formatCurrency(paymentData.invoices.overdueAmount)}
                </div>
                <div className="text-xs text-red-600">Overdue</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Payments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Receipt className="h-5 w-5" />
              Recent Payments
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/projects/${projectId}/edit`}>
                <Eye className="h-4 w-4 mr-2" />
                View All
              </Link>
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {paymentData.invoices.recentPayments.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              <Receipt className="mx-auto h-12 w-12 mb-3 opacity-50" />
              <p>No payments recorded yet</p>
            </div>
          ) : (
            <div className="space-y-3">
              {paymentData.invoices.recentPayments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-full">
                      <DollarSign className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium">{payment.invoiceNumber}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(payment.date).toLocaleDateString()} • {payment.method}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-600">{formatCurrency(payment.amount)}</p>
                    {payment.reference && (
                      <p className="text-xs text-muted-foreground">{payment.reference}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cash Flow Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Income</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${paymentData.cashFlow.netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(paymentData.cashFlow.netIncome)}
            </div>
            <p className="text-xs text-muted-foreground">
              Revenue - Expenses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profit Margin</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${paymentData.cashFlow.profitMargin >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {paymentData.cashFlow.profitMargin.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Profitability ratio
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Payment Time</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {paymentData.cashFlow.avgPaymentTime} days
            </div>
            <p className="text-xs text-muted-foreground">
              Average collection time
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 