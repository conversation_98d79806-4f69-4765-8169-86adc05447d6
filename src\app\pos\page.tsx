"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import {
  Search,
  Plus,
  Minus,
  Trash2,
  ShoppingCart,
  CreditCard,
  Banknote,
  Receipt,
  Calculator,
  User,
  Package,
  X,
  Check,
  Printer,
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { createPOSInvoice } from "@/lib/invoice-storage"
import { useI18n } from "@/lib/i18n"

interface CartItem {
  id: string
  name: string
  nameAr: string
  price: number
  quantity: number
  total: number
  image?: string
}

export default function POSPage() {
  const { t, language } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [cart, setCart] = useState<CartItem[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null)
  const [paymentMethod, setPaymentMethod] = useState("cash")
  const [amountPaid, setAmountPaid] = useState("")
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false)
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false)
  const [isNewCustomerDialogOpen, setIsNewCustomerDialogOpen] = useState(false)
  const [customerSearchTerm, setCustomerSearchTerm] = useState("")
  const [categorySearchTerm, setCategorySearchTerm] = useState("")
  const [newCustomer, setNewCustomer] = useState({
    name: "",
    nameEn: "",
    email: "",
    phone: "",
    company: "",
    companyEn: ""
  })
  const [mounted, setMounted] = useState(false)
  const [customers, setCustomers] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    setMounted(true)
    fetchCustomers()
    fetchProducts()
  }, [])

  const fetchCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      if (response.ok) {
        const data = await response.json()
        setCustomers(data.customers || [])
      } else {
        console.error('Failed to fetch customers')
      }
    } catch (error) {
      console.error('Error fetching customers:', error)
    }
  }

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products')
      if (response.ok) {
        const data = await response.json()
        setProducts(data.products || [])
      } else {
        console.error('Failed to fetch products')
      }
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (product.nameAr && product.nameAr.includes(searchTerm)) ||
                         (product.sku && product.sku.toLowerCase().includes(searchTerm.toLowerCase()))
    
    // Handle category filtering - category might be an object or string
    const productCategoryName = typeof product.category === 'object' && product.category?.name 
      ? product.category.name 
      : typeof product.category === 'string' 
        ? product.category 
        : null
    
    const matchesCategory = selectedCategory === "all" || productCategoryName === selectedCategory
    const isActive = product.isActive !== false // Default to true if not specified
    return matchesSearch && matchesCategory && isActive
  })

  const allCategories = ["all", ...Array.from(new Set(
    products
      .map(p => typeof p.category === 'object' && p.category?.name ? p.category.name : typeof p.category === 'string' ? p.category : null)
      .filter(Boolean)
  ))]
  const filteredCategories = allCategories.filter(category =>
    category === "all" || (typeof category === 'string' && category.toLowerCase().includes(categorySearchTerm.toLowerCase()))
  )

  const filteredCustomers = customers.filter(customer =>
    (customer.name && customer.name.toLowerCase().includes(customerSearchTerm.toLowerCase())) ||
    (customer.nameAr && customer.nameAr.includes(customerSearchTerm)) ||
    (customer.company && customer.company.toLowerCase().includes(customerSearchTerm.toLowerCase())) ||
    (customer.mobile && customer.mobile.includes(customerSearchTerm)) ||
    (customer.phone && customer.phone.includes(customerSearchTerm)) ||
    (customer.email && customer.email.toLowerCase().includes(customerSearchTerm.toLowerCase()))
  )

  const addToCart = (product: any) => {
    const existingItem = cart.find(item => item.id === product.id)

    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1, total: (item.quantity + 1) * item.price }
          : item
      ))
    } else {
      setCart([...cart, {
        id: product.id,
        name: product.name,
        nameAr: product.nameAr,
        price: product.price,
        quantity: 1,
        total: product.price,
        image: product.image
      }])
    }
  }

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(id)
      return
    }

    setCart(cart.map(item =>
      item.id === id
        ? { ...item, quantity: newQuantity, total: newQuantity * item.price }
        : item
    ))
  }

  const removeFromCart = (id: string) => {
    setCart(cart.filter(item => item.id !== id))
  }

  const clearCart = () => {
    setCart([])
    setSelectedCustomer(null)
  }

  const subtotal = cart.reduce((sum, item) => sum + item.total, 0)
  const taxRate = 0.05 // 5% VAT
  const taxAmount = subtotal * taxRate
  const total = subtotal + taxAmount

  const handlePayment = () => {
    if (cart.length === 0) {
      alert("Cart is empty!")
      return
    }
    setIsPaymentDialogOpen(true)
  }

  const processPayment = () => {
    const paid = parseFloat(amountPaid) || 0
    if (paymentMethod === "cash" && paid < total) {
      alert("Insufficient payment amount!")
      return
    }

    try {
      // Create invoice from POS sale
      const invoice = createPOSInvoice(
        cart,
        selectedCustomer,
        paymentMethod as 'cash' | 'card',
        subtotal,
        taxAmount,
        total,
        paid
      )

      console.log("Sale completed, invoice created:", invoice.number)
      alert(`Sale completed successfully! Invoice ${invoice.number} created.`)

      // Clear everything
      clearCart()
      setAmountPaid("")
      setIsPaymentDialogOpen(false)
    } catch (error) {
      console.error("Error processing sale:", error)
      alert("Error processing sale. Please try again.")
    }
  }

  const handleAddNewCustomer = async () => {
    if (!newCustomer.nameEn || !newCustomer.phone) {
      alert("Please fill in at least the English name and phone number.")
      return
    }

    try {
      // Create new customer via API
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCustomer.name || newCustomer.nameEn,
          nameAr: newCustomer.name,
          email: newCustomer.email,
          mobile: newCustomer.phone,
          phone: newCustomer.phone,
          company: newCustomer.company || newCustomer.companyEn,
          isActive: true
        })
      })

      if (response.ok) {
        const data = await response.json()
        const newCustomerData = data.customer

        // Add to customers list
        setCustomers(prev => [...prev, newCustomerData])

        // Select the new customer
        setSelectedCustomer(newCustomerData)

        // Reset form and close dialogs
        setNewCustomer({
          name: "",
          nameEn: "",
          email: "",
          phone: "",
          company: "",
          companyEn: ""
        })
        setIsNewCustomerDialogOpen(false)
        setIsCustomerDialogOpen(false)

        alert("Customer added successfully!")
      } else {
        const error = await response.json()
        alert(`Failed to add customer: ${error.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error adding customer:', error)
      alert('Failed to add customer. Please try again.')
    }
  }

  const change = paymentMethod === "cash" ? Math.max(0, (parseFloat(amountPaid) || 0) - total) : 0

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold">{t('pos.title')}</h1>
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => window.close()}>
              <X className="mr-2 h-4 w-4" />
              {t('pos.close')} POS
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Products Section */}
          <div className="lg:col-span-2 space-y-4">
            {/* Search and Filters */}
            <div className="flex space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('pos.searchProducts')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <div className="p-2">
                    <Input
                      placeholder={t('pos.searchProducts')}
                      value={categorySearchTerm}
                      onChange={(e) => setCategorySearchTerm(e.target.value)}
                      className="mb-2"
                    />
                  </div>
                  {filteredCategories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === "all" ? t('pos.allCategories') : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Products Grid */}
            <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredProducts.map((product) => (
                <Card key={product.id} className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => addToCart(product)}>
                  <CardContent className="p-4">
                    <div className="aspect-square bg-gray-100 rounded-lg mb-3 overflow-hidden">
                      {product.image ? (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="h-12 w-12 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <h3 className="font-medium text-sm mb-1 line-clamp-2">{product.name}</h3>
                    <p className="text-xs text-muted-foreground mb-2">{product.nameAr}</p>
                    <div className="flex items-center justify-between">
                      <span className="font-bold text-green-600">{formatCurrency(product.price)}</span>
                      <Badge variant={product.type === "SERVICE" ? "secondary" : (product.currentStock > product.minStock ? "default" : "destructive")} className="text-xs">
                        {product.type === "SERVICE" ? "Service" : product.currentStock}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">{product.sku}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Cart Section */}
          <div className="space-y-4">
            {/* Customer Selection */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center justify-between">
                  <span>Customer</span>
                  <Button size="sm" variant="outline" onClick={() => setIsCustomerDialogOpen(true)}>
                    <User className="mr-2 h-4 w-4" />
                    Select
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {selectedCustomer ? (
                  <div className="space-y-2">
                    <p className="font-medium">{selectedCustomer.name}</p>
                    <p className="text-sm text-muted-foreground">{selectedCustomer.nameAr}</p>
                    <p className="text-sm text-muted-foreground">{selectedCustomer.mobile || selectedCustomer.phone}</p>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Walk-in Customer</p>
                )}
              </CardContent>
            </Card>

            {/* Cart Items */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center justify-between">
                  <span>Cart ({cart.length})</span>
                  {cart.length > 0 && (
                    <Button size="sm" variant="outline" onClick={clearCart}>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Clear
                    </Button>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {cart.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <ShoppingCart className="mx-auto h-12 w-12 mb-4" />
                    <p>Cart is empty</p>
                    <p className="text-sm">Add products to get started</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {cart.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3 flex-1">
                          <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                            {item.image ? (
                              <img
                                src={item.image}
                                alt={item.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Package className="h-4 w-4 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{item.name}</h4>
                            <p className="text-xs text-muted-foreground">{item.nameAr}</p>
                            <p className="text-sm font-medium text-green-600">{formatCurrency(item.price)}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="w-8 text-center font-medium">{item.quantity}</span>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => removeFromCart(item.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Order Summary */}
            {cart.length > 0 && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>{formatCurrency(subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>VAT (5%):</span>
                    <span>{formatCurrency(taxAmount)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span className="text-green-600">{formatCurrency(total)}</span>
                  </div>
                  <Button className="w-full" size="lg" onClick={handlePayment}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    Process Payment
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Customer Selection Dialog */}
        <Dialog open={isCustomerDialogOpen} onOpenChange={setIsCustomerDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Select Customer</DialogTitle>
              <DialogDescription>
                Choose a customer or continue as walk-in
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search customers..."
                  value={customerSearchTerm}
                  onChange={(e) => setCustomerSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Add New Customer Button */}
              <Button
                variant="default"
                className="w-full justify-start"
                onClick={() => setIsNewCustomerDialogOpen(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add New Customer
              </Button>

              {/* Walk-in Customer */}
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => {
                  setSelectedCustomer(null)
                  setCustomerSearchTerm("")
                  setIsCustomerDialogOpen(false)
                }}
              >
                <User className="mr-2 h-4 w-4" />
                Walk-in Customer
              </Button>

              {/* Customer List */}
              <div className="max-h-60 overflow-y-auto space-y-2">
                {filteredCustomers.length === 0 && customerSearchTerm ? (
                  <div className="text-center py-4 text-muted-foreground">
                    <p>No customers found</p>
                    <p className="text-sm">Try a different search term</p>
                  </div>
                ) : (
                  filteredCustomers.map((customer) => (
                    <Button
                      key={customer.id}
                      variant="outline"
                      className="w-full justify-start h-auto p-3"
                      onClick={() => {
                        setSelectedCustomer(customer)
                        setCustomerSearchTerm("")
                        setIsCustomerDialogOpen(false)
                      }}
                    >
                      <div className="text-left w-full">
                        <div className="font-medium">{customer.name}</div>
                        <div className="text-sm text-muted-foreground">{customer.nameAr}</div>
                        <div className="text-sm text-muted-foreground">{customer.company}</div>
                        <div className="text-xs text-muted-foreground">{customer.mobile || customer.phone}</div>
                      </div>
                    </Button>
                  ))
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Add New Customer Dialog */}
        <Dialog open={isNewCustomerDialogOpen} onOpenChange={setIsNewCustomerDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Customer</DialogTitle>
              <DialogDescription>
                Create a new customer account
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="nameEn">English Name *</Label>
                  <Input
                    id="nameEn"
                    value={newCustomer.nameEn}
                    onChange={(e) => setNewCustomer({...newCustomer, nameEn: e.target.value})}
                    placeholder="John Doe"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Arabic Name</Label>
                  <Input
                    id="name"
                    value={newCustomer.name}
                    onChange={(e) => setNewCustomer({...newCustomer, name: e.target.value})}
                    placeholder="جون دو"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={newCustomer.phone}
                  onChange={(e) => setNewCustomer({...newCustomer, phone: e.target.value})}
                  placeholder="+968 9123 4567"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={newCustomer.email}
                  onChange={(e) => setNewCustomer({...newCustomer, email: e.target.value})}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyEn">Company (English)</Label>
                  <Input
                    id="companyEn"
                    value={newCustomer.companyEn}
                    onChange={(e) => setNewCustomer({...newCustomer, companyEn: e.target.value})}
                    placeholder="ABC Company"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company">Company (Arabic)</Label>
                  <Input
                    id="company"
                    value={newCustomer.company}
                    onChange={(e) => setNewCustomer({...newCustomer, company: e.target.value})}
                    placeholder="شركة ABC"
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsNewCustomerDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddNewCustomer}>
                <Check className="mr-2 h-4 w-4" />
                Add Customer
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Payment Dialog */}
        <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Process Payment</DialogTitle>
              <DialogDescription>
                Complete the sale transaction
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between text-lg font-bold">
                  <span>Total Amount:</span>
                  <span className="text-green-600">{formatCurrency(total)}</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Payment Method</Label>
                <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">
                      <div className="flex items-center">
                        <Banknote className="mr-2 h-4 w-4" />
                        Cash
                      </div>
                    </SelectItem>
                    <SelectItem value="card">
                      <div className="flex items-center">
                        <CreditCard className="mr-2 h-4 w-4" />
                        Credit/Debit Card
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {paymentMethod === "cash" && (
                <div className="space-y-2">
                  <Label>Amount Paid</Label>
                  <Input
                    type="number"
                    placeholder="0.00"
                    value={amountPaid}
                    onChange={(e) => setAmountPaid(e.target.value)}
                    step="0.01"
                  />
                  {mounted && amountPaid && (
                    <div className="text-sm">
                      <div className="flex justify-between">
                        <span>Change:</span>
                        <span className={change >= 0 ? "text-green-600" : "text-red-600"}>
                          {formatCurrency(change)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsPaymentDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={processPayment}>
                <Check className="mr-2 h-4 w-4" />
                Complete Sale
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
