"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft, Save } from "lucide-react"
import { useI18n } from "@/lib/i18n"

export default function CreateLeadPage() {
  const router = useRouter()
  const { t, language, direction } = useI18n()
  const [saving, setSaving] = useState(false)
  
  // Translated Lead Sources
  const LEAD_SOURCES = [
    { value: 'WEBSITE', label: t('leads.sources.website') || 'Website' },
    { value: 'SOCIAL_MEDIA', label: t('leads.sources.socialMedia') || 'Social Media' },
    { value: 'REFERRAL', label: t('leads.sources.referral') || 'Referral' },
    { value: 'COLD_CALL', label: t('leads.sources.coldCall') || 'Cold Call' },
    { value: 'EMAIL', label: t('leads.sources.email') || 'Email Campaign' },
    { value: 'TRADE_SHOW', label: t('leads.sources.tradeShow') || 'Trade Show' },
    { value: 'ADVERTISEMENT', label: t('leads.sources.advertisement') || 'Advertisement' },
    { value: 'OTHER', label: t('leads.sources.other') || 'Other' },
  ]

  // Translated Lead Statuses
  const LEAD_STATUSES = [
    { value: 'NEW', label: t('leads.statuses.new') || 'New' },
    { value: 'CONTACTED', label: t('leads.statuses.contacted') || 'Contacted' },
    { value: 'QUALIFIED', label: t('leads.statuses.qualified') || 'Qualified' },
    { value: 'PROPOSAL', label: t('leads.statuses.proposal') || 'Proposal Sent' },
    { value: 'NEGOTIATION', label: t('leads.statuses.negotiation') || 'In Negotiation' },
    { value: 'CONVERTED', label: t('leads.statuses.converted') || 'Converted' },
    { value: 'LOST', label: t('leads.statuses.lost') || 'Lost' },
  ]
  
  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    email: '',
    mobile: '',
    phone: '',
    address: '',
    city: '',
    country: 'Oman',
    company: '',
    source: 'WEBSITE',
    status: 'NEW',
    notes: '',
    assignedToId: '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const response = await fetch('/api/leads', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          assignedToId: formData.assignedToId || null,
        })
      })

      if (response.ok) {
        alert(t('leads.createSuccess') || 'Lead created successfully!')
        router.push('/dashboard/leads')
      } else {
        const error = await response.json()
        alert(`${t('leads.createError') || 'Failed to create lead'}: ${error.error}`)
      }
    } catch (error) {
      console.error('Error creating lead:', error)
      alert(t('leads.createError') || 'Failed to create lead')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className={`flex-1 space-y-6 p-8 pt-6 ${direction === 'rtl' ? 'font-arabic' : ''}`} dir={direction}>
      <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{t('common.back') || 'Back'}</span>
        </Button>
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('leads.createLead') || 'Create New Lead'}</h2>
          <p className="text-muted-foreground">
            {t('leads.createDescription') || 'Add a new potential customer to your leads pipeline'}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('leads.leadInformation') || 'Lead Information'}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Two Column Layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('common.name') || 'Name'} *</Label>
                  <Input
                    id="name"
                    placeholder={t('leads.namePlaceholder') || 'Lead name'}
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nameAr">{t('leads.nameArabic') || 'Name (Arabic)'}</Label>
                  <Input
                    id="nameAr"
                    placeholder={t('leads.nameArabicPlaceholder') || 'اسم العميل المحتمل'}
                    dir="rtl"
                    className={direction === 'rtl' ? 'text-right' : 'text-left'}
                    value={formData.nameAr}
                    onChange={(e) => setFormData(prev => ({ ...prev, nameAr: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company">{t('common.company') || 'Company'}</Label>
                  <Input
                    id="company"
                    placeholder={t('leads.companyPlaceholder') || 'Company name'}
                    value={formData.company}
                    onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">{t('common.email') || 'Email'}</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t('leads.emailPlaceholder') || '<EMAIL>'}
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="mobile">{t('common.mobile') || 'Mobile'} *</Label>
                  <Input
                    id="mobile"
                    placeholder={t('leads.mobilePlaceholder') || '+968 9123 4567'}
                    value={formData.mobile}
                    onChange={(e) => setFormData(prev => ({ ...prev, mobile: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">{t('common.phone') || 'Phone'}</Label>
                  <Input
                    id="phone"
                    placeholder={t('leads.phonePlaceholder') || '+968 2456 7890'}
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  />
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="source">{t('leads.leadSource') || 'Lead Source'}</Label>
                  <Select value={formData.source} onValueChange={(value) => setFormData(prev => ({ ...prev, source: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('leads.selectSource') || 'Select source'} />
                    </SelectTrigger>
                    <SelectContent>
                      {LEAD_SOURCES.map(source => (
                        <SelectItem key={source.value} value={source.value}>{source.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">{t('common.status') || 'Status'}</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('leads.selectStatus') || 'Select status'} />
                    </SelectTrigger>
                    <SelectContent>
                      {LEAD_STATUSES.map(status => (
                        <SelectItem key={status.value} value={status.value}>{status.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="city">{t('common.city') || 'City'}</Label>
                  <Input
                    id="city"
                    placeholder={t('leads.cityPlaceholder') || 'City'}
                    value={formData.city}
                    onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">{t('common.country') || 'Country'}</Label>
                  <Input
                    id="country"
                    value={formData.country}
                    onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">{t('common.address') || 'Address'}</Label>
                  <Textarea
                    id="address"
                    placeholder={t('leads.addressPlaceholder') || 'Street address'}
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">{t('common.notes') || 'Notes'}</Label>
                  <Textarea
                    id="notes"
                    placeholder={t('leads.notesPlaceholder') || 'Additional notes about this lead...'}
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className={`flex justify-end ${direction === 'rtl' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                {t('common.cancel') || 'Cancel'}
              </Button>
              <Button type="submit" disabled={saving}>
                <Save className="h-4 w-4 mr-2" />
                {saving ? (t('common.saving') || 'Saving...') : (t('common.save') || 'Save Lead')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
