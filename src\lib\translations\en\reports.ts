export const reports = {
  // Page title and description
  title: "Reports & Analytics",
  description: "Comprehensive business analytics and reporting",
  
  // Date range options
  dateRange: "Date Range",
  selectPeriod: "Select period",
  last7days: "Last 7 days",
  last30days: "Last 30 days", 
  last3months: "Last 3 months",
  last6months: "Last 6 months",
  lastyear: "Last year",
  
  // Actions
  export: "Export",
  download: "Download",
  
  // Summary cards
  summary: {
    totalRevenue: "Total Revenue",
    totalOrders: "Total Orders",
    activeCustomers: "Active Customers",
    avgOrderValue: "Avg Order Value",
    profitMargin: "profit margin",
    totalInvoicesGenerated: "Total invoices generated",
    customersInPeriod: "Customers in period",
    perInvoiceAverage: "Per invoice average"
  },
  
  // Tab labels
  tabs: {
    salesAnalytics: "Sales Analytics",
    productPerformance: "Product Performance",
    customerAnalytics: "Customer Analytics",
    taskPerformance: "Task Performance"
  },
  
  // Sales tab
  sales: {
    salesOverview: "Sales Overview",
    monthlySalesPerformance: "Monthly sales performance",
    invoiceStatistics: "Invoice Statistics",
    invoiceStatusBreakdown: "Invoice status breakdown",
    paidInvoices: "Paid Invoices",
    pendingInvoices: "Pending Invoices",
    overdueInvoices: "Overdue Invoices"
  },
  
  // Products tab
  products: {
    topSellingProducts: "Top Selling Products/Services",
    bestPerformingItems: "Best performing items this period",
    unitsSold: "units sold",
    revenue: "Revenue"
  },
  
  // Customers tab
  customers: {
    topCustomers: "Top Customers",
    highestValueCustomers: "Highest value customers this period",
    orders: "orders",
    totalSpent: "Total spent"
  },
  
  // Tasks tab
  tasks: {
    taskCompletionRate: "Task Completion Rate",
    employeePerformanceMetrics: "Employee performance metrics",
    taskStatusOverview: "Task Status Overview",
    currentTaskDistribution: "Current task distribution",
    completedTasks: "Completed Tasks",
    inProgress: "In Progress",
    pending: "Pending",
    overdue: "Overdue"
  },
  
  // Loading and error states
  loading: "Loading reports data...",
  noData: "No reports data available",
  error: "Error loading reports data"
}; 