"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  ArrowLeft,
  Edit,
  Package,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  MoreHorizontal,
  Trash2,
  Copy,
  Archive
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"

interface Product {
  id: string
  name: string
  nameAr?: string
  sku: string
  category: string
  type: 'PHYSICAL' | 'SERVICE'
  price: number
  costPrice?: number
  currentStock: number
  minStock: number
  unit: string
  description?: string
  image?: string
  supplier: string
  lastOrderDate: string
  totalSold: number
  revenue: number
  isActive: boolean
  // Keep cost for backward compatibility display
  cost?: number
}

interface SalesRecord {
  id: string
  date: string
  invoiceNumber: string
  customer: string
  quantity: number
  unitPrice: number
  total: number
}

interface PurchaseRecord {
  id: string
  date: string
  purchaseOrderNumber: string
  supplier: string
  quantity: number
  unitCost: number
  total: number
}

// Product data will be fetched from API

const mockSalesRecords: SalesRecord[] = [
  {
    id: "1",
    date: "2024-01-20",
    invoiceNumber: "INV-001",
    customer: "Ahmed Al-Rashid",
    quantity: 50,
    unitPrice: 12.500,
    total: 625
  },
  {
    id: "2",
    date: "2024-01-18",
    invoiceNumber: "INV-002",
    customer: "Tech Solutions Ltd",
    quantity: 25,
    unitPrice: 12.500,
    total: 312.5
  },
  {
    id: "3",
    date: "2024-01-15",
    invoiceNumber: "INV-003",
    customer: "Modern Solutions",
    quantity: 100,
    unitPrice: 12.500,
    total: 1250
  },
]

const mockPurchaseRecords: PurchaseRecord[] = [
  {
    id: "1",
    date: "2024-01-15",
    purchaseOrderNumber: "PO-001",
    supplier: "Al-Noor Trading LLC",
    quantity: 500,
    unitCost: 8.750,
    total: 4375
  },
  {
    id: "2",
    date: "2024-01-10",
    purchaseOrderNumber: "PO-002",
    supplier: "Al-Noor Trading LLC",
    quantity: 300,
    unitCost: 8.750,
    total: 2625
  },
]

export default function ProductDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true)

        const response = await fetch(`/api/products/${params.id}`)
        if (response.ok) {
          const productData = await response.json()

          // Transform the API data to match the expected format
          const transformedProduct = {
            ...productData,
            category: productData.category?.name || 'No Category',
            supplier: productData.supplier?.name || 'No Supplier',
            totalSold: 0, // This would come from sales data
            revenue: 0, // This would come from sales data
            lastOrderDate: productData.createdAt || new Date().toISOString(),
          }

          setProduct(transformedProduct)
        } else {
          setProduct(null)
        }
      } catch (error) {
        console.error('Error fetching product:', error)
        setProduct(null)
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchProduct()
    }
  }, [params.id])

  const handleEdit = () => {
    router.push(`/dashboard/products/${product?.id}/edit`)
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this product?')) {
      console.log('Deleting product:', product?.name)
      alert('Delete functionality would be implemented here')
      router.push('/dashboard/products')
    }
  }

  const handleDuplicate = () => {
    console.log('Duplicating product:', product?.name)
    alert('Duplicate functionality would be implemented here')
  }

  const handleArchive = () => {
    if (confirm(`Are you sure you want to ${product?.isActive ? 'archive' : 'activate'} this product?`)) {
      console.log(`${product?.isActive ? 'Archiving' : 'Activating'} product:`, product?.name)
      alert('Archive/Activate functionality would be implemented here')
    }
  }

  const isLowStock = () => {
    return product?.type === 'PHYSICAL' && product.currentStock <= product.minStock
  }

  const profitMargin = () => {
    if (!product || !product.costPrice) return 0
    return ((product.price - product.costPrice) / product.price * 100)
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Loading...</h2>
          </div>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Product Not Found</h2>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              {isLowStock() && (
                <AlertTriangle className="h-5 w-5 text-orange-500" />
              )}
              <h2 className="text-3xl font-bold tracking-tight">{product.name}</h2>
            </div>
            <p className="text-muted-foreground" dir="rtl">
              {product.nameAr}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge variant={product.isActive ? "default" : "secondary"}>
            {product.isActive ? "Active" : "Inactive"}
          </Badge>

          <Badge variant={product.type === 'PHYSICAL' ? 'default' : 'secondary'}>
            {product.type === 'PHYSICAL' ? 'Physical' : 'Service'}
          </Badge>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Product
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDuplicate}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleArchive}>
                <Archive className="mr-2 h-4 w-4" />
                {product.isActive ? 'Archive' : 'Activate'}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Product
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Product Information */}
          <Card>
            <CardHeader>
              <CardTitle>Product Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Basic Information</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">SKU:</span>
                        <span className="font-mono text-sm">{product.sku}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Category:</span>
                        <Badge variant="outline">{product.category}</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Type:</span>
                        <Badge variant={product.type === 'PHYSICAL' ? 'default' : 'secondary'}>
                          {product.type === 'PHYSICAL' ? 'Physical' : 'Service'}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Pricing</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Selling Price:</span>
                        <span className="font-medium">{formatCurrency(product.price)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Cost Price:</span>
                        <span className="text-muted-foreground">{formatCurrency(product.costPrice || 0)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Profit Margin:</span>
                        <span className={`font-medium ${profitMargin() > 30 ? 'text-green-600' : profitMargin() > 15 ? 'text-orange-600' : 'text-red-600'}`}>
                          {profitMargin().toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {product.type === 'PHYSICAL' && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Inventory</Label>
                      <div className="mt-2 space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Current Stock:</span>
                          <span className={`font-medium ${isLowStock() ? 'text-orange-600' : 'text-green-600'}`}>
                            {product.currentStock} {product.unit}
                            {isLowStock() && <span className="text-xs ml-1">(Low!)</span>}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Minimum Stock:</span>
                          <span className="text-muted-foreground">{product.minStock} {product.unit}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Unit:</span>
                          <span className="text-muted-foreground">{product.unit}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Supplier Information</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Supplier:</span>
                        <span className="text-sm">{product.supplier}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Last Order:</span>
                        <span className="text-sm">{new Date(product.lastOrderDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Sales Performance</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Total Sold:</span>
                        <span className="font-medium">{product.totalSold} {product.unit}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Total Revenue:</span>
                        <span className="font-medium text-green-600">{formatCurrency(product.revenue)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {product.description && (
                <div className="mt-6">
                  <Label className="text-sm font-medium text-muted-foreground">Description</Label>
                  <p className="text-sm text-muted-foreground mt-1">{product.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tabs for Sales and Purchase History */}
          <Card>
            <CardContent className="p-0">
              <Tabs defaultValue="sales" className="w-full">
                <div className="px-6 pt-6">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="sales">Sales History</TabsTrigger>
                    <TabsTrigger value="purchases">Purchase History</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="sales" className="px-6 pb-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Sales History</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Invoice #</TableHead>
                          <TableHead>Customer</TableHead>
                          <TableHead>Quantity</TableHead>
                          <TableHead>Unit Price</TableHead>
                          <TableHead className="text-right">Total</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockSalesRecords.map((sale) => (
                          <TableRow key={sale.id}>
                            <TableCell>{new Date(sale.date).toLocaleDateString()}</TableCell>
                            <TableCell className="font-medium">
                              <Button
                                variant="link"
                                className="p-0 h-auto font-medium"
                                onClick={() => router.push(`/dashboard/invoices/${sale.invoiceNumber}`)}
                              >
                                {sale.invoiceNumber}
                              </Button>
                            </TableCell>
                            <TableCell>{sale.customer}</TableCell>
                            <TableCell>{sale.quantity} {product.unit}</TableCell>
                            <TableCell>{formatCurrency(sale.unitPrice)}</TableCell>
                            <TableCell className="text-right font-medium">
                              {formatCurrency(sale.total)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>

                <TabsContent value="purchases" className="px-6 pb-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Purchase History</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>PO #</TableHead>
                          <TableHead>Supplier</TableHead>
                          <TableHead>Quantity</TableHead>
                          <TableHead>Unit Cost</TableHead>
                          <TableHead className="text-right">Total</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockPurchaseRecords.map((purchase) => (
                          <TableRow key={purchase.id}>
                            <TableCell>{new Date(purchase.date).toLocaleDateString()}</TableCell>
                            <TableCell className="font-medium">
                              <Button
                                variant="link"
                                className="p-0 h-auto font-medium"
                                onClick={() => router.push(`/dashboard/purchases/${purchase.purchaseOrderNumber}`)}
                              >
                                {purchase.purchaseOrderNumber}
                              </Button>
                            </TableCell>
                            <TableCell>{purchase.supplier}</TableCell>
                            <TableCell>{purchase.quantity} {product.unit}</TableCell>
                            <TableCell>{formatCurrency(purchase.unitCost)}</TableCell>
                            <TableCell className="text-right font-medium">
                              {formatCurrency(purchase.total)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Product Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Product Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Selling Price:</span>
                <span className="font-medium">{formatCurrency(product.price)}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Cost Price:</span>
                <span className="text-muted-foreground">{formatCurrency(product.costPrice || 0)}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Profit Margin:</span>
                <span className={`font-medium ${profitMargin() > 30 ? 'text-green-600' : profitMargin() > 15 ? 'text-orange-600' : 'text-red-600'}`}>
                  {profitMargin().toFixed(1)}%
                </span>
              </div>

              {product.type === 'PHYSICAL' && (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Current Stock:</span>
                    <span className={`font-medium ${isLowStock() ? 'text-orange-600' : 'text-green-600'}`}>
                      {product.currentStock} {product.unit}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Min Stock:</span>
                    <span className="text-muted-foreground">{product.minStock} {product.unit}</span>
                  </div>
                </>
              )}

              <Separator />

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Total Sold:</span>
                <span className="font-medium">{product.totalSold} {product.unit}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Total Revenue:</span>
                <span className="font-medium text-green-600">{formatCurrency(product.revenue)}</span>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                <Badge variant={product.isActive ? "default" : "secondary"}>
                  {product.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Product Image */}
          {product.image && (
            <Card>
              <CardHeader>
                <CardTitle>Product Image</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-square w-full overflow-hidden rounded-lg border">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="h-full w-full object-cover"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Revenue</span>
                </div>
                <span className="text-sm font-medium">{formatCurrency(product.revenue)}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">Units Sold</span>
                </div>
                <span className="text-sm font-medium">{product.totalSold}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${profitMargin() > 30 ? 'bg-green-500' : profitMargin() > 15 ? 'bg-orange-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm">Profit Margin</span>
                </div>
                <span className="text-sm font-medium">{profitMargin().toFixed(1)}%</span>
              </div>

              {product.type === 'PHYSICAL' && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${isLowStock() ? 'bg-orange-500' : 'bg-green-500'}`}></div>
                    <span className="text-sm">Stock Level</span>
                  </div>
                  <span className="text-sm font-medium">{isLowStock() ? 'Low' : 'Good'}</span>
                </div>
              )}

              <Separator />

              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  Last updated: {new Date().toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}