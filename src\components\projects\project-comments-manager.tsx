"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { MessageSquare, Plus, Edit, Trash2, Pin, MoreHorizontal } from "lucide-react"

interface ProjectCommentsManagerProps {
  projectId: string
  isEditable?: boolean
  comments?: any[]
  setComments?: React.Dispatch<React.SetStateAction<any[]>>
}

export function ProjectCommentsManager({ 
  projectId, 
  isEditable = true, 
  comments: externalComments,
  setComments: externalSetComments 
}: ProjectCommentsManagerProps) {
  const [newComment, setNewComment] = useState("")
  
  // Use external state if provided, otherwise use internal state
  const [internalComments, setInternalComments] = useState<any[]>([])
  
  const comments = externalComments || internalComments
  const setComments = externalSetComments || setInternalComments
  const [editingComment, setEditingComment] = useState<string | null>(null)
  const [editContent, setEditContent] = useState("")

  useEffect(() => {
    // Only fetch once when component mounts if no data exists
    if (!externalComments || (Array.isArray(externalComments) && externalComments.length === 0)) {
      fetchComments()
    }
  }, [projectId]) // Only depend on projectId to avoid infinite loops

  const fetchComments = async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}/comments`)
      if (response.ok) {
        const data = await response.json()
        
        // Update the appropriate state (external if provided, otherwise internal)
        if (externalSetComments) {
          externalSetComments(data)
        } else {
          setInternalComments(data)
        }
      }
    } catch (error) {
      console.error("Error fetching comments:", error)
    }
  }

  const handleAddComment = async () => {
    if (!newComment.trim()) return

    try {
      const response = await fetch(`/api/projects/${projectId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: newComment,
          isPinned: false
        })
      })

      if (response.ok) {
        const newCommentData = await response.json()
        setComments((prev: any[]) => [newCommentData, ...prev])
        setNewComment("")
      } else {
        throw new Error('Failed to create comment')
      }
    } catch (error) {
      console.error("Error creating comment:", error)
      alert("Failed to create note")
    }
  }

  const handleDeleteComment = async (commentId: string) => {
    if (confirm("Are you sure you want to delete this note?")) {
      try {
        const response = await fetch(`/api/projects/${projectId}/comments/${commentId}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          setComments((prev: any[]) => prev.filter((comment: any) => comment.id !== commentId))
          alert("Note deleted successfully")
        } else {
          throw new Error('Failed to delete comment')
        }
      } catch (error) {
        console.error("Error deleting comment:", error)
        alert("Failed to delete note")
      }
    }
  }

  const handleTogglePin = async (commentId: string) => {
    try {
      const currentComment = comments.find((c: any) => c.id === commentId)
      if (!currentComment) return

      const response = await fetch(`/api/projects/${projectId}/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isPinned: !currentComment.isPinned
        })
      })

      if (response.ok) {
        const updatedComment = await response.json()
        setComments((prev: any[]) => prev.map((comment: any) => 
          comment.id === commentId ? updatedComment : comment
        ))
      } else {
        throw new Error('Failed to update comment')
      }
    } catch (error) {
      console.error("Error updating comment:", error)
      alert("Failed to update note")
    }
  }

  const handleEditComment = (comment: any) => {
    setEditingComment(comment.id)
    setEditContent(comment.content)
  }

  const handleSaveEdit = async (commentId: string) => {
    if (!editContent.trim()) return

    try {
      const response = await fetch(`/api/projects/${projectId}/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: editContent
        })
      })

      if (response.ok) {
        const updatedComment = await response.json()
        setComments((prev: any[]) => prev.map((comment: any) => 
          comment.id === commentId ? updatedComment : comment
        ))
        
        setEditingComment(null)
        setEditContent("")
      } else {
        throw new Error('Failed to update comment')
      }
    } catch (error) {
      console.error("Error updating comment:", error)
      alert("Failed to update comment")
    }
  }

  const handleCancelEdit = () => {
    setEditingComment(null)
    setEditContent("")
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <MessageSquare className="h-5 w-5" />
        <h3 className="text-lg font-semibold">Project Notes & Comments</h3>
        <Badge variant="secondary">{comments.length} notes</Badge>
      </div>

      {/* Add New Comment */}
      {isEditable && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Add New Note</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="Add a note about this project..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              rows={3}
            />
            <div className="flex justify-end">
              <Button onClick={handleAddComment} disabled={!newComment.trim()}>
                <Plus className="h-4 w-4 mr-2" />
                Add Note
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Comments List */}
      <div className="space-y-4">
        {comments.map((comment) => (
          <Card key={comment.id} className={comment.isPinned ? 'border-primary bg-primary/5' : ''}>
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="text-xs">
                    {comment.author.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-sm">{comment.author.name}</span>
                      {comment.isPinned && (
                        <Badge variant="secondary" className="text-xs">
                          <Pin className="h-3 w-3 mr-1" />
                          Pinned
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        {new Date(comment.createdAt).toLocaleDateString()}
                      </span>
                      
                      {isEditable && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleTogglePin(comment.id)}>
                              <Pin className="mr-2 h-4 w-4" />
                              {comment.isPinned ? 'Unpin' : 'Pin'} Note
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditComment(comment)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteComment(comment.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                  
                  {editingComment === comment.id ? (
                    <div className="space-y-2">
                      <Textarea
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        rows={3}
                      />
                      <div className="flex gap-2">
                        <Button size="sm" onClick={() => handleSaveEdit(comment.id)}>
                          Save
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          onClick={handleCancelEdit}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm leading-relaxed whitespace-pre-wrap">
                      {comment.content}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
} 