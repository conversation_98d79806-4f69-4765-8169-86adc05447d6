"use client"

// Session storage manager for handling browser close detection
export class SessionStorageManager {
  private static instance: SessionStorageManager
  private sessionId: string
  private heartbeatInterval: NodeJS.Timeout | null = null
  private readonly HEARTBEAT_KEY = 'session_heartbeat'
  private readonly SESSION_ID_KEY = 'session_id'
  private readonly LAST_ACTIVITY_KEY = 'last_activity'
  private readonly HEARTBEAT_INTERVAL_MS = 30000 // 30 seconds

  private constructor() {
    this.sessionId = this.generateSessionId()
    this.initializeSession()
  }

  public static getInstance(): SessionStorageManager {
    if (!SessionStorageManager.instance) {
      SessionStorageManager.instance = new SessionStorageManager()
    }
    return SessionStorageManager.instance
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private initializeSession(): void {
    // Set session ID in sessionStorage (cleared when browser/tab closes)
    sessionStorage.setItem(this.SESSION_ID_KEY, this.sessionId)
    
    // Set initial heartbeat
    this.updateHeartbeat()
    
    // Start heartbeat interval
    this.startHeartbeat()
    
    // Listen for beforeunload to clean up
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this))
    
    // Listen for storage events to detect other tabs
    window.addEventListener('storage', this.handleStorageChange.bind(this))
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.updateHeartbeat()
    }, this.HEARTBEAT_INTERVAL_MS)
  }

  private updateHeartbeat(): void {
    const timestamp = Date.now()
    localStorage.setItem(this.HEARTBEAT_KEY, timestamp.toString())
    localStorage.setItem(this.LAST_ACTIVITY_KEY, timestamp.toString())
  }

  private handleBeforeUnload(): void {
    // Clear session data when browser/tab is closing
    this.cleanup()
  }

  private handleStorageChange(event: StorageEvent): void {
    // Detect if another tab is active
    if (event.key === this.HEARTBEAT_KEY && event.newValue) {
      const otherTabTimestamp = parseInt(event.newValue)
      const currentTimestamp = Date.now()
      
      // If another tab updated heartbeat very recently, this tab might be inactive
      if (currentTimestamp - otherTabTimestamp < this.HEARTBEAT_INTERVAL_MS / 2) {
        // Another tab is active, reduce this tab's activity
        this.reduceActivity()
      }
    }
  }

  private reduceActivity(): void {
    // Reduce heartbeat frequency for inactive tabs
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = setInterval(() => {
        this.updateHeartbeat()
      }, this.HEARTBEAT_INTERVAL_MS * 2) // Double the interval for inactive tabs
    }
  }

  public updateActivity(): void {
    this.updateHeartbeat()
    
    // Restore normal heartbeat if it was reduced
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.startHeartbeat()
    }
  }

  public getLastActivity(): number {
    const lastActivity = localStorage.getItem(this.LAST_ACTIVITY_KEY)
    return lastActivity ? parseInt(lastActivity) : Date.now()
  }

  public isSessionExpired(maxInactiveTime: number = 8 * 60 * 60 * 1000): boolean {
    const lastActivity = this.getLastActivity()
    return Date.now() - lastActivity > maxInactiveTime
  }

  public cleanup(): void {
    // Clear intervals
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    
    // Clear session storage
    sessionStorage.removeItem(this.SESSION_ID_KEY)
    
    // Clear localStorage items
    localStorage.removeItem(this.HEARTBEAT_KEY)
    localStorage.removeItem(this.LAST_ACTIVITY_KEY)
    
    // Remove event listeners
    window.removeEventListener('beforeunload', this.handleBeforeUnload.bind(this))
    window.removeEventListener('storage', this.handleStorageChange.bind(this))
  }

  public getSessionId(): string {
    return this.sessionId
  }

  public isValidSession(): boolean {
    const storedSessionId = sessionStorage.getItem(this.SESSION_ID_KEY)
    return storedSessionId === this.sessionId
  }
}

// Utility functions for easy access
export const sessionManager = {
  getInstance: () => SessionStorageManager.getInstance(),
  updateActivity: () => SessionStorageManager.getInstance().updateActivity(),
  isSessionExpired: (maxInactiveTime?: number) => 
    SessionStorageManager.getInstance().isSessionExpired(maxInactiveTime),
  cleanup: () => SessionStorageManager.getInstance().cleanup(),
  getLastActivity: () => SessionStorageManager.getInstance().getLastActivity(),
  isValidSession: () => SessionStorageManager.getInstance().isValidSession()
}
