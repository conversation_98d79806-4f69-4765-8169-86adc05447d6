"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ArrowLeft, Loader2 } from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { createSupplierPayout } from "@/lib/supplier-payout-storage"

interface Supplier {
  id: string
  name: string
  email?: string
  phone?: string
  company?: string
}

export default function CreatePayoutPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [loading, setLoading] = useState(false)
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [purchases, setPurchases] = useState<any[]>([])
  const [loadingSuppliers, setLoadingSuppliers] = useState(true)

  const [formData, setFormData] = useState({
    supplierId: "",
    purchaseId: "",
    amount: 0,
    method: "BANK_TRANSFER" as 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHECK' | 'OTHER',
    reference: "",
    description: "",
    date: new Date().toISOString().split('T')[0],
    dueDate: "",
    notes: "",
  })

  // Load suppliers and handle URL parameters
  useEffect(() => {
    const loadData = async () => {
      setLoadingSuppliers(true)
      try {
        // Load suppliers
        const suppliersResponse = await fetch('/api/suppliers')
        if (suppliersResponse.ok) {
          const suppliersData = await suppliersResponse.json()
          setSuppliers(suppliersData.suppliers || [])
        }

        // Get URL parameters
        const supplierId = searchParams.get('supplierId')
        const purchaseId = searchParams.get('purchaseId')

        // Set initial form data from URL parameters
        if (supplierId) {
          setFormData(prev => ({ ...prev, supplierId }))

          // Load purchases for this supplier
          const purchasesResponse = await fetch(`/api/purchases?supplierId=${supplierId}`)
          if (purchasesResponse.ok) {
            const purchasesData = await purchasesResponse.json()
            setPurchases(purchasesData.purchases || [])
          }
        }

        if (purchaseId) {
          setFormData(prev => ({ ...prev, purchaseId }))

          // Load specific purchase to get amount
          const purchaseResponse = await fetch(`/api/purchases/${purchaseId}`)
          if (purchaseResponse.ok) {
            const purchaseData = await purchaseResponse.json()
            setFormData(prev => ({
              ...prev,
              amount: Number(purchaseData.total),
              description: `Payment for Purchase Order ${purchaseData.number}`,
            }))
          }
        }
      } catch (error) {
        console.error('Error loading data:', error)
      } finally {
        setLoadingSuppliers(false)
      }
    }

    loadData()
  }, [searchParams])

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Load purchases when supplier changes
    if (field === 'supplierId' && value) {
      loadPurchasesForSupplier(value)
    }
  }

  const loadPurchasesForSupplier = async (supplierId: string) => {
    try {
      const response = await fetch(`/api/purchases?supplierId=${supplierId}`)
      if (response.ok) {
        const data = await response.json()
        setPurchases(data.purchases || [])
      }
    } catch (error) {
      console.error('Error loading purchases:', error)
      setPurchases([])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!formData.supplierId) {
      alert('Please select a supplier')
      return
    }

    if (formData.amount <= 0) {
      alert('Please enter a valid amount')
      return
    }

    setLoading(true)
    try {
      const payout = await createSupplierPayout({
        supplierId: formData.supplierId,
        amount: formData.amount,
        method: formData.method,
        reference: formData.reference || undefined,
        description: formData.description || undefined,
        date: formData.date,
        dueDate: formData.dueDate || undefined,
        purchaseId: formData.purchaseId || undefined,
        notes: formData.notes || undefined,
      })

      if (payout) {
        alert('Payout created successfully!')
        router.push('/dashboard/supplier-payouts')
      }
    } catch (error) {
      console.error('Error creating payout:', error)
      alert(`Failed to create payout: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const selectedSupplier = suppliers.find(s => s.id === formData.supplierId)
  const selectedPurchase = purchases.find(p => p.id === formData.purchaseId)

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Create Supplier Payout</h2>
            <p className="text-muted-foreground">
              Create a new payment to a supplier
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Payout Details */}
            <Card>
              <CardHeader>
                <CardTitle>Payout Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="supplier">Supplier *</Label>
                    {loadingSuppliers ? (
                      <div className="flex items-center space-x-2 p-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-sm">Loading suppliers...</span>
                      </div>
                    ) : (
                      <Select
                        value={formData.supplierId}
                        onValueChange={(value) => handleInputChange('supplierId', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select supplier" />
                        </SelectTrigger>
                        <SelectContent>
                          {suppliers.map((supplier) => (
                            <SelectItem key={supplier.id} value={supplier.id}>
                              {supplier.name} {supplier.company && `(${supplier.company})`}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount *</Label>
                    <Input
                      id="amount"
                      type="number"
                      min="0"
                      step="0.001"
                      placeholder="0.000"
                      value={formData.amount || ''}
                      onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="method">Payment Method *</Label>
                    <Select
                      value={formData.method}
                      onValueChange={(value) => handleInputChange('method', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                        <SelectItem value="CHECK">Check</SelectItem>
                        <SelectItem value="CASH">Cash</SelectItem>
                        <SelectItem value="CARD">Card</SelectItem>
                        <SelectItem value="OTHER">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="reference">Reference</Label>
                    <Input
                      id="reference"
                      placeholder="Transaction reference, check number, etc."
                      value={formData.reference}
                      onChange={(e) => handleInputChange('reference', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    placeholder="What is this payout for?"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                  />
                </div>

                {formData.supplierId && purchases.length > 0 && (
                  <div className="space-y-2">
                    <Label htmlFor="purchase">Related Purchase Order (Optional)</Label>
                    <Select
                      value={formData.purchaseId}
                      onValueChange={(value) => handleInputChange('purchaseId', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select purchase order" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No specific purchase</SelectItem>
                        {purchases.map((purchase) => (
                          <SelectItem key={purchase.id} value={purchase.id}>
                            {purchase.number} - {formatCurrency(purchase.total)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Payout Date *</Label>
                    <Input
                      id="date"
                      type="date"
                      value={formData.date}
                      onChange={(e) => handleInputChange('date', e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="dueDate">Due Date</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={formData.dueDate}
                      onChange={(e) => handleInputChange('dueDate', e.target.value)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Additional notes (optional)"
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    className="min-h-[80px]"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Payout Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Supplier:</span>
                  <span className="font-medium">
                    {selectedSupplier ? selectedSupplier.name : "Not selected"}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span>Amount:</span>
                  <span className="font-medium">{formatCurrency(formData.amount)}</span>
                </div>

                {selectedPurchase && (
                  <div className="flex justify-between">
                    <span>Purchase:</span>
                    <span className="text-sm font-medium">
                      {selectedPurchase.number}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span>Method:</span>
                  <span className="text-sm">
                    {formData.method.replace('_', ' ')}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span>Date:</span>
                  <span className="text-sm">
                    {formData.date ? new Date(formData.date).toLocaleDateString() : "Not set"}
                  </span>
                </div>
                
                {formData.dueDate && (
                  <div className="flex justify-between">
                    <span>Due Date:</span>
                    <span className="text-sm">
                      {new Date(formData.dueDate).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button
                  type="submit"
                  className="w-full"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Payout'
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push('/dashboard/supplier-payouts')}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  )
}
