"use client"

import { useState, useEffect } from "react"
import { Sidebar } from "@/components/layout/sidebar"
import { <PERSON><PERSON> } from "@/components/layout/header"
import { PrayerTimesBanner } from "@/components/notifications/prayer-times-banner"
import { SessionMonitor } from "@/components/auth/session-monitor"
import { AuthGuard } from "@/components/auth/auth-guard"
import { useI18n } from "@/lib/i18n"
import { initializeTheme } from "@/lib/theme"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const { direction } = useI18n()

  // Initialize theme on mount
  useEffect(() => {
    initializeTheme()
  }, [])

  const toggleSidebarCollapse = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <AuthGuard>
      <SessionMonitor />
      <div className={`flex h-screen bg-gray-100 ${direction === 'rtl' ? 'rtl flex-row-reverse' : 'ltr'}`} dir={direction}>
        {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        sidebar-container
        ${sidebarOpen ? 'translate-x-0' : direction === 'rtl' ? 'translate-x-full' : '-translate-x-full'}
        fixed inset-y-0 z-50 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0
        ${direction === 'rtl' ? 'right-0' : 'left-0'}
        ${sidebarCollapsed ? 'lg:w-16' : 'lg:w-64'}
      `}>
        <Sidebar 
          isCollapsed={sidebarCollapsed} 
          onToggleCollapse={toggleSidebarCollapse}
        />
      </div>

      <div className="main-content flex flex-col flex-1 overflow-hidden">
        <Header 
          sidebarOpen={sidebarOpen} 
          setSidebarOpen={setSidebarOpen}
          sidebarCollapsed={sidebarCollapsed}
          onToggleSidebarCollapse={toggleSidebarCollapse}
        />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100">
          <div className="p-4">
            <PrayerTimesBanner />
          </div>
          {children}
        </main>
      </div>
      </div>
    </AuthGuard>
  )
}
