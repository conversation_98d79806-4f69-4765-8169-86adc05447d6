export const suppliers = {
  // Page titles and descriptions
  title: "الموردين",
  dashboard: "لوحة الموردين",
  dashboardDescription: "نظرة عامة على شبكة الموردين ومؤشرات الشراء",
  viewAllSuppliers: "عرض جميع الموردين",
  
  // Supplier management
  addSupplier: "إضافة مورد",
  editSupplier: "تعديل المورد",
  createSupplier: "إنشاء مورد",
  updateSupplier: "تحديث المورد",
  deleteSupplier: "حذف المورد",
  confirmDelete: "هل أنت متأكد من حذف هذا المورد؟",
  confirmDeleteSupplier: "هل أنت متأكد من حذف هذا المورد؟",
  
  // Supplier information
  supplierName: "اسم المورد",
  supplierEmail: "بريد المورد الإلكتروني",
  supplierPhone: "هاتف المورد",
  supplierAddress: "عنوان المورد",
  supplierCompany: "شركة المورد",
  supplierDetails: "تفاصيل المورد",
  supplierInformation: "معلومات المورد",
  supplierNotFound: "المورد غير موجود",
  supplierDetailsAndHistory: "تفاصيل المورد وسجل المشتريات",
  supplierSince: "مورد منذ",
  lastOrder: "آخر طلب",
  lastOrderDate: "تاريخ آخر طلب",
  contactPerson: "الشخص المسؤول",
  taxNumber: "الرقم الضريبي",
  
  // Search and filters
  searchSuppliers: "البحث في الموردين...",
  noSuppliers: "لم يتم العثور على موردين",
  noSuppliersFound: "لم يتم العثور على موردين",
  
  // Financial information
  totalSuppliers: "إجمالي الموردين",
  thisMonth: "هذا الشهر",
  totalPurchases: "إجمالي المشتريات",
  fromLastMonth: "عن الشهر الماضي",
  avgOrderValue: "متوسط قيمة الطلب",
  reliableSuppliers: "الموردون الموثوقون",
  activeSuppliers: "الموردون النشطون",
  inactiveSuppliers: "الموردون غير النشطين",
  outstandingPayments: "المدفوعات المستحقة",
  supplierSummary: "ملخص المورد",
  needReEngagement: "يحتاج لإعادة تفعيل",
  
  // Purchase orders
  purchaseOrders: "أوامر الشراء",
  purchaseOrderNumber: "رقم أمر الشراء",
  createPurchaseOrder: "إنشاء أمر شراء",
  noPurchaseOrdersFound: "لم يتم العثور على أوامر شراء لهذا المورد",
  viewPurchaseDetails: "عرض تفاصيل الشراء",
  editOrder: "تعديل الطلب",
  totalOrders: "إجمالي الطلبات",
  paidOrders: "الطلبات المدفوعة",
  receivedOrders: "الطلبات المستلمة",
  pendingOrders: "الطلبات المعلقة",
  overdueOrders: "الطلبات المتأخرة",
  
  // Payments
  paymentHistory: "سجل المدفوعات",
  createPayout: "إنشاء دفعة",
  lastPayment: "آخر دفعة",
  method: "الطريقة",
  reference: "المرجع",
  noPaymentsFound: "لم يتم العثور على مدفوعات لهذا المورد",
  paymentStatus: "حالة الدفع",
  
  // Products
  supplierProducts: "منتجات المورد",
  products: "المنتجات",
  noProductsFound: "لم يتم العثور على منتجات لهذا المورد",
  sku: "رمز المنتج",
  unitPrice: "سعر الوحدة",
  stock: "المخزون",
  
  // Actions
  quickActions: "إجراءات سريعة",
  sendEmail: "إرسال بريد إلكتروني",
  callSupplier: "الاتصال بالمورد",
  viewDetails: "عرض التفاصيل",
  actions: "الإجراءات",
  
  // Tabs
  ordersTab: "الطلبات",
  paymentsTab: "المدفوعات",
  productsTab: "المنتجات",
  
  // Other fields
  dueDate: "تاريخ الاستحقاق",
  balance: "الرصيد",
  status: "الحالة",
  active: "نشط",
  inactive: "غير نشط",
  
  // UI elements
  openMenu: "فتح القائمة",
  back: "رجوع",
  loading: "جاري التحميل...",
  
  // Messages
  supplierAdded: "تم إضافة المورد بنجاح",
  supplierUpdated: "تم تحديث المورد بنجاح",
  supplierDeleted: "تم حذف المورد بنجاح"
} as const; 