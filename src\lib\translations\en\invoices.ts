export const invoices = {
  // Page titles and descriptions
  title: "Invoices",
  description: "Manage and track customer invoices",
  createInvoiceTitle: "Create Invoice",
  createInvoiceDescription: "Create a new invoice for your customer",
  editInvoice: "Edit Invoice",
  viewInvoice: "View Invoice",
  duplicateInvoice: "Duplicate Invoice",
  
  // Invoice information
  customerInformation: "Customer Information",
  invoiceNumber: "Invoice Number",
  invoiceDate: "Invoice Date",
  dueDate: "Due Date",
  customer: "Customer",
  selectCustomer: "Select Customer",
  billTo: "Bill To",
  shipTo: "Ship To",
  
  // Invoice details
  items: "Items",
  itemDescription: "Description",
  quantity: "Quantity",
  unitPrice: "Unit Price",
  discount: "Discount",
  amount: "Amount",
  addItem: "Add Item",
  removeItem: "Remove Item",
  
  // Calculations
  subtotal: "Subtotal",
  tax: "Tax",
  taxRate: "Tax Rate",
  totalAmount: "Total Amount",
  amountPaid: "Amount Paid",
  amountDue: "Amount Due",
  
  // Invoice status
  draft: "Draft",
  pending: "Pending",
  sent: "Sent",
  paid: "Paid",
  overdue: "Overdue",
  cancelled: "Cancelled",
  pendingPayment: "Pending Payment",
  
  // Actions
  saveDraft: "Save Draft",
  saveAndSend: "Save & Send",
  print: "Print",
  download: "Download",
  send: "Send",
  markAsPaid: "Mark as Paid",
  createFinal: "Create Final",
  delete: "Delete",
  
  // Payment information
  paymentTerms: "Payment Terms",
  paymentMethod: "Payment Method",
  paymentDate: "Payment Date",
  paymentReference: "Payment Reference",
  
  // Customer management
  pleaseEnterNameMobile: "Please enter name and mobile number",
  customerMobileExists: "A customer with this mobile number already exists",
  customerAddedSuccessfully: "Customer added successfully",
  failedToCreateCustomer: "Failed to create customer",
  newCustomer: "New Customer",
  
  // Validation messages
  pleaseAllowPopups: "Please allow popups",
  pleaseSelectCustomer: "Please select a customer",
  pleaseAddInvoiceItem: "Please add an invoice item",
  invoiceNumberRequired: "Invoice number is required",
  dueDateRequired: "Due date is required",
  itemDescriptionRequired: "Item description is required",
  quantityRequired: "Quantity is required",
  unitPriceRequired: "Unit price is required",
  checkItemQuantitiesPrices: "Please check item quantities and prices. All items must have valid quantities and non-negative prices.",
  
  // Success/Error messages
  invoiceCreatedSuccessfully: "Invoice created successfully",
  invoiceUpdatedSuccessfully: "Invoice updated successfully",
  invoiceDeletedSuccessfully: "Invoice deleted successfully",
  invoiceSentSuccessfully: "Invoice sent successfully",
  paymentRecordedSuccessfully: "Payment recorded successfully",
  failedToCreateInvoice: "Failed to create invoice",
  failedToUpdateInvoice: "Failed to update invoice",
  failedToDeleteInvoice: "Failed to delete invoice",
  failedToSendInvoice: "Failed to send invoice",
  failedToSaveInvoice: "Failed to save invoice",
  failedToLoadInvoiceData: "Failed to load invoice data.",
  
  // Filters and search
  searchInvoices: "Search invoices...",
  searchCustomer: "Search customer...",
  filterByStatus: "Filter by Status",
  filterByCustomer: "Filter by Customer",
  allStatus: "All Status",
  dateRange: "Date Range",
  
  // Statistics
  totalInvoices: "Total Invoices",
  paidInvoices: "Paid Invoices",
  pendingInvoices: "Pending Invoices",
  overdueInvoices: "Overdue Invoices",
  totalRevenue: "Total Revenue",
  
  // Empty states
  noInvoicesFound: "No invoices found",
  noInvoicesFoundSearch: "No invoices found matching your search",
  createFirstInvoice: "Create your first invoice",
  
  // Confirmations
  confirmDeleteInvoice: "Are you sure you want to delete this invoice?",
  confirmMarkAsPaid: "Are you sure you want to mark this invoice as paid?",
  
  // Notes
  notes: "Notes",
  addNotesPlaceholder: "Add notes or terms and conditions...",
  thankYouNote: "Thank you for your business!",
  terms: "Terms & Conditions",
  footer: "Footer",
  
  // Loading states
  loadingInvoices: "Loading invoices...",
  savingInvoice: "Saving invoice...",
  sendingInvoice: "Sending invoice...",
  deletingInvoice: "Deleting invoice...",
  loadingInvoice: "Loading Invoice...",
  
  // Dashboard metrics
  pendingAmount: "Pending Amount",
  nanPercentOfTotal: "NaN% of total",
  acrossAllInvoices: "Across all invoices",
  paidUnpaid: "paid, 0 unpaid",
  
  // Table headers and filters
  actions: "Actions",
  balance: "Balance",
  source: "Source",
  moreFilters: "More Filters",
  export: "Export",
  allTime: "All Time",
  
  // Actions menu
  view: "View",
  edit: "Edit",
  save: "Save",
  duplicate: "Duplicate",
  recordPayment: "Record Payment",
  email: "Email",
  
  // Status messages
  manageYourInvoicesAndTrackPayments: "Manage your invoices and track payments",
  createInvoice: "Create Invoice",

  // Create Invoice Page - Additional translations
  // Form labels and placeholders
  dueDateLabel: "Due Date",
  linkToTask: "Link to Task (Optional)",
  selectTask: "Select task",
  searchTasks: "Search tasks...",
  loadingTasks: "Loading tasks...",
  noTaskFound: "No task found",
  taskDescriptionAutoAdd: "Task description will be added to the first item automatically",
  invoiceStatus: "Invoice Status",
  selectStatus: "Select status",
  final: "Final",
  convertedFromQuotation: "Converted from Quotation",
  fromQuotation: "From Quotation:",
  convertedFromQuotationDesc: "This invoice was created from an approved quotation",
  
  // Customer search and management
  searchCustomersByNameMobile: "Search customers by name or mobile...",
  loadingCustomers: "Loading customers...",
  noCustomerFound: "No customer found",
  addNewCustomer: "Add New Customer",
  addNewCustomerTitle: "Add New Customer",
  addNewCustomerDesc: "Create a new customer record. We'll check if the mobile number already exists",
  customerName: "Customer Name",
  customerNameRequired: "Customer Name *",
  enterCustomerName: "Enter customer name",
  mobileNumber: "Mobile Number",
  mobileNumberRequired: "Mobile Number *",
  mobilePlaceholder: "+968 9XXX XXXX",
  emailOptional: "Email (Optional)",
  customerExists: "Customer exists:",
  selectFromDropdown: "You can select this customer from the dropdown instead",
  cancel: "Cancel",
  addCustomer: "Add Customer",
  
  // Invoice items section
  invoiceItems: "Invoice Items",
  itemHeader: "Item",
  image: "Image",
  productService: "Product/Service",
  unitPriceOMR: "Unit Price (OMR)",
  total: "Total",
  action: "Action",
  itemDescriptionPlaceholder: "Item description",
  selectProduct: "Select product...",
  searchProducts: "Search products...",
  loadingProducts: "Loading products...",
  noProductFound: "No product found",
  stock: "Stock",
  inStock: "In Stock",
  outOfStock: "Out of Stock",
  
  // Discount section
  discountType: "Discount Type",
  discountAmount: "Discount Amount",
  discountPercentage: "Discount Percentage",
  percentage: "Percentage",
  enterPercentage: "Enter percentage (0-100%)",
  enterAmountOMR: "Enter amount in OMR",
  currencyOMR: "OMR",
  
  // Payment settings
  paymentSettings: "Payment Settings",
  preferredPaymentMethod: "Preferred Payment Method",
  selectPaymentTerms: "Select payment terms",
  selectPaymentMethod: "Select payment method",
  recordPaymentOnCreation: "Record Payment on Creation",
  paymentAmount: "Payment Amount",
  paymentAmountPlaceholder: "Payment amount",
  autoRecordPaymentDesc: "Check to automatically record payment when creating the invoice",
  
  // Payment terms
  dueImmediately: "Due Immediately",
  net7Days: "Net 7 Days",
  net15Days: "Net 15 Days",
  net30Days: "Net 30 Days",
  net60Days: "Net 60 Days",
  net90Days: "Net 90 Days",
  
  // Payment methods
  cash: "Cash",
  bankTransfer: "Bank Transfer",
  check: "Check",
  creditCard: "Credit Card",
  debitCard: "Debit Card",
  
  // Invoice summary
  invoiceSummary: "Invoice Summary",
  discountLabel: "Discount",
  taxLabel: "Tax",
  totalLabel: "Total",
  balanceDue: "Balance Due",
  paymentMethodLabel: "Payment Method",
  invoiceWillBeMarkedAsPaid: "✅ Invoice will be marked as PAID",
  
  // Action buttons
  saveAsDraft: "Save as Draft",
  savePrint: "Save & Print",
  createFinalInvoice: "Create Final Invoice",
  createPrintInvoice: "Create & Print Invoice",
  creating: "Creating...",
  
  // Notes section
  notesOptional: "Notes (Optional)",

  // Print Preview
  invoicePreview: "Invoice Preview",
  invoiceDetailsTitle: "Invoice Details",
  date: "Date:",
  item: "Item",
  qty: "Qty",
  price: "Price",
  status: "Status"
}; 