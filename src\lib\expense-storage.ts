/**
 * Expense API utility - replaces localStorage with database API calls
 */

export interface Expense {
  id: string
  number: string
  date: string
  description: string
  amount: number
  paymentMethod: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHECK' | 'OTHER'
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID'
  receipt?: string
  notes?: string
  createdAt: string
  updatedAt: string
  expenseTypeId: string
  expenseType: {
    id: string
    name: string
    nameAr?: string
  }
  createdById: string
  createdBy: {
    id: string
    name: string
    email: string
  }
}

export interface ExpenseFilters {
  search?: string
  status?: string
  expenseTypeId?: string
  page?: number
  limit?: number
}

export interface ExpenseResponse {
  expenses: Expense[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// Get all expenses from API
export const getExpenses = async (filters: ExpenseFilters = {}): Promise<ExpenseResponse> => {
  try {
    const params = new URLSearchParams()
    
    if (filters.search) params.append('search', filters.search)
    if (filters.status) params.append('status', filters.status)
    if (filters.expenseTypeId) params.append('expenseTypeId', filters.expenseTypeId)
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())

    const response = await fetch(`/api/expenses?${params.toString()}`)
    if (!response.ok) {
      throw new Error('Failed to fetch expenses')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading expenses:', error)
    return {
      expenses: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        pages: 0,
      },
    }
  }
}

// Add new expense via API
export const addExpense = async (expense: {
  description: string
  amount: number
  expenseTypeId: string
  paymentMethod?: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHECK' | 'OTHER'
  status?: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID'
  receipt?: string
  notes?: string
  date?: string
  createdById: string
}): Promise<Expense | null> => {
  try {
    const response = await fetch('/api/expenses', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(expense),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create expense')
    }

    return await response.json()
  } catch (error) {
    console.error('Error creating expense:', error)
    return null
  }
}

// Update existing expense via API
export const updateExpense = async (id: string, updates: {
  description?: string
  amount?: number
  expenseTypeId?: string
  paymentMethod?: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHECK' | 'OTHER'
  status?: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID'
  receipt?: string
  notes?: string
  date?: string
}): Promise<Expense | null> => {
  try {
    const response = await fetch(`/api/expenses/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update expense')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating expense:', error)
    return null
  }
}

// Delete expense via API
export const deleteExpense = async (id: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/expenses/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      console.error('Delete expense API error:', error)
      throw new Error(error.error || 'Failed to delete expense')
    }

    return true
  } catch (error) {
    console.error('Error deleting expense:', error)
    throw error // Re-throw to show the actual error message
  }
}

// Get expense by ID via API
export const getExpenseById = async (id: string): Promise<Expense | null> => {
  try {
    const response = await fetch(`/api/expenses/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch expense')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading expense:', error)
    return null
  }
}
