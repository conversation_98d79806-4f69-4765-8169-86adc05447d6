import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function DELETE(
  request: NextRequest, 
  { params }: { params: Promise<{ id: string; documentId: string }> }
) {
  try {
    const { documentId } = await params

    await prisma.projectDocument.delete({
      where: {
        id: documentId
      }
    })

    return NextResponse.json({ message: 'Document deleted successfully' })
  } catch (error) {
    console.error('Error deleting project document:', error)
    return NextResponse.json({ error: 'Failed to delete document' }, { status: 500 })
  }
} 