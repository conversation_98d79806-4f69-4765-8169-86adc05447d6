/**
 * Expense Type API utility - replaces localStorage with database API calls
 */

export interface ExpenseType {
  id: string
  name: string
  nameAr?: string
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  _count?: {
    expenses: number
  }
}

// Get all expense types from API
export const getExpenseTypes = async (): Promise<ExpenseType[]> => {
  try {
    const response = await fetch('/api/expense-types')
    if (!response.ok) {
      throw new Error('Failed to fetch expense types')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading expense types:', error)
    return []
  }
}

// Add new expense type via API
export const addExpenseType = async (expenseType: {
  name: string
  nameAr?: string
  description?: string
  isActive?: boolean
}): Promise<ExpenseType | null> => {
  try {
    const response = await fetch('/api/expense-types', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(expenseType),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create expense type')
    }

    return await response.json()
  } catch (error) {
    console.error('Error creating expense type:', error)
    return null
  }
}

// Update existing expense type via API
export const updateExpenseType = async (id: string, updates: {
  name?: string
  nameAr?: string
  description?: string
  isActive?: boolean
}): Promise<ExpenseType | null> => {
  try {
    const response = await fetch('/api/expense-types', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ id, ...updates }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update expense type')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating expense type:', error)
    return null
  }
}

// Delete expense type via API
export const deleteExpenseType = async (id: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/expense-types/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete expense type')
    }

    return true
  } catch (error) {
    console.error('Error deleting expense type:', error)
    return false
  }
}

// Get expense type by ID via API
export const getExpenseTypeById = async (id: string): Promise<ExpenseType | null> => {
  try {
    const response = await fetch(`/api/expense-types/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch expense type')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading expense type:', error)
    return null
  }
}
