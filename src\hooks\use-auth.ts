"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"

export interface AuthUser {
  id: string
  email: string
  name: string
  role: string
  phone?: string
  avatar?: string
}

export function useAuth() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    setIsLoading(status === "loading")
  }, [status])

  const logout = async () => {
    try {
      // Clear local storage
      localStorage.clear()
      sessionStorage.clear()
      
      // Sign out from NextAuth
      await signOut({ redirect: false })
      
      // Redirect to login
      router.push("/auth/login")
    } catch (error) {
      console.error("Logout error:", error)
      // Force redirect even if signOut fails
      router.push("/auth/login")
    }
  }

  const isAuthenticated = status === "authenticated" && !!session?.user
  const user = session?.user as AuthUser | undefined

  return {
    user,
    isAuthenticated,
    isLoading,
    logout,
    session,
    status
  }
}

export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/auth/login")
    }
  }, [isAuthenticated, isLoading, router])

  return { isAuthenticated, isLoading }
}

export function useRoleCheck() {
  const { user } = useAuth()

  const isAdmin = user?.role === "ADMIN"
  const isManager = user?.role === "MANAGER" 
  const isEmployee = user?.role === "EMPLOYEE"
  const isAdminOrManager = isAdmin || isManager

  const hasRole = (role: string | string[]) => {
    if (!user?.role) return false
    if (Array.isArray(role)) {
      return role.includes(user.role)
    }
    return user.role === role
  }

  return {
    isAdmin,
    isManager,
    isEmployee,
    isAdminOrManager,
    hasRole,
    userRole: user?.role
  }
}

// Hook for checking specific permissions
export function usePermissions() {
  const { user } = useAuth()

  const canAccess = (module: string, action: string = "read") => {
    if (!user) return false
    
    // Admin has access to everything
    if (user.role === "ADMIN") return true
    
    // Define role-based permissions
    const permissions: Record<string, Record<string, string[]>> = {
      dashboard: {
        read: ["ADMIN", "MANAGER", "EMPLOYEE"]
      },
      users: {
        read: ["ADMIN", "MANAGER"],
        create: ["ADMIN"],
        update: ["ADMIN"],
        delete: ["ADMIN"]
      },
      customers: {
        read: ["ADMIN", "MANAGER", "EMPLOYEE"],
        create: ["ADMIN", "MANAGER"],
        update: ["ADMIN", "MANAGER"],
        delete: ["ADMIN"]
      },
      products: {
        read: ["ADMIN", "MANAGER", "EMPLOYEE"],
        create: ["ADMIN", "MANAGER"],
        update: ["ADMIN", "MANAGER"],
        delete: ["ADMIN"]
      },
      invoices: {
        read: ["ADMIN", "MANAGER", "EMPLOYEE"],
        create: ["ADMIN", "MANAGER", "EMPLOYEE"],
        update: ["ADMIN", "MANAGER"],
        delete: ["ADMIN"]
      },
      reports: {
        read: ["ADMIN", "MANAGER"],
        create: ["ADMIN", "MANAGER"],
        update: ["ADMIN", "MANAGER"],
        delete: ["ADMIN"]
      },
      settings: {
        read: ["ADMIN"],
        create: ["ADMIN"],
        update: ["ADMIN"],
        delete: ["ADMIN"]
      }
    }

    const modulePermissions = permissions[module]
    if (!modulePermissions) return false

    const actionPermissions = modulePermissions[action]
    if (!actionPermissions) return false

    return actionPermissions.includes(user.role)
  }

  return { canAccess }
}
