"use client"

import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { formatCurrency, formatDate, formatOmaniAddress } from "@/lib/localization"

interface QuotationItem {
  id: string
  description: string
  descriptionAr?: string
  quantity: number
  unitPrice: number
  total: number
  product?: {
    name: string
    nameAr?: string
    unit: string
  }
}

interface Customer {
  name: string
  nameAr?: string
  company?: string
  companyAr?: string
  phone: string
  email?: string
  civilId?: string
  taxNumber?: string
  governorate?: string
  wilayat?: string
  area?: string
  street?: string
  building?: string
  postalCode?: string
}

interface QuotationTemplateProps {
  quotation: {
    id: string
    number: string
    date: string
    validUntil: string
    status: string
    subtotal: number
    taxAmount: number
    discount: number
    total: number
    notes?: string
    customer: Customer
    items: QuotationItem[]
    taskTitle?: string
  }
  company: {
    name: string
    nameAr: string
    address: string
    addressAr: string
    phone: string
    email: string
    taxNumber: string
    logo?: string
    termsConditions?: string
    termsConditionsAr?: string
    signature?: string
    stamp?: string
  }
  locale?: string
}

export function QuotationTemplate({ quotation, company, locale = "en" }: QuotationTemplateProps) {
  const isArabic = locale === "ar"

  const getStatusColor = (status: string) => {
    switch (status) {
      case "APPROVED": return "bg-green-100 text-green-800"
      case "PENDING": return "bg-yellow-100 text-yellow-800"
      case "REJECTED": return "bg-red-100 text-red-800"
      case "EXPIRED": return "bg-gray-100 text-gray-800"
      case "CONVERTED": return "bg-blue-100 text-blue-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusText = (status: string) => {
    if (isArabic) {
      switch (status) {
        case "APPROVED": return "موافق عليه"
        case "PENDING": return "في الانتظار"
        case "REJECTED": return "مرفوض"
        case "EXPIRED": return "منتهي الصلاحية"
        case "CONVERTED": return "تم التحويل"
        default: return status
      }
    }
    return status
  }

  return (
    <div className={`max-w-4xl mx-auto p-8 bg-white ${isArabic ? 'rtl' : 'ltr'}`} dir={isArabic ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div className="flex-1">
          {company.logo && (
            <img src={company.logo} alt="Company Logo" className="h-16 mb-4" />
          )}
          <h1 className="text-2xl font-bold text-gray-900">
            {isArabic ? company.nameAr : company.name}
          </h1>
          <p className="text-gray-600 mt-2">
            {isArabic ? company.addressAr : company.address}
          </p>
          <p className="text-gray-600">{company.phone}</p>
          <p className="text-gray-600">{company.email}</p>
          {company.taxNumber && (
            <p className="text-gray-600">
              {isArabic ? "الرقم الضريبي: " : "Tax Number: "}{company.taxNumber}
            </p>
          )}
        </div>

        <div className="text-right">
          <h2 className="text-3xl font-bold text-blue-600 mb-2">
            {isArabic ? "عرض سعر" : "QUOTATION"}
          </h2>
          <div className="space-y-1">
            <p className="text-lg font-semibold">{quotation.number}</p>
            <Badge className={getStatusColor(quotation.status)}>
              {getStatusText(quotation.status)}
            </Badge>
          </div>
        </div>
      </div>

      {/* Quotation Details */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-900">
            {isArabic ? "تفاصيل عرض السعر" : "Quotation Details"}
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">
                {isArabic ? "تاريخ عرض السعر:" : "Quotation Date:"}
              </span>
              <span>{formatDate(quotation.date, locale)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">
                {isArabic ? "صالح حتى:" : "Valid Until:"}
              </span>
              <span>{formatDate(quotation.validUntil, locale)}</span>
            </div>
            {quotation.taskTitle && (
              <div className="flex justify-between">
                <span className="text-gray-600">
                  {isArabic ? "المهمة:" : "Task:"}
                </span>
                <span className="font-medium">{quotation.taskTitle}</span>
              </div>
            )}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-900">
            {isArabic ? "بيانات العميل" : "Quote For"}
          </h3>
          <div className="space-y-1">
            <p className="font-semibold">
              {isArabic && quotation.customer.nameAr
                ? quotation.customer.nameAr
                : quotation.customer.name}
            </p>
            {quotation.customer.company && (
              <p className="text-gray-600">
                {isArabic && quotation.customer.companyAr
                  ? quotation.customer.companyAr
                  : quotation.customer.company}
              </p>
            )}
            <p className="text-gray-600">{quotation.customer.phone}</p>
            {quotation.customer.email && (
              <p className="text-gray-600">{quotation.customer.email}</p>
            )}
            {quotation.customer.civilId && (
              <p className="text-gray-600">
                {isArabic ? "الرقم المدني: " : "Civil ID: "}{quotation.customer.civilId}
              </p>
            )}
            {quotation.customer.taxNumber && (
              <p className="text-gray-600">
                {isArabic ? "الرقم الضريبي: " : "Tax Number: "}{quotation.customer.taxNumber}
              </p>
            )}
            {(quotation.customer.governorate || quotation.customer.area) && (
              <p className="text-gray-600">
                {formatOmaniAddress({
                  building: quotation.customer.building,
                  street: quotation.customer.street,
                  area: quotation.customer.area,
                  wilayat: quotation.customer.wilayat,
                  governorate: quotation.customer.governorate,
                  postalCode: quotation.customer.postalCode,
                }, locale)}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-8">
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b-2 border-gray-300">
              <th className="text-left py-3 px-2 font-semibold">
                {isArabic ? "الوصف" : "Description"}
              </th>
              <th className="text-center py-3 px-2 font-semibold w-20">
                {isArabic ? "الكمية" : "Qty"}
              </th>
              <th className="text-right py-3 px-2 font-semibold w-24">
                {isArabic ? "سعر الوحدة" : "Unit Price"}
              </th>
              <th className="text-right py-3 px-2 font-semibold w-24">
                {isArabic ? "المجموع" : "Total"}
              </th>
            </tr>
          </thead>
          <tbody>
            {quotation.items.map((item, index) => (
              <tr key={item.id} className="border-b border-gray-200">
                <td className="py-3 px-2">
                  <div>
                    <p className="font-medium">
                      {isArabic && item.descriptionAr
                        ? item.descriptionAr
                        : item.description}
                    </p>
                    {item.product && (
                      <p className="text-sm text-gray-600">
                        {isArabic && item.product.nameAr
                          ? item.product.nameAr
                          : item.product.name}
                      </p>
                    )}
                  </div>
                </td>
                <td className="py-3 px-2 text-center">
                  {item.quantity} {item.product?.unit || ''}
                </td>
                <td className="py-3 px-2 text-right">
                  {formatCurrency(item.unitPrice, locale)}
                </td>
                <td className="py-3 px-2 text-right font-medium">
                  {formatCurrency(item.total, locale)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-80">
          <div className="space-y-2">
            <div className="flex justify-between py-2">
              <span className="text-gray-600">
                {isArabic ? "المجموع الفرعي:" : "Subtotal:"}
              </span>
              <span>{formatCurrency(quotation.subtotal, locale)}</span>
            </div>

            {quotation.discount > 0 && (
              <div className="flex justify-between py-2">
                <span className="text-gray-600">
                  {isArabic ? "الخصم:" : "Discount:"}
                </span>
                <span>-{formatCurrency(quotation.discount, locale)}</span>
              </div>
            )}

            <div className="flex justify-between py-2">
              <span className="text-gray-600">
                {isArabic ? "ضريبة القيمة المضافة (5%):" : "VAT (5%):"}
              </span>
              <span>{formatCurrency(quotation.taxAmount, locale)}</span>
            </div>

            <Separator />

            <div className="flex justify-between py-3 text-lg font-bold">
              <span>{isArabic ? "المجموع الكلي:" : "Total:"}</span>
              <span className="text-blue-600">
                {formatCurrency(quotation.total, locale)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Validity Notice */}
      <div className="mb-8 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm text-yellow-800">
          <strong>
            {isArabic ? "ملاحظة هامة: " : "Important Note: "}
          </strong>
          {isArabic
            ? `هذا العرض صالح حتى ${formatDate(quotation.validUntil, locale)} ويخضع للشروط والأحكام المذكورة أدناه.`
            : `This quotation is valid until ${formatDate(quotation.validUntil, locale)} and is subject to the terms and conditions mentioned below.`
          }
        </p>
      </div>

      {/* Notes */}
      {quotation.notes && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">
            {isArabic ? "ملاحظات:" : "Notes:"}
          </h3>
          <p className="text-gray-600 bg-gray-50 p-4 rounded">
            {quotation.notes}
          </p>
        </div>
      )}

      {/* Terms & Conditions */}
      {(company.termsConditions || company.termsConditionsAr) && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-3">
            {isArabic ? "الشروط والأحكام:" : "Terms & Conditions:"}
          </h3>
          <div className="text-sm text-gray-600 bg-gray-50 p-4 rounded">
            {isArabic && company.termsConditionsAr
              ? company.termsConditionsAr
              : company.termsConditions}
          </div>
        </div>
      )}

      {/* Signature and Stamp Section */}
      {(company.signature || company.stamp) && (
        <div className="mb-8">
          <div className="flex justify-between items-end">
            {/* Signature */}
            {company.signature && (
              <div className="text-center">
                <div className="mb-2">
                  <img
                    src={company.signature}
                    alt="Authorized Signature"
                    className="h-16 w-auto max-w-[150px] object-contain mx-auto"
                  />
                </div>
                <div className="border-t border-gray-400 pt-2 w-40">
                  <p className="text-sm text-gray-600">
                    {isArabic ? "التوقيع المعتمد" : "Authorized Signature"}
                  </p>
                </div>
              </div>
            )}

            {/* Stamp */}
            {company.stamp && (
              <div className="text-center">
                <div className="mb-2">
                  <img
                    src={company.stamp}
                    alt="Company Stamp"
                    className="h-20 w-auto max-w-[100px] object-contain mx-auto"
                  />
                </div>
                <p className="text-sm text-gray-600">
                  {isArabic ? "ختم الشركة" : "Company Stamp"}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="border-t pt-6 text-center text-gray-600">
        <p className="mb-2">
          {isArabic
            ? "شكراً لك على اهتمامك بخدماتنا"
            : "Thank you for considering our services"}
        </p>
        <p className="text-sm">
          {isArabic
            ? "هذا عرض سعر مُنشأ إلكترونياً"
            : "This is a computer-generated quotation"}
        </p>
      </div>
    </div>
  )
}
