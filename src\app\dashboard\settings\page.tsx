"use client"

import { useState, useRef, useEffect } from "react"
import {
  getSettings,
  updateMultipleSettings,
  updateSettingByKey,
  type Setting
} from "@/lib/settings-storage"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import {
  Building,
  MessageSquare,
  Users,
  Globe,
  Save,
  TestTube,
  Upload,
  X,
  Image
} from "lucide-react"
import { useI18n } from "@/lib/i18n"

export default function SettingsPage() {
  const { t } = useI18n()
  const [isLoading, setIsLoading] = useState(false)
  const [dataLoading, setDataLoading] = useState(true)
  const [logo, setLogo] = useState<string | null>(null)
  const [companyData, setCompanyData] = useState({
    companyName: '',
    companyNameAr: '',
    companyAddress: '',
    companyAddressAr: '',
    companyPhone: '',
    companyEmail: '',
    taxRate: '5',
    currency: 'OMR',
    taxNumber: '',
    crNumber: '',
    establishmentCard: '',
    termsConditions: '',
    termsConditionsAr: '',
    signature: '',
    stamp: ''
  })
  const fileInputRef = useRef<HTMLInputElement>(null)
  const signatureInputRef = useRef<HTMLInputElement>(null)
  const stampInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    const loadSettings = async () => {
      setDataLoading(true)
      try {
        const { settingsObject } = await getSettings()

        // Map database settings to component state
        setCompanyData({
          companyName: settingsObject.company_name?.value || 'Office Services Pro',
          companyNameAr: settingsObject.company_name_ar?.value || 'خدمات المكاتب المحترفة',
          companyAddress: settingsObject.company_address?.value || '',
          companyAddressAr: settingsObject.company_address_ar?.value || '',
          companyPhone: settingsObject.company_phone?.value || '',
          companyEmail: settingsObject.company_email?.value || '',
          taxRate: settingsObject.tax_rate?.value || '5',
          currency: settingsObject.currency?.value || 'OMR',
          taxNumber: settingsObject.tax_number?.value || '',
          crNumber: settingsObject.cr_number?.value || '',
          establishmentCard: settingsObject.establishment_card?.value || '',
          termsConditions: settingsObject.terms_conditions?.value || '',
          termsConditionsAr: settingsObject.terms_conditions_ar?.value || '',
          signature: settingsObject.company_signature?.value || '',
          stamp: settingsObject.company_stamp?.value || ''
        })

        // Load logo
        if (settingsObject.company_logo?.value) {
          setLogo(settingsObject.company_logo.value)
        }
      } catch (error) {
        console.error('Error loading settings:', error)
      } finally {
        setDataLoading(false)
      }
    }

    loadSettings()
  }, [])

  const handleSave = async () => {
    setIsLoading(true)

    try {
      // Prepare settings updates - exclude logo since it's saved immediately on upload
      const settingsUpdates = [
        { key: 'company_name', value: companyData.companyName || '' },
        { key: 'company_name_ar', value: companyData.companyNameAr || '' },
        { key: 'company_address', value: companyData.companyAddress || '' },
        { key: 'company_address_ar', value: companyData.companyAddressAr || '' },
        { key: 'company_phone', value: companyData.companyPhone || '' },
        { key: 'company_email', value: companyData.companyEmail || '' },
        { key: 'tax_rate', value: companyData.taxRate || '5' },
        { key: 'currency', value: companyData.currency || 'OMR' },
        { key: 'tax_number', value: companyData.taxNumber || '' },
        { key: 'cr_number', value: companyData.crNumber || '' },
        { key: 'establishment_card', value: companyData.establishmentCard || '' },
        { key: 'terms_conditions', value: companyData.termsConditions || '' },
        { key: 'terms_conditions_ar', value: companyData.termsConditionsAr || '' },
        { key: 'company_signature', value: companyData.signature || '' },
        { key: 'company_stamp', value: companyData.stamp || '' },
      ]

      // Update all settings
      const success = await updateMultipleSettings(settingsUpdates)

      if (success) {
        alert(t('settings.settingsSavedSuccessfully'))
        // Trigger a page reload to update the header and sidebar
        window.location.reload()
      } else {
        alert(t('settings.failedToSaveSettings'))
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      alert(t('settings.failedToSaveSettings'))
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setCompanyData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const testWhatsApp = async () => {
    // Test WhatsApp integration
    console.log("Testing WhatsApp integration...")
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Check file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file')
        return
      }

      // Check file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        alert('File size must be less than 2MB')
        return
      }

      const reader = new FileReader()
      reader.onload = async (e) => {
        const logoData = e.target?.result as string
        setLogo(logoData)
        // Save to database immediately
        await updateSettingByKey('company_logo', logoData)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleRemoveLogo = async () => {
    setLogo(null)
    // Remove from database
    await updateSettingByKey('company_logo', '')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  const handleSignatureUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file')
        return
      }

      if (file.size > 2 * 1024 * 1024) {
        alert('File size must be less than 2MB')
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        const signatureData = e.target?.result as string
        setCompanyData(prev => ({ ...prev, signature: signatureData }))
      }
      reader.readAsDataURL(file)
    }
  }

  const handleStampUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file')
        return
      }

      if (file.size > 2 * 1024 * 1024) {
        alert('File size must be less than 2MB')
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        const stampData = e.target?.result as string
        setCompanyData(prev => ({ ...prev, stamp: stampData }))
      }
      reader.readAsDataURL(file)
    }
  }

  const triggerSignatureInput = () => {
    signatureInputRef.current?.click()
  }

  const triggerStampInput = () => {
    stampInputRef.current?.click()
  }

  const removeSignature = () => {
    setCompanyData(prev => ({ ...prev, signature: '' }))
    if (signatureInputRef.current) {
      signatureInputRef.current.value = ''
    }
  }

  const removeStamp = () => {
    setCompanyData(prev => ({ ...prev, stamp: '' }))
    if (stampInputRef.current) {
      stampInputRef.current.value = ''
    }
  }

  if (dataLoading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading settings...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">{t('settings.title')}</h2>
        <Button onClick={handleSave} disabled={isLoading}>
          <Save className="mr-2 h-4 w-4" />
          {isLoading ? t('common.saving') : t('settings.saveSettings')}
        </Button>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">
            <Building className="mr-2 h-4 w-4" />
            {t('settings.generalSettings')}
          </TabsTrigger>
          <TabsTrigger value="whatsapp">
            <MessageSquare className="mr-2 h-4 w-4" />
            {t('settings.whatsappIntegration')}
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="mr-2 h-4 w-4" />
            {t('settings.userSettings')}
          </TabsTrigger>
          <TabsTrigger value="localization">
            <Globe className="mr-2 h-4 w-4" />
            {t('settings.languageSettings')}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.companyLogo')}</CardTitle>
              <CardDescription>
                {t('settings.companyLogoDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {logo ? (
                    <div className="relative">
                      <img
                        src={logo}
                        alt={t('settings.companyLogo')}
                        className="w-24 h-24 object-contain rounded-lg border-2 border-gray-200"
                      />
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                        onClick={handleRemoveLogo}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                      <Image className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                </div>
                <div className="flex-1 space-y-2">
                  <div>
                    <Button
                      variant="outline"
                      onClick={triggerFileInput}
                      className="w-full sm:w-auto"
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      {logo ? t('settings.changeLogo') : t('settings.uploadLogo')}
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {t('settings.logoRecommendation')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('settings.companyInformation')}</CardTitle>
              <CardDescription>
                {t('settings.companyInformationDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyName">{t('settings.companyName')}</Label>
                  <Input
                    id="companyName"
                    value={companyData.companyName}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="companyNameAr">{t('settings.companyNameAr')}</Label>
                  <Input
                    id="companyNameAr"
                    value={companyData.companyNameAr}
                    onChange={(e) => handleInputChange('companyNameAr', e.target.value)}
                    dir="rtl"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="companyAddress">{t('settings.addressEnglish')}</Label>
                <Textarea
                  id="companyAddress"
                  value={companyData.companyAddress}
                  onChange={(e) => handleInputChange('companyAddress', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="companyAddressAr">{t('settings.addressArabic')}</Label>
                <Textarea
                  id="companyAddressAr"
                  value={companyData.companyAddressAr}
                  onChange={(e) => handleInputChange('companyAddressAr', e.target.value)}
                  dir="rtl"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyPhone">{t('settings.phone')}</Label>
                  <Input
                    id="companyPhone"
                    value={companyData.companyPhone}
                    onChange={(e) => handleInputChange('companyPhone', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="companyEmail">{t('settings.email')}</Label>
                  <Input
                    id="companyEmail"
                    type="email"
                    value={companyData.companyEmail}
                    onChange={(e) => handleInputChange('companyEmail', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="taxRate">{t('settings.vatRate')}</Label>
                  <Input
                    id="taxRate"
                    type="number"
                    value={companyData.taxRate}
                    onChange={(e) => handleInputChange('taxRate', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">{t('settings.currency')}</Label>
                  <Input
                    id="currency"
                    value={companyData.currency}
                    disabled
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="taxNumber">{t('settings.taxRegistrationNumber')}</Label>
                  <Input
                    id="taxNumber"
                    value={companyData.taxNumber}
                    onChange={(e) => handleInputChange('taxNumber', e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="crNumber">{t('settings.commercialRegistration')}</Label>
                  <Input
                    id="crNumber"
                    value={companyData.crNumber}
                    onChange={(e) => handleInputChange('crNumber', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="establishmentCard">{t('settings.establishmentCard')}</Label>
                  <Input
                    id="establishmentCard"
                    value={companyData.establishmentCard}
                    onChange={(e) => handleInputChange('establishmentCard', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('settings.invoiceSettings')}</CardTitle>
              <CardDescription>
                {t('settings.invoiceSettingsDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Terms & Conditions */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium">{t('settings.termsConditions')}</h4>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="termsConditions">{t('settings.termsConditionsEnglish')}</Label>
                    <Textarea
                      id="termsConditions"
                      value={companyData.termsConditions}
                      onChange={(e) => handleInputChange('termsConditions', e.target.value)}
                      rows={3}
                      placeholder={t('settings.termsConditionsEnglishPlaceholder')}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="termsConditionsAr">{t('settings.termsConditionsArabic')}</Label>
                    <Textarea
                      id="termsConditionsAr"
                      value={companyData.termsConditionsAr}
                      onChange={(e) => handleInputChange('termsConditionsAr', e.target.value)}
                      rows={3}
                      dir="rtl"
                      placeholder={t('settings.termsConditionsArabicPlaceholder')}
                    />
                  </div>
                </div>
              </div>

              {/* Signature */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium">{t('settings.authorizedSignature')}</h4>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {companyData.signature ? (
                      <div className="relative">
                        <img
                          src={companyData.signature}
                          alt={t('settings.signature')}
                          className="w-32 h-16 object-contain rounded-lg border-2 border-gray-200 bg-white"
                        />
                        <Button
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                          onClick={removeSignature}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      <div className="w-32 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                        <span className="text-xs text-gray-400">{t('settings.signature')}</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 space-y-2">
                    <Button
                      variant="outline"
                      onClick={triggerSignatureInput}
                      className="w-full sm:w-auto"
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      {companyData.signature ? t('settings.changeSignature') : t('settings.uploadSignature')}
                    </Button>
                    <input
                      ref={signatureInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleSignatureUpload}
                      className="hidden"
                    />
                    <p className="text-sm text-muted-foreground">
                      {t('settings.signatureRecommendation')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Stamp */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium">{t('settings.companyStamp')}</h4>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {companyData.stamp ? (
                      <div className="relative">
                        <img
                          src={companyData.stamp}
                          alt={t('settings.stamp')}
                          className="w-24 h-24 object-contain rounded-lg border-2 border-gray-200 bg-white"
                        />
                        <Button
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                          onClick={removeStamp}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                        <span className="text-xs text-gray-400">{t('settings.stamp')}</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 space-y-2">
                    <Button
                      variant="outline"
                      onClick={triggerStampInput}
                      className="w-full sm:w-auto"
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      {companyData.stamp ? t('settings.changeStamp') : t('settings.uploadStamp')}
                    </Button>
                    <input
                      ref={stampInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleStampUpload}
                      className="hidden"
                    />
                    <p className="text-sm text-muted-foreground">
                      {t('settings.stampRecommendation')}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="whatsapp" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.whatsappIntegration')}</CardTitle>
              <CardDescription>
                {t('settings.whatsappIntegrationDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch id="whatsapp-enabled" defaultChecked />
                <Label htmlFor="whatsapp-enabled">{t('settings.enableWhatsappNotifications')}</Label>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="apiUrl">{t('settings.apiUrl')}</Label>
                  <Input
                    id="apiUrl"
                    placeholder="https://api.textcloud.com"
                    defaultValue="https://api.textcloud.com"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="accountKey">{t('settings.accountKey')}</Label>
                  <Input
                    id="accountKey"
                    placeholder={t('settings.accountKeyPlaceholder')}
                    type="password"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="secretKey">{t('settings.secretKey')}</Label>
                  <Input
                    id="secretKey"
                    placeholder={t('settings.secretKeyPlaceholder')}
                    type="password"
                  />
                </div>

                <Button variant="outline" onClick={testWhatsApp}>
                  <TestTube className="mr-2 h-4 w-4" />
                  {t('settings.testConnection')}
                </Button>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="text-sm font-medium">{t('settings.notificationSettings')}</h4>

                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Switch id="task-assigned" defaultChecked />
                    <Label htmlFor="task-assigned">{t('settings.taskAssignmentNotifications')}</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="task-started" defaultChecked />
                    <Label htmlFor="task-started">{t('settings.taskStartedNotifications')}</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="task-completed" defaultChecked />
                    <Label htmlFor="task-completed">{t('settings.taskCompletionNotifications')}</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="invoice-sent" defaultChecked />
                    <Label htmlFor="invoice-sent">{t('settings.invoiceNotifications')}</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="quotation-sent" defaultChecked />
                    <Label htmlFor="quotation-sent">{t('settings.quotationNotifications')}</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.userManagement')}</CardTitle>
              <CardDescription>
                {t('settings.userManagementDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button>
                  <Users className="mr-2 h-4 w-4" />
                  {t('settings.addNewUser')}
                </Button>

                <div className="text-sm text-muted-foreground">
                  {t('settings.userManagementPlaceholder')}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="localization" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('settings.localizationSettings')}</CardTitle>
              <CardDescription>
                {t('settings.localizationSettingsDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="defaultLanguage">{t('settings.defaultLanguage')}</Label>
                <select id="defaultLanguage" className="w-full p-2 border rounded-md">
                  <option value="en">{t('settings.english')}</option>
                  <option value="ar">{t('settings.arabic')}</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="dateFormat">{t('settings.dateFormat')}</Label>
                <select id="dateFormat" className="w-full p-2 border rounded-md">
                  <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                  <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                  <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeFormat">{t('settings.timeFormat')}</Label>
                <select id="timeFormat" className="w-full p-2 border rounded-md">
                  <option value="12">{t('settings.hour12')}</option>
                  <option value="24">{t('settings.hour24')}</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="timezone">{t('settings.timezone')}</Label>
                <select id="timezone" className="w-full p-2 border rounded-md">
                  <option value="UTC">{t('settings.utc')}</option>
                  <option value="America/New_York">{t('settings.easternTime')}</option>
                  <option value="America/Los_Angeles">{t('settings.pacificTime')}</option>
                  <option value="Europe/London">{t('settings.london')}</option>
                  <option value="Asia/Dubai">{t('settings.dubai')}</option>
                </select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="rtl-support" defaultChecked />
                <Label htmlFor="rtl-support">{t('settings.enableRtlSupport')}</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
