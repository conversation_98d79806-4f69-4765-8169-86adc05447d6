# Leads Activities and Follow-ups Feature

## Overview
This document describes the implementation of activities and follow-ups functionality for the leads management system. This feature allows users to track interactions with leads and schedule future follow-up actions.

## Database Schema

### Activity Model
```prisma
model Activity {
  id          String       @id @default(cuid())
  type        ActivityType @default(NOTE)
  title       String
  description String?
  date        DateTime     @default(now())
  time        String?
  leadId      String
  userId      String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  lead        Lead         @relation(fields: [leadId], references: [id], onDelete: Cascade)
  user        User         @relation("UserActivities", fields: [userId], references: [id])
}
```

### FollowUp Model
```prisma
model FollowUp {
  id            String         @id @default(cuid())
  type          FollowUpType   @default(CALL)
  description   String
  scheduledDate DateTime
  scheduledTime String?
  status        FollowUpStatus @default(PENDING)
  leadId        String
  assignedToId  String
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  lead          Lead           @relation(fields: [leadId], references: [id], onDelete: Cascade) 
  assignedTo    User           @relation("UserFollowUps", fields: [assignedToId], references: [id])
}
```

### Enums
```prisma
enum ActivityType {
  CALL
  EMAIL
  MEETING
  NOTE
  SMS
  WHATSAPP
}

enum FollowUpType {
  CALL
  EMAIL
  MEETING
  PROPOSAL
  VISIT
  QUOTE
}

enum FollowUpStatus {
  PENDING
  COMPLETED
  CANCELLED
  OVERDUE
}
```

## API Endpoints

### Activities API

#### GET /api/leads/[id]/activities
Retrieves all activities for a specific lead.

**Response:**
```json
[
  {
    "id": "activity_id",
    "type": "CALL",
    "title": "Initial Contact Call",
    "description": "Discussed printing needs",
    "date": "2024-01-20T00:00:00.000Z",
    "time": "10:30",
    "leadId": "lead_id",
    "userId": "user_id",
    "createdAt": "2024-01-20T10:30:00.000Z",
    "user": {
      "id": "user_id",
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }
]
```

#### POST /api/leads/[id]/activities
Creates a new activity for a lead.

**Request Body:**
```json
{
  "type": "CALL",
  "title": "Follow-up Call",
  "description": "Discussed project requirements",
  "date": "2024-01-21",
  "time": "14:30"
}
```

### Follow-ups API

#### GET /api/leads/[id]/followups
Retrieves all follow-ups for a specific lead.

**Response:**
```json
[
  {
    "id": "followup_id",
    "type": "CALL",
    "description": "Follow up on proposal",
    "scheduledDate": "2024-01-25T00:00:00.000Z",
    "scheduledTime": "10:00",
    "status": "PENDING",
    "leadId": "lead_id",
    "assignedToId": "user_id",
    "createdAt": "2024-01-20T10:30:00.000Z",
    "assignedTo": {
      "id": "user_id",
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }
]
```

#### POST /api/leads/[id]/followups
Creates a new follow-up for a lead.

**Request Body:**
```json
{
  "type": "CALL",
  "description": "Follow up on proposal discussion",
  "scheduledDate": "2024-01-25",
  "scheduledTime": "10:00",
  "assignedToId": "user_id"
}
```

## Frontend Implementation

### Lead Details Page Updates
The lead details page (`/dashboard/leads/[id]`) has been updated to include:

1. **Activities Tab**: Displays chronological list of all activities
2. **Follow-ups Tab**: Shows scheduled follow-ups in a table format
3. **Add Activity Dialog**: Form to create new activities
4. **Schedule Follow-up Dialog**: Form to schedule new follow-ups

### Key Features
- Real-time data fetching from API endpoints
- Form validation and error handling
- Automatic refresh after creating new records
- Responsive design with proper loading states

### State Management
```typescript
const [activities, setActivities] = useState<Activity[]>([])
const [followUps, setFollowUps] = useState<FollowUp[]>([])
const [activityForm, setActivityForm] = useState({
  type: 'NOTE',
  title: '',
  description: '',
  date: new Date().toISOString().split('T')[0],
  time: new Date().toTimeString().slice(0, 5)
})
const [followUpForm, setFollowUpForm] = useState({
  type: 'CALL',
  description: '',
  scheduledDate: new Date().toISOString().split('T')[0],
  scheduledTime: '09:00',
  assignedToId: ''
})
```

## Security Considerations
- All API endpoints require authentication
- Users can only access leads they have permission to view
- Activities and follow-ups are automatically associated with the authenticated user
- Cascade deletion ensures data integrity when leads are deleted

## Usage Examples

### Creating an Activity
1. Navigate to lead details page
2. Click "Add Activity" button
3. Select activity type (Call, Email, Meeting, Note, SMS, WhatsApp)
4. Fill in title, description, date, and time
5. Click "Add Activity" to save

### Scheduling a Follow-up
1. Navigate to lead details page
2. Click "Schedule Follow-up" button
3. Select follow-up type (Call, Email, Meeting, Proposal, Visit, Quote)
4. Fill in description, scheduled date, and time
5. Optionally assign to a different user
6. Click "Schedule Follow-up" to save

## Migration Instructions
1. Update Prisma schema with new models and enums
2. Run `npx prisma db push` to sync database
3. Run `npx prisma generate` to update client
4. Deploy API endpoints
5. Update frontend components
6. Test functionality thoroughly

## Future Enhancements
- Email notifications for upcoming follow-ups
- Activity templates for common interactions
- Bulk actions for multiple follow-ups
- Integration with calendar systems
- Activity analytics and reporting
- Mobile app support 