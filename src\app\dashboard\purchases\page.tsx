"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Package,
  Truck,
  CheckCircle,
  MoreHorizontal,
  Eye,
  Printer,
  Mail,
  Download,
  DollarSign
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

interface Purchase {
  id: string
  number: string
  date: string
  expectedDate: string
  supplier: {
    id: string
    name: string
    mobile: string
    email?: string
  }
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'ORDERED' | 'RECEIVED' | 'CANCELLED'
  subtotal: number
  taxAmount: number
  discountAmount: number
  total: number
  items: any[]
  notes?: string
  payoutStatus?: 'PENDING' | 'PARTIAL' | 'PAID'
  totalPaid?: number
}

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800",
  ORDERED: "bg-purple-100 text-purple-800",
  RECEIVED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
}

export default function PurchasesPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [statusFilter, setStatusFilter] = useState("all")
  const [loading, setLoading] = useState(true)

  // Load purchases from API
  useEffect(() => {
    const loadPurchases = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/purchases')
        if (response.ok) {
          const data = await response.json()
          setPurchases(data)
        }
      } catch (error) {
        console.error('Error loading purchases:', error)
      } finally {
        setLoading(false)
      }
    }

    loadPurchases()
  }, [])

  const filteredPurchases = purchases.filter(purchase => {
    const matchesSearch = purchase.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      purchase.supplier?.name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || purchase.status.toLowerCase() === statusFilter.toLowerCase()

    return matchesSearch && matchesStatus
  })

  const handleViewPurchase = (purchase: any) => {
    router.push(`/dashboard/purchases/${purchase.id}`)
  }

  const handleEditPurchase = (purchase: any) => {
    router.push(`/dashboard/purchases/${purchase.id}/edit`)
  }

  const handleMarkAsReceived = async (purchase: Purchase) => {
    try {
      const response = await fetch(`/api/purchases/${purchase.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...purchase, status: 'RECEIVED' })
      })

      if (response.ok) {
        setPurchases(prev => prev.map(p =>
          p.id === purchase.id ? { ...p, status: 'RECEIVED' as const } : p
        ))
        alert(`Purchase order ${purchase.number} marked as received!`)
      }
    } catch (error) {
      console.error('Error updating purchase:', error)
      alert('Failed to update purchase order')
    }
  }

  const handlePrintPurchase = (purchase: Purchase) => {
    console.log('Printing purchase order:', purchase.number)
    alert('Print functionality would be implemented here')
  }

  const handleEmailPurchase = (purchase: Purchase) => {
    console.log('Emailing purchase order:', purchase.number)
    alert('Email functionality would be implemented here')
  }

  const handleDownloadPurchase = (purchase: Purchase) => {
    console.log('Downloading purchase order:', purchase.number)
    alert('Download functionality would be implemented here')
  }

  const handleDeletePurchase = async (purchase: Purchase) => {
    if (confirm(`Are you sure you want to delete purchase order ${purchase.number}?`)) {
      try {
        const response = await fetch(`/api/purchases/${purchase.id}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          setPurchases(prev => prev.filter(p => p.id !== purchase.id))
          alert(`Purchase order ${purchase.number} deleted successfully!`)
        }
      } catch (error) {
        console.error('Error deleting purchase:', error)
        alert('Failed to delete purchase order')
      }
    }
  }

  // Calculate statistics
  const totalOrders = purchases.length
  const pendingOrders = purchases.filter(p => p.status === 'PENDING').length
  const orderedItems = purchases.filter(p => p.status === 'ORDERED').length
  const receivedOrders = purchases.filter(p => p.status === 'RECEIVED').length
  const totalValue = purchases.reduce((sum, p) => sum + p.total, 0)
  const pendingValue = purchases.filter(p => p.status === 'PENDING').reduce((sum, p) => sum + p.total, 0)

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('purchases.dashboard')}</h2>
          <p className="text-muted-foreground">
            {t('purchases.dashboardDescription')}
          </p>
        </div>
        <Button onClick={() => router.push('/dashboard/purchases/create')}>
          <Plus className="mr-2 h-4 w-4" />
          {t('purchases.createPurchaseOrder')}
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-gradient-to-br from-blue-50 to-indigo-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">{t('purchases.totalOrders')}</p>
              <p className="text-3xl font-bold text-blue-900">{totalOrders}</p>
            </div>
            <div className="rounded-full bg-blue-100 p-3">
              <Package className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <p className="text-xs text-blue-600 mt-2">{t('purchases.allPurchaseOrders')}</p>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-yellow-50 to-orange-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-600">{t('purchases.pendingOrders')}</p>
              <p className="text-3xl font-bold text-yellow-900">{pendingOrders}</p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3">
              <Eye className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
          <p className="text-xs text-yellow-600 mt-2">{formatCurrency(pendingValue)} {t('purchases.pendingValue')}</p>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-purple-50 to-pink-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">{t('purchases.inTransit')}</p>
              <p className="text-3xl font-bold text-purple-900">{orderedItems}</p>
            </div>
            <div className="rounded-full bg-purple-100 p-3">
              <Truck className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <p className="text-xs text-purple-600 mt-2">{t('purchases.ordersShipped')}</p>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-green-50 to-emerald-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">{t('purchases.totalValue')}</p>
              <p className="text-2xl font-bold text-green-900">{formatCurrency(totalValue)}</p>
            </div>
            <div className="rounded-full bg-green-100 p-3">
              <Download className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <p className="text-xs text-green-600 mt-2">{receivedOrders} {t('purchases.ordersReceived')}</p>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('purchases.searchPurchases')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('purchases.filterByStatus')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('purchases.allStatuses')}</SelectItem>
            <SelectItem value="draft">{t('purchases.draft')}</SelectItem>
            <SelectItem value="pending">{t('purchases.pending')}</SelectItem>
            <SelectItem value="approved">{t('purchases.approved')}</SelectItem>
            <SelectItem value="ordered">{t('purchases.ordered')}</SelectItem>
            <SelectItem value="received">{t('purchases.received')}</SelectItem>
            <SelectItem value="cancelled">{t('purchases.cancelled')}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('purchases.purchaseNumber')}</TableHead>
              <TableHead>{t('purchases.supplier')}</TableHead>
              <TableHead>{t('purchases.date')}</TableHead>
              <TableHead>{t('purchases.items')}</TableHead>
              <TableHead>{t('purchases.status')}</TableHead>
              <TableHead>{t('purchases.payoutStatus')}</TableHead>
              <TableHead>{t('purchases.total')}</TableHead>
              <TableHead>{t('purchases.paid')}</TableHead>
              <TableHead className="text-right">{t('purchases.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span>{t('purchases.loadingPurchases')}</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredPurchases.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm ? t('purchases.noPurchasesFoundMatchingSearch') : t('purchases.noPurchasesFoundCreateFirstPurchaseOrder')}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredPurchases.map((purchase) => (
                <TableRow key={purchase.id}>
                  <TableCell className="font-medium">
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium"
                      onClick={() => handleViewPurchase(purchase)}
                    >
                      {purchase.number}
                    </Button>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Truck className="mr-2 h-4 w-4 text-muted-foreground" />
                      {purchase.supplier?.name || t('purchases.unknownSupplier')}
                    </div>
                  </TableCell>
                  <TableCell>{new Date(purchase.date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Package className="mr-1 h-3 w-3" />
                      {purchase.items?.length || 0} {t('purchases.items')}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={statusColors[purchase.status as keyof typeof statusColors]}>
                      {purchase.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={
                      purchase.payoutStatus === 'PAID' ? 'bg-green-100 text-green-800' :
                      purchase.payoutStatus === 'PARTIAL' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }>
                      {purchase.payoutStatus || t('purchases.pending')}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-medium">{formatCurrency(purchase.total)}</TableCell>
                  <TableCell className="font-medium text-green-600">
                    {formatCurrency(purchase.totalPaid || 0)}
                  </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">{t('purchases.openMenu')}</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewPurchase(purchase)}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t('purchases.viewDetails')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditPurchase(purchase)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('purchases.editPurchase')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handlePrintPurchase(purchase)}>
                        <Printer className="mr-2 h-4 w-4" />
                        {t('purchases.printPDF')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEmailPurchase(purchase)}>
                        <Mail className="mr-2 h-4 w-4" />
                        {t('purchases.emailPurchase')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDownloadPurchase(purchase)}>
                        <Download className="mr-2 h-4 w-4" />
                        {t('purchases.download')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => router.push(`/dashboard/supplier-payouts/create?supplierId=${purchase.supplier?.id}&purchaseId=${purchase.id}`)}>
                        <DollarSign className="mr-2 h-4 w-4" />
                        {t('purchases.createPayout')}
                      </DropdownMenuItem>
                      {purchase.status === 'ORDERED' && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleMarkAsReceived(purchase)}
                            className="text-green-600 focus:text-green-600"
                          >
                            <CheckCircle className="mr-2 h-4 w-4" />
                            {t('purchases.markAsReceived')}
                          </DropdownMenuItem>
                        </>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeletePurchase(purchase)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('purchases.delete')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
