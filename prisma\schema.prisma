generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId], map: "accounts_userId_fkey")
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "sessions_userId_fkey")
  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model User {
  id              String              @id @default(cuid())
  email           String              @unique
  password        String
  name            String
  role            UserRole            @default(EMPLOYEE)
  phone           String?
  avatar          String?
  isActive        Boolean             @default(true)
  joinDate        DateTime            @default(now())
  tasksCompleted  Int                 @default(0)
  tasksInProgress Int                 @default(0)
  completionRate  Decimal             @default(0.00) @db.Decimal(5, 2)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  roleId          String?
  idCardNumber    String?
  idCardExpiry    DateTime?
  passportNumber  String?
  passportExpiry  DateTime?
  visaNumber      String?
  visaExpiry      DateTime?
  licenseNumber   String?
  licenseExpiry   DateTime?
  contractExpiry  DateTime?
  accounts        Account[]
  assignedEvents  CalendarEvent[]     @relation("AssignedEvents")
  calendarEvents  CalendarEvent[]
  expenses        Expense[]
  invoices        Invoice[]
  leads           Lead[]
  projectTeams    ProjectTeamMember[]
  createdProjects Project[]           @relation("ProjectCreator")
  managedProjects Project[]           @relation("ProjectManager")
  purchases       Purchase[]
  quotations      Quotation[]
  salesData       SalesData?
  sessions        Session[]
  payouts         SupplierPayout[]
  assignedTasks   Task[]
  createdTasks    Task[]              @relation("TaskCreator")
  userRole        Role?               @relation(fields: [roleId], references: [id])
  projectComments ProjectComment[]    @relation("ProjectCommentAuthor")
  projectDocuments ProjectDocument[]  @relation("ProjectDocumentUploader")
  projectFolders  ProjectFolder[]     @relation("ProjectFolderCreator")
  activities      Activity[]          @relation("UserActivities")
  followUps       FollowUp[]          @relation("UserFollowUps")

  @@index([roleId], map: "users_roleId_fkey")
  @@map("users")
}

model SalesData {
  id             String   @id @default(cuid())
  userId         String   @unique
  totalSales     Decimal  @default(0.000) @db.Decimal(10, 3)
  monthlyTarget  Decimal  @default(0.000) @db.Decimal(10, 3)
  invoicesCount  Int      @default(0)
  avgOrderValue  Decimal  @default(0.000) @db.Decimal(10, 3)
  conversionRate Decimal  @default(0.00) @db.Decimal(5, 2)
  commission     Decimal  @default(0.000) @db.Decimal(10, 3)
  rank           Int      @default(0)
  growth         Decimal  @default(0.00) @db.Decimal(5, 2)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sales_data")
}

model Role {
  id          String           @id @default(cuid())
  name        String           @unique
  nameAr      String?
  description String?
  isActive    Boolean          @default(true)
  isSystem    Boolean          @default(false)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  permissions RolePermission[]
  users       User[]

  @@map("roles")
}

model Permission {
  id          String           @id @default(cuid())
  name        String           @unique
  nameAr      String?
  module      String
  action      String
  resource    String?
  description String?
  isActive    Boolean          @default(true)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  roles       RolePermission[]

  @@unique([module, action, resource])
  @@map("permissions")
}

model RolePermission {
  id           String     @id @default(cuid())
  roleId       String
  permissionId String
  granted      Boolean    @default(true)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@index([permissionId], map: "role_permissions_permissionId_fkey")
  @@map("role_permissions")
}

model Customer {
  id         String      @id @default(cuid())
  name       String
  nameAr     String?
  email      String?
  mobile     String      @unique
  phone      String?
  address    String?
  city       String?
  country    String?     @default("Oman")
  company    String?
  taxNumber  String?
  notes      String?
  isActive   Boolean     @default(true)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  invoices   Invoice[]
  leads      Lead[]
  payments   Payment[]
  projects   Project[]
  quotations Quotation[]
  tasks      Task[]

  @@map("customers")
}

model Lead {
  id            String     @id @default(cuid())
  name          String
  nameAr        String?
  email         String?
  mobile        String     @unique
  phone         String?
  address       String?
  city          String?
  country       String?    @default("Oman")
  company       String?
  source        LeadSource @default(WEBSITE)
  status        LeadStatus @default(NEW)
  notes         String?
  estimatedValue Decimal?  @db.Decimal(10, 2)
  assignedToId  String?
  convertedAt   DateTime?
  customerId    String?
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt
  assignedTo    User?      @relation(fields: [assignedToId], references: [id])
  customer      Customer?  @relation(fields: [customerId], references: [id])
  activities    Activity[]
  followUps     FollowUp[]

  @@index([assignedToId], map: "leads_assignedToId_fkey")
  @@index([customerId], map: "leads_customerId_fkey")
  @@map("leads")
}

model Activity {
  id          String       @id @default(cuid())
  type        ActivityType @default(NOTE)
  title       String
  description String?
  date        DateTime     @default(now())
  time        String?
  leadId      String
  userId      String
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  lead        Lead         @relation(fields: [leadId], references: [id], onDelete: Cascade)
  user        User         @relation("UserActivities", fields: [userId], references: [id])

  @@index([leadId], map: "activities_leadId_fkey")
  @@index([userId], map: "activities_userId_fkey")
  @@map("activities")
}

model FollowUp {
  id            String         @id @default(cuid())
  type          FollowUpType   @default(CALL)
  description   String
  scheduledDate DateTime
  scheduledTime String?
  status        FollowUpStatus @default(PENDING)
  leadId        String
  assignedToId  String
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  lead          Lead           @relation(fields: [leadId], references: [id], onDelete: Cascade) 
  assignedTo    User           @relation("UserFollowUps", fields: [assignedToId], references: [id])

  @@index([leadId], map: "followups_leadId_fkey")
  @@index([assignedToId], map: "followups_assignedToId_fkey")
  @@map("followups")
}

model Supplier {
  id            String           @id @default(cuid())
  name          String
  nameAr        String?
  email         String?
  mobile        String           @unique
  phone         String?
  address       String?
  city          String?
  country       String?          @default("Oman")
  company       String?
  taxNumber     String?
  contactPerson String?
  notes         String?
  isActive      Boolean          @default(true)
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  products      Product[]
  purchases     Purchase[]
  payouts       SupplierPayout[]

  @@map("suppliers")
}

model Category {
  id          String    @id @default(cuid())
  name        String
  nameAr      String?
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]

  @@map("categories")
}

model Unit {
  id            String    @id @default(cuid())
  name          String    @unique
  nameAr        String?
  symbol        String
  symbolAr      String?
  description   String?
  descriptionAr String?
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  products      Product[]

  @@map("units")
}

model Product {
  id             String          @id @default(cuid())
  name           String
  nameAr         String?
  description    String?
  sku            String?         @unique
  barcode        String?
  type           ProductType     @default(PHYSICAL)
  price          Decimal         @db.Decimal(10, 3)
  costPrice      Decimal?        @db.Decimal(10, 3)
  currentStock   Int             @default(0)
  minStock       Int             @default(0)
  maxStock       Int             @default(100)
  location       String?
  lastRestocked  DateTime?
  unit           String          @default("piece")
  image          String?
  isActive       Boolean         @default(true)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  categoryId     String?
  supplierId     String?
  unitId         String?
  invoiceItems   InvoiceItem[]
  category       Category?       @relation(fields: [categoryId], references: [id])
  supplier       Supplier?       @relation(fields: [supplierId], references: [id])
  unitModel      Unit?           @relation(fields: [unitId], references: [id])
  purchaseItems  PurchaseItem[]
  quotationItems QuotationItem[]
  stockMovements StockMovement[]
  taskProducts   TaskProduct[]

  @@index([categoryId], map: "products_categoryId_fkey")
  @@index([supplierId], map: "products_supplierId_fkey")
  @@index([unitId], map: "products_unitId_fkey")
  @@map("products")
}

model StockMovement {
  id        String            @id @default(cuid())
  productId String
  type      StockMovementType
  quantity  Int
  reason    String
  notes     String?
  date      DateTime          @default(now())
  createdAt DateTime          @default(now())
  product   Product           @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId], map: "stock_movements_productId_fkey")
  @@map("stock_movements")
}

model Task {
  id              String        @id @default(cuid())
  title           String
  description     String?
  status          TaskStatus    @default(NEW)
  priority        TaskPriority  @default(MEDIUM)
  startTime       DateTime?
  endTime         DateTime?
  estimatedHours  Decimal?      @db.Decimal(5, 2)
  actualHours     Decimal?      @db.Decimal(5, 2)
  notes           String?
  completionNotes String?
  attachments     String?       @db.LongText
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  customerId      String?
  projectId       String?
  assignedToId    String?
  createdById     String
  invoices        Invoice[]
  taskProducts    TaskProduct[]
  assignedTo      User?         @relation(fields: [assignedToId], references: [id])
  createdBy       User          @relation("TaskCreator", fields: [createdById], references: [id])
  customer        Customer?     @relation(fields: [customerId], references: [id])
  project         Project?      @relation(fields: [projectId], references: [id])

  @@index([assignedToId], map: "tasks_assignedToId_fkey")
  @@index([createdById], map: "tasks_createdById_fkey")
  @@index([customerId], map: "tasks_customerId_fkey")
  @@index([projectId], map: "tasks_projectId_fkey")
  @@map("tasks")
}

model TaskProduct {
  id        String  @id @default(cuid())
  quantity  Int
  notes     String?
  taskId    String
  productId String
  product   Product @relation(fields: [productId], references: [id])
  task      Task    @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@index([productId], map: "task_products_productId_fkey")
  @@index([taskId], map: "task_products_taskId_fkey")
  @@map("task_products")
}

model Invoice {
  id         String        @id @default(cuid())
  number     String        @unique
  date       DateTime      @default(now())
  dueDate    DateTime?
  status     PaymentStatus @default(UNPAID)
  subtotal   Decimal       @db.Decimal(10, 2)
  taxAmount  Decimal       @default(0.00) @db.Decimal(10, 2)
  discount   Decimal       @default(0.00) @db.Decimal(10, 2)
  total      Decimal       @db.Decimal(10, 2)
  notes      String?
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  customerId String?
  userId     String
  taskId     String?
  items      InvoiceItem[]
  customer   Customer?     @relation(fields: [customerId], references: [id])
  task       Task?         @relation(fields: [taskId], references: [id])
  user       User          @relation(fields: [userId], references: [id])
  payments   Payment[]

  @@index([customerId], map: "invoices_customerId_fkey")
  @@index([taskId], map: "invoices_taskId_fkey")
  @@index([userId], map: "invoices_userId_fkey")
  @@map("invoices")
}

model InvoiceItem {
  id          String   @id @default(cuid())
  description String
  quantity    Decimal  @db.Decimal(10, 2)
  unitPrice   Decimal  @db.Decimal(10, 2)
  total       Decimal  @db.Decimal(10, 2)
  invoiceId   String
  productId   String?
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  product     Product? @relation(fields: [productId], references: [id])

  @@index([invoiceId], map: "invoice_items_invoiceId_fkey")
  @@index([productId], map: "invoice_items_productId_fkey")
  @@map("invoice_items")
}

model Quotation {
  id         String          @id @default(cuid())
  number     String          @unique
  date       DateTime        @default(now())
  validUntil DateTime?
  status     QuotationStatus @default(PENDING)
  subtotal   Decimal         @db.Decimal(10, 2)
  taxAmount  Decimal         @default(0.00) @db.Decimal(10, 2)
  discount   Decimal         @default(0.00) @db.Decimal(10, 2)
  total      Decimal         @db.Decimal(10, 2)
  notes      String?
  createdAt  DateTime        @default(now())
  updatedAt  DateTime        @updatedAt
  customerId String?
  userId     String
  items      QuotationItem[]
  customer   Customer?       @relation(fields: [customerId], references: [id])
  user       User            @relation(fields: [userId], references: [id])

  @@index([customerId], map: "quotations_customerId_fkey")
  @@index([userId], map: "quotations_userId_fkey")
  @@map("quotations")
}

model QuotationItem {
  id          String    @id @default(cuid())
  description String
  quantity    Decimal   @db.Decimal(10, 2)
  unitPrice   Decimal   @db.Decimal(10, 2)
  total       Decimal   @db.Decimal(10, 2)
  quotationId String
  productId   String?
  product     Product?  @relation(fields: [productId], references: [id])
  quotation   Quotation @relation(fields: [quotationId], references: [id], onDelete: Cascade)

  @@index([productId], map: "quotation_items_productId_fkey")
  @@index([quotationId], map: "quotation_items_quotationId_fkey")
  @@map("quotation_items")
}

model Purchase {
  id             String           @id @default(cuid())
  number         String           @unique
  date           DateTime         @default(now())
  expectedDate   DateTime?
  status         PurchaseStatus   @default(PENDING)
  subtotal       Decimal          @db.Decimal(10, 3)
  taxAmount      Decimal          @default(0.000) @db.Decimal(10, 3)
  discountAmount Decimal          @default(0.000) @db.Decimal(10, 3)
  total          Decimal          @db.Decimal(10, 3)
  notes          String?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  supplierId     String
  userId         String?
  items          PurchaseItem[]
  supplier       Supplier         @relation(fields: [supplierId], references: [id])
  user           User?            @relation(fields: [userId], references: [id])
  payouts        SupplierPayout[]

  @@index([supplierId], map: "purchases_supplierId_fkey")
  @@index([userId], map: "purchases_userId_fkey")
  @@map("purchases")
}

model PurchaseItem {
  id          String   @id @default(cuid())
  description String
  quantity    Int
  unitPrice   Decimal  @db.Decimal(10, 3)
  total       Decimal  @db.Decimal(10, 3)
  purchaseId  String
  productId   String?
  product     Product? @relation(fields: [productId], references: [id])
  purchase    Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)

  @@index([productId], map: "purchase_items_productId_fkey")
  @@index([purchaseId], map: "purchase_items_purchaseId_fkey")
  @@map("purchase_items")
}

model Payment {
  id         String        @id @default(cuid())
  amount     Decimal       @db.Decimal(10, 3)
  method     PaymentMethod @default(CASH)
  reference  String?
  notes      String?
  date       DateTime      @default(now())
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @updatedAt
  invoiceId  String
  customerId String
  customer   Customer      @relation(fields: [customerId], references: [id])
  invoice    Invoice       @relation(fields: [invoiceId], references: [id])

  @@index([customerId], map: "payments_customerId_fkey")
  @@index([invoiceId], map: "payments_invoiceId_fkey")
  @@map("payments")
}

model ExpenseType {
  id          String    @id @default(cuid())
  name        String    @unique
  nameAr      String?
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  expenses    Expense[]

  @@map("expense_types")
}

model Expense {
  id            String        @id @default(cuid())
  number        String        @unique
  date          DateTime      @default(now())
  description   String
  amount        Decimal       @db.Decimal(10, 3)
  paymentMethod PaymentMethod @default(CASH)
  status        ExpenseStatus @default(PENDING)
  receipt       String?
  notes         String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  expenseTypeId String
  createdById   String
  projectId     String?
  createdBy     User          @relation(fields: [createdById], references: [id])
  expenseType   ExpenseType   @relation(fields: [expenseTypeId], references: [id])
  project       Project?      @relation(fields: [projectId], references: [id])

  @@index([createdById], map: "expenses_createdById_fkey")
  @@index([expenseTypeId], map: "expenses_expenseTypeId_fkey")
  @@index([projectId], map: "expenses_projectId_fkey")
  @@map("expenses")
}

model Setting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String   @db.LongText
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("settings")
}

model SupplierPayout {
  id          String        @id @default(cuid())
  number      String        @unique
  amount      Decimal       @db.Decimal(10, 3)
  method      PaymentMethod @default(BANK_TRANSFER)
  reference   String?
  description String?
  status      PayoutStatus  @default(PENDING)
  date        DateTime      @default(now())
  dueDate     DateTime?
  paidDate    DateTime?
  notes       String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  supplierId  String
  purchaseId  String?
  createdById String
  createdBy   User          @relation(fields: [createdById], references: [id])
  purchase    Purchase?     @relation(fields: [purchaseId], references: [id])
  supplier    Supplier      @relation(fields: [supplierId], references: [id])

  @@index([createdById], map: "supplier_payouts_createdById_fkey")
  @@index([purchaseId], map: "supplier_payouts_purchaseId_fkey")
  @@index([supplierId], map: "supplier_payouts_supplierId_fkey")
  @@map("supplier_payouts")
}

model Project {
  id          String              @id @default(cuid())
  code        String              @unique
  name        String
  nameAr      String?
  description String?
  status      ProjectStatus       @default(PLANNING)
  priority    ProjectPriority     @default(MEDIUM)
  startDate   DateTime
  endDate     DateTime?
  budget      Decimal?            @db.Decimal(10, 3)
  actualCost  Decimal?            @db.Decimal(10, 3)
  progress    Int                 @default(0)
  notes       String?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  clientId    String?
  managerId   String?
  createdById String
  expenses    Expense[]
  teamMembers ProjectTeamMember[]
  client      Customer?           @relation(fields: [clientId], references: [id])
  createdBy   User                @relation("ProjectCreator", fields: [createdById], references: [id])
  manager     User?               @relation("ProjectManager", fields: [managerId], references: [id])
  tasks       Task[]
  comments    ProjectComment[]
  documents   ProjectDocument[]
  folders     ProjectFolder[]

  @@index([clientId], map: "projects_clientId_fkey")
  @@index([createdById], map: "projects_createdById_fkey")
  @@index([managerId], map: "projects_managerId_fkey")
  @@map("projects")
}

model ProjectTeamMember {
  id        String   @id @default(cuid())
  role      String?
  joinedAt  DateTime @default(now())
  projectId String
  userId    String
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id])

  @@unique([projectId, userId])
  @@index([userId], map: "project_team_members_userId_fkey")
  @@map("project_team_members")
}

model ProjectComment {
  id        String   @id @default(cuid())
  content   String   @db.Text
  isPinned  Boolean  @default(false)
  projectId String
  authorId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  author    User     @relation("ProjectCommentAuthor", fields: [authorId], references: [id], onDelete: Cascade)

  @@index([projectId], map: "project_comments_projectId_fkey")
  @@index([authorId], map: "project_comments_authorId_fkey")
  @@map("project_comments")
}

model ProjectDocument {
  id             String   @id @default(cuid())
  name           String
  type           String
  size           BigInt?
  category       String?
  description    String?
  url            String?
  parentFolderId String?
  projectId      String
  uploadedById   String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  project        Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  uploadedBy     User     @relation("ProjectDocumentUploader", fields: [uploadedById], references: [id], onDelete: Cascade)
  parentFolder   ProjectFolder? @relation("FolderDocuments", fields: [parentFolderId], references: [id], onDelete: Cascade)

  @@index([projectId], map: "project_documents_projectId_fkey")
  @@index([uploadedById], map: "project_documents_uploadedById_fkey")
  @@index([parentFolderId], map: "project_documents_parentFolderId_fkey")
  @@map("project_documents")
}

model ProjectFolder {
  id          String            @id @default(cuid())
  name        String
  description String?
  projectId   String
  createdById String
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  project     Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  createdBy   User              @relation("ProjectFolderCreator", fields: [createdById], references: [id], onDelete: Cascade)
  documents   ProjectDocument[] @relation("FolderDocuments")

  @@index([projectId], map: "project_folders_projectId_fkey")
  @@index([createdById], map: "project_folders_createdById_fkey")
  @@map("project_folders")
}

model CalendarEvent {
  id                String         @id @default(cuid())
  title             String
  titleAr           String?
  description       String?
  type              EventType      @default(REMINDER)
  category          EventCategory  @default(DOCUMENT)
  startDate         DateTime
  endDate           DateTime?
  dueDate           DateTime?
  isAllDay          Boolean        @default(false)
  status            EventStatus    @default(PENDING)
  priority          EventPriority  @default(MEDIUM)
  notifyBefore      Int?
  isRecurring       Boolean        @default(false)
  recurringType     RecurringType?
  recurringInterval Int?
  relatedEntityType String?
  relatedEntityId   String?
  notes             String?
  attachments       String?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  createdById       String
  assignedToId      String?
  assignedTo        User?          @relation("AssignedEvents", fields: [assignedToId], references: [id])
  createdBy         User           @relation(fields: [createdById], references: [id])

  @@index([assignedToId], map: "calendar_events_assignedToId_fkey")
  @@index([createdById], map: "calendar_events_createdById_fkey")
  @@map("calendar_events")
}

enum UserRole {
  ADMIN
  MANAGER
  EMPLOYEE
}

enum LeadSource {
  WEBSITE
  PHONE
  EMAIL
  REFERRAL
  SOCIAL_MEDIA
  ADVERTISEMENT
  WALK_IN
  COLD_CALL
  OTHER
}

enum LeadStatus {
  NEW
  CONTACTED
  QUALIFIED
  PROPOSAL
  NEGOTIATION
  CONVERTED
  LOST
  UNQUALIFIED
}

enum ProductType {
  PHYSICAL
  SERVICE
}

enum StockMovementType {
  IN
  OUT
}

enum TaskStatus {
  NEW
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum PaymentStatus {
  PAID
  PARTIAL
  UNPAID
  OVERDUE
}

enum QuotationStatus {
  PENDING
  APPROVED
  REJECTED
  EXPIRED
}

enum PurchaseStatus {
  DRAFT
  PENDING
  APPROVED
  ORDERED
  RECEIVED
  CANCELLED
}

enum PaymentMethod {
  CASH
  CARD
  BANK_TRANSFER
  CHECK
  OTHER
}

enum ExpenseStatus {
  PENDING
  APPROVED
  REJECTED
  PAID
}

enum PayoutStatus {
  PENDING
  APPROVED
  PAID
  CANCELLED
  REJECTED
}

enum ProjectStatus {
  PLANNING
  IN_PROGRESS
  ON_HOLD
  COMPLETED
  CANCELLED
}

enum ProjectPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum EventType {
  REMINDER
  MEETING
  DEADLINE
  RENEWAL
  EXPIRATION
  TASK_DUE
  INVOICE_DUE
  PAYMENT_DUE
  HOLIDAY
  OTHER
}

enum EventCategory {
  DOCUMENT
  FINANCIAL
  LEGAL
  HR
  BUSINESS
  PERSONAL
  SYSTEM
  OTHER
}

enum EventStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
  OVERDUE
}

enum EventPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
  CRITICAL
}

enum RecurringType {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
  CUSTOM
}

enum ActivityType {
  CALL
  EMAIL
  MEETING
  NOTE
  SMS
  WHATSAPP
}

enum FollowUpType {
  CALL
  EMAIL
  MEETING
  PROPOSAL
  VISIT
  QUOTE
}

enum FollowUpStatus {
  PENDING
  COMPLETED
  CANCELLED
  OVERDUE
}
