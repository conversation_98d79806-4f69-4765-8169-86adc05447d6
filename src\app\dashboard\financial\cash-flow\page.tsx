"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  Download,
  Printer,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calculator,
  Building,
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

// Real Cash Flow data for Omani printing company (January 2024)
const cashFlowData = {
  period: "يناير 2024",
  operatingActivities: {
    netIncome: 50080,
    adjustments: {
      depreciation: 11800,
      changeInAccountsReceivable: -12400,
      changeInInventory: -8600,
      changeInPrepaidExpenses: -2100,
      changeInAccountsPayable: 8900,
      changeInAccrued: 4200,
    },
    netCashFromOperating: 51880,
  },
  investingActivities: {
    equipmentPurchases: -25000,
    furniturePurchases: -8500,
    buildingImprovements: -15000,
    netCashFromInvesting: -48500,
  },
  financingActivities: {
    loanRepayments: -12000,
    mortgagePayments: -18000,
    ownerDistributions: -15000,
    netCashFromFinancing: -45000,
  },
  netChangeInCash: -41620,
  beginningCash: 167220,
  endingCash: 125600,
}

export default function CashFlowStatement() {
  const router = useRouter()
  const { t } = useI18n()
  const [selectedPeriod, setSelectedPeriod] = useState("current")
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handlePrint = () => {
    window.print()
  }

  const handleDownload = () => {
    alert('وظيفة التنزيل ستكون متاحة قريباً')
  }

  const formatCashFlow = (amount: number) => {
    return amount >= 0 ? formatCurrency(amount) : `(${formatCurrency(Math.abs(amount))})`
  }

  const getCashFlowColor = (amount: number) => {
    return amount >= 0 ? 'text-green-600' : 'text-red-600'
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            رجوع
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">بيان التدفق النقدي</h2>
            <p className="text-muted-foreground">
              حركة النقدية للفترة المنتهية في {cashFlowData.period}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current">الفترة الحالية</SelectItem>
              <SelectItem value="previous">الفترة السابقة</SelectItem>
              <SelectItem value="ytd">من بداية العام</SelectItem>
              <SelectItem value="quarterly">ربع سنوي</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            طباعة
          </Button>
          <Button onClick={handleDownload}>
            <Download className="mr-2 h-4 w-4" />
            تحميل PDF
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="bg-slate-50/50 dark:bg-slate-900/20 rounded-lg p-6 border border-slate-200/60 dark:border-slate-700/60">
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">التدفق من العمليات</CardTitle>
              <DollarSign className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getCashFlowColor(cashFlowData.operatingActivities.netCashFromOperating)}`}>
                {formatCashFlow(cashFlowData.operatingActivities.netCashFromOperating)}
              </div>
              <div className="text-xs text-muted-foreground">
                الأنشطة التشغيلية
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">التدفق من الاستثمار</CardTitle>
              <Building className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getCashFlowColor(cashFlowData.investingActivities.netCashFromInvesting)}`}>
                {formatCashFlow(cashFlowData.investingActivities.netCashFromInvesting)}
              </div>
              <div className="text-xs text-muted-foreground">
                الأنشطة الاستثمارية
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">التدفق من التمويل</CardTitle>
              <Calculator className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getCashFlowColor(cashFlowData.financingActivities.netCashFromFinancing)}`}>
                {formatCashFlow(cashFlowData.financingActivities.netCashFromFinancing)}
              </div>
              <div className="text-xs text-muted-foreground">
                الأنشطة التمويلية
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">صافي التغيير في النقدية</CardTitle>
              {cashFlowData.netChangeInCash >= 0 ? 
                <TrendingUp className="h-4 w-4 text-green-500" /> : 
                <TrendingDown className="h-4 w-4 text-red-500" />
              }
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getCashFlowColor(cashFlowData.netChangeInCash)}`}>
                {formatCashFlow(cashFlowData.netChangeInCash)}
              </div>
              <div className="text-xs text-muted-foreground">
                التغيير الإجمالي
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Cash Flow Statement */}
      <Card className="bg-white dark:bg-slate-800 shadow-sm">
        <CardHeader>
          <CardTitle className="text-xl font-bold">بيان التدفق النقدي</CardTitle>
          <p className="text-muted-foreground">{cashFlowData.period}</p>
        </CardHeader>
        <CardContent>
          <Table>
            <TableBody>
              {/* Operating Activities */}
              <TableRow className="border-t-2 border-blue-300 font-medium bg-blue-50 dark:bg-blue-800">
                <TableCell className="font-bold text-lg text-blue-800 dark:text-blue-200" colSpan={2}>
                  التدفقات النقدية من الأنشطة التشغيلية
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">صافي الدخل</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(cashFlowData.operatingActivities.netIncome)}</TableCell>
              </TableRow>
              <TableRow className="bg-slate-50 dark:bg-slate-800">
                <TableCell className="font-semibold" colSpan={2}>
                  تعديلات للتوفيق بين صافي الدخل وصافي النقدية:
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-8">الاستهلاك والإطفاء</TableCell>
                <TableCell className="text-right">{formatCurrency(cashFlowData.operatingActivities.adjustments.depreciation)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-8">التغيير في الذمم المدينة</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.operatingActivities.adjustments.changeInAccountsReceivable)}`}>
                  {formatCashFlow(cashFlowData.operatingActivities.adjustments.changeInAccountsReceivable)}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-8">التغيير في المخزون</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.operatingActivities.adjustments.changeInInventory)}`}>
                  {formatCashFlow(cashFlowData.operatingActivities.adjustments.changeInInventory)}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-8">التغيير في المصروفات المدفوعة مقدماً</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.operatingActivities.adjustments.changeInPrepaidExpenses)}`}>
                  {formatCashFlow(cashFlowData.operatingActivities.adjustments.changeInPrepaidExpenses)}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-8">التغيير في الذمم الدائنة</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.operatingActivities.adjustments.changeInAccountsPayable)}`}>
                  {formatCashFlow(cashFlowData.operatingActivities.adjustments.changeInAccountsPayable)}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-8">التغيير في المستحقات</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.operatingActivities.adjustments.changeInAccrued)}`}>
                  {formatCashFlow(cashFlowData.operatingActivities.adjustments.changeInAccrued)}
                </TableCell>
              </TableRow>
              <TableRow className="border-t-2 border-blue-400 font-bold bg-blue-100 dark:bg-blue-900/30">
                <TableCell className="font-bold text-lg">صافي النقدية من الأنشطة التشغيلية</TableCell>
                <TableCell className={`text-right font-bold text-lg ${getCashFlowColor(cashFlowData.operatingActivities.netCashFromOperating)}`}>
                  {formatCashFlow(cashFlowData.operatingActivities.netCashFromOperating)}
                </TableCell>
              </TableRow>

              {/* Investing Activities */}
              <TableRow className="border-t-2 border-purple-300 font-medium bg-purple-50 dark:bg-purple-800">
                <TableCell className="font-bold text-lg text-purple-800 dark:text-purple-200" colSpan={2}>
                  التدفقات النقدية من الأنشطة الاستثمارية
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">شراء المعدات</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.investingActivities.equipmentPurchases)}`}>
                  {formatCashFlow(cashFlowData.investingActivities.equipmentPurchases)}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">شراء الأثاث والتجهيزات</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.investingActivities.furniturePurchases)}`}>
                  {formatCashFlow(cashFlowData.investingActivities.furniturePurchases)}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">تحسينات على المباني</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.investingActivities.buildingImprovements)}`}>
                  {formatCashFlow(cashFlowData.investingActivities.buildingImprovements)}
                </TableCell>
              </TableRow>
              <TableRow className="border-t-2 border-purple-400 font-bold bg-purple-100 dark:bg-purple-900/30">
                <TableCell className="font-bold text-lg">صافي النقدية من الأنشطة الاستثمارية</TableCell>
                <TableCell className={`text-right font-bold text-lg ${getCashFlowColor(cashFlowData.investingActivities.netCashFromInvesting)}`}>
                  {formatCashFlow(cashFlowData.investingActivities.netCashFromInvesting)}
                </TableCell>
              </TableRow>

              {/* Financing Activities */}
              <TableRow className="border-t-2 border-orange-300 font-medium bg-orange-50 dark:bg-orange-800">
                <TableCell className="font-bold text-lg text-orange-800 dark:text-orange-200" colSpan={2}>
                  التدفقات النقدية من الأنشطة التمويلية
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">سداد القروض</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.financingActivities.loanRepayments)}`}>
                  {formatCashFlow(cashFlowData.financingActivities.loanRepayments)}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">دفعات القرض العقاري</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.financingActivities.mortgagePayments)}`}>
                  {formatCashFlow(cashFlowData.financingActivities.mortgagePayments)}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">توزيعات على المالكين</TableCell>
                <TableCell className={`text-right ${getCashFlowColor(cashFlowData.financingActivities.ownerDistributions)}`}>
                  {formatCashFlow(cashFlowData.financingActivities.ownerDistributions)}
                </TableCell>
              </TableRow>
              <TableRow className="border-t-2 border-orange-400 font-bold bg-orange-100 dark:bg-orange-900/30">
                <TableCell className="font-bold text-lg">صافي النقدية من الأنشطة التمويلية</TableCell>
                <TableCell className={`text-right font-bold text-lg ${getCashFlowColor(cashFlowData.financingActivities.netCashFromFinancing)}`}>
                  {formatCashFlow(cashFlowData.financingActivities.netCashFromFinancing)}
                </TableCell>
              </TableRow>

              {/* Net Change in Cash */}
              <TableRow className="border-t-4 border-slate-400 font-bold bg-slate-100 dark:bg-slate-700">
                <TableCell className="font-bold text-lg">صافي التغيير في النقدية ومعادلات النقدية</TableCell>
                <TableCell className={`text-right font-bold text-lg ${getCashFlowColor(cashFlowData.netChangeInCash)}`}>
                  {formatCashFlow(cashFlowData.netChangeInCash)}
                </TableCell>
              </TableRow>

              {/* Beginning and Ending Cash */}
              <TableRow>
                <TableCell className="pl-6">النقدية في بداية الفترة</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(cashFlowData.beginningCash)}</TableCell>
              </TableRow>
              <TableRow className="border-t-2 border-green-400 font-bold bg-green-100 dark:bg-green-900/30">
                <TableCell className="font-bold text-xl">النقدية في نهاية الفترة</TableCell>
                <TableCell className="text-right font-bold text-green-600 text-xl">{formatCurrency(cashFlowData.endingCash)}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Cash Flow Analysis */}
      <Card className="bg-white dark:bg-slate-800 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-bold">تحليل التدفق النقدي</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-sm text-muted-foreground">نسبة التدفق التشغيلي</div>
              <div className="text-2xl font-bold text-blue-600">
                {((cashFlowData.operatingActivities.netCashFromOperating / cashFlowData.operatingActivities.netIncome) * 100).toFixed(1)}%
              </div>
              <div className="text-xs text-muted-foreground">من صافي الدخل</div>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-sm text-muted-foreground">الاستثمار في الأصول</div>
              <div className="text-2xl font-bold text-purple-600">
                {formatCurrency(Math.abs(cashFlowData.investingActivities.netCashFromInvesting))}
              </div>
              <div className="text-xs text-muted-foreground">إجمالي الاستثمارات</div>
            </div>
            <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div className="text-sm text-muted-foreground">معدل التدفق الحر</div>
              <div className="text-2xl font-bold text-orange-600">
                {formatCurrency(cashFlowData.operatingActivities.netCashFromOperating + cashFlowData.investingActivities.netCashFromInvesting)}
              </div>
              <div className="text-xs text-muted-foreground">التشغيلي - الاستثماري</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 