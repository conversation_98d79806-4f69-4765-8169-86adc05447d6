"use client"

import { formatCurrency } from "@/lib/localization"
import { Package } from "lucide-react"

interface QuotationItem {
  id: string
  name: string
  nameAr?: string
  image?: string
  quantity: number
  price: number
  total: number
  unit: string
}

interface QuotationData {
  quotationNumber: string
  date: string
  validUntil: string
  customer: {
    name: string
    email: string
    phone: string
    address: string
  }
  items: QuotationItem[]
  subtotal: number
  tax: number
  total: number
  notes?: string
}

interface QuotationTemplateProps {
  quotation: QuotationData
  showImages?: boolean
}

export function QuotationTemplate({ quotation, showImages = true }: QuotationTemplateProps) {
  return (
    <div className="max-w-4xl mx-auto p-8 bg-white">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">QUOTATION</h1>
          <p className="text-gray-600 mt-1">عرض سعر</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-blue-600">#{quotation.quotationNumber}</div>
          <div className="text-sm text-gray-600 mt-1">
            <div>Date: {quotation.date}</div>
            <div>Valid Until: {quotation.validUntil}</div>
          </div>
        </div>
      </div>

      {/* Company & Customer Info */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">From:</h3>
          <div className="text-sm text-gray-600">
            <div className="font-medium">Office Sales & Services</div>
            <div>مبيعات وخدمات المكتب</div>
            <div>Muscat, Sultanate of Oman</div>
            <div>مسقط، سلطنة عمان</div>
            <div>Phone: +968 1234 5678</div>
            <div>Email: <EMAIL></div>
          </div>
        </div>
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">Quote For:</h3>
          <div className="text-sm text-gray-600">
            <div className="font-medium">{quotation.customer.name}</div>
            <div>{quotation.customer.address}</div>
            <div>Phone: {quotation.customer.phone}</div>
            <div>Email: {quotation.customer.email}</div>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-8">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              {showImages && <th className="border border-gray-300 px-4 py-3 text-left">Image</th>}
              <th className="border border-gray-300 px-4 py-3 text-left">Item / العنصر</th>
              <th className="border border-gray-300 px-4 py-3 text-center">Qty / الكمية</th>
              <th className="border border-gray-300 px-4 py-3 text-right">Price / السعر</th>
              <th className="border border-gray-300 px-4 py-3 text-right">Total / المجموع</th>
            </tr>
          </thead>
          <tbody>
            {quotation.items.map((item) => (
              <tr key={item.id}>
                {showImages && (
                  <td className="border border-gray-300 px-4 py-3">
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                      {item.image ? (
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                  </td>
                )}
                <td className="border border-gray-300 px-4 py-3">
                  <div className="font-medium">{item.name}</div>
                  {item.nameAr && (
                    <div className="text-sm text-gray-600" dir="rtl">{item.nameAr}</div>
                  )}
                </td>
                <td className="border border-gray-300 px-4 py-3 text-center">
                  {item.quantity} {item.unit}
                </td>
                <td className="border border-gray-300 px-4 py-3 text-right">
                  {formatCurrency(item.price)}
                </td>
                <td className="border border-gray-300 px-4 py-3 text-right font-medium">
                  {formatCurrency(item.total)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-64">
          <div className="flex justify-between py-2 border-b border-gray-200">
            <span>Subtotal / المجموع الفرعي:</span>
            <span>{formatCurrency(quotation.subtotal)}</span>
          </div>
          <div className="flex justify-between py-2 border-b border-gray-200">
            <span>Tax / الضريبة:</span>
            <span>{formatCurrency(quotation.tax)}</span>
          </div>
          <div className="flex justify-between py-3 font-bold text-lg border-b-2 border-gray-900">
            <span>Total / المجموع الكلي:</span>
            <span>{formatCurrency(quotation.total)}</span>
          </div>
        </div>
      </div>

      {/* Notes */}
      {quotation.notes && (
        <div className="mb-8">
          <h3 className="font-semibold text-gray-900 mb-2">Notes / ملاحظات:</h3>
          <p className="text-sm text-gray-600">{quotation.notes}</p>
        </div>
      )}

      {/* Terms */}
      <div className="mb-8">
        <h3 className="font-semibold text-gray-900 mb-2">Terms & Conditions / الشروط والأحكام:</h3>
        <div className="text-sm text-gray-600 space-y-1">
          <p>• This quotation is valid for 30 days from the date of issue.</p>
          <p>• Prices are subject to change without prior notice.</p>
          <p>• Payment terms: 50% advance, 50% on completion.</p>
          <p>• عرض السعر هذا صالح لمدة 30 يوماً من تاريخ الإصدار.</p>
          <p>• الأسعار قابلة للتغيير دون إشعار مسبق.</p>
          <p>• شروط الدفع: 50% مقدماً، 50% عند الانتهاء.</p>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-sm text-gray-500 border-t border-gray-200 pt-4">
        <p>Thank you for considering our services! / شكراً لاهتمامكم بخدماتنا!</p>
        <p className="mt-1">Office Sales & Services - Muscat, Sultanate of Oman</p>
      </div>
    </div>
  )
}

// Sample quotation data for testing
export const sampleQuotationData: QuotationData = {
  quotationNumber: "QUO-001",
  date: "2024-01-20",
  validUntil: "2024-02-20",
  customer: {
    name: "XYZ Company",
    email: "<EMAIL>",
    phone: "+968 9876 5432",
    address: "Ruwi, Muscat, Sultanate of Oman"
  },
  items: [
    {
      id: "1",
      name: "Business Card Design & Printing",
      nameAr: "تصميم وطباعة بطاقات العمل",
      image: "https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=400&fit=crop&crop=center",
      quantity: 500,
      price: 0.10,
      total: 50.000,
      unit: "pieces"
    },
    {
      id: "2",
      name: "Brochure Design",
      nameAr: "تصميم البروشور",
      image: "https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=400&h=400&fit=crop&crop=center",
      quantity: 1,
      price: 75.000,
      total: 75.000,
      unit: "design"
    },
    {
      id: "3",
      name: "A4 Flyer Printing",
      nameAr: "طباعة منشورات A4",
      image: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=400&h=400&fit=crop&crop=center",
      quantity: 1000,
      price: 0.05,
      total: 50.000,
      unit: "pieces"
    }
  ],
  subtotal: 175.000,
  tax: 17.500,
  total: 192.500,
  notes: "Design concepts will be provided within 3 business days. Printing will commence upon approval."
}
