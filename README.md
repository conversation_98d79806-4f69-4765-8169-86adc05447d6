# Office Sales & Services Management System
## نظام إدارة المبيعات والخدمات المكتبية

A comprehensive bilingual (English/Arabic) web-based business management platform tailored for office service providers such as printing, copying, and office supplies sales.

## Features

### 🏢 Core Modules
- **Dashboard** - Overview of statistics, charts, and recent activities
- **Customer Management** - Add, edit, and manage customer profiles
- **Supplier Management** - Track suppliers and manage relationships
- **Products & Services** - Inventory management with categories and pricing
- **Task Management** - Assign tasks to employees with status tracking
- **Invoice System** - Create, manage, and track invoices with payment status
- **Quotation System** - Generate quotes and convert to invoices
- **Purchase Management** - Track incoming stock and supplier purchases
- **Employee Management** - Manage staff and assign tasks
- **Reports** - Sales reports, task performance, and analytics
- **Settings** - System configuration and company profile

### 🌐 Internationalization
- **Bilingual Support** - Full English and Arabic language support
- **RTL Layout** - Right-to-left layout for Arabic
- **Localized Content** - All UI elements, notifications, and documents adapt to selected language

### 📱 WhatsApp Integration
- **Task Notifications** - Automatic notifications when tasks are assigned, started, or completed
- **Invoice Sharing** - Send invoices directly to customers via WhatsApp
- **Quotation Delivery** - Share quotations with customers instantly
- **Status Updates** - Real-time notifications for important events

### 🔐 Authentication & Authorization
- **Role-based Access** - Admin, Manager, and Employee roles
- **Secure Authentication** - NextAuth.js with credential-based login
- **Protected Routes** - Middleware protection for authenticated areas

## Tech Stack

- **Frontend**: Next.js 15 with App Router
- **UI/UX**: ShadCN/UI + Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: MySQL with Prisma ORM
- **Authentication**: NextAuth.js
- **Internationalization**: next-intl
- **Notifications**: WhatsApp API (Textcloud)
- **PDF Generation**: jsPDF + html2canvas
- **Charts**: Recharts

## Installation

### Prerequisites
- Node.js 18+
- MySQL database
- npm or yarn

### Setup Steps

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Environment Configuration**

   Update `.env` file with your configuration:
   ```env
   # Database
   DATABASE_URL="mysql://username:password@localhost:3306/print_next_db"

   # NextAuth.js
   NEXTAUTH_SECRET="your-secret-key-here"
   NEXTAUTH_URL="http://localhost:3000"

   # WhatsApp API (Textcloud)
   WHATSAPP_API_URL="https://api.textcloud.com"
   WHATSAPP_ACCOUNT_KEY="your-account-key"
   WHATSAPP_SECRET_KEY="your-secret-key"
   ```

3. **Database Setup**
   ```bash
   # Generate Prisma client
   npx prisma generate

   # Run database migrations
   npx prisma db push
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Access the application**
   - Open [http://localhost:3000](http://localhost:3000)
   - Login with your database credentials

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Main application pages
│   └── globals.css        # Global styles
├── components/            # Reusable components
│   ├── ui/               # ShadCN UI components
│   └── layout/           # Layout components
├── lib/                  # Utility libraries
│   ├── auth.ts           # NextAuth configuration
│   ├── db.ts             # Prisma client
│   ├── whatsapp.ts       # WhatsApp service
│   └── utils.ts          # Utility functions
├── messages/             # Internationalization files
│   ├── en.json           # English translations
│   └── ar.json           # Arabic translations
└── middleware.ts         # Next.js middleware
```

## Features Implemented

✅ **Core Infrastructure**
- Next.js 15 with App Router and TypeScript
- ShadCN/UI components with Tailwind CSS
- Prisma ORM with comprehensive MySQL schema
- NextAuth.js authentication with middleware protection
- Session management and route protection

✅ **Complete Database Schema**
- User management with role-based access (Admin/Manager/Employee)
- Customer and supplier relationship management
- Product and service catalog with categories
- Task management with assignment workflow
- Invoice system with line items and payment tracking
- Quotation system with conversion to invoices
- Purchase order management with supplier tracking
- Payment processing and financial tracking
- Settings and configuration management

✅ **Full-Featured Pages**
- **Dashboard** - Statistics, charts, and activity overview
- **Customers** - Complete CRUD with search and filtering
- **Suppliers** - Supplier management with contact tracking
- **Products & Services** - Inventory with stock alerts and categories
- **Tasks** - Assignment, status tracking, and notifications
- **Invoices** - Creation with line items and tax calculations
- **Quotations** - Quote management with conversion to invoices
- **Purchases** - Purchase order management with receiving
- **Employees** - Staff management with performance tracking
- **Reports** - Comprehensive analytics and performance metrics
- **Settings** - System configuration and WhatsApp integration

✅ **Advanced API Infrastructure**
- **Authentication** - Secure login/logout with session management
- **Customers API** - Full CRUD with search and pagination
- **Tasks API** - Status updates with automated notifications
- **Invoices API** - Creation, management, and stock updates
- **Quotations API** - Quote management and invoice conversion
- **Products API** - Inventory management with stock tracking
- **Dashboard Stats** - Real-time analytics and performance data
- **WhatsApp Integration** - Automated notification system

✅ **Enhanced User Experience**
- **Notification Center** - Real-time notifications with badge counts
- **Session-aware Header** - Dynamic user information display
- **Responsive Design** - Optimized for all device sizes
- **Loading States** - Smooth user interactions
- **Error Handling** - Comprehensive error management
- **Toast Notifications** - User feedback system

## Next Steps for Full Implementation

🔄 **Remaining Pages**
- Suppliers management
- Products & services catalog
- Invoice creation and management
- Quotation system
- Purchase management
- Employee management
- Reports and analytics
- Settings configuration

🔄 **Advanced Features**
- PDF generation for invoices/quotations
- Advanced reporting with charts
- File upload functionality
- Email notifications
- Advanced search and filtering
- Data export capabilities

🔄 **Production Ready**
- Error handling and validation
- Loading states and optimizations
- Testing implementation
- Security enhancements
- Performance optimizations

## License

This project is licensed under the MIT License.

---

**نظام إدارة المبيعات والخدمات المكتبية** - A comprehensive solution for office service management with bilingual support and modern web technologies.
