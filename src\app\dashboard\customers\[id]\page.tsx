"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON>er, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  Building,
  CreditCard,
  FileText,
  Receipt,
  MoreHorizontal,
  Trash2,
  Eye,
  Calendar,
  DollarSign,
  TrendingUp,
  Clock
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"

interface Customer {
  id: string
  name: string
  email?: string
  phone: string
  address?: string
  company?: string
  taxNumber?: string
  notes?: string
  isActive: boolean
  createdAt: string
  totalSpent: number
  outstandingBalance: number
  invoiceCount: number
  quotationCount: number
  lastOrderDate?: string
  invoices?: Invoice[]
  quotations?: Quotation[]
}

interface Invoice {
  id: string
  number: string
  date: string
  dueDate: string
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  total: number
  amountPaid: number
  balance: number
}

interface Payment {
  id: string
  invoiceNumber: string
  amount: number
  method: string
  date: string
  reference?: string
  notes?: string
}

interface Quotation {
  id: string
  number: string
  date: string
  validUntil: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED'
  total: number
}

// Customer data will be fetched from API

const mockInvoices: Invoice[] = [
  {
    id: "1",
    number: "INV-001",
    date: "2024-01-20",
    dueDate: "2024-02-20",
    status: "SENT",
    total: 2500,
    amountPaid: 0,
    balance: 2500
  },
  {
    id: "2",
    number: "INV-002",
    date: "2024-01-15",
    dueDate: "2024-02-15",
    status: "OVERDUE",
    total: 1800,
    amountPaid: 800,
    balance: 1000
  },
  {
    id: "3",
    number: "INV-003",
    date: "2024-01-10",
    dueDate: "2024-02-10",
    status: "PAID",
    total: 3200,
    amountPaid: 3200,
    balance: 0
  },
  {
    id: "4",
    number: "INV-004",
    date: "2024-01-05",
    dueDate: "2024-02-05",
    status: "PAID",
    total: 1500,
    amountPaid: 1500,
    balance: 0
  },
]

const mockPayments: Payment[] = [
  {
    id: "1",
    invoiceNumber: "INV-003",
    amount: 3200,
    method: "Bank Transfer",
    date: "2024-01-12",
    reference: "TXN123456",
    notes: "Payment received on time"
  },
  {
    id: "2",
    invoiceNumber: "INV-004",
    amount: 1500,
    method: "Cash",
    date: "2024-01-08",
    reference: "CASH001",
    notes: "Cash payment at office"
  },
  {
    id: "3",
    invoiceNumber: "INV-002",
    amount: 800,
    method: "Credit Card",
    date: "2024-01-16",
    reference: "CC789012",
    notes: "Partial payment"
  },
]

const mockQuotations: Quotation[] = [
  {
    id: "1",
    number: "QUO-001",
    date: "2024-01-18",
    validUntil: "2024-02-18",
    status: "PENDING",
    total: 4500
  },
  {
    id: "2",
    number: "QUO-002",
    date: "2024-01-10",
    validUntil: "2024-02-10",
    status: "CONVERTED",
    total: 3200
  },
  {
    id: "3",
    number: "QUO-003",
    date: "2024-01-05",
    validUntil: "2024-02-05",
    status: "APPROVED",
    total: 2800
  },
]

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  SENT: "bg-blue-100 text-blue-800",
  PAID: "bg-green-100 text-green-800",
  OVERDUE: "bg-red-100 text-red-800",
  CANCELLED: "bg-gray-100 text-gray-800",
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
  EXPIRED: "bg-gray-100 text-gray-800",
  CONVERTED: "bg-blue-100 text-blue-800",
}

export default function CustomerDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const { t } = useI18n()
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/customers/${params.id}`)
        if (response.ok) {
          const customerData = await response.json()
          setCustomer(customerData)
        } else {
          console.error('Failed to fetch customer')
          setCustomer(null)
        }
      } catch (error) {
        console.error('Error fetching customer:', error)
        setCustomer(null)
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchCustomer()
    }
  }, [params.id])

  const handleEdit = () => {
    router.push(`/dashboard/customers/${customer?.id}/edit`)
  }

  const handleDelete = () => {
    if (confirm(t('customers.confirmDelete'))) {
      console.log('Deleting customer:', customer?.name)
      alert('Delete functionality would be implemented here')
      router.push('/dashboard/customers')
    }
  }

  const handleCreateInvoice = () => {
    router.push(`/dashboard/invoices/create?customer=${customer?.id}`)
  }

  const handleCreateQuotation = () => {
    router.push(`/dashboard/quotations/create?customer=${customer?.id}`)
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('common.loading')}</h2>
          </div>
        </div>
      </div>
    )
  }

  if (!customer) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('customers.customerNotFound')}</h2>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{customer.name}</h2>
            <p className="text-muted-foreground">
              {t('customers.customerDetailsAndHistory')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge variant={customer.isActive ? "default" : "secondary"}>
            {customer.isActive ? t('common.active') : t('common.inactive')}
          </Badge>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                {t('common.actions')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t('customers.editCustomer')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleCreateInvoice}>
                <Receipt className="mr-2 h-4 w-4" />
                {t('customers.createInvoice')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCreateQuotation}>
                <FileText className="mr-2 h-4 w-4" />
                {t('customers.createQuotation')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {t('common.delete')} {t('common.customer')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('customers.customerInformation')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('customers.contactInformation')}</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{customer.phone}</span>
                      </div>
                      {customer.email && (
                        <div className="flex items-center space-x-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span>{customer.email}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {customer.company && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('common.company')}</Label>
                      <div className="mt-1 flex items-center space-x-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span>{customer.company}</span>
                      </div>
                    </div>
                  )}

                  {customer.taxNumber && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('customers.taxNumber')}</Label>
                      <p className="text-base mt-1">{customer.taxNumber}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {customer.address && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('common.address')}</Label>
                      <div className="mt-1 flex items-start space-x-2">
                        <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                        <span className="text-sm">{customer.address}</span>
                      </div>
                    </div>
                  )}

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('customers.customerSince')}</Label>
                    <div className="mt-1 flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{new Date(customer.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {customer.lastOrderDate && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('customers.lastOrderDate')}</Label>
                      <div className="mt-1 flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{new Date(customer.lastOrderDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {customer.notes && (
                <div className="mt-6">
                  <Label className="text-sm font-medium text-muted-foreground">{t('common.notes')}</Label>
                  <p className="text-sm text-muted-foreground mt-1">{customer.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tabs for Invoices, Payments, Quotations */}
          <Card>
            <CardContent className="p-0">
              <Tabs defaultValue="invoices" className="w-full">
                <div className="px-6 pt-6">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="invoices">{t('customers.invoices')}</TabsTrigger>
                    <TabsTrigger value="payments">{t('customers.paymentHistory')}</TabsTrigger>
                    <TabsTrigger value="quotations">{t('quotations.title')}</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="invoices" className="px-6 pb-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">{t('customers.customerInvoices')}</h3>
                      <Button size="sm" onClick={handleCreateInvoice}>
                        <Receipt className="mr-2 h-4 w-4" />
                        {t('customers.createInvoice')}
                      </Button>
                    </div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t('customers.invoiceNumber')}</TableHead>
                          <TableHead>{t('common.date')}</TableHead>
                          <TableHead>{t('customers.dueDate')}</TableHead>
                          <TableHead>{t('common.status')}</TableHead>
                          <TableHead className="text-right">{t('common.total')}</TableHead>
                          <TableHead className="text-right">{t('customers.balance')}</TableHead>
                          <TableHead className="text-right">{t('common.actions')}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {customer?.invoices && customer.invoices.length > 0 ? (
                          customer.invoices.map((invoice) => (
                            <TableRow key={invoice.id}>
                              <TableCell className="font-medium">
                                <Button
                                  variant="link"
                                  className="p-0 h-auto font-medium"
                                  onClick={() => router.push(`/dashboard/invoices/${invoice.id}`)}
                                >
                                  {invoice.number}
                                </Button>
                              </TableCell>
                              <TableCell>{new Date(invoice.date).toLocaleDateString()}</TableCell>
                              <TableCell>{invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : '-'}</TableCell>
                              <TableCell>
                                <Badge className={statusColors[invoice.status] || 'bg-gray-100 text-gray-800'}>
                                  {invoice.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right font-medium">
                                {formatCurrency(invoice.total)}
                              </TableCell>
                              <TableCell className="text-right">
                                {invoice.status !== 'PAID' ? (
                                  <span className="text-orange-600 font-medium">
                                    {formatCurrency(invoice.total)}
                                  </span>
                                ) : (
                                  <span className="text-green-600">{t('customers.paid')}</span>
                                )}
                              </TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => router.push(`/dashboard/invoices/${invoice.id}`)}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                              {t('customers.noInvoicesFound')}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>

                <TabsContent value="payments" className="px-6 pb-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">{t('customers.paymentHistory')}</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t('customers.invoiceNumber')}</TableHead>
                          <TableHead>{t('common.date')}</TableHead>
                          <TableHead>{t('customers.method')}</TableHead>
                          <TableHead>{t('customers.reference')}</TableHead>
                          <TableHead className="text-right">{t('common.amount')}</TableHead>
                          <TableHead>{t('common.notes')}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {mockPayments.map((payment) => (
                          <TableRow key={payment.id}>
                            <TableCell className="font-medium">
                              <Button
                                variant="link"
                                className="p-0 h-auto font-medium"
                                onClick={() => router.push(`/dashboard/invoices?search=${payment.invoiceNumber}`)}
                              >
                                {payment.invoiceNumber}
                              </Button>
                            </TableCell>
                            <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{payment.method}</Badge>
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {payment.reference}
                            </TableCell>
                            <TableCell className="text-right font-medium text-green-600">
                              {formatCurrency(payment.amount)}
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {payment.notes}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>

                <TabsContent value="quotations" className="px-6 pb-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">{t('customers.customerQuotations')}</h3>
                      <Button size="sm" onClick={handleCreateQuotation}>
                        <FileText className="mr-2 h-4 w-4" />
                        {t('customers.createQuotation')}
                      </Button>
                    </div>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>{t('customers.quotationNumber')}</TableHead>
                          <TableHead>{t('common.date')}</TableHead>
                          <TableHead>{t('customers.validUntil')}</TableHead>
                          <TableHead>{t('common.status')}</TableHead>
                          <TableHead className="text-right">{t('common.total')}</TableHead>
                          <TableHead className="text-right">{t('common.actions')}</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {customer?.quotations && customer.quotations.length > 0 ? (
                          customer.quotations.map((quotation) => (
                            <TableRow key={quotation.id}>
                              <TableCell className="font-medium">
                                <Button
                                  variant="link"
                                  className="p-0 h-auto font-medium"
                                  onClick={() => router.push(`/dashboard/quotations/${quotation.id}`)}
                                >
                                  {quotation.number}
                                </Button>
                              </TableCell>
                              <TableCell>{new Date(quotation.date).toLocaleDateString()}</TableCell>
                              <TableCell>{quotation.validUntil ? new Date(quotation.validUntil).toLocaleDateString() : '-'}</TableCell>
                              <TableCell>
                                <Badge className={statusColors[quotation.status] || 'bg-gray-100 text-gray-800'}>
                                  {quotation.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right font-medium">
                                {formatCurrency(quotation.total)}
                              </TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => router.push(`/dashboard/quotations/${quotation.id}`)}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                              {t('customers.noQuotationsFound')}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Customer Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t('customers.customerSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('customers.totalSpent')}:</span>
                <span className="font-medium">{formatCurrency(customer.totalSpent)}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('customers.outstandingBalance')}:</span>
                <span className={`font-medium ${customer.outstandingBalance > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                  {formatCurrency(customer.outstandingBalance)}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('customers.totalInvoices')}:</span>
                <span className="font-medium">{customer.invoiceCount}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('customers.totalQuotations')}:</span>
                <span className="font-medium">{customer.quotations?.length || 0}</span>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('common.status')}:</span>
                <Badge variant={customer.isActive ? "default" : "secondary"}>
                  {customer.isActive ? t('common.active') : t('common.inactive')}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('customers.quickActions')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" onClick={handleCreateInvoice}>
                <Receipt className="mr-2 h-4 w-4" />
                {t('customers.createInvoice')}
              </Button>

              <Button variant="outline" className="w-full" onClick={handleCreateQuotation}>
                <FileText className="mr-2 h-4 w-4" />
                {t('customers.createQuotation')}
              </Button>

              <Button variant="outline" className="w-full" onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t('customers.editCustomer')}
              </Button>

              {customer.email && (
                <Button variant="outline" className="w-full" onClick={() => window.open(`mailto:${customer.email}`)}>
                  <Mail className="mr-2 h-4 w-4" />
                  {t('customers.sendEmail')}
                </Button>
              )}

              <Button variant="outline" className="w-full" onClick={() => window.open(`tel:${customer.phone}`)}>
                <Phone className="mr-2 h-4 w-4" />
                {t('customers.callCustomer')}
              </Button>
            </CardContent>
          </Card>

          {/* Payment Status */}
          <Card>
            <CardHeader>
              <CardTitle>{t('customers.paymentStatus')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">{t('customers.paidInvoices')}</span>
                </div>
                <span className="text-sm font-medium">2</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">{t('customers.sentInvoices')}</span>
                </div>
                <span className="text-sm font-medium">1</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">{t('customers.overdueInvoices')}</span>
                </div>
                <span className="text-sm font-medium">1</span>
              </div>

              <Separator />

              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  {t('customers.lastPayment')}: {new Date('2024-01-16').toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}