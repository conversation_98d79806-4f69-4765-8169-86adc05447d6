import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/stock - Get all products with stock information
export async function GET() {
  try {
    const products = await prisma.product.findMany({
      include: {
        category: true,
        unitModel: true,
        supplier: true,
        stockMovements: {
          orderBy: {
            date: 'desc',
          },
          take: 5, // Get last 5 movements
        },
      },
      orderBy: {
        name: 'asc',
      },
    })

    return NextResponse.json(products)
  } catch (error) {
    console.error('Error fetching stock:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stock' },
      { status: 500 }
    )
  }
}

// POST /api/stock - Create stock movement (adjust stock)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { productId, type, quantity, reason, notes } = body

    // Validate required fields
    if (!productId || !type || !quantity || !reason) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Get current product
    const product = await prisma.product.findUnique({
      where: { id: productId },
    })

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    // Calculate new stock level
    const quantityNum = parseInt(quantity)
    let newStock = product.currentStock

    if (type === 'IN') {
      newStock += quantityNum
    } else if (type === 'OUT') {
      newStock -= quantityNum
      if (newStock < 0) {
        return NextResponse.json(
          { error: 'Insufficient stock' },
          { status: 400 }
        )
      }
    }

    // Create stock movement and update product in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create stock movement
      const stockMovement = await tx.stockMovement.create({
        data: {
          productId,
          type,
          quantity: quantityNum,
          reason,
          notes,
          date: new Date(),
        },
      })

      // Update product stock
      const updatedProduct = await tx.product.update({
        where: { id: productId },
        data: {
          currentStock: newStock,
          lastRestocked: type === 'IN' ? new Date() : product.lastRestocked,
        },
        include: {
          category: true,
          unitModel: true,
          supplier: true,
          stockMovements: {
            orderBy: { date: 'desc' },
            take: 5,
          },
        },
      })

      return { stockMovement, product: updatedProduct }
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Error creating stock movement:', error)
    return NextResponse.json(
      { error: 'Failed to create stock movement' },
      { status: 500 }
    )
  }
}
