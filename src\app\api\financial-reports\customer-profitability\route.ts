import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { Decimal } from '@prisma/client/runtime/library'

export async function GET(request: NextRequest) {
  try {
    // 1. Fetch all customers
    const customers = await prisma.customer.findMany({
      where: {
        invoices: {
          some: {} // Only include customers with at least one invoice
        }
      }
    });

    const profitabilityData = [];

    for (const customer of customers) {
      // 2. Calculate total revenue from invoices
      const invoices = await prisma.invoice.findMany({
        where: { customerId: customer.id, status: { in: ['PAID', 'PARTIAL'] } },
        include: { items: { include: { product: true } } }
      });
      const totalRevenue = invoices.reduce((sum, inv) => sum.add(inv.total), new Decimal(0));

      if (totalRevenue.equals(0)) {
        continue; // Skip customers with no paid revenue
      }

      // 3. Calculate Cost of Goods Sold (COGS) from invoice items
      let totalCOGS = new Decimal(0);
      for (const invoice of invoices) {
        for (const item of invoice.items) {
          const productCost = (item.product as any)?.cost;
          if (productCost) {
            totalCOGS = totalCOGS.add(new Decimal(productCost).mul(item.quantity));
          }
        }
      }

      // 4. Calculate direct project-related expenses
      const projects = await prisma.project.findMany({
        where: { clientId: customer.id },
        select: { id: true }
      });
      const projectIds = projects.map(p => p.id);
      
      const projectExpenses = await prisma.expense.findMany({
        where: { 
          projectId: { in: projectIds },
          status: { in: ['APPROVED', 'PAID'] }
        }
      });
      const totalProjectExpenses = projectExpenses.reduce((sum, exp) => sum.add(exp.amount), new Decimal(0));

      // 5. Calculate final profit and margin
      const totalCost = totalCOGS.add(totalProjectExpenses);
      const netProfit = totalRevenue.sub(totalCost);
      const profitMargin = totalRevenue.isZero() ? new Decimal(0) : netProfit.div(totalRevenue).mul(100);

      profitabilityData.push({
        customerId: customer.id,
        customerName: customer.name,
        customerNameAr: customer.nameAr,
        totalRevenue: totalRevenue.toNumber(),
        totalCost: totalCost.toNumber(),
        netProfit: netProfit.toNumber(),
        profitMargin: profitMargin.toNumber(),
      });
    }

    // Sort by most profitable
    profitabilityData.sort((a, b) => b.netProfit - a.netProfit);

    return NextResponse.json(profitabilityData);

  } catch (error: any) {
    console.error('Error fetching customer profitability:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer profitability data', details: error.message },
      { status: 500 }
    );
  }
} 