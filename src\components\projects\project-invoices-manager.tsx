"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, FileText, Eye, DollarSign, Calendar, User, CreditCard, Edit, Receipt, AlertCircle } from "lucide-react"
import Link from "next/link"
import { formatCurrency } from "@/lib/localization"
import { PaymentDialog } from "@/components/invoices/payment-dialog"

interface Invoice {
  id: string
  number: string
  date: string
  dueDate?: string
  status: string
  total: number
  amountPaid?: number
  customer?: {
    id: string
    name: string
    company?: string
  }
  task?: {
    id: string
    title: string
  }
}

interface RecentPayment {
  id: string
  amount: number
  date: string
  method: string
  invoiceNumber: string
  reference?: string
}

interface ProjectInvoicesManagerProps {
  projectId: string
  isEditable?: boolean
}

const statusColors = {
  PAID: "bg-green-100 text-green-800",
  PARTIAL: "bg-yellow-100 text-yellow-800", 
  UNPAID: "bg-red-100 text-red-800",
  OVERDUE: "bg-red-100 text-red-800",
}

export function ProjectInvoicesManager({ projectId, isEditable = true }: ProjectInvoicesManagerProps) {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [recentPayments, setRecentPayments] = useState<RecentPayment[]>([])
  const [loading, setLoading] = useState(true)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    totalInvoiced: 0,
    totalPaid: 0,
    totalOutstanding: 0,
    paidCount: 0,
    unpaidCount: 0,
    partialCount: 0,
    overdueCount: 0
  })

  useEffect(() => {
    fetchInvoices()
    fetchRecentPayments()
  }, [projectId])

  const fetchInvoices = async () => {
    try {
      const response = await fetch(`/api/invoices?projectId=${projectId}&limit=100`)
      if (response.ok) {
        const data = await response.json()
        const rawInvoices = data.invoices || []
        
        // Calculate amountPaid for each invoice from payments
        const invoicesWithPaidAmounts = rawInvoices.map((invoice: any) => {
          const payments = invoice.payments || []
          const amountPaid = payments.reduce((sum: number, payment: any) => sum + Number(payment.amount), 0)
          
          return {
            ...invoice,
            amountPaid,
            total: Number(invoice.total)
          }
        })
        
        setInvoices(invoicesWithPaidAmounts)
        
        // Calculate comprehensive stats
        const total = invoicesWithPaidAmounts.length
        const totalInvoiced = invoicesWithPaidAmounts.reduce((sum: number, inv: Invoice) => sum + Number(inv.total), 0)
        
        // Calculate total actually paid (sum of all actual payments)
        const totalPaid = invoicesWithPaidAmounts.reduce((sum: number, inv: Invoice) => sum + Number(inv.amountPaid || 0), 0)
        
        // Outstanding = Total Invoiced - Total Paid
        const totalOutstanding = totalInvoiced - totalPaid
        
        // Count by status
        const paidCount = invoicesWithPaidAmounts.filter((inv: Invoice) => inv.status === 'PAID').length
        const unpaidCount = invoicesWithPaidAmounts.filter((inv: Invoice) => inv.status === 'UNPAID').length
        const partialCount = invoicesWithPaidAmounts.filter((inv: Invoice) => inv.status === 'PARTIAL').length
        const overdueCount = invoicesWithPaidAmounts.filter((inv: Invoice) => inv.status === 'OVERDUE').length
        
        setStats({ 
          total, 
          totalInvoiced, 
          totalPaid, 
          totalOutstanding, 
          paidCount, 
          unpaidCount, 
          partialCount, 
          overdueCount 
        })
      }
    } catch (error) {
      console.error('Error fetching invoices:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchRecentPayments = async () => {
    try {
      // Get project invoices first
      const invoicesResponse = await fetch(`/api/invoices?projectId=${projectId}&limit=1000`)
      if (!invoicesResponse.ok) return

      const invoicesData = await invoicesResponse.json()
      const projectInvoices = invoicesData.invoices || []

      if (projectInvoices.length === 0) {
        setRecentPayments([])
        return
      }

      // Fetch payments for each invoice
      const allPayments: RecentPayment[] = []
      
      for (const invoice of projectInvoices) {
        try {
          const paymentsResponse = await fetch(`/api/invoices/${invoice.id}/payments`)
          if (paymentsResponse.ok) {
            const paymentsData = await paymentsResponse.json()
            const payments = paymentsData.payments || []
            
            // Convert payments to RecentPayment format
            payments.forEach((payment: any) => {
              allPayments.push({
                id: payment.id,
                amount: Number(payment.amount),
                date: payment.date,
                method: payment.method || 'Unknown',
                invoiceNumber: invoice.number,
                reference: payment.reference || `PAY-${invoice.number}`
              })
            })
          }
        } catch (error) {
          console.error(`Error fetching payments for invoice ${invoice.id}:`, error)
        }
      }

      // Sort by date (newest first) and take latest 5
      const sortedPayments = allPayments
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, 5)

      setRecentPayments(sortedPayments)
    } catch (error) {
      console.error('Error fetching recent payments:', error)
      setRecentPayments([])
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID':
        return <DollarSign className="h-4 w-4 text-green-600" />
      case 'PARTIAL':
        return <Calendar className="h-4 w-4 text-yellow-600" />
      case 'OVERDUE':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <FileText className="h-4 w-4 text-red-600" />
    }
  }

  const handlePaymentClick = (invoice: Invoice) => {
    setSelectedInvoice(invoice)
    setShowPaymentDialog(true)
  }

  const handlePaymentSuccess = () => {
    // Refresh the invoices list and payments
    fetchInvoices()
    fetchRecentPayments()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Project Invoices</h3>
          <Badge variant="secondary">{stats.total}</Badge>
        </div>
        
        {isEditable && (
          <div className="flex gap-2">
            <Button size="sm" variant="outline" className="flex items-center gap-2" asChild>
              <Link href={`/dashboard/invoices/create?projectId=${projectId}`}>
                <Plus className="h-4 w-4" />
                Create Invoice
              </Link>
            </Button>
            <Button size="sm" className="flex items-center gap-2" asChild>
              <Link href="/dashboard/invoices">
                <Eye className="h-4 w-4" />
                All Invoices
              </Link>
            </Button>
          </div>
        )}
      </div>

      {/* Financial Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
                <p className="text-sm text-muted-foreground font-medium">Total Invoiced</p>
                <p className="text-2xl font-bold text-blue-600">{formatCurrency(stats.totalInvoiced)}</p>
                <p className="text-xs text-muted-foreground">{stats.total} invoice(s)</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground font-medium">Total Paid</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.totalPaid)}</p>
                <p className="text-xs text-muted-foreground">{stats.paidCount + stats.partialCount} payment(s)</p>
          </div>
              <DollarSign className="h-8 w-8 text-green-500" />
        </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
                <p className="text-sm text-muted-foreground font-medium">Outstanding</p>
                <p className="text-2xl font-bold text-red-600">{formatCurrency(stats.totalOutstanding)}</p>
                <p className="text-xs text-muted-foreground">{stats.unpaidCount + stats.overdueCount + stats.partialCount} pending</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
                <p className="text-sm text-muted-foreground font-medium">Collection Rate</p>
                <p className="text-2xl font-bold text-purple-600">
                  {stats.totalInvoiced > 0 ? Math.round((stats.totalPaid / stats.totalInvoiced) * 100) : 0}%
                </p>
                <p className="text-xs text-muted-foreground">of invoiced amount</p>
              </div>
              <Receipt className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Status Breakdown */}
      {stats.total > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Invoice Status Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-sm">Paid ({stats.paidCount})</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <span className="text-sm">Partial ({stats.partialCount})</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="text-sm">Unpaid ({stats.unpaidCount})</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-red-700"></div>
                <span className="text-sm">Overdue ({stats.overdueCount})</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Invoices List */}
        <div className="space-y-4">
          <h4 className="font-medium">Invoices List</h4>
      {invoices.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <FileText className="mx-auto h-12 w-12 mb-3 opacity-50" />
          <p>No invoices created yet</p>
          {isEditable && (
            <p className="text-sm">Create the first invoice for this project</p>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {invoices.map((invoice) => (
            <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
              <div className="flex items-start gap-3 flex-1">
                {getStatusIcon(invoice.status)}
                
                <div className="flex-1 space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{invoice.number}</span>
                    <Badge variant="secondary" className={statusColors[invoice.status as keyof typeof statusColors]}>
                      {invoice.status}
                    </Badge>
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold text-green-600">{formatCurrency(Number(invoice.total))}</span>
                          {invoice.amountPaid > 0 && invoice.status !== 'PAID' && (
                            <span className="text-sm text-muted-foreground">
                              ({formatCurrency(invoice.amountPaid)} paid)
                            </span>
                          )}
                        </div>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>Date: {formatDate(invoice.date)}</span>
                    {invoice.dueDate && <span>Due: {formatDate(invoice.dueDate)}</span>}
                    {invoice.customer && (
                      <span className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {invoice.customer.name}
                        {invoice.customer.company && ` (${invoice.customer.company})`}
                      </span>
                    )}
                    {invoice.task && <span>Task: {invoice.task.title}</span>}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {/* Record Payment Button for unpaid invoices */}
                {(invoice.status === 'UNPAID' || invoice.status === 'PARTIAL' || invoice.status === 'OVERDUE') && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePaymentClick(invoice)}
                    className="text-green-600 hover:text-green-700 border-green-300 hover:border-green-400"
                  >
                    <CreditCard className="h-4 w-4 mr-1" />
                    Pay
                  </Button>
                )}
                    
                    {/* Edit Invoice Button */}
                    {isEditable && (
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/dashboard/invoices/${invoice.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    )}
                
                {/* View Invoice Button */}
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/dashboard/invoices/${invoice.id}`}>
                    <Eye className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
        </div>

        {/* Recent Payments */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium flex items-center gap-2">
              <Receipt className="h-4 w-4" />
              Recent Payments
            </h4>
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/invoices?projectId=${projectId}`}>
                <Eye className="h-4 w-4 mr-2" />
                View All
              </Link>
            </Button>
          </div>
          
          {recentPayments.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground border rounded-lg">
              <Receipt className="mx-auto h-8 w-8 mb-2 opacity-50" />
              <p className="text-sm">No payments recorded yet</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentPayments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-full">
                      <DollarSign className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium">{payment.invoiceNumber}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(payment.date)} • {payment.method}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-600">{formatCurrency(payment.amount)}</p>
                    {payment.reference && (
                      <p className="text-xs text-muted-foreground">{payment.reference}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Payment Dialog */}
      {selectedInvoice && (
        <PaymentDialog
          open={showPaymentDialog}
          onOpenChange={setShowPaymentDialog}
          invoice={{
            id: selectedInvoice.id,
            number: selectedInvoice.number,
            date: selectedInvoice.date,
            dueDate: selectedInvoice.dueDate || selectedInvoice.date,
            customer: selectedInvoice.customer?.name || "Project Customer",
            status: selectedInvoice.status as 'PAID' | 'UNPAID' | 'PARTIAL' | 'OVERDUE',
            subtotal: Number(selectedInvoice.total) * 0.952, // Assuming 5% tax
            taxAmount: Number(selectedInvoice.total) * 0.048,
            total: Number(selectedInvoice.total),
            amountPaid: Number(selectedInvoice.amountPaid || 0),
          }}
          onPaymentSuccess={handlePaymentSuccess}
        />
      )}
    </div>
  )
} 