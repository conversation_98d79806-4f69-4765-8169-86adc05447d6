"use client"

import { useState, useEffect } from "react"
import {
  getExpenseTypes,
  addExpenseType,
  updateExpenseType,
  deleteExpenseType,
  type ExpenseType
} from "@/lib/expense-type-storage"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Search,
  Edit,
  Trash2,
  MoreHorizontal,
  Tag,
  DollarSign
} from "lucide-react"
import { useI18n } from "@/lib/i18n"

export default function ExpenseTypesPage() {
  const { t } = useI18n()
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingType, setEditingType] = useState<ExpenseType | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    nameAr: "",
    description: "",
    isActive: true
  })

  // Load expense types on component mount
  useEffect(() => {
    const loadExpenseTypes = async () => {
      const loadedTypes = await getExpenseTypes()
      setExpenseTypes(loadedTypes)
    }
    loadExpenseTypes()
  }, [])

  const filteredExpenseTypes = expenseTypes.filter(type =>
    type.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (type.nameAr && type.nameAr.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (type.description && type.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const handleOpenDialog = (type?: ExpenseType) => {
    if (type) {
      setEditingType(type)
      setFormData({
        name: type.name,
        nameAr: type.nameAr || "",
        description: type.description || "",
        isActive: type.isActive
      })
    } else {
      setEditingType(null)
      setFormData({
        name: "",
        nameAr: "",
        description: "",
        isActive: true
      })
    }
    setIsDialogOpen(true)
  }

  const handleSave = async () => {
    if (!formData.name.trim()) {
      alert(t('expenses.types.pleaseEnterExpenseTypeName'))
      return
    }

    if (editingType) {
      // Update existing expense type
      const updated = await updateExpenseType(editingType.id, formData)
      if (updated) {
        setExpenseTypes(prev => prev.map(type =>
          type.id === editingType.id ? updated : type
        ))
        setIsDialogOpen(false)
        setEditingType(null)
        setFormData({ name: "", nameAr: "", description: "", isActive: true })
        alert(`${t('expenses.types.expenseTypeUpdatedSuccessfully')}`)
      } else {
        alert(t('expenses.types.failedToUpdateExpenseType'))
      }
    } else {
      // Create new expense type
      const newType = await addExpenseType(formData)
      if (newType) {
        setExpenseTypes(prev => [newType, ...prev])
        setIsDialogOpen(false)
        setEditingType(null)
        setFormData({ name: "", nameAr: "", description: "", isActive: true })
        alert(`${t('expenses.types.expenseTypeCreatedSuccessfully')}`)
      } else {
        alert(t('expenses.types.failedToCreateExpenseType'))
      }
    }
  }

  const handleDelete = async (type: ExpenseType) => {
    if (confirm(`${t('expenses.types.confirmDeleteExpenseType')} "${type.name}"?`)) {
      const deleted = await deleteExpenseType(type.id)
      if (deleted) {
        setExpenseTypes(prev => prev.filter(t => t.id !== type.id))
        alert(`${t('expenses.types.expenseTypeDeletedSuccessfully')}`)
      } else {
        alert(t('expenses.types.failedToDeleteExpenseType'))
      }
    }
  }

  const handleToggleStatus = async (type: ExpenseType) => {
    const updated = await updateExpenseType(type.id, { isActive: !type.isActive })
    if (updated) {
      setExpenseTypes(prev => prev.map(t =>
        t.id === type.id ? updated : t
      ))
      const successMessage = updated.isActive 
        ? t('expenses.types.expenseTypeActivatedSuccessfully')
        : t('expenses.types.expenseTypeDeactivatedSuccessfully')
      alert(successMessage)
    } else {
      alert(t('expenses.types.failedToUpdateExpenseTypeStatus'))
    }
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('expenses.types.title')}</h2>
          <p className="text-muted-foreground">
            {t('expenses.types.subtitle')}
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => handleOpenDialog()}>
              <Plus className="mr-2 h-4 w-4" />
              {t('expenses.types.addExpenseType')}
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {editingType ? t('expenses.types.editExpenseType') : t('expenses.types.addNewExpenseType')}
              </DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t('expenses.types.nameRequired')}</Label>
                <Input
                  id="name"
                  placeholder={t('expenses.types.namePlaceholder')}
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="nameAr">{t('expenses.types.arabicName')}</Label>
                <Input
                  id="nameAr"
                  placeholder={t('expenses.types.arabicNamePlaceholder')}
                  value={formData.nameAr}
                  onChange={(e) => setFormData(prev => ({ ...prev, nameAr: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">{t('expenses.types.description')}</Label>
                <Textarea
                  id="description"
                  placeholder={t('expenses.types.descriptionPlaceholder')}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="min-h-[80px]"
                />
              </div>
              <div className="flex gap-2 pt-4">
                <Button onClick={handleSave} disabled={!formData.name.trim()} className="flex-1">
                  {editingType ? t('expenses.types.update') : t('expenses.types.create')}
                </Button>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  {t('expenses.types.cancel')}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('expenses.types.searchExpenseTypes')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('expenses.types.name')}</TableHead>
              <TableHead>{t('expenses.types.description')}</TableHead>
              <TableHead>{t('expenses.types.status')}</TableHead>
              <TableHead>{t('expenses.types.created')}</TableHead>
              <TableHead className="text-right">{t('expenses.types.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredExpenseTypes.map((type) => (
              <TableRow key={type.id}>
                <TableCell>
                  <div className="flex items-center">
                    <Tag className="mr-2 h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{type.name}</span>
                  </div>
                </TableCell>
                <TableCell className="max-w-[300px]">
                  <p className="text-sm text-muted-foreground truncate">
                    {type.description || t('expenses.types.noDescription')}
                  </p>
                </TableCell>
                <TableCell>
                  <Badge
                    className={type.isActive
                      ? "bg-green-100 text-green-800 border-green-200"
                      : "bg-gray-100 text-gray-800 border-gray-200"
                    }
                  >
                    {type.isActive ? t('expenses.types.activeStatus') : t('expenses.types.inactiveStatus')}
                  </Badge>
                </TableCell>
                <TableCell>
                  {new Date(type.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">{t('expenses.types.openMenu')}</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleOpenDialog(type)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('expenses.types.edit')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleToggleStatus(type)}>
                        <DollarSign className="mr-2 h-4 w-4" />
                        {type.isActive ? t('expenses.types.deactivate') : t('expenses.types.activate')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDelete(type)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('expenses.types.delete')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
