export const leads = {
  title: "Leads",
  dashboard: "Leads Dashboard",
  description: "Overview of your sales pipeline and lead management performance",
  addLead: "Add Lead",
  createLead: "Create New Lead",
  createDescription: "Add a new potential customer to your leads list",
  editLead: "Edit Lead",
  updateLead: "Update Lead",
  deleteLead: "Delete Lead",
  leadDetails: "Lead Details",
  leadInformation: "Lead Information",
  leadName: "Lead Name",
  leadNameAr: "Lead Name (Arabic)",
  nameArabic: "Name (Arabic)",
  nameArabicPlaceholder: "Lead name in Arabic",
  namePlaceholder: "Lead name",
  companyPlaceholder: "Company name",
  emailPlaceholder: "<EMAIL>",
  mobilePlaceholder: "+968 9123 4567",
  phonePlaceholder: "+968 2456 7890",
  addressPlaceholder: "Street address",
  cityPlaceholder: "City",
  notesPlaceholder: "Additional notes about this lead...",
  selectSource: "Select Source",
  selectStatus: "Select Status",
  createSuccess: "Lead created successfully",
  createError: "Failed to create lead",
  manageDescription: "Manage potential customers and sales opportunities",
  searchPlaceholder: "Search leads, company, email, or phone...",
  noLeadsFound: "No leads found",
  createFirstLead: "Create your first lead!",
  leadSource: "Lead Source",
  estimatedValue: "Estimated Value",
  convertToCustomer: "Convert to Customer",
  leadScore: "Lead Score",
  followUp: "Follow Up",
  addFollowUp: "Add Follow Up",
  activities: "Activities",
  addActivity: "Add Activity",
  convertLead: "Convert Lead",
  leadConversion: "Lead Conversion",
  topLeads: "Top Leads",
  recentLeads: "Recent Leads",
  upcomingFollowups: "Upcoming Follow-ups",
  leadPerformance: "Lead Performance",
  conversionRate: "Conversion Rate",
  averageValue: "Average Value",
  totalLeads: "Total Leads",
  qualifiedLeads: "Qualified Leads",
  newLeads: "New Leads",
  convertedLeads: "Converted Leads",
  lostLeads: "Lost Leads",
  activeLeads: "Active Leads",
  
  // Table headers and common fields
  created: "Created",
  assignedTo: "Assigned To",
  contactInfo: "Contact Info",
  source: "Source",
  
  sources: {
    website: "Website",
    socialMedia: "Social Media",
    referral: "Referral",
    coldCall: "Cold Call",
    email: "Email Campaign",
    tradeShow: "Trade Show",
    advertisement: "Advertisement",
    other: "Other"
  },
  statuses: {
    new: "New",
    contacted: "Contacted",
    qualified: "Qualified",
    proposal: "Proposal Sent",
    negotiation: "In Negotiation", 
    won: "Won",
    lost: "Lost",
    converted: "Converted"
  },
  industries: {
    trading: "Trading",
    consulting: "Consulting",
    realEstate: "Real Estate",
    services: "Services",
    manufacturing: "Manufacturing",
    development: "Development",
    technology: "Technology",
    healthcare: "Healthcare",
    education: "Education",
    hospitality: "Hospitality"
  },
  activityTypes: {
    call: "Phone Call",
    email: "Email",
    meeting: "Meeting",
    proposal: "Send Proposal",
    visit: "Site Visit",
    quote: "Send Quote",
    note: "Note"
  },
  followUpTypes: {
    call: "Phone Call",
    email: "Email", 
    meeting: "Meeting",
    proposal: "Send Proposal",
    visit: "Site Visit",
    quote: "Send Quote"
  },
  dashboardTitle: "Leads Dashboard",
  dashboardOverview: "Overview of your sales pipeline and lead management performance",
  viewAllLeads: "View All Leads",
  pipelineValue: "Pipeline Value",
  fromLastMonth: "from last month",
  monthlyPerformance: "Monthly Performance",
  leadStatusDistribution: "Lead Status Distribution",
  lead: "Lead",
  status: "Status",
  value: "Value",
  score: "Score",
  actions: "Actions",
  conversions: "Conversions",
  leads: "Leads",
  upcomingFollowUps: "Upcoming Follow-ups",
  leadSourcePerformance: "Lead Source Performance"
} as const; 