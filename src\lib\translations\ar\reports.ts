export const reports = {
  // Page title and description
  title: "التقارير والتحليلات",
  description: "تحليلات وتقارير الأعمال الشاملة",
  
  // Date range options
  dateRange: "الفترة الزمنية",
  selectPeriod: "اختر الفترة",
  last7days: "آخر 7 أيام",
  last30days: "آخر 30 يوم",
  last3months: "آخر 3 أشهر",
  last6months: "آخر 6 أشهر",
  lastyear: "السنة الماضية",
  
  // Actions
  export: "تصدير",
  download: "تحميل",
  
  // Summary cards
  totalRevenue: "إجمالي الإيرادات",
  totalOrders: "إجمالي الطلبات",
  activeCustomers: "العملاء النشطين",
  avgOrderValue: "متوسط قيمة الطلب",
  profitMargin: "هامش الربح",
  totalInvoicesGenerated: "إجمالي الفواتير المنشأة",
  customersInPeriod: "العملاء في الفترة",
  perInvoiceAverage: "متوسط كل فاتورة",
  
  // Tab labels
  salesAnalytics: "تحليلات المبيعات",
  productPerformance: "أداء المنتجات",
  customerAnalytics: "تحليلات العملاء",
  taskPerformance: "أداء المهام",
  
  // Sales tab
  salesOverview: "نظرة عامة على المبيعات",
  monthlySalesPerformance: "أداء المبيعات الشهرية",
  invoiceStatistics: "إحصائيات الفواتير",
  invoiceStatusBreakdown: "تفصيل حالات الفواتير",
  paidInvoices: "الفواتير المدفوعة",
  pendingInvoices: "الفواتير المعلقة",
  overdueInvoices: "الفواتير المتأخرة",
  
  // Products tab
  topSellingProducts: "أفضل المنتجات/الخدمات مبيعاً",
  bestPerformingItems: "أفضل العناصر أداءً في هذه الفترة",
  unitsSold: "وحدة مباعة",
  revenue: "الإيرادات",
  
  // Customers tab
  topCustomers: "أفضل العملاء",
  highestValueCustomers: "العملاء الأعلى قيمة في هذه الفترة",
  orders: "طلبات",
  totalSpent: "إجمالي المصروف",
  
  // Tasks tab
  taskCompletionRate: "معدل إنجاز المهام",
  employeePerformanceMetrics: "مقاييس أداء الموظفين",
  taskStatusOverview: "نظرة عامة على حالة المهام",
  currentTaskDistribution: "توزيع المهام الحالية",
  completedTasks: "المهام المكتملة",
  inProgress: "قيد التنفيذ",
  pending: "معلقة",
  overdue: "متأخرة",
  
  // Loading and error states
  loadingReportsData: "جاري تحميل بيانات التقارير...",
  noReportsDataAvailable: "لا توجد بيانات تقارير متاحة",
  errorLoadingData: "خطأ في تحميل بيانات التقارير"
}; 