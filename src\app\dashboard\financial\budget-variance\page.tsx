"use client"

import { useState, useEffect } from 'react'
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, TrendingUp, TrendingDown, DollarSign, BarChart3, PieChart, Download, Printer, Share2, Calendar } from "lucide-react"
import { useI18n } from '@/lib/i18n'
import { AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Pie<PERSON>hart as RechartsPieChart, Cell } from 'recharts'

interface BudgetVarianceItem {
  category: string
  budget: number
  actual: number
  variance: number
  variancePercent: number
  status: 'favorable' | 'unfavorable'
}

interface MonthlyTrendItem {
  month: string
  budgetRevenue: number
  actualRevenue: number
  budgetExpenses: number
  actualExpenses: number
  budgetNet: number
  actualNet: number
}

interface BudgetVarianceData {
  summary: {
    totalBudget: number
    totalActual: number
    totalVariance: number
    variancePercentage: number
    totalCategories: number
    favorableVariances: number
    unfavorableVariances: number
    averageVariancePercent: number
    budgetUtilization: number
  }
  budgetVarianceData: BudgetVarianceItem[]
  monthlyTrend: MonthlyTrendItem[]
  period: string
  dateRange: {
    start: string
    end: string
  }
}

export default function BudgetVariance() {
  const router = useRouter()
  const { t, formatCurrency } = useI18n()
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<BudgetVarianceData | null>(null)
  const [period, setPeriod] = useState('6months')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBudgetVarianceData()
  }, [period, categoryFilter])

  const fetchBudgetVarianceData = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/financial-reports/budget-variance?period=${period}&category=${categoryFilter}`)

      if (!response.ok) {
        throw new Error('Failed to fetch budget variance data')
      }

      const result = await response.json()
      setData(result)
    } catch (error) {
      console.error('Error fetching budget variance data:', error)
      setError('Failed to load budget variance data')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'favorable':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'unfavorable':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getVarianceIcon = (variance: number) => {
    if (variance > 0) {
      return <TrendingUp className="h-4 w-4 text-green-500" />
    } else if (variance < 0) {
      return <TrendingDown className="h-4 w-4 text-red-500" />
    }
    return <DollarSign className="h-4 w-4 text-gray-500" />
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {t('finance.financial.budgetVariance.title')}
          </h2>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">{t('finance.financial.loading')}</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {t('finance.financial.budgetVariance.title')}
          </h2>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error || t('finance.financial.noData')}</p>
            <Button onClick={fetchBudgetVarianceData}>
              {t('common.retry') || 'Retry'}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">
              {t('finance.financial.budgetVariance.title')}
            </h2>
            <p className="text-muted-foreground">
              {t('finance.financial.budgetVariance.subtitle')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            {t('finance.financial.budgetVariance.actions.exportReport')}
          </Button>
          <Button variant="outline" size="sm">
            <Printer className="mr-2 h-4 w-4" />
            {t('finance.financial.budgetVariance.actions.printReport')}
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="mr-2 h-4 w-4" />
            {t('finance.financial.budgetVariance.actions.shareReport')}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4 bg-muted/50 p-4 rounded-lg">
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">{t('finance.financial.budgetVariance.filters.period')}:</span>
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1month">{t('finance.financial.period.lastMonth')}</SelectItem>
              <SelectItem value="3months">{t('finance.financial.period.last3Months')}</SelectItem>
              <SelectItem value="6months">{t('finance.financial.period.last6Months')}</SelectItem>
              <SelectItem value="1year">{t('finance.financial.period.lastYear')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{t('finance.financial.budgetVariance.filters.category')}:</span>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('finance.financial.reports.categories.allReports')}</SelectItem>
              <SelectItem value="revenue">{t('finance.financial.budgetVariance.categories.revenue')}</SelectItem>
              <SelectItem value="expenses">{t('finance.financial.budgetVariance.categories.expenses')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.budgetVariance.overview.totalBudget')}
            </CardTitle>
            <DollarSign className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(data.summary.totalBudget)}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('finance.financial.budgetVariance.summary.budgetUtilization')}: {data.summary.budgetUtilization.toFixed(1)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.budgetVariance.overview.totalActual')}
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(data.summary.totalActual)}
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getVarianceIcon(data.summary.totalVariance)}
              <span className="ml-1">
                {data.summary.totalVariance >= 0 ? '+' : ''}{formatCurrency(data.summary.totalVariance)}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.budgetVariance.overview.totalVariance')}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${data.summary.totalVariance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {data.summary.variancePercentage >= 0 ? '+' : ''}{data.summary.variancePercentage.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              {t('finance.financial.budgetVariance.summary.averageVariance')}: {data.summary.averageVariancePercent.toFixed(1)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.budgetVariance.summary.totalCategories')}
            </CardTitle>
            <PieChart className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {data.summary.totalCategories}
            </div>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span className="text-green-600">
                {t('finance.financial.budgetVariance.status.favorable')}: {data.summary.favorableVariances}
              </span>
              <span className="text-red-600">
                {t('finance.financial.budgetVariance.status.unfavorable')}: {data.summary.unfavorableVariances}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Budget vs Actual Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-blue-500" />
              <span>{t('finance.financial.budgetVariance.charts.monthlyTrend')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={data.monthlyTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    formatCurrency(value as number),
                    name === 'budgetRevenue' ? t('finance.financial.budgetVariance.table.budget') + ' ' + t('finance.financial.budgetVariance.categories.revenue') :
                    name === 'actualRevenue' ? t('finance.financial.budgetVariance.table.actual') + ' ' + t('finance.financial.budgetVariance.categories.revenue') :
                    name === 'budgetExpenses' ? t('finance.financial.budgetVariance.table.budget') + ' ' + t('finance.financial.budgetVariance.categories.expenses') :
                    name === 'actualExpenses' ? t('finance.financial.budgetVariance.table.actual') + ' ' + t('finance.financial.budgetVariance.categories.expenses') :
                    name
                  ]}
                />
                <Area
                  type="monotone"
                  dataKey="budgetRevenue"
                  stackId="1"
                  stroke="#0088FE"
                  fill="#0088FE"
                  fillOpacity={0.6}
                />
                <Area
                  type="monotone"
                  dataKey="actualRevenue"
                  stackId="2"
                  stroke="#00C49F"
                  fill="#00C49F"
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Variance by Category */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChart className="h-5 w-5 text-purple-500" />
              <span>{t('finance.financial.budgetVariance.charts.varianceByCategory')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={data.budgetVarianceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="category"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    formatCurrency(value as number),
                    name === 'budget' ? t('finance.financial.budgetVariance.table.budget') :
                    name === 'actual' ? t('finance.financial.budgetVariance.table.actual') :
                    name === 'variance' ? t('finance.financial.budgetVariance.table.variance') :
                    name
                  ]}
                />
                <Bar dataKey="budget" fill="#0088FE" />
                <Bar dataKey="actual" fill="#00C49F" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Budget Variance Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-blue-500" />
            <span>{t('finance.financial.budgetVariance.charts.budgetVsActual')}</span>
          </CardTitle>
          <CardDescription>
            {t('finance.financial.budgetVariance.charts.varianceAnalysis')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('finance.financial.budgetVariance.table.category')}</TableHead>
                <TableHead className="text-right">{t('finance.financial.budgetVariance.table.budget')}</TableHead>
                <TableHead className="text-right">{t('finance.financial.budgetVariance.table.actual')}</TableHead>
                <TableHead className="text-right">{t('finance.financial.budgetVariance.table.variance')}</TableHead>
                <TableHead className="text-right">{t('finance.financial.budgetVariance.table.variancePercent')}</TableHead>
                <TableHead className="text-center">{t('finance.financial.budgetVariance.table.status')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.budgetVarianceData.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    {t(`finance.financial.budgetVariance.categories.${item.category.toLowerCase()}`) || item.category}
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    {formatCurrency(item.budget)}
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    {formatCurrency(item.actual)}
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    <div className="flex items-center justify-end space-x-1">
                      {getVarianceIcon(item.variance)}
                      <span className={item.variance >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {item.variance >= 0 ? '+' : ''}{formatCurrency(item.variance)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    <span className={item.variancePercent >= 0 ? 'text-green-600' : 'text-red-600'}>
                      {item.variancePercent >= 0 ? '+' : ''}{item.variancePercent.toFixed(1)}%
                    </span>
                  </TableCell>
                  <TableCell className="text-center">
                    <Badge className={getStatusColor(item.status)}>
                      {t(`finance.financial.budgetVariance.status.${item.status}`)}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}