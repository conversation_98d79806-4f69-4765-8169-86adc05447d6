export const quotations = {
  // Page titles and descriptions
  title: "عروض الأسعار",
  dashboard: "لوحة عروض الأسعار",
  dashboardDescription: "نظرة عامة على أداء عروض الأسعار والمؤشرات",
  createQuotation: "إنشاء عرض سعر",
  editQuotation: "تعديل عرض السعر",
  quotationDetails: "تفاصيل عرض السعر",
  
  // Search and filters
  searchQuotations: "البحث في عروض الأسعار...",
  
  // Status
  pending: "في الانتظار",
  approved: "موافق عليه",
  rejected: "مرفوض",
  expired: "منتهي الصلاحية",
  
  // Table headers
  quotationNumber: "رقم العرض",
  quotationDate: "تاريخ عرض السعر",
  validUntil: "صالح حتى",
  customer: "العميل",
  total: "الإجمالي",
  status: "الحالة",
  
  // Actions
  printQuotation: "طباعة عرض السعر",
  emailQuotation: "إرسال عرض السعر بالبريد",
  downloadQuotation: "تحميل عرض السعر",
  sendToCustomer: "إرسال للعميل",
  convertToInvoice: "تحويل إلى فاتورة",
  deleteQuotation: "حذف عرض السعر",
  
  // Form fields
  quotationInformation: "معلومات عرض السعر",
  customerRequired: "العميل مطلوب",
  quotationDateRequired: "تاريخ عرض السعر مطلوب",
  validUntilRequired: "تاريخ انتهاء الصلاحية مطلوب",
  
  // Messages
  quotationCreatedSuccessfully: "تم إنشاء عرض السعر بنجاح",
  quotationUpdatedSuccessfully: "تم تحديث عرض السعر بنجاح",
  quotationDeletedSuccessfully: "تم حذف عرض السعر بنجاح",
  quotationSentSuccessfully: "تم إرسال عرض السعر بنجاح",
  quotationConvertedSuccessfully: "تم تحويل عرض السعر إلى فاتورة بنجاح",
  
  // Error messages
  failedToCreateQuotation: "فشل في إنشاء عرض السعر",
  failedToUpdateQuotation: "فشل في تحديث عرض السعر",
  failedToDeleteQuotation: "فشل في حذف عرض السعر",
  failedToSendQuotation: "فشل في إرسال عرض السعر",
  failedToConvertQuotation: "فشل في تحويل عرض السعر",
  
  // Empty states
  noQuotationsFound: "لم يتم العثور على عروض أسعار",
  createFirstQuotation: "أنشئ عرض السعر الأول",
  
  // Loading states
  loadingQuotations: "جاري تحميل عروض الأسعار...",
  savingQuotation: "جاري حفظ عرض السعر...",
  sendingQuotation: "جاري إرسال عرض السعر...",
  convertingQuotation: "جاري تحويل عرض السعر...",
  
  // New keys from the code block
  viewAllQuotations: "عرض جميع العروض",
  totalQuotations: "إجمالي العروض",
  totalValue: "إجمالي القيمة",
  fromLastMonth: "عن الشهر الماضي",
  conversionRate: "معدل التحويل",
  avgQuotationValue: "متوسط قيمة العرض",
  noChange: "لا يوجد تغيير",
  monthlyTrend: "اتجاه عروض الأسعار الشهري",
  monthlyTrendDescription: "عدد العروض والقيمة الإجمالية خلال آخر 5 أشهر",
  quotations: "العروض",
  value: "القيمة",
  statusDistribution: "توزيع حالات العروض",
  statusDistributionDescription: "التوزيع الحالي لحالات عروض الأسعار",
  statusLabels: {
    PENDING: "قيد الانتظار",
    APPROVED: "معتمد",
    REJECTED: "مرفوض",
    EXPIRED: "منتهي الصلاحية",
    CONVERTED: "تم التحويل"
  },
  recentQuotations: "العروض الأخيرة",
  recentQuotationsDescription: "أحدث العروض وحالتها الحالية",
  viewAll: "عرض الكل",
  date: "التاريخ",
  amount: "المبلغ",
  actions: "الإجراءات",

  list: {
    description: "إدارة عروض الأسعار الخاصة بك وتحويلها إلى فواتير",
    filterByStatus: "تصفية حسب الحالة",
    allStatuses: "جميع الحالات",
    loading: "جاري تحميل عروض الأسعار...",
    emptySearch: "لم يتم العثور على عروض أسعار مطابقة لبحثك.",
    empty: "لم يتم العثور على عروض أسعار. قم بإنشاء عرض السعر الأول!",
    confirmDelete: "هل أنت متأكد أنك تريد حذف عرض السعر رقم {number}؟",
    deleteSuccess: "تم حذف عرض السعر رقم {number} بنجاح!",
    deleteFailed: "فشل حذف عرض السعر: {error}",
    deleteFailedGeneric: "فشل حذف عرض السعر. يرجى المحاولة مرة أخرى.",
    openMenu: "افتح القائمة"
  },

  details: {
    title: "عرض السعر رقم {number}",
    description: "عرض وإدارة تفاصيل عرض السعر",
    loading: "جاري التحميل...",
    notFound: "عرض السعر غير موجود",
    backButton: "رجوع",
    actions: "الإجراءات",
    duplicate: "تكرار",
    printPDF: "طباعة/PDF",
    download: "تحميل",
    delete: "حذف",
    confirmDelete: "هل أنت متأكد أنك تريد حذف عرض السعر هذا؟",
    
    infoCard: {
      title: "معلومات عرض السعر",
      customer: "العميل",
      linkedTask: "المهمة المرتبطة",
      quotationDate: "تاريخ عرض السعر",
      validUntil: "صالح حتى",
      status: "الحالة",
      expired: "منتهي الصلاحية"
    },

    itemsCard: {
      title: "بنود عرض السعر",
      headers: {
        description: "الوصف",
        quantity: "الكمية",
        unitPrice: "سعر الوحدة",
        total: "الإجمالي"
      }
    },

    notesCard: {
      title: "ملاحظات"
    },

    summaryCard: {
      title: "ملخص عرض السعر",
      subtotal: "المجموع الفرعي:",
      discount: "الخصم:",
      vat: "ضريبة القيمة المضافة:",
      total: "الإجمالي:"
    },

    quickActionsCard: {
      title: "إجراءات سريعة"
    }
  },

  // Create/Edit Page
  create: {
    title: "إنشاء عرض سعر",
    description: "إنشاء عرض سعر جديد لعميلك",
    backButton: "رجوع",
    saveDraft: "حفظ كمسودة",
    createFinal: "إنشاء عرض سعر نهائي",

    customerSection: {
      title: "معلومات العميل",
      label: "العميل *",
      placeholder: "اختر العميل...",
      searchPlaceholder: "ابحث عن العملاء بالاسم أو رقم الجوال...",
      notFound: "لم يتم العثور على عميل.",
      addNew: "إضافة عميل جديد",
    },

    infoSection: {
      title: "معلومات عرض السعر",
      validUntil: "صالح حتى",
      status: "حالة عرض السعر",
      notes: "ملاحظات",
      notesPlaceholder: "شكراً لاهتمامكم بخدماتنا!",
      statusOptions: {
        draft: "مسودة",
        final: "نهائي",
        sent: "تم الإرسال"
      }
    },

    itemsSection: {
      title: "بنود عرض السعر",
      addItem: "إضافة بند",
      headers: {
        description: "الوصف",
        product: "المنتج/الخدمة",
        quantity: "الكمية",
        unitPrice: "سعر الوحدة (ريال عماني)",
        total: "الإجمالي (ريال عماني)",
        action: "إجراء"
      },
      descriptionPlaceholder: "وصف البند",
      productPlaceholder: "اختر المنتج",
      productSearchPlaceholder: "ابحث عن المنتجات...",
      productNotFound: "لم يتم العثور على منتج.",
      productsGroup: "المنتجات",
    },

    settingsSection: {
      title: "إعدادات {label} والخصم",
      rateLabel: "نسبة {label} (%)",
      defaultRate: "النسبة الافتراضية من الإعدادات: {rate}%",
      discount: "الخصم",
      amount: "مبلغ",
      percentage: "نسبة مئوية",
      percentageHint: "أدخل نسبة مئوية (0-100%)",
      amountHint: "أدخل المبلغ بالريال العماني"
    },

    summarySection: {
      title: "ملخص عرض السعر",
      subtotal: "المجموع الفرعي:",
      discount: "الخصم ({details}):",
      tax: "{label} ({rate}%):",
      total: "الإجمالي:",
      items: "البنود",
      status: "الحالة"
    },

    newCustomerDialog: {
      title: "إضافة عميل جديد",
      nameLabel: "اسم العميل *",
      namePlaceholder: "أدخل اسم العميل",
      mobileLabel: "رقم الجوال *",
      mobilePlaceholder: "XXXX XXXX 968+",
      emailLabel: "البريد الإلكتروني (اختياري)",
      emailPlaceholder: "<EMAIL>",
      customerExists: "العميل موجود: {name}",
      customerExistsHint: "يمكنك اختيار هذا العميل من القائمة المنسدلة بدلاً من ذلك.",
      cancel: "إلغاء",
      add: "إضافة عميل"
    },

    alerts: {
      nameAndMobileRequired: "الرجاء إدخال الاسم ورقم الجوال",
      customerExists: "يوجد عميل بنفس رقم الجوال هذا بالفعل",
      customerCreated: "تم إنشاء العميل بنجاح!",
      customerCreateFailed: "فشل في إنشاء العميل. الرجاء المحاولة مرة أخرى.",
      selectCustomer: "الرجاء اختيار عميل",
      addOneItem: "الرجاء إضافة بند واحد على الأقل لعرض السعر",
      checkItems: "يرجى التحقق من كميات وأسعار البنود. يجب أن تكون لجميع البنود كميات صالحة وأسعار غير سلبية.",
      savedAsDraft: "تم حفظ عرض السعر رقم {number} كمسودة بنجاح!",
      createdSuccessfully: "تم إنشاء عرض السعر رقم {number} بنجاح!",
      saveFailed: "فشل في حفظ عرض السعر. الرجاء المحاولة مرة أخرى."
    }
  },

  // Add these new keys for the Edit page
  edit: {
    title: 'تعديل عرض السعر',
    description: 'تحديث تفاصيل عرض السعر.',
    saveButton: 'حفظ التغييرات',
    alerts: {
      updateSuccess: 'تم تحديث عرض السعر {number} بنجاح.',
      updateFailed: 'فشل تحديث عرض السعر.',
      notFound: 'عرض السعر غير موجود.',
      loadFailed: 'فشل تحميل البيانات المطلوبة للتعديل.',
    },
  },
};
