"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useI18n } from "@/lib/i18n"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Plus,
  Search,
  Edit,
  FileText,
  Download,
  Eye,
  DollarSign,
  Clock,
  CheckCircle,
  Filter,
  Printer,
  Mail,
  CreditCard,
  MoreHorizontal,
  Trash2
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"

interface Invoice {
  id: string
  number: string
  date: string
  dueDate: string
  customer: string
  status: string
  subtotal: number
  taxAmount: number
  total: number
  amountPaid?: number
  source?: string
  items?: any[]
  createdAt: string
  updatedAt: string
}

const statusColors: { [key: string]: string } = {
  PAID: "bg-green-100 text-green-800",
  UNPAID: "bg-red-100 text-red-800",
  PARTIAL: "bg-yellow-100 text-yellow-800",
  OVERDUE: "bg-purple-100 text-purple-800",
}

export default function InvoicesPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [dateFilter, setDateFilter] = useState("all")
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch invoices
  useEffect(() => {
    fetchInvoices()
  }, [])

  const fetchInvoices = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/invoices?limit=1000') // Get all invoices
      if (response.ok) {
        const data = await response.json()
        // Map database invoices to the expected format
        const mappedInvoices = (data.invoices || []).map((invoice: any) => ({
          id: invoice.id,
          number: invoice.number,
          date: invoice.date,
          dueDate: invoice.dueDate,
          customer: invoice.customer?.name || 'Unknown Customer',
          status: invoice.status,
          subtotal: Number(invoice.subtotal),
          taxAmount: Number(invoice.taxAmount),
          total: Number(invoice.total),
          amountPaid: invoice.payments?.reduce((sum: number, payment: any) => sum + Number(payment.amount), 0) || 0,
          source: 'database',
          items: invoice.items || [],
          createdAt: invoice.createdAt,
          updatedAt: invoice.updatedAt
        }))
        setInvoices(mappedInvoices)
      } else {
        console.error('Failed to fetch invoices')
        setInvoices([])
      }
    } catch (error) {
      console.error('Error fetching invoices:', error)
      setInvoices([])
    } finally {
      setLoading(false)
    }
  }

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customer.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || invoice.status === statusFilter

    const matchesDate = dateFilter === "all" || (() => {
      const invoiceDate = new Date(invoice.date)
      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

      switch (dateFilter) {
        case "today":
          return invoiceDate.toDateString() === now.toDateString()
        case "week":
          return invoiceDate >= sevenDaysAgo
        case "month":
          return invoiceDate >= thirtyDaysAgo
        default:
          return true
      }
    })()

    return matchesSearch && matchesStatus && matchesDate
  })

  // Calculate statistics
  const stats = {
    total: invoices.length,
    paid: invoices.filter(inv => inv.status === 'PAID').length,
    unpaid: invoices.filter(inv => inv.status === 'UNPAID').length,
    overdue: invoices.filter(inv => inv.status === 'OVERDUE').length,
    totalAmount: invoices.reduce((sum, inv) => sum + inv.total, 0),
    paidAmount: invoices.reduce((sum, inv) => sum + (inv.amountPaid || 0), 0),
    pendingAmount: invoices.reduce((sum, inv) => sum + (inv.total - (inv.amountPaid || 0)), 0),
  }

  const handleViewInvoice = (invoice: Invoice) => {
    router.push(`/dashboard/invoices/${invoice.id}`)
  }

  const handleEditInvoice = (invoice: Invoice) => {
    router.push(`/dashboard/invoices/${invoice.id}/edit`)
  }

  const handleRecordPayment = (invoice: Invoice) => {
    router.push(`/dashboard/invoices/${invoice.id}/payment`)
  }

  const handlePrintInvoice = (invoice: Invoice) => {
    // Open invoice in print view
    window.open(`/dashboard/invoices/print/${invoice.id}`, '_blank')
  }

  const handleEmailInvoice = (invoice: Invoice) => {
    // TODO: Implement email functionality
    console.log('Emailing invoice:', invoice.number)
    alert(`Email functionality will be implemented soon for invoice ${invoice.number}`)
  }

  const handleDownloadInvoice = (invoice: Invoice) => {
    // TODO: Implement PDF download functionality
    console.log('Downloading invoice:', invoice.number)
    alert(`PDF download functionality will be implemented soon for invoice ${invoice.number}`)
  }

  const handleDeleteInvoice = async (invoice: Invoice) => {
    if (confirm(`Are you sure you want to delete invoice ${invoice.number}?`)) {
      try {
        const response = await fetch(`/api/invoices/${invoice.id}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          setInvoices(prev => prev.filter(inv => inv.id !== invoice.id))
          alert(`Invoice ${invoice.number} deleted successfully!`)
        } else {
          const error = await response.json()
          alert(`Failed to delete invoice: ${error.error}`)
        }
      } catch (error) {
        console.error('Error deleting invoice:', error)
        alert('Failed to delete invoice')
      }
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('invoices.title')}</h2>
          <p className="text-muted-foreground">
            {t('invoices.manageYourInvoicesAndTrackPayments')}
          </p>
        </div>
        <Button onClick={() => router.push('/dashboard/invoices/create')}>
          <Plus className="mr-2 h-4 w-4" />
          {t('invoices.createInvoice')}
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('invoices.totalInvoices')}</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.paid} {t('invoices.paid')}, {stats.unpaid} {t('common.unpaid')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('invoices.totalAmount')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalAmount)}</div>
            <p className="text-xs text-muted-foreground">
              {t('invoices.acrossAllInvoices')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('invoices.amountPaid')}</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.paidAmount)}</div>
            <p className="text-xs text-muted-foreground">
              {((stats.paidAmount / stats.totalAmount) * 100).toFixed(1)}% {t('common.total')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('invoices.pendingAmount')}</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{formatCurrency(stats.pendingAmount)}</div>
            <p className="text-xs text-muted-foreground">
              {stats.overdue} {t('invoices.overdueInvoices')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('invoices.searchInvoices')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={t('common.status')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('invoices.allStatus')}</SelectItem>
              <SelectItem value="PAID">{t('invoices.paid')}</SelectItem>
              <SelectItem value="UNPAID">{t('common.unpaid')}</SelectItem>
              <SelectItem value="PARTIAL">{t('common.partial')}</SelectItem>
              <SelectItem value="OVERDUE">{t('invoices.overdue')}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder={t('common.date')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('invoices.allTime')}</SelectItem>
              <SelectItem value="today">{t('common.today')}</SelectItem>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            {t('invoices.export')}
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            {t('invoices.moreFilters')}
          </Button>
        </div>
      </div>

      {/* Invoices Table */}
      <div className="table-container">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('invoices.invoiceNumber')}</TableHead>
              <TableHead>{t('invoices.customer')}</TableHead>
              <TableHead>{t('invoices.source')}</TableHead>
              <TableHead>{t('common.date')}</TableHead>
              <TableHead>{t('common.status')}</TableHead>
              <TableHead>{t('invoices.amount')}</TableHead>
              <TableHead>{t('invoices.paid')}</TableHead>
              <TableHead>{t('invoices.balance')}</TableHead>
              <TableHead className="text-right">{t('invoices.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  Loading invoices...
                </TableCell>
              </TableRow>
            ) : filteredInvoices.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  No invoices found
                </TableCell>
              </TableRow>
            ) : (
              filteredInvoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell className="font-medium">
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium"
                      onClick={() => handleViewInvoice(invoice)}
                    >
                      {invoice.number}
                    </Button>
                  </TableCell>
                  <TableCell>{invoice.customer}</TableCell>
                  <TableCell>
                    <Badge variant={invoice.source === 'pos' ? 'default' : 'secondary'}>
                      {invoice.source === 'pos' ? 'POS' : 'Manual'}
                    </Badge>
                  </TableCell>
                  <TableCell>{new Date(invoice.date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Badge className={statusColors[invoice.status]}>
                      {invoice.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-medium">{formatCurrency(invoice.total)}</TableCell>
                  <TableCell className="text-green-600">{formatCurrency(invoice.amountPaid || 0)}</TableCell>
                  <TableCell className="text-orange-600">
                    {formatCurrency(invoice.total - (invoice.amountPaid || 0))}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewInvoice(invoice)}>
                          <Eye className="mr-2 h-4 w-4" />
                          {t('invoices.viewDetails')}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditInvoice(invoice)}>
                          <Edit className="mr-2 h-4 w-4" />
                          {t('invoices.editInvoice')}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handlePrintInvoice(invoice)}>
                          <Printer className="mr-2 h-4 w-4" />
                          {t('invoices.printPdf')}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEmailInvoice(invoice)}>
                          <Mail className="mr-2 h-4 w-4" />
                          {t('invoices.emailInvoice')}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDownloadInvoice(invoice)}>
                          <Download className="mr-2 h-4 w-4" />
                          {t('invoices.download')}
                        </DropdownMenuItem>
                        {invoice.status !== 'PAID' && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleRecordPayment(invoice)}
                              className="text-green-600 focus:text-green-600"
                            >
                              <CreditCard className="mr-2 h-4 w-4" />
                              {t('invoices.recordPayment')}
                            </DropdownMenuItem>
                          </>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteInvoice(invoice)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          {t('invoices.delete')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Invoice Details Dialog */}
      {selectedInvoice && (
        <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <div className="flex items-center justify-between">
                <div>
                  <DialogTitle className="text-xl">{t('invoices.invoice')} {selectedInvoice.number}</DialogTitle>
                  <DialogDescription>
                    {t('invoices.completeInvoiceDetails')}
                  </DialogDescription>
                </div>
                <Badge className={statusColors[selectedInvoice.status]}>
                  {selectedInvoice.status}
                </Badge>
              </div>
            </DialogHeader>

            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('invoices.customer')}</Label>
                    <p className="text-lg font-medium">{selectedInvoice.customer}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('invoices.invoiceDate')}</Label>
                    <p className="text-base">{new Date(selectedInvoice.date).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('invoices.dueDate')}</Label>
                    <p className="text-base">{new Date(selectedInvoice.dueDate).toLocaleDateString()}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('invoices.invoiceTotal')}</Label>
                    <p className="text-2xl font-bold">{formatCurrency(selectedInvoice.total)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('invoices.amountPaid')}</Label>
                    <p className="text-lg font-medium text-green-600">{formatCurrency(selectedInvoice.amountPaid || 0)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('invoices.balanceDue')}</Label>
                    <p className="text-lg font-medium text-orange-600">{formatCurrency(selectedInvoice.total - (selectedInvoice.amountPaid || 0))}</p>
                  </div>
                </div>
              </div>

              {/* Invoice Items */}
              <div className="space-y-3">
                <Label className="text-base font-medium">{t('invoices.invoiceItems')}</Label>
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t('invoices.itemDescription')}</TableHead>
                        <TableHead>{t('invoices.quantity')}</TableHead>
                        <TableHead>{t('invoices.unitPrice')}</TableHead>
                        <TableHead className="text-right">{t('invoices.total')}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>Business Card Design & Printing</TableCell>
                        <TableCell>500 pcs</TableCell>
                        <TableCell>{formatCurrency(2.00)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(1000.00)}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Logo Design Services</TableCell>
                        <TableCell>1 design</TableCell>
                        <TableCell>{formatCurrency(150.00)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(150.00)}</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* Invoice Summary */}
              <div className="bg-muted/50 rounded-lg p-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>{t('invoices.subtotal')}:</span>
                    <span>{formatCurrency(selectedInvoice.subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{t('invoices.tax')} (5%):</span>
                    <span>{formatCurrency(selectedInvoice.taxAmount)}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-medium text-lg">
                    <span>{t('invoices.total')}:</span>
                    <span>{formatCurrency(selectedInvoice.total)}</span>
                  </div>
                </div>
              </div>

              {/* Payment History */}
              <div className="space-y-3">
                <Label className="text-base font-medium">{t('invoices.paymentHistory')}</Label>
                <div className="border rounded-lg p-4">
                  {selectedInvoice.amountPaid && selectedInvoice.amountPaid > 0 ? (
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{t('invoices.paymentReceived')}</p>
                          <p className="text-sm text-muted-foreground">Bank Transfer • {new Date().toLocaleDateString()}</p>
                        </div>
                        <span className="font-medium text-green-600">{formatCurrency(selectedInvoice.amountPaid)}</span>
                      </div>
                    </div>
                  ) : (
                    <p className="text-muted-foreground text-center py-4">{t('invoices.noPaymentsRecorded')}</p>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2 pt-4 border-t">
                <Button onClick={() => console.log('Printing invoice:', selectedInvoice.number)}>
                  <Printer className="mr-2 h-4 w-4" />
                  {t('invoices.printPdf')}
                </Button>

                <Button variant="outline" onClick={() => console.log('Emailing invoice:', selectedInvoice.number)}>
                  <Mail className="mr-2 h-4 w-4" />
                  {t('invoices.emailInvoice')}
                </Button>

                <Button variant="outline">
                  <Edit className="mr-2 h-4 w-4" />
                  {t('invoices.editInvoice')}
                </Button>

                {(selectedInvoice.total - (selectedInvoice.amountPaid || 0)) > 0 && (
                  <Button onClick={() => {
                    setIsDetailsDialogOpen(false)
                    router.push(`/dashboard/invoices/${selectedInvoice.id}/payment`)
                  }}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    {t('invoices.recordPayment')}
                  </Button>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
