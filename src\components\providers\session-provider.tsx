"use client"

import { Session<PERSON>rovider as NextAuthSessionProvider } from "next-auth/react"
import { ReactNode } from "react"

interface SessionProviderProps {
  children: ReactNode
}

export function SessionProvider({ children }: SessionProviderProps) {
  return (
    <NextAuthSessionProvider
      // Refetch session every 2 minutes for better security
      refetchInterval={2 * 60}
      // Refetch session when window gains focus
      refetchOnWindowFocus={true}
      // Refetch when user comes back online
      refetchWhenOffline={false}
    >
      {children}
    </NextAuthSessionProvider>
  )
}
