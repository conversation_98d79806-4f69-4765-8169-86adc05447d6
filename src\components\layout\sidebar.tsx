"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useState, useEffect } from "react"
import { useI18n } from "@/lib/i18n"
import { useCompanySettings } from "@/hooks/use-company-settings"
import { PermissionGuard } from "@/components/auth/permission-guard"
import {
  LayoutDashboard,
  Users,
  Truck,
  Package,
  ClipboardList,
  FileText,
  FileBarChart,
  ShoppingCart,
  UserCheck,
  Shield,
  BarChart3,
  Settings,
  CreditCard,
  Target,
  Calculator,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  Grid3X3,
  Ruler,
  Plus,
  Archive,
  FolderOpen,
  Calendar
} from "lucide-react"

interface SidebarProps {
  isCollapsed?: boolean
  onToggleCollapse?: () => void
}

export function Sidebar({ isCollapsed = false, onToggleCollapse }: SidebarProps) {
  const pathname = usePathname()
  const { t, direction } = useI18n()
  const { logo, companyName, companyNameAr, loading } = useCompanySettings()
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]) // All submenus collapsed by default

  const toggleSubmenu = (menuName: string) => {
    setExpandedMenus(prev =>
      prev.includes(menuName)
        ? prev.filter(name => name !== menuName)
        : [...prev, menuName]
    )
  }

  // Generate navigation items with translations
  const navigation = [
    {
      name: t('navigation.dashboard'),
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      name: t('navigation.pos'),
      href: "/pos",
      icon: ShoppingCart,
      external: true,
      className: "bg-green-600 text-white hover:bg-green-700 md:hidden", // Only show on mobile
    },
    {
      name: t('navigation.leads'),
      href: "/dashboard/leads",
      icon: Target,
    },
    {
      name: t('navigation.projects'),
      href: "/dashboard/projects",
      icon: FolderOpen,
    },
    {
      name: t('navigation.calendar'),
      href: "/dashboard/calendar",
      icon: Calendar,
    },
    {
      name: t('navigation.customers'),
      href: "/dashboard/customers",
      icon: Users,
    },
    {
      name: t('navigation.suppliers'),
      href: "/dashboard/suppliers",
      icon: Truck,
    },
    {
      name: t('navigation.products'),
      href: "/dashboard/products",
      icon: Package,
      hasSubmenu: true,
      submenu: [
        {
          name: t('navigation.productManagement'),
          href: "/dashboard/products",
          icon: Package,
        },
        {
          name: t('navigation.createProduct'),
          href: "/dashboard/products/create",
          icon: Plus,
        },
        {
          name: t('navigation.stockManagement'),
          href: "/dashboard/products/stock",
          icon: Archive,
        },
        {
          name: t('navigation.categoryManagement'),
          href: "/dashboard/categories",
          icon: Grid3X3,
        },
        {
          name: t('navigation.unitManagement'),
          href: "/dashboard/units",
          icon: Ruler,
        },
      ],
    },
    {
      name: t('navigation.tasks'),
      href: "/dashboard/tasks",
      icon: ClipboardList,
    },
    {
      name: t('navigation.invoices'),
      href: "/dashboard/invoices",
      icon: FileText,
    },
    {
      name: t('navigation.quotations'),
      href: "/dashboard/quotations",
      icon: FileBarChart,
    },
    {
      name: t('navigation.purchases'),
      href: "/dashboard/purchases",
      icon: ShoppingCart,
    },
    {
      name: t('navigation.expenses'),
      href: "/dashboard/expenses",
      icon: CreditCard,
    },
    {
      name: t('navigation.financial'),
      href: "/dashboard/financial",
      icon: Calculator,
    },
    {
      name: t('navigation.employees'),
      href: "/dashboard/employees",
      icon: UserCheck,
    },
    {
      name: t('navigation.reports'),
      href: "/dashboard/reports",
      icon: BarChart3,
    },
    {
      name: t('navigation.settings'),
      href: "/dashboard/settings",
      icon: Settings,
    },
    {
      name: t('navigation.administration'),
      icon: Shield,
      hasSubmenu: true,
      submenu: [
        {
          name: t('navigation.userManagement'),
          href: "/dashboard/roles",
          icon: Shield,
        },
        {
          name: t('navigation.systemSettings'),
          href: "/dashboard/permissions",
          icon: Shield,
        },
      ],
    },
  ]

  return (
    <div className={`flex flex-col bg-white shadow-lg transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } ${direction === 'rtl' ? 'border-l' : 'border-r'}`} dir={direction}>
      <div className={`flex items-center h-16 px-4 ${logo ? 'bg-white' : 'theme-primary'} ${
        isCollapsed ? 'justify-center' : 'justify-center'
      }`}>
        {!isCollapsed && logo ? (
          <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
            <img
              src={logo}
              alt="Company Logo"
              className="h-10 w-auto max-w-[120px] object-contain"
            />
          </div>
        ) : !isCollapsed && !logo ? (
          <h1 className={`text-xl font-bold text-white ${direction === 'rtl' ? 'text-right' : 'text-left'}`}>
            {direction === 'rtl' ? companyNameAr : companyName}
          </h1>
        ) : isCollapsed && logo ? (
          <img
            src={logo}
            alt="Company Logo"
            className="h-8 w-8 object-contain"
          />
        ) : (
          <div className="w-8 h-8 bg-blue-600 rounded-md flex items-center justify-center">
            <span className="text-white font-bold text-sm">
              {(direction === 'rtl' ? companyNameAr : companyName)?.charAt(0) || 'C'}
            </span>
          </div>
        )}
      </div>

      <ScrollArea className={`flex-1 px-3 py-2 ${direction === 'rtl' ? 'rtl' : 'ltr'}`}>
        <nav className="space-y-2">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            const isSubmenuActive = item.submenu?.some(subItem => pathname === subItem.href)
            const isExpanded = expandedMenus.includes(item.name.toLowerCase()) && !isCollapsed

            if (item.hasSubmenu) {
              return (
                <div key={item.name} className="space-y-1">
                  <Button
                    variant="ghost"
                    onClick={() => !isCollapsed && toggleSubmenu(item.name.toLowerCase())}
                    className={cn(
                      "w-full h-10 px-3 py-2 transition-all duration-200 flex items-center",
                      (isActive || isSubmenuActive) && "theme-secondary",
                      !(isActive || isSubmenuActive) && "hover:bg-gray-100 text-gray-700",
                      isCollapsed 
                        ? "justify-center" 
                        : direction === 'rtl'
                        ? "justify-end text-right flex-row-reverse gap-0"
                        : "justify-start text-left gap-0"
                    )}
                    dir={direction}
                    title={isCollapsed ? item.name : undefined}
                  >
                    <item.icon className={cn(
                      "h-4 w-4 flex-shrink-0",
                      !isCollapsed && (direction === 'rtl' ? 'ml-3' : 'mr-3'),
                      (isActive || isSubmenuActive) ? 'text-current' : 'text-gray-500'
                    )} />
                    {!isCollapsed && (
                      <>
                        <span className={cn(
                          "flex-1 font-medium text-sm",
                          direction === 'rtl' ? 'text-right' : 'text-left',
                          (isActive || isSubmenuActive) ? 'text-current' : 'text-gray-700'
                        )}>
                          {item.name}
                        </span>
                        {isExpanded ? (
                          <ChevronDown className={cn(
                            "h-4 w-4 flex-shrink-0",
                            direction === 'rtl' ? 'mr-2' : 'ml-2',
                            (isActive || isSubmenuActive) ? 'text-current' : 'text-gray-500'
                          )} />
                        ) : (
                          direction === 'rtl' ? (
                            <ChevronLeft className={cn(
                              "h-4 w-4 flex-shrink-0 mr-2",
                              (isActive || isSubmenuActive) ? 'text-current' : 'text-gray-500'
                            )} />
                          ) : (
                            <ChevronRight className={cn(
                              "h-4 w-4 flex-shrink-0 ml-2",
                              (isActive || isSubmenuActive) ? 'text-current' : 'text-gray-500'
                            )} />
                          )
                        )}
                      </>
                    )}
                  </Button>

                  {isExpanded && !isCollapsed && (
                    <div className={cn(
                      "space-y-1",
                      direction === 'rtl' ? 'pr-6' : 'pl-6'
                    )}>
                      {item.submenu?.map((subItem) => {
                        const isSubActive = pathname === subItem.href
                        return (
                          <Link key={subItem.href} href={subItem.href}>
                            <Button
                              variant={isSubActive ? "default" : "ghost"}
                              className={cn(
                                "w-full h-9 px-3 py-2 transition-all duration-200 flex items-center gap-0",
                                isSubActive && "theme-primary shadow-sm",
                                !isSubActive && "hover:bg-gray-100 text-gray-600",
                                direction === 'rtl'
                                  ? "justify-end text-right flex-row-reverse"
                                  : "justify-start text-left"
                              )}
                              dir={direction}
                            >
                              <subItem.icon className={cn(
                                "h-3 w-3 flex-shrink-0",
                                direction === 'rtl' ? 'ml-2' : 'mr-2',
                                isSubActive ? 'text-white' : 'text-gray-400'
                              )} />
                              <span className={cn(
                                "flex-1 font-medium text-xs",
                                direction === 'rtl' ? 'text-right' : 'text-left',
                                isSubActive ? 'text-white' : 'text-gray-600'
                              )}>
                                {subItem.name}
                              </span>
                            </Button>
                          </Link>
                        )
                      })}
                    </div>
                  )}
                </div>
              )
            }

            // Regular menu item without submenu
            if (item.submenu) {
              // Handle submenu items
              return (
                <div key={item.name} className="space-y-1">
                  <Button
                    variant="ghost"
                    onClick={() => !isCollapsed && toggleSubmenu(item.name.toLowerCase())}
                    className={cn(
                      "w-full h-10 px-3 py-2 transition-all duration-200 flex items-center",
                      "hover:bg-gray-100 text-gray-700",
                      isCollapsed 
                        ? "justify-center" 
                        : direction === 'rtl'
                        ? "justify-end text-right flex-row-reverse gap-0"
                        : "justify-start text-left gap-0"
                    )}
                    dir={direction}
                    title={isCollapsed ? item.name : undefined}
                  >
                    <item.icon className={cn(
                      "h-4 w-4 flex-shrink-0",
                      !isCollapsed && (direction === 'rtl' ? 'ml-3' : 'mr-3'),
                      'text-gray-500'
                    )} />
                    {!isCollapsed && (
                      <>
                        <span className={cn(
                          "flex-1 font-medium text-sm",
                          direction === 'rtl' ? 'text-right' : 'text-left',
                          'text-gray-700'
                        )}>
                          {item.name}
                        </span>
                        <ChevronDown className={cn(
                          "h-4 w-4 transition-transform duration-200",
                          isExpanded ? "rotate-180" : "",
                          direction === 'rtl' ? 'mr-2' : 'ml-2'
                        )} />
                      </>
                    )}
                  </Button>

                  {isExpanded && !isCollapsed && (
                    <div className={`space-y-1 ${direction === 'rtl' ? 'mr-4' : 'ml-4'}`}>
                      {item.submenu.map((subItem) => {
                        const isSubActive = pathname === subItem.href
                        const SubComponent = (subItem as any).external ? 'a' : Link
                        const subLinkProps = (subItem as any).external
                          ? { href: subItem.href, target: '_blank', rel: 'noopener noreferrer' }
                          : { href: subItem.href }

                        return (
                          <SubComponent key={subItem.name} {...subLinkProps}>
                            <Button
                              variant={isSubActive ? "default" : "ghost"}
                              className={cn(
                                "w-full h-9 px-3 py-2 transition-all duration-200 flex items-center gap-0",
                                isSubActive && "theme-primary shadow-sm",
                                !isSubActive && "hover:bg-gray-100 text-gray-600",
                                direction === 'rtl'
                                  ? "justify-end text-right flex-row-reverse"
                                  : "justify-start text-left"
                              )}
                              dir={direction}
                            >
                              <subItem.icon className={cn(
                                "h-3 w-3 flex-shrink-0",
                                direction === 'rtl' ? 'ml-2' : 'mr-2',
                                isSubActive ? 'text-current' : 'text-gray-400'
                              )} />
                              <span className={cn(
                                "flex-1 font-medium text-xs",
                                direction === 'rtl' ? 'text-right' : 'text-left',
                                isSubActive ? 'text-current' : 'text-gray-600'
                              )}>
                                {subItem.name}
                              </span>
                            </Button>
                          </SubComponent>
                        )
                      })}
                    </div>
                  )}
                </div>
              )
            }

            // Regular menu item with href
            const Component = (item as any).external ? 'a' : Link
            const linkProps = (item as any).external
              ? { href: item.href, target: '_blank', rel: 'noopener noreferrer' }
              : { href: item.href }

            const menuItem = (
              <Component key={item.name} {...linkProps}>
                <Button
                  variant={isActive ? "default" : "ghost"}
                  className={cn(
                    "w-full h-10 px-3 py-2 transition-all duration-200 flex items-center",
                    isActive && "theme-primary shadow-sm",
                    !isActive && "hover:bg-gray-100 text-gray-700",
                    isCollapsed 
                      ? "justify-center" 
                      : direction === 'rtl'
                      ? "justify-end text-right flex-row-reverse gap-0"
                      : "justify-start text-left gap-0",
                    (item as any).className // Apply custom className if provided
                  )}
                  dir={direction}
                  title={isCollapsed ? item.name : undefined}
                >
                  <item.icon className={cn(
                    "h-4 w-4 flex-shrink-0",
                    !isCollapsed && (direction === 'rtl' ? 'ml-3' : 'mr-3'),
                    isActive ? 'text-current' : 'text-gray-500',
                    (item as any).className && 'text-white' // Override icon color for custom styled items
                  )} />
                  {!isCollapsed && (
                    <span className={cn(
                      "flex-1 font-medium text-sm",
                      direction === 'rtl' ? 'text-right' : 'text-left',
                      isActive ? 'text-current' : 'text-gray-700',
                      (item as any).className && 'text-white' // Override text color for custom styled items
                    )}>
                      {item.name}
                    </span>
                  )}
                </Button>
              </Component>
            )

            // Wrap with permission guard if permission is specified
            if ((item as any).permission) {
              return (
                <PermissionGuard
                  key={item.name}
                  module={(item as any).permission.module}
                  action={(item as any).permission.action}
                  resource={(item as any).permission.resource}
                >
                  {menuItem}
                </PermissionGuard>
              )
            }

            return menuItem
          })}
        </nav>
      </ScrollArea>
    </div>
  )
}
