# Arabization Examples

## Overview
This document provides practical examples of how to implement and use the arabization features in the print_next application.

## Table Components with RTL Support

### Basic RTL Table
```tsx
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { RTLContainer, RTLNumeric } from "@/components/ui/rtl-wrapper"
import { useI18n } from "@/lib/i18n"

export function CustomersTable() {
  const { t, direction, formatCurrency } = useI18n()
  
  return (
    <RTLContainer className="table-container">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('customers.customerName')}</TableHead>
            <TableHead>{t('common.contact')}</TableHead>
            <TableHead align="right">{t('customers.totalSpent')}</TableHead>
            <TableHead align="right">{t('customers.outstandingBalance')}</TableHead>
            <TableHead align="right">{t('common.actions')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {customers.map((customer) => (
            <TableRow key={customer.id}>
              <TableCell>{customer.name}</TableCell>
              <TableCell>{customer.email}</TableCell>
              <TableCell align="right" className="numeric-cell">
                <RTLNumeric>{formatCurrency(customer.totalSpent)}</RTLNumeric>
              </TableCell>
              <TableCell align="right" className="numeric-cell">
                <RTLNumeric>{formatCurrency(customer.balance)}</RTLNumeric>
              </TableCell>
              <TableCell align="right">
                {/* Action buttons */}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </RTLContainer>
  )
}
```

## Form Components with RTL Support

### Bilingual Form Fields
```tsx
import { Input, Label } from "@/components/ui"
import { useI18n } from "@/lib/i18n"

export function CustomerForm() {
  const { t, direction } = useI18n()
  
  return (
    <div className={`space-y-4 ${direction === 'rtl' ? 'font-arabic' : ''}`} dir={direction}>
      {/* English Name */}
      <div className="space-y-2">
        <Label htmlFor="name">{t('customers.name')} (English)</Label>
        <Input
          id="name"
          placeholder="Customer name"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
        />
      </div>
      
      {/* Arabic Name */}
      <div className="space-y-2">
        <Label htmlFor="nameAr">{t('customers.name')} (العربية)</Label>
        <Input
          id="nameAr"
          placeholder="اسم العميل"
          dir="rtl"
          className="text-right font-arabic"
          value={formData.nameAr}
          onChange={(e) => setFormData(prev => ({ ...prev, nameAr: e.target.value }))}
        />
      </div>
      
      {/* Phone (always LTR) */}
      <div className="space-y-2">
        <Label htmlFor="phone">{t('customers.phone')}</Label>
        <Input
          id="phone"
          placeholder="+968 9123 4567"
          dir="ltr"
          className="text-left"
          value={formData.phone}
          onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
        />
      </div>
    </div>
  )
}
```

## RTL Utility Hooks

### Using useRTLClasses Hook
```tsx
import { useRTLClasses } from "@/components/ui/rtl-wrapper"

export function ProductCard({ product }) {
  const rtl = useRTLClasses()
  
  return (
    <div className={`p-4 border rounded-lg ${rtl.fontFamily}`} dir={rtl.direction}>
      <div className={`flex items-center ${rtl.flexRow} ${rtl.spaceX} space-x-3`}>
        <img 
          src={product.image} 
          alt={product.name}
          className={`w-12 h-12 rounded ${rtl.dir('mr-3', 'ml-3')}`}
        />
        <div className={`flex-1 ${rtl.textAlign}`}>
          <h3 className="font-medium">{product.name}</h3>
          {product.nameAr && (
            <p className="text-sm text-muted-foreground font-arabic" dir="rtl">
              {product.nameAr}
            </p>
          )}
        </div>
        <div className={`${rtl.textAlign} numeric-content`} dir="ltr">
          {formatCurrency(product.price)}
        </div>
      </div>
    </div>
  )
}
```

## Testing RTL Implementation

### Manual Testing Checklist
- [ ] Text alignment is correct in both languages
- [ ] Numbers and currencies display in LTR format
- [ ] Navigation flows naturally in RTL
- [ ] Icons and buttons are properly positioned
- [ ] Forms work correctly in both directions
- [ ] Tables are readable and functional
- [ ] PDF generation works with Arabic text
- [ ] Mobile responsive design works in RTL

This comprehensive guide provides practical examples for implementing RTL support throughout the application. 