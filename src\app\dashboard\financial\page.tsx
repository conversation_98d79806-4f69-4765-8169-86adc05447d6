"use client"

import { useState, useEffect } from "react"
import { use<PERSON>outer } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from "recharts"
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  FileText,
  Calculator,
  PieChart as PieChartIcon,
  BarChart3,
  Calendar,
  Download,
  Filter,
  Eye,
  Building,
  CreditCard,
  Wallet,
  Target,
  AlertTriangle,
  CheckCircle,
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

// Mock financial data - Real business data for Omani printing company
const financialSummary = {
  totalRevenue: 485600,
  totalExpenses: 324800,
  netProfit: 160800,
  grossMargin: 33.148,
  operatingMargin: 28.527,
  netMargin: 25.216,
  revenueGrowth: 15.8,
  expenseGrowth: 8.2,
  profitGrowth: 28.4,
}

const monthlyFinancials = [
  { month: 'Aug', revenue: 38500, expenses: 25200, profit: 13300, margin: 34.545 },
  { month: 'Sep', revenue: 42100, expenses: 27800, profit: 14300, margin: 33.967 },
  { month: 'Oct', revenue: 39800, expenses: 26500, profit: 13300, margin: 33.417 },
  { month: 'Nov', revenue: 45200, expenses: 29100, profit: 16100, margin: 35.619 },
  { month: 'Dec', revenue: 48900, expenses: 31200, profit: 17700, margin: 36.197 },
  { month: 'Jan', revenue: 52400, expenses: 33800, profit: 18600, margin: 35.496 },
]

const revenueBreakdown = [
  { category: 'Printing Services', amount: 285600, percentage: 58.824, color: '#3b82f6' },
  { category: 'Design Services', amount: 98400, percentage: 20.264, color: '#10b981' },
  { category: 'Office Supplies', amount: 67200, percentage: 13.836, color: '#f59e0b' },
  { category: 'Consulting', amount: 34400, percentage: 7.076, color: '#ef4444' },
]

const expenseBreakdown = [
  { category: 'Materials & Supplies', amount: 145600, percentage: 44.827, color: '#3b82f6' },
  { category: 'Salaries & Benefits', amount: 89200, percentage: 27.463, color: '#10b981' },
  { category: 'Rent & Utilities', amount: 42800, percentage: 13.177, color: '#f59e0b' },
  { category: 'Equipment & Maintenance', amount: 28400, percentage: 8.748, color: '#ef4444' },
  { category: 'Marketing & Advertising', amount: 18800, percentage: 5.787, color: '#8b5cf6' },
]

const cashFlowData = [
  { month: 'Aug', inflow: 38500, outflow: 25200, net: 13300 },
  { month: 'Sep', inflow: 42100, outflow: 27800, net: 14300 },
  { month: 'Oct', inflow: 39800, outflow: 26500, net: 13300 },
  { month: 'Nov', inflow: 45200, outflow: 29100, net: 16100 },
  { month: 'Dec', inflow: 48900, outflow: 31200, net: 17700 },
  { month: 'Jan', inflow: 52400, outflow: 33800, net: 18600 },
]

const kpiMetrics = [
  { 
    nameKey: 'الإيرادات لكل عميل',
    value: 2840, 
    target: 3000, 
    unit: 'OMR', 
    trend: 'up', 
    change: 8.5 
  },
  { 
    nameKey: 'تكلفة اكتساب العملاء',
    value: 145, 
    target: 120, 
    unit: 'OMR', 
    trend: 'down', 
    change: -12.3 
  },
  { 
    nameKey: 'متوسط قيمة الطلب',
    value: 485, 
    target: 500, 
    unit: 'OMR', 
    trend: 'up', 
    change: 5.2 
  },
  { 
    nameKey: 'الإيرادات الشهرية المتكررة',
    value: 28400, 
    target: 30000, 
    unit: 'OMR', 
    trend: 'up', 
    change: 15.8 
  },
]

// Helper function to format percentages with max 3 decimal places
const formatPercentage = (value: number, decimals: number = 3) => {
  return `${value.toFixed(decimals)}%`
}

export default function FinancialDashboard() {
  const router = useRouter()
  const { t } = useI18n()
  const [selectedPeriod, setSelectedPeriod] = useState("6months")
  const [mounted, setMounted] = useState(false)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [selectedPeriod])

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? TrendingUp : TrendingDown
  }

  const getTrendColor = (trend: string) => {
    return trend === 'up' ? 'text-green-500' : 'text-red-500'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">{t('finance.financial.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('finance.financial.title')}</h2>
          <p className="text-muted-foreground">
            {t('finance.financial.subtitle')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1month">{t('finance.financial.period.lastMonth')}</SelectItem>
              <SelectItem value="3months">{t('finance.financial.period.last3Months')}</SelectItem>
              <SelectItem value="6months">{t('finance.financial.period.last6Months')}</SelectItem>
              <SelectItem value="1year">{t('finance.financial.period.lastYear')}</SelectItem>
              <SelectItem value="ytd">{t('finance.financial.period.ytd')}</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={() => router.push('/dashboard/financial/reports')}>
            <FileText className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.reports')}
          </Button>
          <Button onClick={() => router.push('/dashboard/financial/profit-loss')}>
            <Calculator className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.plStatement')}
          </Button>
        </div>
      </div>

      {/* Financial Summary Cards */}
      <div className="bg-slate-50/50 dark:bg-slate-900/20 rounded-lg p-6 border border-slate-200/60 dark:border-slate-700/60">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('finance.financial.cards.totalRevenue')}</CardTitle>
              <DollarSign className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{formatCurrency(financialSummary.totalRevenue)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                +{financialSummary.revenueGrowth}% {t('finance.financial.cards.fromLastPeriod')}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('finance.financial.cards.totalExpenses')}</CardTitle>
              <CreditCard className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{formatCurrency(financialSummary.totalExpenses)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="mr-1 h-3 w-3 text-red-500" />
                +{financialSummary.expenseGrowth}% {t('finance.financial.cards.fromLastPeriod')}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('finance.financial.cards.netProfit')}</CardTitle>
              <Wallet className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{formatCurrency(financialSummary.netProfit)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                +{financialSummary.profitGrowth}% {t('finance.financial.cards.fromLastPeriod')}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('finance.financial.cards.profitMargin')}</CardTitle>
              <Target className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{formatPercentage(financialSummary.netMargin)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                {t('finance.financial.cards.healthyMargin')}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Revenue vs Expenses Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-blue-500" />
              <span>{t('finance.financial.charts.revenueVsExpensesTrend')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={monthlyFinancials}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    formatCurrency(value as number),
                    name === 'revenue' ? t('finance.financial.charts.revenue') : name === 'expenses' ? t('finance.financial.charts.expenses') : t('finance.financial.charts.profit')
                  ]}
                />
                <Area type="monotone" dataKey="revenue" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.6} />
                <Area type="monotone" dataKey="expenses" stackId="2" stroke="#ef4444" fill="#ef4444" fillOpacity={0.6} />
                <Line type="monotone" dataKey="profit" stroke="#3b82f6" strokeWidth={3} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Profit Margin Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              <span>{t('finance.financial.charts.profitMarginTrend')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyFinancials}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip
                  formatter={(value) => [`${formatPercentage(value as number)}`, t('finance.financial.charts.profitMargin')]}
                />
                <Line type="monotone" dataKey="margin" stroke="#8b5cf6" strokeWidth={3} dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Revenue and Expense Breakdown */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Revenue Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChartIcon className="h-5 w-5 text-green-500" />
              <span>{t('finance.financial.cards.revenueBreakdown')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={revenueBreakdown}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="amount"
                    label={({ percentage }) => `${percentage.toFixed(3)}%`}
                  >
                    {revenueBreakdown.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                </PieChart>
              </ResponsiveContainer>
              <div className="space-y-2">
                {revenueBreakdown.map((item: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                      <span className="text-sm">{item.category}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{formatCurrency(item.amount)}</div>
                      <div className="text-xs text-muted-foreground">{formatPercentage(item.percentage)}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Expense Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChartIcon className="h-5 w-5 text-red-500" />
              <span>{t('finance.financial.cards.expenseBreakdown')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={expenseBreakdown}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="amount"
                    label={({ percentage }) => `${percentage.toFixed(3)}%`}
                  >
                    {expenseBreakdown.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => formatCurrency(value as number)} />
                </PieChart>
              </ResponsiveContainer>
              <div className="space-y-2">
                {expenseBreakdown.map((item: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                      <span className="text-sm">{item.category}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{formatCurrency(item.amount)}</div>
                      <div className="text-xs text-muted-foreground">{formatPercentage(item.percentage)}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Cash Flow and KPIs */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* Cash Flow */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              <span>{t('finance.financial.cards.cashFlowAnalysis')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={cashFlowData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip
                  formatter={(value, name) => [
                    formatCurrency(value as number),
                    name === 'inflow' ? t('finance.financial.cards.inflow') : name === 'outflow' ? t('finance.financial.cards.outflow') : t('finance.financial.cards.net')
                  ]}
                />
                <Bar dataKey="inflow" fill="#10b981" name={t('finance.financial.cards.inflow')} />
                <Bar dataKey="outflow" fill="#ef4444" name={t('finance.financial.cards.outflow')} />
                <Bar dataKey="net" fill="#3b82f6" name={t('finance.financial.cards.net')} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Key Performance Indicators */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-purple-500" />
              <span>{t('finance.financial.cards.keyMetrics')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {kpiMetrics.map((metric, index) => {
              const TrendIcon = getTrendIcon(metric.trend)
              const trendColor = getTrendColor(metric.trend)
              const progress = (metric.value / metric.target) * 100

              return (
                <div key={index} className="p-4 border rounded-lg bg-slate-50/30 dark:bg-slate-800/30">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <TrendIcon className={`h-4 w-4 ${trendColor}`} />
                      <span className="text-sm font-medium">{formatCurrency(metric.value)}</span>
                    </div>
                    <span className={`text-xs ${trendColor}`}>
                      {metric.change >= 0 ? '+' : ''}{metric.change}%
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">{metric.nameKey}</div>
                  <div className="text-xs text-muted-foreground">
                    {t('finance.financial.cards.target')} {formatCurrency(metric.target)}
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${metric.trend === 'up' ? 'bg-green-500' : 'bg-yellow-500'}`}
                      style={{ width: `${Math.min(progress, 100)}%` }}
                    ></div>
                  </div>
                </div>
              )
            })}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
