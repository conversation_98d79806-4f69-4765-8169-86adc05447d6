// Simple test to verify leads API is working
async function testLeadsAPI() {
  try {
    console.log('Testing Leads API...')
    
    // Test GET /api/leads
    const response = await fetch('http://localhost:3000/api/leads')
    const data = await response.json()
    
    if (response.ok) {
      console.log('✅ GET /api/leads successful')
      console.log('Leads found:', data.pagination.total)
      console.log('Sample leads:', data.leads.slice(0, 2).map(l => ({ id: l.id, name: l.name, mobile: l.mobile })))
    } else {
      console.log('❌ GET /api/leads failed:', data.error)
    }
    
  } catch (error) {
    console.error('❌ API test failed:', error.message)
  }
}

testLeadsAPI()
