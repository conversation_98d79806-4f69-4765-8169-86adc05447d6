export const stock = {
  // Page titles and descriptions
  title: "إدارة المخزون",
  description: "راقب وادر مستويات المخزون لديك",
  stockReport: "تقرير المخزون",
  
  // Stock status
  outOfStock: "نفد المخزون",
  lowStock: "مخزون منخفض", 
  inStock: "متوفر في المخزون",
  
  // Stock actions
  stockIncreased: "تم زيادة المخزون",
  stockDecreased: "تم تقليل المخزون",
  fillAllRequiredFields: "يرجى ملء جميع الحقول المطلوبة",
  failedToSaveStockAdjustment: "فشل في حفظ تعديل المخزون",
  
  // Stock data
  loadingStockData: "جاري تحميل بيانات المخزون...",
  noStockDataAvailable: "لا توجد بيانات مخزون متاحة",
  totalProducts: "إجمالي المنتجات",
  activeInventoryItems: "عناصر المخزون النشطة",
  products: "المنتجات",
  
  // Stock levels
  currentStock: "المخزون الحالي",
  minStock: "الحد الأدنى للمخزون",
  maxStock: "الحد الأقصى للمخزون",
  reorderLevel: "مستوى إعادة الطلب",
  stockValue: "قيمة المخزون",
  averageCost: "متوسط التكلفة",
  
  // Stock movements
  stockIn: "إدخال مخزون",
  stockOut: "إخراج مخزون",
  stockAdjustment: "تعديل المخزون",
  stockTransfer: "نقل المخزون",
  
  // Stock alerts
  lowStockAlert: "تنبيه انخفاض المخزون",
  outOfStockAlert: "تنبيه نفاد المخزون",
  reorderAlert: "تنبيه إعادة الطلب",
  
  // Stock reports
  stockMovementReport: "تقرير حركة المخزون",
  stockValuationReport: "تقرير تقييم المخزون",
  lowStockReport: "تقرير المخزون المنخفض",
  
  // Actions
  adjustStock: "تعديل المخزون",
  addStock: "إضافة مخزون",
  removeStock: "إزالة مخزون",
  updateStock: "تحديث المخزون",
  transferStock: "نقل المخزون",
  
  // Messages
  stockUpdatedSuccessfully: "تم تحديث المخزون بنجاح",
  stockAdjustmentSaved: "تم حفظ تعديل المخزون بنجاح",
  failedToUpdateStock: "فشل في تحديث المخزون",
  invalidStockQuantity: "كمية مخزون غير صحيحة",
  stockCannotBeNegative: "لا يمكن أن يكون المخزون سالبًا",
  
  // Filters and search
  filterByStatus: "تصفية حسب الحالة",
  filterByCategory: "تصفية حسب الفئة",
  searchProducts: "البحث في المنتجات...",
  allStatuses: "جميع الحالات",
  
  // Inventory tracking
  lastUpdated: "آخر تحديث",
  updatedBy: "تم التحديث بواسطة",
  stockMovements: "حركات المخزون",
  movementHistory: "تاريخ الحركات",
  
  // Warehouse management
  warehouse: "المستودع",
  location: "الموقع",
  bin: "الصندوق",
  shelf: "الرف",
  zone: "المنطقة"
}; 