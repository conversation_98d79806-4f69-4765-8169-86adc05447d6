/**
 * Supplier Payout API utility functions
 */

export interface SupplierPayout {
  id: string
  number: string
  amount: number
  method: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHECK' | 'OTHER'
  reference?: string
  description?: string
  status: 'PENDING' | 'APPROVED' | 'PAID' | 'CANCELLED' | 'REJECTED'
  date: string
  dueDate?: string
  paidDate?: string
  notes?: string
  createdAt: string
  updatedAt: string
  supplierId: string
  supplier?: any
  purchaseId?: string
  purchase?: any
  createdById: string
  createdBy?: any
}

export interface PayoutStats {
  totalPayouts: number
  totalAmount: number
  averageAmount: number
  statusCounts: Record<string, number>
  statusAmounts: Record<string, number>
  methodCounts: Record<string, number>
  overduePayouts: number
  upcomingPayouts: number
  topSuppliers: any[]
  period: string
}

// Get all supplier payouts from API
export const getSupplierPayouts = async (params?: {
  supplierId?: string
  status?: string
  page?: number
  limit?: number
}): Promise<{ payouts: SupplierPayout[]; pagination: any }> => {
  try {
    const searchParams = new URLSearchParams()
    
    if (params?.supplierId) searchParams.append('supplierId', params.supplierId)
    if (params?.status) searchParams.append('status', params.status)
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())

    const response = await fetch(`/api/supplier-payouts?${searchParams.toString()}`)
    if (!response.ok) {
      throw new Error('Failed to fetch supplier payouts')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading supplier payouts:', error)
    return { payouts: [], pagination: { page: 1, limit: 10, total: 0, pages: 0 } }
  }
}

// Get supplier payout by ID
export const getSupplierPayoutById = async (id: string): Promise<SupplierPayout | null> => {
  try {
    const response = await fetch(`/api/supplier-payouts/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch supplier payout')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading supplier payout:', error)
    return null
  }
}

// Create new supplier payout
export const createSupplierPayout = async (payout: {
  supplierId: string
  amount: number
  method?: string
  reference?: string
  description?: string
  date?: string
  dueDate?: string
  purchaseId?: string
  notes?: string
}): Promise<SupplierPayout | null> => {
  try {
    const response = await fetch('/api/supplier-payouts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payout),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create supplier payout')
    }

    return await response.json()
  } catch (error) {
    console.error('Error creating supplier payout:', error)
    throw error
  }
}

// Update supplier payout
export const updateSupplierPayout = async (
  id: string,
  updates: Partial<SupplierPayout>
): Promise<SupplierPayout | null> => {
  try {
    const response = await fetch(`/api/supplier-payouts/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update supplier payout')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating supplier payout:', error)
    throw error
  }
}

// Delete supplier payout
export const deleteSupplierPayout = async (id: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/supplier-payouts/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete supplier payout')
    }

    return true
  } catch (error) {
    console.error('Error deleting supplier payout:', error)
    throw error
  }
}

// Get payout statistics
export const getPayoutStats = async (period?: string): Promise<PayoutStats | null> => {
  try {
    const searchParams = new URLSearchParams()
    if (period) searchParams.append('period', period)

    const response = await fetch(`/api/supplier-payouts/stats?${searchParams.toString()}`)
    if (!response.ok) {
      throw new Error('Failed to fetch payout statistics')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading payout statistics:', error)
    return null
  }
}

// Mark payout as paid
export const markPayoutAsPaid = async (id: string, paidDate?: string): Promise<SupplierPayout | null> => {
  return updateSupplierPayout(id, {
    status: 'PAID',
    paidDate: paidDate || new Date().toISOString(),
  })
}

// Approve payout
export const approvePayout = async (id: string): Promise<SupplierPayout | null> => {
  return updateSupplierPayout(id, {
    status: 'APPROVED',
  })
}

// Reject payout
export const rejectPayout = async (id: string, notes?: string): Promise<SupplierPayout | null> => {
  return updateSupplierPayout(id, {
    status: 'REJECTED',
    notes: notes || undefined,
  })
}

// Cancel payout
export const cancelPayout = async (id: string, notes?: string): Promise<SupplierPayout | null> => {
  return updateSupplierPayout(id, {
    status: 'CANCELLED',
    notes: notes || undefined,
  })
}

// Get payouts for a specific supplier
export const getSupplierPayoutsBySupplier = async (supplierId: string): Promise<SupplierPayout[]> => {
  const result = await getSupplierPayouts({ supplierId, limit: 100 })
  return result.payouts
}

// Get pending payouts
export const getPendingPayouts = async (): Promise<SupplierPayout[]> => {
  const result = await getSupplierPayouts({ status: 'PENDING', limit: 100 })
  return result.payouts
}

// Get overdue payouts
export const getOverduePayouts = async (): Promise<SupplierPayout[]> => {
  const result = await getSupplierPayouts({ limit: 100 })
  const now = new Date()
  return result.payouts.filter(payout => 
    payout.dueDate && 
    new Date(payout.dueDate) < now && 
    payout.status !== 'PAID' && 
    payout.status !== 'CANCELLED' && 
    payout.status !== 'REJECTED'
  )
}
