"use client"

import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useI18n } from '@/lib/i18n'

export default function FinancialRatios() {
  const router = useRouter()
  const { t } = useI18n()

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('finance.financial.buttons.back') || 'رجوع'}
        </Button>
        <h2 className="text-3xl font-bold tracking-tight">
          {t('finance.financial.reports.reportTypes.financialratios.name') || 'النسب المالية'}
        </h2>
      </div>
      <p className="text-muted-foreground">
        {t('finance.financial.reports.reportTypes.financialratios.description')}
      </p>
      <div className="border rounded-lg p-8 text-center text-muted-foreground">
        {t('common.comingSoon') || 'سيتم توفير هذا التقرير قريباً'}
      </div>
    </div>
  )
} 