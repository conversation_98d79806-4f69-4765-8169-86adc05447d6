"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { ArrowLeft, TrendingUp, TrendingDown, BarChart3, PieChart, Download, Printer, Share2, Calendar, Target, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { useI18n } from '@/lib/i18n'
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ra<PERSON><PERSON><PERSON> } from 'recharts'

interface FinancialRatio {
  name: string
  value: number
  benchmark: number
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical'
  trend: 'improving' | 'declining' | 'stable'
  category: 'liquidity' | 'profitability' | 'efficiency' | 'leverage' | 'growth'
  description: string
  formula: string
}

interface MonthlyRatioData {
  month: string
  currentRatio: number
  quickRatio: number
  grossProfitMargin: number
  netProfitMargin: number
  assetTurnover: number
  debtToEquity: number
  revenueGrowth: number
}

interface CategoryBreakdown {
  category: string
  ratiosCount: number
  averageScore: number
  status: string
}

interface FinancialRatiosData {
  summary: {
    totalRatios: number
    healthyRatios: number
    warningRatios: number
    criticalRatios: number
    overallScore: number
    lastUpdated: string
    dataAccuracy: number
  }
  ratios: FinancialRatio[]
  monthlyTrends: MonthlyRatioData[]
  categoryBreakdown: CategoryBreakdown[]
  period: string
  dateRange: {
    start: string
    end: string
  }
}

export default function FinancialRatios() {
  const router = useRouter()
  const { t, formatCurrency } = useI18n()
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<FinancialRatiosData | null>(null)
  const [period, setPeriod] = useState('6months')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchFinancialRatiosData()
  }, [period, categoryFilter, statusFilter])

  const fetchFinancialRatiosData = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/financial-reports/financial-ratios?period=${period}&category=${categoryFilter}&status=${statusFilter}`)

      if (!response.ok) {
        throw new Error('Failed to fetch financial ratios data')
      }

      const result = await response.json()
      setData(result)
    } catch (error) {
      console.error('Error fetching financial ratios data:', error)
      setError('Failed to load financial ratios data')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'good':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'fair':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'poor':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
      case 'good':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'fair':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'poor':
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Target className="h-4 w-4 text-gray-500" />
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'declining':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      case 'stable':
        return <BarChart3 className="h-4 w-4 text-blue-500" />
      default:
        return <BarChart3 className="h-4 w-4 text-gray-500" />
    }
  }

  const formatRatioValue = (ratio: FinancialRatio) => {
    if (ratio.category === 'profitability' || ratio.category === 'leverage') {
      return `${ratio.value.toFixed(1)}%`
    }
    return ratio.value.toFixed(2)
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {t('finance.financial.financialRatios.title')}
          </h2>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">{t('finance.financial.loading')}</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !data) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {t('finance.financial.financialRatios.title')}
          </h2>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error || t('finance.financial.noData')}</p>
            <Button onClick={fetchFinancialRatiosData}>
              {t('common.retry') || 'Retry'}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">
              {t('finance.financial.financialRatios.title')}
            </h2>
            <p className="text-muted-foreground">
              {t('finance.financial.financialRatios.subtitle')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            {t('finance.financial.financialRatios.actions.exportReport')}
          </Button>
          <Button variant="outline" size="sm">
            <Printer className="mr-2 h-4 w-4" />
            {t('finance.financial.financialRatios.actions.printReport')}
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="mr-2 h-4 w-4" />
            {t('finance.financial.financialRatios.actions.shareReport')}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4 bg-muted/50 p-4 rounded-lg">
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">{t('finance.financial.financialRatios.filters.period')}:</span>
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1month">{t('finance.financial.period.lastMonth')}</SelectItem>
              <SelectItem value="3months">{t('finance.financial.period.last3Months')}</SelectItem>
              <SelectItem value="6months">{t('finance.financial.period.last6Months')}</SelectItem>
              <SelectItem value="1year">{t('finance.financial.period.lastYear')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{t('finance.financial.financialRatios.filters.category')}:</span>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('finance.financial.reports.categories.allReports')}</SelectItem>
              <SelectItem value="liquidity">{t('finance.financial.financialRatios.categories.liquidity')}</SelectItem>
              <SelectItem value="profitability">{t('finance.financial.financialRatios.categories.profitability')}</SelectItem>
              <SelectItem value="efficiency">{t('finance.financial.financialRatios.categories.efficiency')}</SelectItem>
              <SelectItem value="leverage">{t('finance.financial.financialRatios.categories.leverage')}</SelectItem>
              <SelectItem value="growth">{t('finance.financial.financialRatios.categories.growth')}</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{t('finance.financial.financialRatios.filters.status')}:</span>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('finance.financial.reports.categories.allReports')}</SelectItem>
              <SelectItem value="excellent">{t('finance.financial.financialRatios.status.excellent')}</SelectItem>
              <SelectItem value="good">{t('finance.financial.financialRatios.status.good')}</SelectItem>
              <SelectItem value="fair">{t('finance.financial.financialRatios.status.fair')}</SelectItem>
              <SelectItem value="poor">{t('finance.financial.financialRatios.status.poor')}</SelectItem>
              <SelectItem value="critical">{t('finance.financial.financialRatios.status.critical')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.financialRatios.overview.totalRatios')}
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {data.summary.totalRatios}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('finance.financial.financialRatios.overview.dataAccuracy')}: {data.summary.dataAccuracy.toFixed(1)}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.financialRatios.overview.overallScore')}
            </CardTitle>
            <Target className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {data.summary.overallScore.toFixed(0)}%
            </div>
            <Progress value={data.summary.overallScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.financialRatios.overview.healthyRatios')}
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {data.summary.healthyRatios}
            </div>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span className="text-yellow-600">
                {t('finance.financial.financialRatios.overview.warningRatios')}: {data.summary.warningRatios}
              </span>
              <span className="text-red-600">
                {t('finance.financial.financialRatios.overview.criticalRatios')}: {data.summary.criticalRatios}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.financialRatios.overview.lastUpdated')}
            </CardTitle>
            <Calendar className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-sm font-bold text-orange-600">
              {new Date(data.summary.lastUpdated).toLocaleDateString()}
            </div>
            <p className="text-xs text-muted-foreground">
              {new Date(data.summary.lastUpdated).toLocaleTimeString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Ratio Trends Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="mr-2 h-5 w-5 text-blue-500" />
              {t('finance.financial.financialRatios.charts.ratioTrends')}
            </CardTitle>
            <CardDescription>
              {t('finance.financial.financialRatios.charts.liquidityAnalysis')} & {t('finance.financial.financialRatios.charts.profitabilityAnalysis')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={data.monthlyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="currentRatio"
                  stroke="#0088FE"
                  strokeWidth={2}
                  name={t('finance.financial.financialRatios.ratios.currentRatio')}
                />
                <Line
                  type="monotone"
                  dataKey="quickRatio"
                  stroke="#00C49F"
                  strokeWidth={2}
                  name={t('finance.financial.financialRatios.ratios.quickRatio')}
                />
                <Line
                  type="monotone"
                  dataKey="grossProfitMargin"
                  stroke="#FFBB28"
                  strokeWidth={2}
                  name={t('finance.financial.financialRatios.ratios.grossProfitMargin')}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Category Breakdown Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="mr-2 h-5 w-5 text-green-500" />
              {t('finance.financial.financialRatios.charts.categoryComparison')}
            </CardTitle>
            <CardDescription>
              {t('finance.financial.financialRatios.summary.financialHealth')} {t('finance.financial.financialRatios.filters.category')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Tooltip />
                <RechartsPieChart data={data.categoryBreakdown}>
                  {data.categoryBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </RechartsPieChart>
              </RechartsPieChart>
            </ResponsiveContainer>
            <div className="mt-4 grid grid-cols-2 gap-2">
              {data.categoryBreakdown.map((category, index) => (
                <div key={category.category} className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <span className="text-xs">
                    {t(`finance.financial.financialRatios.categories.${category.category}`)} ({category.ratiosCount})
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Ratios Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="mr-2 h-5 w-5 text-purple-500" />
            {t('finance.financial.financialRatios.title')} - {t('finance.financial.financialRatios.table.ratio')}
          </CardTitle>
          <CardDescription>
            {t('finance.financial.financialRatios.charts.benchmarkComparison')} & {t('finance.financial.financialRatios.summary.recommendations')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('finance.financial.financialRatios.table.ratio')}</TableHead>
                <TableHead>{t('finance.financial.financialRatios.table.category')}</TableHead>
                <TableHead className="text-right">{t('finance.financial.financialRatios.table.value')}</TableHead>
                <TableHead className="text-right">{t('finance.financial.financialRatios.table.benchmark')}</TableHead>
                <TableHead className="text-center">{t('finance.financial.financialRatios.table.status')}</TableHead>
                <TableHead className="text-center">{t('finance.financial.financialRatios.table.trend')}</TableHead>
                <TableHead>{t('finance.financial.financialRatios.table.formula')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.ratios.map((ratio, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold">
                        {t(`finance.financial.financialRatios.ratios.${ratio.name}`)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {ratio.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="text-xs">
                      {t(`finance.financial.financialRatios.categories.${ratio.category}`)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right font-mono">
                    <span className="font-semibold">
                      {formatRatioValue(ratio)}
                    </span>
                  </TableCell>
                  <TableCell className="text-right font-mono text-muted-foreground">
                    {ratio.category === 'profitability' || ratio.category === 'leverage'
                      ? `${ratio.benchmark.toFixed(1)}%`
                      : ratio.benchmark.toFixed(2)
                    }
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center space-x-1">
                      {getStatusIcon(ratio.status)}
                      <Badge className={getStatusColor(ratio.status)}>
                        {t(`finance.financial.financialRatios.status.${ratio.status}`)}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center space-x-1">
                      {getTrendIcon(ratio.trend)}
                      <span className="text-xs">
                        {t(`finance.financial.financialRatios.status.${ratio.trend}`)}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-xs font-mono text-muted-foreground">
                    {ratio.formula}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}