"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatOmaniPhone, isValidOmaniPhone, isValidOmaniCivilId, omaniGovernorates } from "@/lib/localization"
import { toast } from "sonner"

interface CustomerFormProps {
  customer?: any
  onSubmit: (data: any) => void
  onCancel: () => void
  isLoading?: boolean
}

export function CustomerForm({ customer, onSubmit, onCancel, isLoading }: CustomerFormProps) {
  const [formData, setFormData] = useState({
    name: customer?.name || "",
    nameAr: customer?.nameAr || "",
    email: customer?.email || "",
    phone: customer?.phone || "",
    company: customer?.company || "",
    companyAr: customer?.companyAr || "",
    taxNumber: customer?.taxNumber || "",
    civilId: customer?.civilId || "",
    customerType: customer?.customerType || "INDIVIDUAL",
    governorate: customer?.governorate || "",
    wilayat: customer?.wilayat || "",
    area: customer?.area || "",
    street: customer?.street || "",
    building: customer?.building || "",
    postalCode: customer?.postalCode || "",
    notes: customer?.notes || "",
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }

    // Format phone number as user types
    if (field === "phone") {
      const formatted = formatOmaniPhone(value)
      setFormData(prev => ({ ...prev, phone: formatted }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Name is required"
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required"
    } else if (!isValidOmaniPhone(formData.phone)) {
      newErrors.phone = "Please enter a valid Omani phone number"
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }

    if (formData.civilId && !isValidOmaniCivilId(formData.civilId)) {
      newErrors.civilId = "Please enter a valid Omani Civil ID (8 digits)"
    }

    if (formData.customerType === "BUSINESS" && !formData.company.trim()) {
      newErrors.company = "Company name is required for business customers"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (validateForm()) {
      onSubmit(formData)
    } else {
      toast.error("Please fix the errors in the form")
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Customer Type */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Customer Type</CardTitle>
          <CardDescription>Select the type of customer</CardDescription>
        </CardHeader>
        <CardContent>
          <Select 
            value={formData.customerType} 
            onValueChange={(value) => handleInputChange("customerType", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select customer type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="INDIVIDUAL">Individual</SelectItem>
              <SelectItem value="BUSINESS">Business</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Basic Information</CardTitle>
          <CardDescription>Customer contact details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">
                Name (English) <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="nameAr">Name (Arabic)</Label>
              <Input
                id="nameAr"
                value={formData.nameAr}
                onChange={(e) => handleInputChange("nameAr", e.target.value)}
                dir="rtl"
                placeholder="الاسم بالعربية"
              />
            </div>
          </div>

          {formData.customerType === "BUSINESS" && (
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company">
                  Company Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => handleInputChange("company", e.target.value)}
                  className={errors.company ? "border-red-500" : ""}
                />
                {errors.company && <p className="text-sm text-red-500">{errors.company}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="companyAr">Company Name (Arabic)</Label>
                <Input
                  id="companyAr"
                  value={formData.companyAr}
                  onChange={(e) => handleInputChange("companyAr", e.target.value)}
                  dir="rtl"
                  placeholder="اسم الشركة بالعربية"
                />
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">
                Phone Number <span className="text-red-500">*</span>
              </Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                placeholder="+968 XX XXX XXX"
                className={errors.phone ? "border-red-500" : ""}
              />
              {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="civilId">Civil ID</Label>
              <Input
                id="civilId"
                value={formData.civilId}
                onChange={(e) => handleInputChange("civilId", e.target.value)}
                placeholder="XXXX-XXXX"
                maxLength={9}
                className={errors.civilId ? "border-red-500" : ""}
              />
              {errors.civilId && <p className="text-sm text-red-500">{errors.civilId}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="taxNumber">Tax Registration Number</Label>
              <Input
                id="taxNumber"
                value={formData.taxNumber}
                onChange={(e) => handleInputChange("taxNumber", e.target.value)}
                placeholder="For business customers"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Address Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Address Information</CardTitle>
          <CardDescription>Location details in Oman</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="governorate">Governorate</Label>
              <Select 
                value={formData.governorate} 
                onValueChange={(value) => handleInputChange("governorate", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select governorate" />
                </SelectTrigger>
                <SelectContent>
                  {omaniGovernorates.en.map((gov, index) => (
                    <SelectItem key={gov} value={gov}>
                      {gov} ({omaniGovernorates.ar[index]})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="wilayat">Wilayat</Label>
              <Input
                id="wilayat"
                value={formData.wilayat}
                onChange={(e) => handleInputChange("wilayat", e.target.value)}
                placeholder="Enter wilayat"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="area">Area</Label>
              <Input
                id="area"
                value={formData.area}
                onChange={(e) => handleInputChange("area", e.target.value)}
                placeholder="Area/District"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="street">Street</Label>
              <Input
                id="street"
                value={formData.street}
                onChange={(e) => handleInputChange("street", e.target.value)}
                placeholder="Street name"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="building">Building</Label>
              <Input
                id="building"
                value={formData.building}
                onChange={(e) => handleInputChange("building", e.target.value)}
                placeholder="Building number/name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="postalCode">Postal Code</Label>
              <Input
                id="postalCode"
                value={formData.postalCode}
                onChange={(e) => handleInputChange("postalCode", e.target.value)}
                placeholder="XXX"
                maxLength={3}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Additional Notes */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Additional Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Any additional notes about the customer..."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : customer ? "Update Customer" : "Add Customer"}
        </Button>
      </div>
    </form>
  )
}
