export const pos = {
  // Page title and description
  title: "Point of Sale",
  description: "Process sales transactions and manage customer orders",
  
  // Product search and categories
  searchProducts: "Search products...",
  allCategories: "All Categories",
  category: "Category",
  
  // Product information
  product: "Product",
  products: "Products",
  name: "Name",
  price: "Price",
  stock: "Stock",
  sku: "SKU",
  unit: "Unit",
  inStock: "In Stock",
  outOfStock: "Out of Stock",
  lowStock: "Low Stock",
  
  // Cart management
  cart: "Cart",
  addToCart: "Add to Cart",
  removeFromCart: "Remove from Cart",
  clearCart: "Clear Cart",
  quantity: "Quantity",
  total: "Total",
  subtotal: "Subtotal",
  tax: "Tax",
  discount: "Discount",
  grandTotal: "Grand Total",
  orderSummary: "Order Summary",
  vat: "VAT",
  select: "Select",
  clear: "Clear",
  service: "Service",
  
  // Customer management
  customer: "Customer",
  selectCustomer: "Select Customer",
  addCustomer: "Add Customer",
  newCustomer: "New Customer",
  customerName: "Customer Name",
  customerPhone: "Customer Phone",
  customerEmail: "Customer Email",
  company: "Company",
  searchCustomers: "Search customers...",
  noCustomerSelected: "No customer selected",
  walkInCustomer: "Walk-in Customer",
  
  // Payment processing
  payment: "Payment",
  paymentMethod: "Payment Method",
  cash: "Cash",
  card: "Card",
  creditCard: "Credit Card",
  debitCard: "Debit Card",
  bankTransfer: "Bank Transfer",
  amountPaid: "Amount Paid",
  change: "Change",
  processPayment: "Process Payment",
  completeTransaction: "Complete Transaction",
  
  // Transaction status
  transactionCompleted: "Transaction Completed",
  transactionFailed: "Transaction Failed",
  paymentSuccessful: "Payment Successful",
  insufficientPayment: "Insufficient Payment",
  
  // Actions
  checkout: "Checkout",
  print: "Print",
  printReceipt: "Print Receipt",
  emailReceipt: "Email Receipt",
  newTransaction: "New Transaction",
  hold: "Hold",
  recall: "Recall",
  
  // Receipt
  receipt: "Receipt",
  receiptNumber: "Receipt #",
  date: "Date",
  time: "Time",
  cashier: "Cashier",
  thankYou: "Thank you for your business!",
  
  // Validation messages
  selectAtLeastOneItem: "Please select at least one item",
  enterValidAmount: "Please enter a valid amount",
  insufficientStock: "Insufficient stock available",
  customerRequired: "Customer selection is required",
  
  // Error messages
  failedToProcessPayment: "Failed to process payment",
  failedToAddProduct: "Failed to add product to cart",
  failedToUpdateQuantity: "Failed to update quantity",
  productNotFound: "Product not found",
  
  // Success messages
  productAddedToCart: "Product added to cart",
  quantityUpdated: "Quantity updated",
  customerAdded: "Customer added successfully",
  transactionSaved: "Transaction saved successfully",
  
  // Empty states
  cartEmpty: "Cart is empty",
  noProductsFound: "No products found",
  noCustomersFound: "No customers found",
  addProductsToGetStarted: "Add products to get started",
  chooseCustomerOrWalkIn: "Choose a customer or continue as walk-in",
  addNewCustomer: "Add New Customer",
  tryDifferentSearchTerm: "Try a different search term",
  createNewCustomerAccount: "Create a new customer account",
  englishName: "English Name",
  arabicName: "Arabic Name",
  phoneNumber: "Phone Number",
  email: "Email",
  companyEnglish: "Company (English)",
  companyArabic: "Company (Arabic)",
  completeSaleTransaction: "Complete the sale transaction",
  totalAmount: "Total Amount",
  creditDebitCard: "Credit/Debit Card",
  completeSale: "Complete Sale",
  
  // Buttons
  add: "Add",
  remove: "Remove",
  update: "Update",
  save: "Save",
  cancel: "Cancel",
  close: "Close",
  confirm: "Confirm",
  
  // Numbers and calculations
  items: "Items",
  itemCount: "Item Count",
  totalItems: "Total Items",
  
  // Categories (common ones for printing business)
  officeSupplies: "Office Supplies",
  printingServices: "Printing Services",
  writingSupplies: "Writing Supplies",
  officeEquipment: "Office Equipment",
  designServices: "Design Services",
  
  // Units
  piece: "Piece",
  ream: "Ream",
  set: "Set",
  design: "Design",
  
  // Status indicators
  processing: "Processing...",
  loading: "Loading...",
  saving: "Saving...",
  
  // Quick actions
  quickAdd: "Quick Add",
  quickPay: "Quick Pay",
  exactAmount: "Exact Amount",
  
  // Keyboard shortcuts
  shortcuts: "Shortcuts",
  enterToSearch: "Enter to search",
  escToCancel: "Esc to cancel",
  
  // Till management
  openTill: "Open Till",
  closeTill: "Close Till",
  tillBalance: "Till Balance",
  cashDrawer: "Cash Drawer"
}; 