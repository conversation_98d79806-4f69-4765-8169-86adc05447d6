import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/calendar/upcoming - Get upcoming renewals and deadlines
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if calendar model is available
    if (!prisma.calendarEvent) {
      console.log('CalendarEvent model not available yet, returning empty array')
      return NextResponse.json([])
    }

    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30') // Default to next 30 days
    const limit = parseInt(searchParams.get('limit') || '10') // Default to 10 items

    const now = new Date()
    const futureDate = new Date()
    futureDate.setDate(now.getDate() + days)

    // Get upcoming events (renewals, expirations, deadlines)
    const upcomingEvents = await prisma.calendarEvent.findMany({
      where: {
        OR: [
          {
            dueDate: {
              gte: now,
              lte: futureDate,
            },
            status: {
              not: 'COMPLETED',
            },
          },
          {
            startDate: {
              gte: now,
              lte: futureDate,
            },
            type: {
              in: ['RENEWAL', 'EXPIRATION', 'DEADLINE', 'TASK_DUE', 'INVOICE_DUE', 'PAYMENT_DUE'],
            },
            status: {
              not: 'COMPLETED',
            },
          },
        ],
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: [
        { dueDate: 'asc' },
        { startDate: 'asc' },
      ],
      take: limit,
    })

    // Also get employee document expirations
    let employeeExpirations: any[] = []
    if (prisma.user) {
      try {
        const employees = await prisma.user.findMany({
          where: {
            OR: [
              {
                idCardExpiry: {
                  gte: now,
                  lte: futureDate,
                },
              },
              {
                passportExpiry: {
                  gte: now,
                  lte: futureDate,
                },
              },
              {
                visaExpiry: {
                  gte: now,
                  lte: futureDate,
                },
              },
              {
                licenseExpiry: {
                  gte: now,
                  lte: futureDate,
                },
              },
              {
                contractExpiry: {
                  gte: now,
                  lte: futureDate,
                },
              },
            ],
          },
          select: {
            id: true,
            name: true,
            email: true,
            idCardNumber: true,
            idCardExpiry: true,
            passportNumber: true,
            passportExpiry: true,
            visaNumber: true,
            visaExpiry: true,
            licenseNumber: true,
            licenseExpiry: true,
            contractExpiry: true,
          },
        })

        // Convert employee expirations to event-like format
        employeeExpirations = employees.flatMap(employee => {
          const expirations = []
          
          if (employee.idCardExpiry && employee.idCardExpiry >= now && employee.idCardExpiry <= futureDate) {
            expirations.push({
              id: `emp-id-${employee.id}`,
              title: `ID Card Renewal - ${employee.name}`,
              type: 'EXPIRATION',
              category: 'HR',
              dueDate: employee.idCardExpiry,
              priority: 'HIGH',
              relatedEntityType: 'employee',
              relatedEntityId: employee.id,
              assignedTo: employee,
            })
          }
          
          if (employee.passportExpiry && employee.passportExpiry >= now && employee.passportExpiry <= futureDate) {
            expirations.push({
              id: `emp-passport-${employee.id}`,
              title: `Passport Renewal - ${employee.name}`,
              type: 'EXPIRATION',
              category: 'HR',
              dueDate: employee.passportExpiry,
              priority: 'HIGH',
              relatedEntityType: 'employee',
              relatedEntityId: employee.id,
              assignedTo: employee,
            })
          }
          
          if (employee.visaExpiry && employee.visaExpiry >= now && employee.visaExpiry <= futureDate) {
            expirations.push({
              id: `emp-visa-${employee.id}`,
              title: `Visa Renewal - ${employee.name}`,
              type: 'EXPIRATION',
              category: 'HR',
              dueDate: employee.visaExpiry,
              priority: 'URGENT',
              relatedEntityType: 'employee',
              relatedEntityId: employee.id,
              assignedTo: employee,
            })
          }
          
          if (employee.licenseExpiry && employee.licenseExpiry >= now && employee.licenseExpiry <= futureDate) {
            expirations.push({
              id: `emp-license-${employee.id}`,
              title: `License Renewal - ${employee.name}`,
              type: 'EXPIRATION',
              category: 'HR',
              dueDate: employee.licenseExpiry,
              priority: 'MEDIUM',
              relatedEntityType: 'employee',
              relatedEntityId: employee.id,
              assignedTo: employee,
            })
          }
          
          if (employee.contractExpiry && employee.contractExpiry >= now && employee.contractExpiry <= futureDate) {
            expirations.push({
              id: `emp-contract-${employee.id}`,
              title: `Contract Renewal - ${employee.name}`,
              type: 'EXPIRATION',
              category: 'HR',
              dueDate: employee.contractExpiry,
              priority: 'HIGH',
              relatedEntityType: 'employee',
              relatedEntityId: employee.id,
              assignedTo: employee,
            })
          }
          
          return expirations
        })
      } catch (error) {
        console.error('Error fetching employee expirations:', error)
      }
    }

    // Combine and sort all upcoming items
    const allUpcoming = [...upcomingEvents, ...employeeExpirations]
      .sort((a, b) => {
        const dateA = new Date(a.dueDate || a.startDate)
        const dateB = new Date(b.dueDate || b.startDate)
        return dateA.getTime() - dateB.getTime()
      })
      .slice(0, limit)

    return NextResponse.json(allUpcoming)
  } catch (error) {
    console.error('Error fetching upcoming events:', error)
    // Return empty array if calendar model is not available
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    if (errorMessage?.includes('calendarEvent') || errorMessage?.includes('findMany')) {
      return NextResponse.json([])
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
