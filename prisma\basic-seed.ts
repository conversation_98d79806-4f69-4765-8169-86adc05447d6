import { PrismaClient } from '@prisma/client'
import * as bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database with basic data...')

  try {
    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 10)

    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Admin User',
        role: 'ADMIN',
        phone: '+968 24123456',
        isActive: true,
      },
    })

    // Create categories
    const printingCategory = await prisma.category.create({
      data: {
        name: 'Printing Services',
        nameAr: 'خدمات الطباعة',
        description: 'All printing related services',
      },
    })

    const suppliesCategory = await prisma.category.create({
      data: {
        name: 'Office Supplies',
        nameAr: 'المستلزمات المكتبية',
        description: 'Office supplies and materials',
      },
    })

    const designCategory = await prisma.category.create({
      data: {
        name: 'Design Services',
        nameAr: 'خدمات التصميم',
        description: 'Graphic design and creative services',
      },
    })

    const marketingCategory = await prisma.category.create({
      data: {
        name: 'Marketing Materials',
        nameAr: 'المواد التسويقية',
        description: 'Brochures, flyers, and promotional materials',
      },
    })

    // Create suppliers
    const existingSupplier = await prisma.supplier.findFirst({
      where: { mobile: '+968 91123456' }
    })

    const supplier1 = existingSupplier || await prisma.supplier.create({
      data: {
        name: 'Al Warak Trading LLC',
        email: '<EMAIL>',
        mobile: '+968 91123456',
        phone: '+968 24123456',
        company: 'Al Warak Trading LLC',
        address: 'Ruwi Commercial District, Muscat, Oman',
        contactPerson: 'Ahmed Al Rashid',
        taxNumber: 'OM1234567890',
        notes: 'Main paper and office supplies distributor',
      },
    })

    const existingSupplier2 = await prisma.supplier.findFirst({
      where: { mobile: '+968 92567890' }
    })

    const supplier2 = existingSupplier2 || await prisma.supplier.create({
      data: {
        name: 'Modern Office Solutions',
        email: '<EMAIL>',
        mobile: '+968 92567890',
        phone: '+968 24567890',
        company: 'Modern Office Solutions LLC',
        address: 'Al Qurum Commercial District, Muscat, Oman',
        contactPerson: 'Fatima Al Zahra',
        taxNumber: 'OM2345678901',
        notes: 'Office equipment and technology supplier',
      },
    })

    // Create products
    const product1 = await prisma.product.upsert({
      where: { sku: 'A4-PAPER-PREMIUM-001' },
      update: {},
      create: {
        name: 'A4 Paper - Premium',
        nameAr: 'ورق A4 - ممتاز',
        description: 'High quality A4 printing paper 80gsm',
        sku: 'A4-PAPER-PREMIUM-001',
        type: 'PHYSICAL',
        price: 2.500,
        costPrice: 1.800,
        currentStock: 1000,
        minStock: 100,
        unit: 'ream',
        categoryId: suppliesCategory.id,
        supplierId: supplier1.id,
      },
    })

    const product2 = await prisma.product.upsert({
      where: { sku: 'BC-PRINT-SERVICE-001' },
      update: {},
      create: {
        name: 'Business Card Printing',
        nameAr: 'طباعة بطاقات العمل',
        description: 'Professional business card printing service (500 cards)',
        sku: 'BC-PRINT-SERVICE-001',
        type: 'SERVICE',
        price: 15.000,
        costPrice: 8.000,
        currentStock: 0,
        minStock: 0,
        unit: 'set',
        categoryId: printingCategory.id,
      },
    })

    const product3 = await prisma.product.upsert({
      where: { sku: 'LOGO-DESIGN-SERVICE-001' },
      update: {},
      create: {
        name: 'Logo Design Service',
        nameAr: 'خدمة تصميم الشعار',
        description: 'Professional logo design service with 3 concepts and unlimited revisions',
        sku: 'LOGO-DESIGN-SERVICE-001',
        type: 'SERVICE',
        price: 50.000,
        costPrice: 25.000,
        currentStock: 0,
        minStock: 0,
        unit: 'project',
        categoryId: designCategory.id,
      },
    })

    // Create customers
    const existingCustomer1 = await prisma.customer.findFirst({
      where: { phone: '+968 24111222' }
    })

    const customer1 = existingCustomer1 || await prisma.customer.create({
      data: {
        name: 'Muscat Trading Company LLC',
        email: '<EMAIL>',
        mobile: '+968 24111222',
        phone: '+968 24111222',
        company: 'Muscat Trading Company LLC',
        address: 'Al Khuwair, Way 3018, Building 145, Muscat, Oman',
        taxNumber: 'OM1001234567',
        notes: 'Regular corporate customer, bulk printing orders',
      },
    })

    const existingCustomer2 = await prisma.customer.findFirst({
      where: { phone: '+968 99123456' }
    })

    const customer2 = existingCustomer2 || await prisma.customer.create({
      data: {
        name: 'Ahmed Al Kindi',
        email: '<EMAIL>',
        mobile: '+968 99123456',
        phone: '+968 99123456',
        company: 'Individual',
        address: 'Al Mawaleh, Muscat, Oman',
        notes: 'Individual customer, occasional printing needs',
      },
    })

    // Create a simple invoice
    const invoice1 = await prisma.invoice.upsert({
      where: { number: 'INV-2024-001' },
      update: {},
      create: {
        number: 'INV-2024-001',
        date: new Date('2024-01-15'),
        dueDate: new Date('2024-02-15'),
        status: 'PAID',
        subtotal: 45.000,
        taxAmount: 2.250,
        total: 47.250,
        customerId: customer1.id,
        userId: adminUser.id,
        notes: 'Business cards and paper order',
      },
    })

    // Create invoice items
    await prisma.invoiceItem.create({
      data: {
        description: 'Business Card Printing (500 cards)',
        quantity: 1,
        unitPrice: 15.000,
        total: 15.000,
        invoiceId: invoice1.id,
        productId: product2.id,
      },
    })

    await prisma.invoiceItem.create({
      data: {
        description: 'A4 Paper - Premium (10 reams)',
        quantity: 10,
        unitPrice: 2.500,
        total: 25.000,
        invoiceId: invoice1.id,
        productId: product1.id,
      },
    })

    // Create a task
    await prisma.task.create({
      data: {
        title: 'Print Business Cards for Muscat Trading',
        description: 'Design and print 500 business cards with company logo',
        status: 'COMPLETED',
        priority: 'HIGH',
        startTime: new Date('2024-01-15'),
        endTime: new Date('2024-01-20'),
        estimatedHours: 8.0,
        actualHours: 6.5,
        customerId: customer1.id,
        assignedToId: adminUser.id,
        createdById: adminUser.id,
        notes: 'Completed successfully with customer approval',
      },
    })

    // Create settings
    await prisma.setting.upsert({
      where: { key: 'company_name' },
      update: { value: 'Muscat Print & Design Center' },
      create: {
        key: 'company_name',
        value: 'Muscat Print & Design Center',
        description: 'Company name',
      },
    })

    await prisma.setting.upsert({
      where: { key: 'default_currency' },
      update: { value: 'OMR' },
      create: {
        key: 'default_currency',
        value: 'OMR',
        description: 'Default currency (Omani Rial)',
      },
    })

    console.log('✅ Basic database seeded successfully!')
    console.log('👤 Admin user: <EMAIL> / admin123')
    console.log('📊 Created: 4 categories, 2 suppliers, 3 products, 2 customers, 1 invoice, 1 task')
    console.log('💰 Currency: Omani Rial (OMR)')
    console.log('🌍 Location: Muscat, Sultanate of Oman')

  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
