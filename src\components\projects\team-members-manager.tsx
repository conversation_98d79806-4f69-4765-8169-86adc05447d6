"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Plus, X, Users, UserPlus } from "lucide-react"

interface TeamMember {
  id: string
  role?: string
  joinedAt: string
  user: {
    id: string
    name: string
    email: string
    phone?: string
    role: string
  }
}

interface User {
  id: string
  name: string
  email: string
  role: string
}

interface TeamMembersManagerProps {
  projectId: string
  isEditable?: boolean
}

export function TeamMembersManager({ projectId, isEditable = true }: TeamMembersManagerProps) {
  const [members, setMembers] = useState<TeamMember[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [addingMember, setAddingMember] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)

  const [newMember, setNewMember] = useState({
    userId: '',
    role: '',
  })

  useEffect(() => {
    fetchMembers()
    if (isEditable) {
      fetchUsers()
    }
  }, [projectId, isEditable])

  const fetchMembers = async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}/members`)
      if (response.ok) {
        const data = await response.json()
        setMembers(data)
      }
    } catch (error) {
      console.error('Error fetching team members:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users')
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users || data)
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    }
  }

  const handleAddMember = async () => {
    if (!newMember.userId) return

    try {
      setAddingMember(true)
      const response = await fetch(`/api/projects/${projectId}/members`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newMember),
      })

      if (response.ok) {
        await fetchMembers()
        setNewMember({ userId: '', role: '' })
        setDialogOpen(false)
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error adding team member:', error)
      alert('Error adding team member')
    } finally {
      setAddingMember(false)
    }
  }

  const handleRemoveMember = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this team member?')) return

    try {
      const response = await fetch(`/api/projects/${projectId}/members?userId=${userId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchMembers()
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error removing team member:', error)
      alert('Error removing team member')
    }
  }

  const getAvailableUsers = () => {
    const memberUserIds = members.map(m => m.user.id)
    return users.filter(user => !memberUserIds.includes(user.id))
  }

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getRoleColor = (role?: string) => {
    switch (role?.toLowerCase()) {
      case 'developer':
        return 'bg-blue-100 text-blue-800'
      case 'designer':
        return 'bg-purple-100 text-purple-800'
      case 'tester':
        return 'bg-green-100 text-green-800'
      case 'manager':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Team Members</h3>
          <Badge variant="secondary">{members.length}</Badge>
        </div>
        
        {isEditable && (
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                Add Member
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Team Member</DialogTitle>
                <DialogDescription>
                  Add a new member to this project team
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="user">Team Member</Label>
                  <Select value={newMember.userId} onValueChange={(value) => setNewMember(prev => ({ ...prev, userId: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a user" />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableUsers().map(user => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.name} ({user.email})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="role">Role (Optional)</Label>
                  <Input
                    id="role"
                    placeholder="e.g., Developer, Designer, Tester"
                    value={newMember.role}
                    onChange={(e) => setNewMember(prev => ({ ...prev, role: e.target.value }))}
                  />
                </div>
                
                <div className="flex gap-2">
                  <Button onClick={handleAddMember} disabled={!newMember.userId || addingMember} className="flex-1">
                    {addingMember ? 'Adding...' : 'Add Member'}
                  </Button>
                  <Button variant="outline" onClick={() => setDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {members.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Users className="mx-auto h-12 w-12 mb-3 opacity-50" />
          <p>No team members assigned yet</p>
          {isEditable && (
            <p className="text-sm">Click &quot;Add Member&quot; to start building your team</p>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {members.map((member) => (
            <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarFallback className="bg-primary/10 text-primary font-medium">
                    {getUserInitials(member.user.name)}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{member.user.name}</span>
                    {member.role && (
                      <Badge variant="secondary" className={getRoleColor(member.role)}>
                        {member.role}
                      </Badge>
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {member.user.email}
                    {member.user.phone && ` • ${member.user.phone}`}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {member.user.role}
                </Badge>
                {isEditable && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveMember(member.user.id)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
} 