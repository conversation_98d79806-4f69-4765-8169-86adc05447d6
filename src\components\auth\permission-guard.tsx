"use client"

import { ReactNode } from 'react'
import { useHasPermission, useHasAnyPermission, useHasAllPermissions } from '@/hooks/use-permissions'

interface PermissionGuardProps {
  children: ReactNode
  fallback?: ReactNode
  module?: string
  action?: string
  resource?: string
  permissions?: Array<{ module: string; action: string; resource?: string }>
  requireAll?: boolean // If true, requires all permissions; if false, requires any permission
}

export function PermissionGuard({
  children,
  fallback = null,
  module,
  action,
  resource,
  permissions,
  requireAll = false,
}: PermissionGuardProps) {
  // Always call hooks at the top level
  const singlePermission = useHasPermission(module || '', action || '', resource)
  const allPermissions = useHasAllPermissions(permissions || [])
  const anyPermission = useHasAnyPermission(permissions || [])

  let hasPermission = false

  if (module && action) {
    // Single permission check
    hasPermission = singlePermission
  } else if (permissions && permissions.length > 0) {
    // Multiple permissions check
    if (requireAll) {
      hasPermission = allPermissions
    } else {
      hasPermission = anyPermission
    }
  } else {
    // No permission specified, allow access
    hasPermission = true
  }

  return hasPermission ? <>{children}</> : <>{fallback}</>
}

// Convenience components for common use cases
export function ViewPermission({ 
  module, 
  children, 
  fallback 
}: { 
  module: string
  children: ReactNode
  fallback?: ReactNode 
}) {
  return (
    <PermissionGuard module={module} action="view" fallback={fallback}>
      {children}
    </PermissionGuard>
  )
}

export function CreatePermission({ 
  module, 
  children, 
  fallback 
}: { 
  module: string
  children: ReactNode
  fallback?: ReactNode 
}) {
  return (
    <PermissionGuard module={module} action="create" fallback={fallback}>
      {children}
    </PermissionGuard>
  )
}

export function EditPermission({ 
  module, 
  children, 
  fallback 
}: { 
  module: string
  children: ReactNode
  fallback?: ReactNode 
}) {
  return (
    <PermissionGuard module={module} action="edit" fallback={fallback}>
      {children}
    </PermissionGuard>
  )
}

export function DeletePermission({ 
  module, 
  children, 
  fallback 
}: { 
  module: string
  children: ReactNode
  fallback?: ReactNode 
}) {
  return (
    <PermissionGuard module={module} action="delete" fallback={fallback}>
      {children}
    </PermissionGuard>
  )
}

// Admin-only component
export function AdminOnly({ 
  children, 
  fallback 
}: { 
  children: ReactNode
  fallback?: ReactNode 
}) {
  return (
    <PermissionGuard 
      permissions={[
        { module: 'users', action: 'view' },
        { module: 'roles', action: 'view' },
        { module: 'settings', action: 'edit' },
      ]}
      requireAll={false}
      fallback={fallback}
    >
      {children}
    </PermissionGuard>
  )
}
