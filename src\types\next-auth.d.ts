import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      name?: string | null
      email?: string | null
      image?: string | null
      role?: string
      phone?: string
      avatar?: string
    }
  }

  interface User {
    id: string
    email: string
    name: string
    role: string
    phone?: string
    avatar?: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id?: string
    role?: string
    phone?: string
    avatar?: string
    isActive?: boolean
    lastActivity?: number
  }
}
