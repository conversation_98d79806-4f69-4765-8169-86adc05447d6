import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const quotation = await prisma.quotation.findUnique({
      where: { id },
      include: {
        customer: true,
        user: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    if (!quotation) {
      return NextResponse.json(
        { error: 'Quotation not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(quotation)
  } catch (error) {
    console.error('Error fetching quotation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const {
      customerId,
      validUntil,
      items,
      taxAmount,
      discount,
      notes,
      status,
    } = body

    // Check if quotation exists
    const existingQuotation = await prisma.quotation.findUnique({
      where: { id },
    })

    if (!existingQuotation) {
      return NextResponse.json(
        { error: 'Quotation not found' },
        { status: 404 }
      )
    }

    // Calculate totals if items are provided
    let updateData: any = {
      notes,
      status,
    }

    if (customerId) {
      updateData.customerId = customerId
    }

    if (validUntil) {
      updateData.validUntil = new Date(validUntil)
    }

    if (items && items.length > 0) {
      const subtotal = items.reduce((sum: number, item: { quantity: string; unitPrice: string }) =>
        sum + (parseFloat(item.quantity) * parseFloat(item.unitPrice)), 0
      )

      const calculatedTaxAmount = taxAmount || (subtotal * 0.15)
      const calculatedDiscount = discount || 0
      const total = subtotal + calculatedTaxAmount - calculatedDiscount

      updateData = {
        ...updateData,
        subtotal,
        taxAmount: calculatedTaxAmount,
        discount: calculatedDiscount,
        total,
      }
    }

    const quotation = await prisma.quotation.update({
      where: { id },
      data: updateData,
      include: {
        customer: true,
        user: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    // Update items if provided
    if (items && items.length > 0) {
      // Delete existing items
      await prisma.quotationItem.deleteMany({
        where: { quotationId: id },
      })

      // Create new items
      await prisma.quotationItem.createMany({
        data: items.map((item: { description: string; quantity: string; unitPrice: string; productId?: string }) => ({
          quotationId: id,
          description: item.description,
          quantity: parseFloat(item.quantity),
          unitPrice: parseFloat(item.unitPrice),
          total: parseFloat(item.quantity) * parseFloat(item.unitPrice),
          productId: item.productId || null,
        })),
      })
    }

    return NextResponse.json(quotation)
  } catch (error) {
    console.error('Error updating quotation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if quotation exists
    const existingQuotation = await prisma.quotation.findUnique({
      where: { id },
    })

    if (!existingQuotation) {
      return NextResponse.json(
        { error: 'Quotation not found' },
        { status: 404 }
      )
    }

    // Delete quotation items first
    await prisma.quotationItem.deleteMany({
      where: { quotationId: id },
    })

    // Delete quotation
    await prisma.quotation.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Quotation deleted successfully' })
  } catch (error) {
    console.error('Error deleting quotation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
