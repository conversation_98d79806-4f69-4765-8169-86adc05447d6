import { format, formatDistanceToNow, parseISO } from 'date-fns'
import { ar, enUS } from 'date-fns/locale'

// Omani Rial currency formatting
export const formatCurrency = (amount: number | null | undefined, locale: string = 'en'): string => {
  // Handle null, undefined, or invalid values
  if (amount === null || amount === undefined || isNaN(amount)) {
    amount = 0
  }
  
  const formatter = new Intl.NumberFormat(locale === 'ar' ? 'ar-OM' : 'en-OM', {
    style: 'currency',
    currency: 'OMR',
    minimumFractionDigits: 3,
    maximumFractionDigits: 3,
  })
  
  return formatter.format(amount)
}

// Format numbers for Omani locale
export const formatNumber = (number: number, locale: string = 'en'): string => {
  const formatter = new Intl.NumberFormat(locale === 'ar' ? 'ar-OM' : 'en-OM')
  return formatter.format(number)
}

// Format dates for Omani timezone
export const formatDate = (date: Date | string, locale: string = 'en', formatStr: string = 'PPP'): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  
  // Convert to Muscat timezone
  const muscatDate = new Date(dateObj.toLocaleString("en-US", {timeZone: "Asia/Muscat"}))
  
  return format(muscatDate, formatStr, {
    locale: locale === 'ar' ? ar : enUS
  })
}

// Format date and time for Omani timezone
export const formatDateTime = (date: Date | string, locale: string = 'en'): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  
  // Convert to Muscat timezone
  const muscatDate = new Date(dateObj.toLocaleString("en-US", {timeZone: "Asia/Muscat"}))
  
  return format(muscatDate, 'PPP p', {
    locale: locale === 'ar' ? ar : enUS
  })
}

// Format relative time (e.g., "2 hours ago")
export const formatRelativeTime = (date: Date | string, locale: string = 'en'): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  
  return formatDistanceToNow(dateObj, {
    addSuffix: true,
    locale: locale === 'ar' ? ar : enUS
  })
}

// Get current time in Muscat timezone
export const getCurrentMuscatTime = (): Date => {
  return new Date(new Date().toLocaleString("en-US", {timeZone: "Asia/Muscat"}))
}

// Omani phone number formatting
export const formatOmaniPhone = (phone: string): string => {
  // Remove all non-digits
  const cleaned = phone.replace(/\D/g, '')
  
  // Check if it's an Omani number
  if (cleaned.startsWith('968')) {
    // Format: +968 XX XXX XXX
    return `+968 ${cleaned.slice(3, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`
  } else if (cleaned.length === 8 && (cleaned.startsWith('9') || cleaned.startsWith('7') || cleaned.startsWith('2'))) {
    // Local Omani number, add country code
    return `+968 ${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5)}`
  }
  
  return phone // Return original if not recognized as Omani
}

// Validate Omani phone number
export const isValidOmaniPhone = (phone: string): boolean => {
  const cleaned = phone.replace(/\D/g, '')
  
  // Check if it's a valid Omani number
  if (cleaned.startsWith('968') && cleaned.length === 11) {
    const localPart = cleaned.slice(3)
    return localPart.startsWith('9') || localPart.startsWith('7') || localPart.startsWith('2')
  } else if (cleaned.length === 8) {
    return cleaned.startsWith('9') || cleaned.startsWith('7') || cleaned.startsWith('2')
  }
  
  return false
}

// Omani business hours check
export const isBusinessHours = (): boolean => {
  const now = getCurrentMuscatTime()
  const hour = now.getHours()
  const day = now.getDay()
  
  // Sunday to Thursday: 8 AM to 6 PM
  // Friday: 2 PM to 6 PM (after Jummah prayer)
  // Saturday: Closed
  
  if (day === 6) return false // Saturday closed
  if (day === 5) return hour >= 14 && hour < 18 // Friday 2 PM - 6 PM
  return hour >= 8 && hour < 18 // Sunday-Thursday 8 AM - 6 PM
}

// Get business hours text
export const getBusinessHoursText = (locale: string = 'en'): string => {
  if (locale === 'ar') {
    return 'الأحد - الخميس: 8:00 ص - 6:00 م\nالجمعة: 2:00 م - 6:00 م\nالسبت: مغلق'
  }
  return 'Sunday - Thursday: 8:00 AM - 6:00 PM\nFriday: 2:00 PM - 6:00 PM\nSaturday: Closed'
}

// Omani tax calculation (5% VAT)
export const calculateOmaniTax = (amount: number): number => {
  return amount * 0.05
}

// Format Omani Civil ID
export const formatOmaniCivilId = (civilId: string): string => {
  const cleaned = civilId.replace(/\D/g, '')
  if (cleaned.length === 8) {
    return `${cleaned.slice(0, 4)}-${cleaned.slice(4)}`
  }
  return civilId
}

// Validate Omani Civil ID
export const isValidOmaniCivilId = (civilId: string): boolean => {
  const cleaned = civilId.replace(/\D/g, '')
  return cleaned.length === 8
}

// Omani address formatting
export const formatOmaniAddress = (address: {
  building?: string
  street?: string
  area?: string
  wilayat?: string
  governorate?: string
  postalCode?: string
}, locale: string = 'en'): string => {
  const parts = []
  
  if (address.building) parts.push(address.building)
  if (address.street) parts.push(address.street)
  if (address.area) parts.push(address.area)
  if (address.wilayat) parts.push(address.wilayat)
  if (address.governorate) parts.push(address.governorate)
  if (address.postalCode) parts.push(address.postalCode)
  
  return parts.join(locale === 'ar' ? '، ' : ', ')
}

// Common Omani governorates
export const omaniGovernorates = {
  en: [
    'Muscat',
    'Dhofar',
    'Al Batinah North',
    'Al Batinah South',
    'Al Sharqiyah North',
    'Al Sharqiyah South',
    'Ad Dakhiliyah',
    'Ad Dhahirah',
    'Al Buraimi',
    'Al Wusta',
    'Musandam'
  ],
  ar: [
    'مسقط',
    'ظفار',
    'شمال الباطنة',
    'جنوب الباطنة',
    'شمال الشرقية',
    'جنوب الشرقية',
    'الداخلية',
    'الظاهرة',
    'البريمي',
    'الوسطى',
    'مسندم'
  ]
}

// Get prayer times notification text
export const getPrayerTimesText = (locale: string = 'en'): string => {
  if (locale === 'ar') {
    return 'يرجى مراعاة أوقات الصلاة عند التواصل مع العملاء'
  }
  return 'Please consider prayer times when contacting customers'
}

// Check if current time is near prayer time (approximate)
export const isNearPrayerTime = (): boolean => {
  const now = getCurrentMuscatTime()
  const hour = now.getHours()
  const minute = now.getMinutes()
  
  // Approximate prayer times for Muscat (these would ideally come from an API)
  const prayerTimes = [
    { hour: 5, minute: 30 }, // Fajr
    { hour: 12, minute: 15 }, // Dhuhr
    { hour: 15, minute: 30 }, // Asr
    { hour: 18, minute: 0 }, // Maghrib
    { hour: 19, minute: 30 }, // Isha
  ]
  
  return prayerTimes.some(prayer => {
    const timeDiff = Math.abs((hour * 60 + minute) - (prayer.hour * 60 + prayer.minute))
    return timeDiff <= 15 // Within 15 minutes of prayer time
  })
}
