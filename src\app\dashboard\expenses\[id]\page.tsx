"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  ArrowLeft,
  Edit,
  Receipt,
  Calendar,
  User,
  DollarSign,
  Clock,
  CheckCircle,
  X,
  Download,
  MoreHorizontal,
  Trash2
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

interface Expense {
  id: string
  number: string
  date: string
  description: string
  amount: number
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID'
  paymentMethod: string
  receipt?: string
  notes?: string
  createdAt: string
  updatedAt: string
  expenseType: {
    id: string
    name: string
    nameAr?: string
    description?: string
  }
  createdBy: {
    id: string
    name: string
    email: string
  }
  project?: {
    id: string
    name: string
    code: string
  }
}

export default function ExpenseDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { t } = useI18n()
  const [expense, setExpense] = useState<Expense | null>(null)
  const [loading, setLoading] = useState(true)

  const statusColors = {
    PENDING: "bg-yellow-100 text-yellow-800",
    APPROVED: "bg-blue-100 text-blue-800",
    REJECTED: "bg-red-100 text-red-800",
    PAID: "bg-green-100 text-green-800",
  }

  const statusIcons = {
    PENDING: Clock,
    APPROVED: CheckCircle,
    REJECTED: X,
    PAID: DollarSign,
  }

  useEffect(() => {
    if (params.id) {
      fetchExpense(params.id as string)
    }
  }, [params.id])

  const fetchExpense = async (id: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/expenses/${id}`)
      if (response.ok) {
        const data = await response.json()
        setExpense(data)
      } else {
        console.error('Failed to fetch expense')
        setExpense(null)
      }
    } catch (error) {
      console.error('Error fetching expense:', error)
      setExpense(null)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async () => {
    if (!expense) return
    
    try {
      const response = await fetch(`/api/expenses/${expense.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'APPROVED' }),
      })

      if (response.ok) {
        const updatedExpense = await response.json()
        setExpense(updatedExpense)
        alert(t('expenses.view.expenseApprovedSuccess'))
      } else {
        alert(t('expenses.view.expenseApprovedError'))
      }
    } catch (error) {
      console.error('Error approving expense:', error)
      alert(t('expenses.view.expenseApprovedError'))
    }
  }

  const handleReject = async () => {
    if (!expense) return
    
    const reason = prompt(t('expenses.view.pleaseEnterRejectionReason'))
    if (reason) {
      try {
        const response = await fetch(`/api/expenses/${expense.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            status: 'REJECTED',
            notes: reason 
          }),
        })

        if (response.ok) {
          const updatedExpense = await response.json()
          setExpense(updatedExpense)
          alert(t('expenses.view.expenseRejected'))
        } else {
          alert(t('expenses.view.expenseRejectedError'))
        }
      } catch (error) {
        console.error('Error rejecting expense:', error)
        alert(t('expenses.view.expenseRejectedError'))
      }
    }
  }

  const handleMarkAsPaid = async () => {
    if (!expense) return
    
    try {
      const response = await fetch(`/api/expenses/${expense.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'PAID' }),
      })

      if (response.ok) {
        const updatedExpense = await response.json()
        setExpense(updatedExpense)
        alert(t('expenses.view.expenseMarkedAsPaidSuccess'))
      } else {
        alert(t('expenses.view.expenseMarkedAsPaidError'))
      }
    } catch (error) {
      console.error('Error marking expense as paid:', error)
      alert(t('expenses.view.expenseMarkedAsPaidError'))
    }
  }

  const handleEdit = () => {
    router.push(`/dashboard/expenses/${expense?.id}/edit`)
  }

  const handleDelete = async () => {
    if (!expense) return
    
    if (confirm(`${t('expenses.view.confirmDeleteExpense')} ${expense.number}?`)) {
      try {
        const response = await fetch(`/api/expenses/${expense.id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          alert(t('expenses.view.expenseDeletedSuccess'))
          router.push('/dashboard/expenses')
        } else {
          alert(t('expenses.view.expenseDeletedError'))
        }
      } catch (error) {
        console.error('Error deleting expense:', error)
        alert(t('expenses.view.expenseDeletedError'))
      }
    }
  }

  const handleDownloadReceipt = () => {
    if (expense?.receipt) {
      console.log('Downloading receipt:', expense.receipt)
      alert(t('expenses.view.receiptDownloadInfo'))
    } else {
      alert(t('expenses.view.noReceiptAvailable'))
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="text-center py-8">{t('expenses.view.loadingExpense')}</div>
      </div>
    )
  }

  if (!expense) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="text-center py-8">{t('expenses.view.expenseNotFound')}</div>
      </div>
    )
  }

  const StatusIcon = statusIcons[expense.status]

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('expenses.view.back')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{t('expenses.view.title')} {expense.number}</h1>
            <p className="text-muted-foreground">
              {t('expenses.view.createdOn')} {new Date(expense.date).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge className={statusColors[expense.status]}>
            {t(`expenses.statusLabels.${expense.status}`)}
          </Badge>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                {t('expenses.view.actions')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t('expenses.view.editExpense')}
              </DropdownMenuItem>
              {expense.receipt && (
                <DropdownMenuItem onClick={handleDownloadReceipt}>
                  <Download className="mr-2 h-4 w-4" />
                  {t('expenses.view.downloadReceipt')}
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {expense.status === 'PENDING' && (
                <>
                  <DropdownMenuItem
                    onClick={handleApprove}
                    className="text-green-600 focus:text-green-600"
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    {t('expenses.view.approve')}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleReject}
                    className="text-red-600 focus:text-red-600"
                  >
                    <X className="mr-2 h-4 w-4" />
                    {t('expenses.view.reject')}
                  </DropdownMenuItem>
                </>
              )}
              {expense.status === 'APPROVED' && (
                <DropdownMenuItem
                  onClick={handleMarkAsPaid}
                  className="text-blue-600 focus:text-blue-600"
                >
                  <DollarSign className="mr-2 h-4 w-4" />
                  {t('expenses.view.markAsPaid')}
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {t('expenses.view.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Expense Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('expenses.view.amount')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{formatCurrency(expense.amount)}</div>
            <p className="text-xs text-muted-foreground">{t('expenses.view.expenseAmount')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('expenses.view.status')}</CardTitle>
            <StatusIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{t(`expenses.statusLabels.${expense.status}`)}</div>
            <p className="text-xs text-muted-foreground">{t('expenses.view.currentStatus')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('expenses.view.type')}</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{expense.expenseType.name}</div>
            <p className="text-xs text-muted-foreground">{t('expenses.view.expenseCategory')}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('expenses.view.method')}</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{t(`expenses.paymentMethodLabels.${expense.paymentMethod}`)}</div>
            <p className="text-xs text-muted-foreground">{t('expenses.view.paymentMethod')}</p>
          </CardContent>
        </Card>
      </div>

      {/* Expense Details */}
      <Card>
        <CardHeader>
          <CardTitle>{t('expenses.view.expenseDetails')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">{t('expenses.view.description')}</h4>
                <p className="font-medium">{expense.description}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">{t('expenses.view.date')}</h4>
                <p className="font-medium">{new Date(expense.date).toLocaleDateString()}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">{t('expenses.view.createdBy')}</h4>
                <p className="font-medium">{expense.createdBy.name}</p>
                <p className="text-sm text-muted-foreground">{expense.createdBy.email}</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">{t('expenses.view.expenseType')}</h4>
                <p className="font-medium">{expense.expenseType.name}</p>
                {expense.expenseType.description && (
                  <p className="text-sm text-muted-foreground">{expense.expenseType.description}</p>
                )}
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">{t('expenses.view.paymentMethod')}</h4>
                <p className="font-medium">{t(`expenses.paymentMethodLabels.${expense.paymentMethod}`)}</p>
              </div>
              {expense.project && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">{t('expenses.view.project')}</h4>
                  <p className="font-medium">{expense.project.name}</p>
                  <p className="text-sm text-muted-foreground">{t('expenses.view.code')}: {expense.project.code}</p>
                </div>
              )}
            </div>
          </div>

          {expense.notes && (
            <>
              <Separator />
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-2">{t('expenses.view.notes')}</h4>
                <p className="text-sm">{expense.notes}</p>
              </div>
            </>
          )}

          {expense.receipt && (
            <>
              <Separator />
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-2">{t('expenses.view.receipt')}</h4>
                <Button variant="outline" onClick={handleDownloadReceipt}>
                  <Download className="mr-2 h-4 w-4" />
                  {t('expenses.view.downloadReceipt')}
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>{t('expenses.view.activityTimeline')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">{t('expenses.view.expenseCreated')}</p>
                <p className="text-xs text-muted-foreground">
                  {new Date(expense.createdAt).toLocaleString()} {t('expenses.view.by')} {expense.createdBy.name}
                </p>
              </div>
            </div>
            
            {expense.updatedAt !== expense.createdAt && (
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{t('expenses.view.expenseUpdated')}</p>
                  <p className="text-xs text-muted-foreground">
                    {new Date(expense.updatedAt).toLocaleString()}
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}