export const system = {
  // Success messages
  success: {
    created: "تم الإنشاء بنجاح",
    updated: "تم التحديث بنجاح",
    deleted: "تم الحذف بنجاح",
    saved: "تم الحفظ بنجاح",
    uploaded: "تم الرفع بنجاح",
    sent: "تم الإرسال بنجاح",
    approved: "تم الموافقة بنجاح",
    rejected: "تم الرفض بنجاح",
    completed: "تم الإكمال بنجاح",
    processed: "تم المعالجة بنجاح",
    
    // Specific success messages
    customerCreated: "تم إنشاء العميل بنجاح",
    customerUpdated: "تم تحديث العميل بنجاح",
    customerDeleted: "تم حذف العميل بنجاح",
    supplierCreated: "تم إنشاء المورد بنجاح",
    supplierUpdated: "تم تحديث المورد بنجاح",
    supplierDeleted: "تم حذف المورد بنجاح",
    productCreated: "تم إنشاء المنتج بنجاح",
    productUpdated: "تم تحديث المنتج بنجاح",
    productDeleted: "تم حذف المنتج بنجاح",
    invoiceCreated: "تم إنشاء الفاتورة بنجاح",
    invoiceUpdated: "تم تحديث الفاتورة بنجاح",
    invoiceDeleted: "تم حذف الفاتورة بنجاح",
    expenseApproved: "تم الموافقة على المصروف بنجاح",
    expenseRejected: "تم رفض المصروف بنجاح",
    expenseMarkedAsPaid: "تم تسجيل المصروف كمدفوع بنجاح",
    paymentRecorded: "تم تسجيل الدفعة بنجاح",
    employeeCreated: "تم إنشاء الموظف بنجاح",
    employeeUpdated: "تم تحديث الموظف بنجاح",
    employeeDeleted: "تم حذف الموظف بنجاح",
    settingsSaved: "تم حفظ الإعدادات بنجاح",
    imageUploaded: "تم رفع الصورة بنجاح",
    fileUploaded: "تم رفع الملف بنجاح",
    linkCopied: "تم نسخ الرابط بنجاح",
  },

  // Error messages
  error: {
    general: "حدث خطأ. يرجى المحاولة مرة أخرى",
    networkError: "خطأ في الشبكة. يرجى التحقق من الاتصال",
    serverError: "خطأ في الخادم. يرجى المحاولة لاحقاً",
    unauthorized: "وصول غير مصرح",
    forbidden: "الوصول محظور",
    notFound: "غير موجود",
    validationError: "خطأ في التحقق",
    
    // Specific error messages
    failedToCreate: "فشل في الإنشاء",
    failedToUpdate: "فشل في التحديث",
    failedToDelete: "فشل في الحذف",
    failedToSave: "فشل في الحفظ",
    failedToUpload: "فشل في الرفع",
    failedToSend: "فشل في الإرسال",
    failedToApprove: "فشل في الموافقة",
    failedToReject: "فشل في الرفض",
    failedToComplete: "فشل في الإكمال",
    failedToProcess: "فشل في المعالجة",
    
    // Entity specific errors
    failedToCreateCustomer: "فشل في إنشاء العميل",
    failedToUpdateCustomer: "فشل في تحديث العميل",
    failedToDeleteCustomer: "فشل في حذف العميل",
    failedToCreateSupplier: "فشل في إنشاء المورد",
    failedToUpdateSupplier: "فشل في تحديث المورد",
    failedToDeleteSupplier: "فشل في حذف المورد",
    failedToCreateProduct: "فشل في إنشاء المنتج",
    failedToUpdateProduct: "فشل في تحديث المنتج",
    failedToDeleteProduct: "فشل في حذف المنتج",
    failedToCreateInvoice: "فشل في إنشاء الفاتورة",
    failedToUpdateInvoice: "فشل في تحديث الفاتورة",
    failedToDeleteInvoice: "فشل في حذف الفاتورة",
    failedToApproveExpense: "فشل في الموافقة على المصروف",
    failedToRejectExpense: "فشل في رفض المصروف",
    failedToMarkExpenseAsPaid: "فشل في تسجيل المصروف كمدفوع",
    failedToRecordPayment: "فشل في تسجيل الدفعة",
    failedToCreateEmployee: "فشل في إنشاء الموظف",
    failedToUpdateEmployee: "فشل في تحديث الموظف",
    failedToDeleteEmployee: "فشل في حذف الموظف",
    failedToSaveSettings: "فشل في حفظ الإعدادات",
    failedToUploadImage: "فشل في رفع الصورة",
    failedToUploadFile: "فشل في رفع الملف",
  },

  // Validation messages
  validation: {
    required: "هذا الحقل مطلوب",
    email: "يرجى إدخال عنوان بريد إلكتروني صحيح",
    phone: "يرجى إدخال رقم هاتف صحيح",
    password: "كلمة المرور يجب أن تكون 8 أحرف على الأقل",
    passwordMismatch: "كلمات المرور غير متطابقة",
    minLength: "يجب أن يكون {min} أحرف على الأقل",
    maxLength: "يجب أن يكون {max} أحرف على الأكثر",
    minValue: "القيمة يجب أن تكون أكبر من {min}",
    maxValue: "القيمة يجب أن تكون أقل من {max}",
    invalidFormat: "تنسيق غير صحيح",
    invalidDate: "تاريخ غير صحيح",
    invalidNumber: "رقم غير صحيح",
    invalidUrl: "رابط غير صحيح",
    
    // Specific validation messages
    nameRequired: "الاسم مطلوب",
    emailRequired: "البريد الإلكتروني مطلوب",
    phoneRequired: "رقم الهاتف مطلوب",
    passwordRequired: "كلمة المرور مطلوبة",
    amountRequired: "المبلغ مطلوب",
    quantityRequired: "الكمية مطلوبة",
    priceRequired: "السعر مطلوب",
    dateRequired: "التاريخ مطلوب",
    categoryRequired: "الفئة مطلوبة",
    supplierRequired: "المورد مطلوب",
    customerRequired: "العميل مطلوب",
    productRequired: "المنتج مطلوب",
    descriptionRequired: "الوصف مطلوب",
    
    // File validation
    fileRequired: "الملف مطلوب",
    fileSizeExceeded: "حجم الملف كبير جداً",
    invalidFileType: "نوع الملف غير مدعوم",
    imageRequired: "الصورة مطلوبة",
    imageSizeExceeded: "حجم الصورة كبير جداً",
    invalidImageType: "نوع الصورة غير مدعوم",
    
    // Business logic validation
    insufficientStock: "المخزون غير كافي",
    insufficientBalance: "الرصيد غير كافي",
    duplicateEntry: "هذا الإدخال موجود بالفعل",
    invalidQuantity: "كمية غير صحيحة",
    invalidPrice: "سعر غير صحيح",
    invalidAmount: "مبلغ غير صحيح",
    exceedsMaximum: "يتجاوز الحد الأقصى",
    belowMinimum: "أقل من الحد الأدنى",
  },

  // Confirmation messages
  confirmation: {
    delete: "هل أنت متأكد من الحذف؟",
    save: "هل تريد حفظ التغييرات؟",
    cancel: "هل تريد الإلغاء؟",
    approve: "هل تريد الموافقة؟",
    reject: "هل تريد الرفض؟",
    submit: "هل تريد الإرسال؟",
    
    // Specific confirmations
    deleteCustomer: "هل أنت متأكد من حذف هذا العميل؟",
    deleteSupplier: "هل أنت متأكد من حذف هذا المورد؟",
    deleteProduct: "هل أنت متأكد من حذف هذا المنتج؟",
    deleteInvoice: "هل أنت متأكد من حذف هذه الفاتورة؟",
    deleteEmployee: "هل أنت متأكد من حذف هذا الموظف؟",
    approveExpense: "هل تريد الموافقة على هذا المصروف؟",
    rejectExpense: "هل تريد رفض هذا المصروف؟",
    markAsPaid: "هل تريد تسجيل هذا كمدفوع؟",
    resetSettings: "هل تريد إعادة تعيين الإعدادات؟",
    logout: "هل تريد تسجيل الخروج؟",
  },

  // Loading states
  loading: {
    general: "جاري التحميل...",
    saving: "جاري الحفظ...",
    deleting: "جاري الحذف...",
    uploading: "جاري الرفع...",
    processing: "جاري المعالجة...",
    sending: "جاري الإرسال...",
    loading: "جاري التحميل...",
    
    // Specific loading states
    creatingCustomer: "جاري إنشاء العميل...",
    updatingCustomer: "جاري تحديث العميل...",
    deletingCustomer: "جاري حذف العميل...",
    creatingSupplier: "جاري إنشاء المورد...",
    updatingSupplier: "جاري تحديث المورد...",
    deletingSupplier: "جاري حذف المورد...",
    creatingProduct: "جاري إنشاء المنتج...",
    updatingProduct: "جاري تحديث المنتج...",
    deletingProduct: "جاري حذف المنتج...",
    creatingInvoice: "جاري إنشاء الفاتورة...",
    updatingInvoice: "جاري تحديث الفاتورة...",
    deletingInvoice: "جاري حذف الفاتورة...",
    uploadingImage: "جاري رفع الصورة...",
    uploadingFile: "جاري رفع الملف...",
    savingSettings: "جاري حفظ الإعدادات...",
    signingIn: "جاري تسجيل الدخول...",
    signingOut: "جاري تسجيل الخروج...",
  },

  // Status messages
  status: {
    online: "متصل",
    offline: "غير متصل",
    connected: "متصل",
    disconnected: "منقطع",
    syncing: "جاري المزامنة",
    synced: "تم المزامنة",
    pending: "معلق",
    processing: "جاري المعالجة",
    completed: "مكتمل",
    failed: "فشل",
    cancelled: "ملغي",
    expired: "منتهي الصلاحية",
    active: "نشط",
    inactive: "غير نشط",
    enabled: "مفعل",
    disabled: "معطل",
  },

  // File and upload messages
  file: {
    selectFile: "اختر ملف",
    selectImage: "اختر صورة",
    dragAndDrop: "اسحب وأفلت الملف هنا",
    clickToUpload: "انقر للرفع",
    uploadProgress: "تقدم الرفع: {progress}%",
    uploadComplete: "اكتمل الرفع",
    uploadFailed: "فشل الرفع",
    fileNotSelected: "لم يتم اختيار ملف",
    imageNotSelected: "لم يتم اختيار صورة",
    fileSizeLimit: "حجم الملف يجب أن يكون أقل من {size}",
    allowedFormats: "التنسيقات المسموحة: {formats}",
    fileDeleted: "تم حذف الملف بنجاح",
    folderCreated: "تم إنشاء المجلد بنجاح",
    folderDeleted: "تم حذف المجلد بنجاح",
  },

  // API and network messages
  api: {
    connectionError: "خطأ في الاتصال",
    timeoutError: "انتهت مهلة الاتصال",
    serverUnavailable: "الخادم غير متاح",
    badRequest: "طلب خاطئ",
    unauthorized: "غير مصرح",
    forbidden: "محظور",
    notFound: "غير موجود",
    methodNotAllowed: "الطريقة غير مسموحة",
    conflict: "تضارب في البيانات",
    internalServerError: "خطأ داخلي في الخادم",
    serviceUnavailable: "الخدمة غير متاحة",
    rateLimitExceeded: "تم تجاوز حد المعدل",
  }
}; 