import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Get all products with stock information
    const products = await prisma.product.findMany({
      include: {
        category: true,
        supplier: true,
        stockMovements: {
          orderBy: {
            date: 'desc'
          },
          take: 5 // Get last 5 movements per product
        },
        invoiceItems: {
          include: {
            invoice: true
          }
        }
      },
      orderBy: {
        name: 'asc'
      }
    })

    // Calculate stock statistics
    const totalProducts = products.length
    const lowStockProducts = products.filter(p => p.currentStock <= p.minStock).length
    const outOfStockProducts = products.filter(p => p.currentStock === 0).length
    const totalStockValue = products.reduce((sum, p) => sum + (p.currentStock * Number(p.price)), 0)
    const totalCostValue = products.reduce((sum, p) => sum + (p.currentStock * Number(p.costPrice || 0)), 0)

    // Calculate stock movements summary
    const allMovements = await prisma.stockMovement.findMany({
      where: {
        date: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      include: {
        product: true
      },
      orderBy: {
        date: 'desc'
      }
    })

    const stockIn = allMovements
      .filter(m => m.type === 'IN')
      .reduce((sum, m) => sum + m.quantity, 0)
    
    const stockOut = allMovements
      .filter(m => m.type === 'OUT')
      .reduce((sum, m) => sum + m.quantity, 0)

    // Calculate top selling products
    const productSales = new Map()
    products.forEach(product => {
      const totalSold = product.invoiceItems.reduce((sum, item) => sum + Number(item.quantity), 0)
      const totalRevenue = product.invoiceItems.reduce((sum, item) => sum + Number(item.total), 0)
      
      if (totalSold > 0) {
        productSales.set(product.id, {
          product,
          totalSold,
          totalRevenue
        })
      }
    })

    const topSellingProducts = Array.from(productSales.values())
      .sort((a, b) => b.totalSold - a.totalSold)
      .slice(0, 10)

    // Calculate products needing restock
    const needRestockProducts = products
      .filter(p => p.currentStock <= p.minStock)
      .sort((a, b) => (a.currentStock - a.minStock) - (b.currentStock - b.minStock))
      .slice(0, 10)

    // Format products data
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      nameAr: product.nameAr,
      sku: product.sku,
      category: product.category?.name || 'Uncategorized',
      currentStock: product.currentStock,
      minStock: product.minStock,
      maxStock: product.maxStock,
      unit: product.unit,
      price: Number(product.price),
      costPrice: Number(product.costPrice || 0),
      location: product.location,
      lastRestocked: product.lastRestocked,
      supplier: product.supplier?.name || 'No Supplier',
      stockStatus: product.currentStock === 0 ? 'OUT_OF_STOCK' :
                  product.currentStock <= product.minStock ? 'LOW_STOCK' : 'IN_STOCK',
      stockValue: product.currentStock * Number(product.price),
      movements: product.stockMovements.map(movement => ({
        type: movement.type,
        quantity: movement.quantity,
        date: movement.date,
        reason: movement.reason
      }))
    }))

    const response = {
      products: formattedProducts,
      statistics: {
        totalProducts,
        lowStockProducts,
        outOfStockProducts,
        totalStockValue,
        totalCostValue,
        stockIn,
        stockOut,
        stockTurnover: stockOut > 0 ? (stockOut / (totalProducts || 1)) : 0
      },
      topSellingProducts: topSellingProducts.map(item => ({
        id: item.product.id,
        name: item.product.name,
        category: item.product.category?.name || 'Uncategorized',
        totalSold: item.totalSold,
        totalRevenue: item.totalRevenue,
        currentStock: item.product.currentStock,
        stockStatus: item.product.currentStock === 0 ? 'OUT_OF_STOCK' :
                    item.product.currentStock <= item.product.minStock ? 'LOW_STOCK' : 'IN_STOCK'
      })),
      needRestockProducts: needRestockProducts.map(product => ({
        id: product.id,
        name: product.name,
        category: product.category?.name || 'Uncategorized',
        currentStock: product.currentStock,
        minStock: product.minStock,
        supplier: product.supplier?.name || 'No Supplier',
        stockDeficit: product.minStock - product.currentStock,
        estimatedCost: (product.minStock - product.currentStock) * Number(product.costPrice || 0)
      })),
      recentMovements: allMovements.slice(0, 20).map(movement => ({
        id: movement.id,
        productName: movement.product.name,
        type: movement.type,
        quantity: movement.quantity,
        reason: movement.reason,
        date: movement.date,
        notes: movement.notes
      }))
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching stock data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch stock data' },
      { status: 500 }
    )
  }
}
