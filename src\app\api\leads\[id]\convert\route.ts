import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Get the lead
    const lead = await prisma.lead.findUnique({
      where: { id },
    })

    if (!lead) {
      return NextResponse.json({ error: 'Lead not found' }, { status: 404 })
    }

    if (lead.status === 'CONVERTED') {
      return NextResponse.json(
        { error: 'Lead is already converted' },
        { status: 400 }
      )
    }

    // Check if customer with same mobile already exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { mobile: lead.mobile },
    })

    if (existingCustomer) {
      return NextResponse.json(
        { error: 'A customer with this mobile number already exists' },
        { status: 400 }
      )
    }

    // Convert lead to customer using transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create customer
      const customer = await tx.customer.create({
        data: {
          name: lead.name,
          nameAr: lead.nameAr,
          email: lead.email,
          mobile: lead.mobile,
          phone: lead.phone,
          address: lead.address,
          city: lead.city,
          country: lead.country,
          company: lead.company,
          notes: lead.notes,
        },
      })

      // Update lead status and link to customer
      const updatedLead = await tx.lead.update({
        where: { id },
        data: {
          status: 'CONVERTED',
          convertedAt: new Date(),
          customerId: customer.id,
        },
        include: {
          customer: true,
          assignedTo: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      return { lead: updatedLead, customer }
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error converting lead:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
