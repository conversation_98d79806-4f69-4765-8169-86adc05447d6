#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Create directories if they don't exist
const arDir = path.join(__dirname, '../src/lib/translations/ar');
const enDir = path.join(__dirname, '../src/lib/translations/en');

if (!fs.existsSync(arDir)) {
  fs.mkdirSync(arDir, { recursive: true });
}

if (!fs.existsSync(enDir)) {
  fs.mkdirSync(enDir, { recursive: true });
}

// Dashboard module
const dashboardAr = `export const dashboard = {
  // Time periods
  outsideBusinessHours: "خارج ساعات العمل",
  selectPeriod: "اختر الفترة",
  today: "اليوم",
  thisWeek: "هذا الأسبوع",
  thisMonth: "هذا الشهر",
  last6Months: "آخر 6 أشهر",
  thisYear: "هذا العام",
  customDate: "تاريخ مخصص",
  fromDate: "من تاريخ",
  toDate: "إلى تاريخ",
  selectedPeriod: "الفترة المحددة",
  
  // Statistics
  totalRevenue: "إجمالي الإيرادات",
  activeTasks: "المهام النشطة",
  needsAttention: "تحتاج انتباه",
  lowStockItems: "العناصر منخفضة المخزون",
  needsRestocking: "تحتاج إعادة تخزين",
  totalCustomers: "إجمالي العملاء",
  activeCustomers: "العملاء النشطين",
  totalProducts: "إجمالي المنتجات",
  activeProducts: "المنتجات النشطة",
  
  // Tasks and deadlines
  tasksAndDeadlines: "المهام والمواعيد النهائية",
  viewAll: "عرض الكل",
  dueToday: "مستحق اليوم",
  tomorrow: "غداً",
  completed: "مكتمل",
  tasksDueThisWeek: "المهام المستحقة هذا الأسبوع",
  
  // Recent activities
  recentExpenses: "المصروفات الأخيرة",
  officeSupplies: "مستلزمات المكتب",
  approved: "موافق عليه",
  fuel: "وقود",
  pending: "معلق",
  internetBill: "فاتورة الإنترنت",
  paid: "مدفوع",
  
  // Activity feed
  taskCompleted: "تم إكمال المهمة",
  assignedTo: "مُكلف إلى",
  quotationApproved: "تم الموافقة على عرض السعر",
  readyToConvert: "جاهز للتحويل",
  purchaseOrderReceived: "تم استلام أمر الشراء",
  stockUpdated: "تم تحديث المخزون",
  expenseApproved: "تم الموافقة على المصروف",
  equipmentMaintenance: "صيانة المعدات",
  
  // Alerts
  criticalStockAlert: "تنبيه مخزون حرج",
  a4Paper: "ورق A4",
  sheetsLeft: "ورقة متبقية",
  reorderImmediately: "أعد الطلب فوراً",
  invoicePaymentReceived: "تم استلام دفعة الفاتورة",
  paidInFull: "مدفوع بالكامل",
};`;

const dashboardEn = `export const dashboard = {
  // Time periods
  outsideBusinessHours: "Outside Business Hours",
  selectPeriod: "Select Period",
  today: "Today",
  thisWeek: "This Week",
  thisMonth: "This Month",
  last6Months: "Last 6 Months",
  thisYear: "This Year",
  customDate: "Custom Date",
  fromDate: "From Date",
  toDate: "To Date",
  selectedPeriod: "Selected Period",
  
  // Statistics
  totalRevenue: "Total Revenue",
  activeTasks: "Active Tasks",
  needsAttention: "Needs Attention",
  lowStockItems: "Low Stock Items",
  needsRestocking: "Needs Restocking",
  totalCustomers: "Total Customers",
  activeCustomers: "Active Customers",
  totalProducts: "Total Products",
  activeProducts: "Active Products",
  
  // Tasks and deadlines
  tasksAndDeadlines: "Tasks and Deadlines",
  viewAll: "View All",
  dueToday: "Due Today",
  tomorrow: "Tomorrow",
  completed: "Completed",
  tasksDueThisWeek: "Tasks Due This Week",
  
  // Recent activities
  recentExpenses: "Recent Expenses",
  officeSupplies: "Office Supplies",
  approved: "Approved",
  fuel: "Fuel",
  pending: "Pending",
  internetBill: "Internet Bill",
  paid: "Paid",
  
  // Activity feed
  taskCompleted: "Task Completed",
  assignedTo: "Assigned To",
  quotationApproved: "Quotation Approved",
  readyToConvert: "Ready to Convert",
  purchaseOrderReceived: "Purchase Order Received",
  stockUpdated: "Stock Updated",
  expenseApproved: "Expense Approved",
  equipmentMaintenance: "Equipment Maintenance",
  
  // Alerts
  criticalStockAlert: "Critical Stock Alert",
  a4Paper: "A4 Paper",
  sheetsLeft: "Sheets Left",
  reorderImmediately: "Reorder Immediately",
  invoicePaymentReceived: "Invoice Payment Received",
  paidInFull: "Paid in Full",
};`;

// Forms module
const formsAr = `export const forms = {
  required: "مطلوب",
  optional: "اختياري",
  save: "حفظ",
  cancel: "إلغاء",
  submit: "إرسال",
  reset: "إعادة تعيين",
  clear: "مسح",
  search: "بحث",
  filter: "تصفية",
  loading: "جاري التحميل...",
  saving: "جاري الحفظ...",
  deleting: "جاري الحذف...",
  updating: "جاري التحديث...",
  creating: "جاري الإنشاء...",
  pleaseWait: "يرجى الانتظار...",
  processing: "جاري المعالجة...",
  uploading: "جاري الرفع...",
  downloading: "جاري التحميل...",
  validating: "جاري التحقق...",
  connecting: "جاري الاتصال...",
  selectFile: "اختر ملف",
  dragAndDrop: "اسحب وأفلت",
  clickToUpload: "انقر للرفع",
  fileFormats: "تنسيقات الملف",
  maxFileSize: "الحد الأقصى لحجم الملف",
  invalidFileType: "نوع ملف غير صالح",
  fileTooLarge: "الملف كبير جداً",
  uploadSuccess: "تم الرفع بنجاح",
  uploadFailed: "فشل في الرفع",
  selectOption: "اختر خيار",
  noOptionsAvailable: "لا توجد خيارات متاحة",
  searchPlaceholder: "البحث...",
  noResultsFound: "لم يتم العثور على نتائج",
  tryDifferentSearch: "جرب بحث مختلف",
  enterValue: "أدخل قيمة",
  invalidValue: "قيمة غير صالحة",
  valueTooShort: "القيمة قصيرة جداً",
  valueTooLong: "القيمة طويلة جداً",
  confirmAction: "تأكيد الإجراء",
  confirmDelete: "تأكيد الحذف",
  confirmCancel: "تأكيد الإلغاء",
  unsavedChanges: "تغييرات غير محفوظة",
  discardChanges: "تجاهل التغييرات",
  saveChanges: "حفظ التغييرات",
};`;

const formsEn = `export const forms = {
  required: "Required",
  optional: "Optional",
  save: "Save",
  cancel: "Cancel",
  submit: "Submit",
  reset: "Reset",
  clear: "Clear",
  search: "Search",
  filter: "Filter",
  loading: "Loading...",
  saving: "Saving...",
  deleting: "Deleting...",
  updating: "Updating...",
  creating: "Creating...",
  pleaseWait: "Please wait...",
  processing: "Processing...",
  uploading: "Uploading...",
  downloading: "Downloading...",
  validating: "Validating...",
  connecting: "Connecting...",
  selectFile: "Select File",
  dragAndDrop: "Drag and Drop",
  clickToUpload: "Click to Upload",
  fileFormats: "File Formats",
  maxFileSize: "Max File Size",
  invalidFileType: "Invalid File Type",
  fileTooLarge: "File Too Large",
  uploadSuccess: "Upload Successful",
  uploadFailed: "Upload Failed",
  selectOption: "Select Option",
  noOptionsAvailable: "No Options Available",
  searchPlaceholder: "Search...",
  noResultsFound: "No Results Found",
  tryDifferentSearch: "Try Different Search",
  enterValue: "Enter Value",
  invalidValue: "Invalid Value",
  valueTooShort: "Value Too Short",
  valueTooLong: "Value Too Long",
  confirmAction: "Confirm Action",
  confirmDelete: "Confirm Delete",
  confirmCancel: "Confirm Cancel",
  unsavedChanges: "Unsaved Changes",
  discardChanges: "Discard Changes",
  saveChanges: "Save Changes",
};`;

// Categories module
const categoriesAr = `export const categories = {
  title: "الفئات",
  addCategory: "إضافة فئة",
  editCategory: "تعديل الفئة",
  categoryName: "اسم الفئة",
  categoryNameAr: "اسم الفئة بالعربية",
  categoryDescription: "وصف الفئة",
  searchCategories: "البحث في الفئات...",
  productsCount: "عدد المنتجات",
  categoryAdded: "تم إضافة الفئة بنجاح",
  categoryUpdated: "تم تحديث الفئة بنجاح",
  categoryDeleted: "تم حذف الفئة بنجاح",
  confirmDelete: "هل أنت متأكد من حذف هذه الفئة؟",
  noCategoriesFound: "لم يتم العثور على فئات",
  createFirstCategory: "إنشاء أول فئة",
  manageCategories: "إدارة الفئات",
  categoryDetails: "تفاصيل الفئة",
  activeCategories: "الفئات النشطة",
  inactiveCategories: "الفئات غير النشطة",
  categoryStatus: "حالة الفئة",
  enableCategory: "تفعيل الفئة",
  disableCategory: "إلغاء تفعيل الفئة",
};`;

const categoriesEn = `export const categories = {
  title: "Categories",
  addCategory: "Add Category",
  editCategory: "Edit Category",
  categoryName: "Category Name",
  categoryNameAr: "Category Name (Arabic)",
  categoryDescription: "Category Description",
  searchCategories: "Search Categories...",
  productsCount: "Products Count",
  categoryAdded: "Category Added Successfully",
  categoryUpdated: "Category Updated Successfully",
  categoryDeleted: "Category Deleted Successfully",
  confirmDelete: "Are you sure you want to delete this category?",
  noCategoriesFound: "No Categories Found",
  createFirstCategory: "Create First Category",
  manageCategories: "Manage Categories",
  categoryDetails: "Category Details",
  activeCategories: "Active Categories",
  inactiveCategories: "Inactive Categories",
  categoryStatus: "Category Status",
  enableCategory: "Enable Category",
  disableCategory: "Disable Category",
};`;

// Units module
const unitsAr = `export const units = {
  title: "الوحدات",
  addUnit: "إضافة وحدة",
  editUnit: "تعديل الوحدة",
  unitName: "اسم الوحدة",
  unitNameAr: "اسم الوحدة بالعربية",
  unitSymbol: "رمز الوحدة",
  unitSymbolAr: "رمز الوحدة بالعربية",
  searchUnits: "البحث في الوحدات...",
  unitAdded: "تم إضافة الوحدة بنجاح",
  unitUpdated: "تم تحديث الوحدة بنجاح",
  unitDeleted: "تم حذف الوحدة بنجاح",
  confirmDelete: "هل أنت متأكد من حذف هذه الوحدة؟",
  noUnitsFound: "لم يتم العثور على وحدات",
  createFirstUnit: "إنشاء أول وحدة",
  manageUnits: "إدارة الوحدات",
  unitDetails: "تفاصيل الوحدة",
  activeUnits: "الوحدات النشطة",
  inactiveUnits: "الوحدات غير النشطة",
  unitStatus: "حالة الوحدة",
  enableUnit: "تفعيل الوحدة",
  disableUnit: "إلغاء تفعيل الوحدة",
  baseUnit: "الوحدة الأساسية",
  conversionFactor: "معامل التحويل",
  unitType: "نوع الوحدة",
  weightUnits: "وحدات الوزن",
  lengthUnits: "وحدات الطول",
  volumeUnits: "وحدات الحجم",
  areaUnits: "وحدات المساحة",
  timeUnits: "وحدات الوقت",
  quantityUnits: "وحدات الكمية",
};`;

const unitsEn = `export const units = {
  title: "Units",
  addUnit: "Add Unit",
  editUnit: "Edit Unit",
  unitName: "Unit Name",
  unitNameAr: "Unit Name (Arabic)",
  unitSymbol: "Unit Symbol",
  unitSymbolAr: "Unit Symbol (Arabic)",
  searchUnits: "Search Units...",
  unitAdded: "Unit Added Successfully",
  unitUpdated: "Unit Updated Successfully",
  unitDeleted: "Unit Deleted Successfully",
  confirmDelete: "Are you sure you want to delete this unit?",
  noUnitsFound: "No Units Found",
  createFirstUnit: "Create First Unit",
  manageUnits: "Manage Units",
  unitDetails: "Unit Details",
  activeUnits: "Active Units",
  inactiveUnits: "Inactive Units",
  unitStatus: "Unit Status",
  enableUnit: "Enable Unit",
  disableUnit: "Disable Unit",
  baseUnit: "Base Unit",
  conversionFactor: "Conversion Factor",
  unitType: "Unit Type",
  weightUnits: "Weight Units",
  lengthUnits: "Length Units",
  volumeUnits: "Volume Units",
  areaUnits: "Area Units",
  timeUnits: "Time Units",
  quantityUnits: "Quantity Units",
};`;

// Write files
const modules = [
  { name: 'dashboard', ar: dashboardAr, en: dashboardEn },
  { name: 'forms', ar: formsAr, en: formsEn },
  { name: 'categories', ar: categoriesAr, en: categoriesEn },
  { name: 'units', ar: unitsAr, en: unitsEn },
];

modules.forEach(module => {
  const arPath = path.join(arDir, `${module.name}.ts`);
  const enPath = path.join(enDir, `${module.name}.ts`);
  
  if (!fs.existsSync(arPath)) {
    fs.writeFileSync(arPath, module.ar);
    console.log(`✅ Created ${arPath}`);
  } else {
    console.log(`⚠️  ${arPath} already exists, skipping...`);
  }
  
  if (!fs.existsSync(enPath)) {
    fs.writeFileSync(enPath, module.en);
    console.log(`✅ Created ${enPath}`);
  } else {
    console.log(`⚠️  ${enPath} already exists, skipping...`);
  }
});

console.log('\n🎯 Next Steps:');
console.log('1. Update src/lib/translations/ar.ts to import these modules');
console.log('2. Update src/lib/translations/en.ts to import these modules');
console.log('3. Test the translations in your application');
console.log('4. Continue with the remaining modules as per the roadmap');
console.log('\n📋 Run the audit script to see progress:');
console.log('node scripts/audit-translations.js'); 