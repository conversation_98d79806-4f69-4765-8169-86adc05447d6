import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { whatsappService } from '@/lib/whatsapp'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const assignedToId = searchParams.get('assignedTo')
    const projectId = searchParams.get('projectId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const where: Record<string, unknown> = {}

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (status && status !== 'all') {
      where.status = status
    }

    if (assignedToId) {
      where.assignedToId = assignedToId
    }

    if (projectId) {
      where.projectId = projectId
    }

    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        skip,
        take: limit,
        include: {
          customer: true,
          assignedTo: true,
          createdBy: true,
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.task.count({ where }),
    ])

    return NextResponse.json({
      tasks,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching tasks:', error)
    
    // Check if it's a database connection error
    if (error instanceof Error) {
      if (error.message.includes('connect') || error.message.includes('database')) {
        return NextResponse.json(
          { error: 'Database connection error. Please try again later.' },
          { status: 503 }
        )
      }
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      title,
      description,
      customerId,
      assignedToId,
      projectId,
      priority,
      estimatedHours,
      notes,
    } = body

    if (!title) {
      return NextResponse.json(
        { error: 'Title is required' },
        { status: 400 }
      )
    }

    // Find user by ID from session
    const currentUser = await prisma.user.findUnique({
      where: { id: session.user?.id || '' }
    })

    if (!currentUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 400 }
      )
    }

    // Validate customer if provided
    if (customerId && customerId !== '') {
      const customer = await prisma.customer.findUnique({
        where: { id: customerId }
      })
      if (!customer) {
        return NextResponse.json(
          { error: 'Customer not found' },
          { status: 400 }
        )
      }
    }

    // Validate assigned user if provided
    if (assignedToId && assignedToId !== '') {
      const assignedUser = await prisma.user.findUnique({
        where: { id: assignedToId }
      })
      if (!assignedUser) {
        return NextResponse.json(
          { error: 'Assigned user not found' },
          { status: 400 }
        )
      }
    }

    const task = await prisma.task.create({
      data: {
        title,
        description,
        customerId: customerId || null,
        assignedToId: assignedToId || null,
        projectId: projectId || null,
        priority: priority || 'MEDIUM',
        estimatedHours: estimatedHours ? parseFloat(estimatedHours) : null,
        notes,
        createdById: currentUser.id,
      },
      include: {
        customer: true,
        assignedTo: true,
        createdBy: true,
      },
    })

    // Send WhatsApp notification to assigned employee
    if (task.assignedTo?.phone) {
      await whatsappService.sendTaskAssignedNotification(
        task.assignedTo.phone,
        task.title
      )
    }

    return NextResponse.json(task, { status: 201 })
  } catch (error) {
    console.error('Error creating task:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
