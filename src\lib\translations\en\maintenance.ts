export const maintenance = {
  // Page titles and descriptions
  title: "Maintenance",
  description: "Manage equipment maintenance and service schedules",
  addMaintenance: "Add Maintenance",
  createMaintenance: "Create Maintenance",
  editMaintenance: "Edit Maintenance",
  viewDetails: "View Details",
  
  // Maintenance properties
  maintenanceId: "Maintenance ID",
  equipment: "Equipment",
  equipmentName: "Equipment Name",
  equipmentType: "Equipment Type",
  serialNumber: "Serial Number",
  maintenanceType: "Maintenance Type",
  maintenanceDescription: "Maintenance Description",
  technician: "Technician",
  assignedTo: "Assigned To",
  priority: "Priority",
  status: "Status",
  scheduledDate: "Scheduled Date",
  completedDate: "Completed Date",
  estimatedDuration: "Estimated Duration",
  actualDuration: "Actual Duration",
  cost: "Cost",
  estimatedCost: "Estimated Cost",
  actualCost: "Actual Cost",
  
  // Maintenance types
  preventive: "Preventive",
  corrective: "Corrective",
  emergency: "Emergency",
  routine: "Routine",
  inspection: "Inspection",
  repair: "Repair",
  replacement: "Replacement",
  upgrade: "Upgrade",
  
  // Maintenance status
  scheduled: "Scheduled",
  inProgress: "In Progress",
  completed: "Completed",
  cancelled: "Cancelled",
  postponed: "Postponed",
  onHold: "On Hold",
  
  // Priority levels
  low: "Low",
  medium: "Medium",
  high: "High",
  critical: "Critical",
  
  // Actions
  scheduleMaintenance: "Schedule Maintenance",
  startMaintenance: "Start Maintenance",
  completeMaintenance: "Complete Maintenance",
  cancelMaintenance: "Cancel Maintenance",
  postponeMaintenance: "Postpone Maintenance",
  delete: "Delete",
  duplicate: "Duplicate",
  print: "Print",
  export: "Export",
  
  // Equipment categories
  machinery: "Machinery",
  vehicles: "Vehicles",
  electronics: "Electronics",
  hvac: "HVAC",
  plumbing: "Plumbing",
  electrical: "Electrical",
  software: "Software",
  tools: "Tools",
  
  // Maintenance details
  workDescription: "Work Description",
  partsUsed: "Parts Used",
  partsReplaced: "Parts Replaced",
  laborHours: "Labor Hours",
  notes: "Notes",
  attachments: "Attachments",
  beforePhotos: "Before Photos",
  afterPhotos: "After Photos",
  
  // Scheduling
  maintenanceSchedule: "Maintenance Schedule",
  nextMaintenance: "Next Maintenance",
  maintenanceInterval: "Maintenance Interval",
  recurringMaintenance: "Recurring Maintenance",
  daily: "Daily",
  weekly: "Weekly",
  monthly: "Monthly",
  quarterly: "Quarterly",
  yearly: "Yearly",
  
  // Search and filters
  searchMaintenance: "Search maintenance...",
  searchPlaceholder: "Search by equipment, technician, or description...",
  allStatus: "All Status",
  allTypes: "All Types",
  allPriorities: "All Priorities",
  filterByStatus: "Filter by Status",
  filterByType: "Filter by Type",
  filterByPriority: "Filter by Priority",
  filterByTechnician: "Filter by Technician",
  filterByEquipment: "Filter by Equipment",
  dateRange: "Date Range",
  
  // Statistics
  totalMaintenance: "Total Maintenance",
  scheduledMaintenance: "Scheduled Maintenance",
  completedMaintenance: "Completed Maintenance",
  overdueMaintenance: "Overdue Maintenance",
  maintenanceCost: "Maintenance Cost",
  equipmentUptime: "Equipment Uptime",
  
  // Messages and confirmations
  maintenanceScheduledSuccessfully: "Maintenance scheduled successfully",
  maintenanceStartedSuccessfully: "Maintenance started successfully",
  maintenanceCompletedSuccessfully: "Maintenance completed successfully",
  maintenanceCancelledSuccessfully: "Maintenance cancelled successfully",
  maintenanceDeletedSuccessfully: "Maintenance deleted successfully",
  maintenanceUpdatedSuccessfully: "Maintenance updated successfully",
  
  // Error messages
  failedToScheduleMaintenance: "Failed to schedule maintenance",
  failedToStartMaintenance: "Failed to start maintenance",
  failedToCompleteMaintenance: "Failed to complete maintenance",
  failedToCancelMaintenance: "Failed to cancel maintenance",
  failedToDeleteMaintenance: "Failed to delete maintenance",
  failedToUpdateMaintenance: "Failed to update maintenance",
  
  // Confirmations
  confirmStartMaintenance: "Are you sure you want to start this maintenance?",
  confirmCompleteMaintenance: "Are you sure you want to complete this maintenance?",
  confirmCancelMaintenance: "Are you sure you want to cancel this maintenance?",
  confirmDeleteMaintenance: "Are you sure you want to delete this maintenance?",
  
  // Form placeholders
  equipmentNamePlaceholder: "Enter equipment name...",
  descriptionPlaceholder: "Describe the maintenance work required...",
  selectTechnician: "Select Technician...",
  selectEquipment: "Select Equipment...",
  selectMaintenanceType: "Select Maintenance Type...",
  selectPriority: "Select Priority...",
  selectStatus: "Select Status...",
  
  // Empty states
  noMaintenanceFound: "No maintenance records found",
  noMaintenanceFoundSearch: "No maintenance records found matching your search",
  createFirstMaintenance: "Schedule your first maintenance",
  noEquipment: "No equipment selected",
  unassigned: "Unassigned",
  
  // Maintenance history
  maintenanceHistory: "Maintenance History",
  previousMaintenance: "Previous Maintenance",
  maintenanceLog: "Maintenance Log",
  
  // Reports
  maintenanceReport: "Maintenance Report",
  equipmentReport: "Equipment Report",
  costAnalysis: "Cost Analysis",
  performanceMetrics: "Performance Metrics",
  
  // Validation
  equipmentRequired: "Equipment is required",
  maintenanceTypeRequired: "Maintenance type is required",
  technicianRequired: "Technician is required",
  scheduledDateRequired: "Scheduled date is required",
  descriptionRequired: "Description is required",
  invalidCost: "Cost must be a positive number",
  invalidDuration: "Duration must be a positive number",
  
  // Loading states
  loadingMaintenance: "Loading maintenance records...",
  schedulingMaintenance: "Scheduling maintenance...",
  savingMaintenance: "Saving maintenance...",
  deletingMaintenance: "Deleting maintenance...",
  
  // Time tracking
  timeSpent: "Time Spent",
  startTime: "Start Time",
  endTime: "End Time",
  breakTime: "Break Time",
  
  // Warranty
  warranty: "Warranty",
  warrantyStatus: "Warranty Status",
  warrantyExpiry: "Warranty Expiry",
  underWarranty: "Under Warranty",
  warrantyExpired: "Warranty Expired"
}; 