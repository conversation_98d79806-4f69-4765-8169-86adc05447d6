"use client"

import { useState, useEffect } from "react"
import { useRouter, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  CreditCard,
  DollarSign,
  Calculator,
  AlertCircle,
  CheckCircle,
  FileText,
  User,
  Calendar,
  Receipt
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"

interface Invoice {
  id: string
  number: string
  date: string
  dueDate: string
  customer: {
    name: string
    company?: string
    phone: string
    email?: string
  }
  status: 'PAID' | 'UNPAID' | 'PARTIAL' | 'OVERDUE'
  subtotal: number
  taxAmount: number
  total: number
  amountPaid?: number
}

export default function RecordPaymentPage() {
  const router = useRouter()
  const params = useParams()
  const [loading, setLoading] = useState(false)
  const [invoice, setInvoice] = useState<Invoice | null>(null)
  const [paymentAmount, setPaymentAmount] = useState("")
  const [paymentMethod, setPaymentMethod] = useState("")
  const [paymentDate, setPaymentDate] = useState(new Date().toISOString().split('T')[0])
  const [reference, setReference] = useState("")
  const [notes, setNotes] = useState("")



  useEffect(() => {
    // Fetch real invoice by ID from API
    const fetchInvoice = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/invoices/${params.id}`)
        
        if (response.ok) {
          const data = await response.json()
          // Map the API response to our interface
          const mappedInvoice: Invoice = {
            id: data.id,
            number: data.number,
            date: data.date,
            dueDate: data.dueDate,
            customer: {
              name: data.customer?.name || 'Unknown Customer',
              company: data.customer?.company || '',
              phone: data.customer?.mobile || data.customer?.phone || '',
              email: data.customer?.email || ''
            },
            status: data.status,
            subtotal: Number(data.subtotal),
            taxAmount: Number(data.taxAmount),
            total: Number(data.total),
            amountPaid: data.payments?.reduce((sum: number, payment: any) => sum + Number(payment.amount), 0) || 0
          }
          setInvoice(mappedInvoice)
        } else {
          console.error('Failed to fetch invoice:', response.status)
          alert('Invoice not found. You will be redirected to the invoices list.')
          router.push('/dashboard/invoices')
        }
      } catch (error) {
        console.error('Error fetching invoice:', error)
        alert('Error loading invoice. You will be redirected to the invoices list.')
        router.push('/dashboard/invoices')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchInvoice()
    }
  }, [params.id, router])

  if (loading || !invoice) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">Loading invoice...</p>
          </div>
        </div>
      </div>
    )
  }

  const balance = invoice.total - (invoice.amountPaid || 0)
  const paymentAmountNum = parseFloat(paymentAmount) || 0
  const newBalance = balance - paymentAmountNum
  const isFullPayment = paymentAmountNum >= balance
  const isOverpayment = paymentAmountNum > balance

  const paymentMethods = [
    { value: "CASH", label: "Cash" },
    { value: "BANK_TRANSFER", label: "Bank Transfer" },
    { value: "CARD", label: "Credit/Debit Card" },
    { value: "CHECK", label: "Check" },
    { value: "OTHER", label: "Other" },
  ]

  const handleQuickAmount = (amount: number) => {
    setPaymentAmount(amount.toString())
  }

  const handleSubmit = async () => {
    if (!paymentAmount || !paymentMethod || paymentAmountNum <= 0) {
      alert('Please fill in all required fields with valid amounts')
      return
    }

    if (paymentAmountNum > balance) {
      const confirmed = confirm(`Payment amount (${formatCurrency(paymentAmountNum)}) exceeds the balance (${formatCurrency(balance)}). Do you want to continue?`)
      if (!confirmed) return
    }

    setLoading(true)
    try {
      const paymentData = {
        invoiceId: invoice.id,
        amount: paymentAmountNum,
        method: paymentMethod, // Already in correct enum format
        date: paymentDate,
        reference,
        notes
      }

      console.log('Recording payment:', paymentData)

      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(paymentData)
      })

      if (response.ok) {
        const result = await response.json()
        alert(`✅ Payment recorded successfully! Amount: ${formatCurrency(paymentAmountNum)}`)
        router.push('/dashboard/invoices')
      } else {
        const error = await response.json()
        alert(`❌ Error recording payment: ${error.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error recording payment:', error)
      alert('❌ Error recording payment. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Record Payment</h1>
            <p className="text-muted-foreground">
              Record a payment for invoice {invoice.number}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payment Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Invoice Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Invoice Summary</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Invoice Number</Label>
                  <p className="text-lg font-semibold">{invoice.number}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                  <div className="mt-1">
                    <Badge variant={
                      invoice.status === 'PAID' ? 'default' :
                      invoice.status === 'PARTIAL' ? 'secondary' :
                      invoice.status === 'OVERDUE' ? 'destructive' : 'outline'
                    }>
                      {invoice.status}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Customer</Label>
                  <p className="font-medium">{invoice.customer.name}</p>
                  {invoice.customer.company && (
                    <p className="text-sm text-muted-foreground">{invoice.customer.company}</p>
                  )}
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Due Date</Label>
                  <p className="font-medium">{new Date(invoice.dueDate).toLocaleDateString()}</p>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Total Amount</Label>
                  <p className="text-xl font-bold">{formatCurrency(invoice.total)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Amount Paid</Label>
                  <p className="text-xl font-bold text-green-600">{formatCurrency(invoice.amountPaid || 0)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Balance Due</Label>
                  <p className="text-xl font-bold text-orange-600">{formatCurrency(balance)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="h-5 w-5" />
                <span>Payment Details</span>
              </CardTitle>
              <CardDescription>
                Enter the payment information below
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Payment Amount */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="amount">Payment Amount *</Label>
                  <div className="flex space-x-2 mt-1">
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="0.00"
                      value={paymentAmount}
                      onChange={(e) => setPaymentAmount(e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => handleQuickAmount(balance)}
                      className="whitespace-nowrap"
                    >
                      Full Amount
                    </Button>
                  </div>
                </div>

                {/* Quick Amount Buttons */}
                <div className="flex flex-wrap gap-2">
                  <Label className="text-sm font-medium text-muted-foreground w-full">Quick Amounts:</Label>
                  {[100, 250, 500, 1000].map((amount) => (
                    <Button
                      key={amount}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickAmount(amount)}
                    >
                      {formatCurrency(amount)}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Payment Method */}
              <div>
                <Label htmlFor="method">Payment Method *</Label>
                <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map((method) => (
                      <SelectItem key={method.value} value={method.value}>
                        {method.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Payment Date */}
              <div>
                <Label htmlFor="date">Payment Date *</Label>
                <Input
                  id="date"
                  type="date"
                  value={paymentDate}
                  onChange={(e) => setPaymentDate(e.target.value)}
                  className="mt-1"
                />
              </div>

              {/* Reference */}
              <div>
                <Label htmlFor="reference">Reference Number</Label>
                <Input
                  id="reference"
                  placeholder="Transaction ID, Check number, etc."
                  value={reference}
                  onChange={(e) => setReference(e.target.value)}
                  className="mt-1"
                />
              </div>

              {/* Notes */}
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Additional notes about this payment..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="mt-1"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Payment Summary Sidebar */}
        <div className="space-y-6">
          {/* Payment Calculator */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>Payment Calculator</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Current Balance:</span>
                  <span className="font-medium">{formatCurrency(balance)}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Payment Amount:</span>
                  <span className="font-medium">{formatCurrency(paymentAmountNum)}</span>
                </div>

                <Separator />

                <div className="flex justify-between">
                  <span className="text-sm font-medium">New Balance:</span>
                  <span className={`font-bold ${newBalance <= 0 ? 'text-green-600' : 'text-orange-600'}`}>
                    {formatCurrency(Math.max(0, newBalance))}
                  </span>
                </div>

                {isFullPayment && (
                  <div className="flex items-center space-x-2 text-green-600 bg-green-50 p-3 rounded-lg">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      {isOverpayment ? "Overpayment - Credit will be applied" : "Invoice will be marked as PAID"}
                    </span>
                  </div>
                )}

                {isOverpayment && (
                  <div className="flex items-center space-x-2 text-orange-600 bg-orange-50 p-3 rounded-lg">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm">
                      Overpayment of {formatCurrency(paymentAmountNum - balance)} will be recorded as credit
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-3">
                <Button
                  onClick={handleSubmit}
                  disabled={loading || !paymentAmount || !paymentMethod || paymentAmountNum <= 0}
                  className="w-full"
                  size="lg"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Recording Payment...
                    </>
                  ) : (
                    <>
                      <Receipt className="mr-2 h-4 w-4" />
                      Record Payment
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => router.back()}
                  className="w-full"
                  disabled={loading}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Payment Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Payment Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-xs text-muted-foreground space-y-1">
                <p>• Enter the exact amount received from the customer</p>
                <p>• Include reference numbers for bank transfers</p>
                <p>• Partial payments are allowed and tracked</p>
                <p>• Overpayments will be recorded as customer credit</p>
                <p>• Payment notifications will be sent automatically</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
