"use client"

import React, { useRef, useEffect, useState } from 'react'
import { cn } from '@/lib/utils'

interface ScrollableTableProps {
  children: React.ReactNode
  className?: string
}

export function ScrollableTable({ children, className }: ScrollableTableProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [showScrollIndicator, setShowScrollIndicator] = useState(false)
  const [scrollPosition, setScrollPosition] = useState(0)
  const [maxScroll, setMaxScroll] = useState(0)

  useEffect(() => {
    const container = scrollContainerRef.current
    if (!container) return

    const checkScrollable = () => {
      const isScrollable = container.scrollWidth > container.clientWidth
      setShowScrollIndicator(isScrollable)
      setMaxScroll(container.scrollWidth - container.clientWidth)
    }

    const handleScroll = () => {
      setScrollPosition(container.scrollLeft)
    }

    checkScrollable()
    container.addEventListener('scroll', handleScroll)
    window.addEventListener('resize', checkScrollable)

    return () => {
      container.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', checkScrollable)
    }
  }, [])

  const scrollProgress = maxScroll > 0 ? (scrollPosition / maxScroll) * 100 : 0

  return (
    <div className={cn("table-wrapper", className)}>
      {/* Top scroll indicator */}
      {showScrollIndicator && (
        <div className="sticky top-0 z-10 bg-white border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-2 text-sm text-gray-600">
            <span>Scroll horizontally to see all columns</span>
            <div className="flex items-center space-x-2">
              <div className="w-20 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-blue-500 transition-all duration-150"
                  style={{ width: `${scrollProgress}%` }}
                />
              </div>
              <span className="text-xs">{Math.round(scrollProgress)}%</span>
            </div>
          </div>
        </div>
      )}
      
      {/* Scrollable container */}
      <div 
        ref={scrollContainerRef}
        className={cn(
          "table-scroll-container",
          showScrollIndicator && "has-horizontal-scroll"
        )}
      >
        {children}
      </div>
      
      {/* Bottom scroll helper */}
      {showScrollIndicator && (
        <div className="sticky bottom-0 z-10 bg-white border-t border-gray-200">
          <div className="h-3 bg-gray-50 overflow-x-auto">
            <div 
              className="h-full bg-transparent"
              style={{ width: `${scrollContainerRef.current?.scrollWidth}px` }}
              onScroll={(e) => {
                if (scrollContainerRef.current) {
                  scrollContainerRef.current.scrollLeft = e.currentTarget.scrollLeft
                }
              }}
            />
          </div>
        </div>
      )}
    </div>
  )
}

// Hook for keyboard navigation
export function useTableKeyboardNavigation() {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const activeElement = document.activeElement
      const tableContainer = activeElement?.closest('.table-scroll-container') as HTMLElement
      
      if (!tableContainer) return

      switch (e.key) {
        case 'ArrowLeft':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            tableContainer.scrollLeft -= 100
          }
          break
        case 'ArrowRight':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            tableContainer.scrollLeft += 100
          }
          break
        case 'Home':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            tableContainer.scrollLeft = 0
          }
          break
        case 'End':
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault()
            tableContainer.scrollLeft = tableContainer.scrollWidth
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])
}
