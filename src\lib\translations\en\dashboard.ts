export const dashboard = {
  // Main title
  title: "Dashboard",
  
  // Time periods
  outsideBusinessHours: "Outside Business Hours",
  selectPeriod: "Select Period",
  today: "Today",
  thisWeek: "This Week",
  thisMonth: "This Month",
  last6Months: "Last 6 Months",
  thisYear: "This Year",
  customDate: "Custom Date",
  fromDate: "From Date",
  toDate: "To Date",
  selectedPeriod: "Selected Period",
  
  // Statistics
  totalRevenue: "Total Revenue",
  pendingTasks: "Pending Tasks",
  activeTasks: "Active Tasks",
  overdueInvoices: "Overdue Invoices",
  needsAttention: "Needs Attention",
  lowStockItems: "Low Stock Items",
  needsRestocking: "Needs Restocking",
  totalCustomers: "Total Customers",
  activeCustomers: "Active Customers",
  totalProducts: "Total Products",
  activeProducts: "Active Products",
  
  // Tasks and deadlines
  tasksAndDeadlines: "Tasks and Deadlines",
  viewAll: "View All",
  dueToday: "Due Today",
  tomorrow: "Tomorrow",
  completed: "Completed",
  tasksDueThisWeek: "Tasks Due This Week",
  
  // Recent activities
  recentActivities: "Recent Activities",
  recentExpenses: "Recent Expenses",
  officeSupplies: "Office Supplies",
  approved: "Approved",
  fuel: "Fuel",
  pending: "Pending",
  internetBill: "Internet Bill",
  paid: "Paid",
  
  // Activity feed
  taskCompleted: "Task Completed",
  assignedTo: "Assigned To",
  quotationApproved: "Quotation Approved",
  readyToConvert: "Ready to Convert",
  purchaseOrderReceived: "Purchase Order Received",
  stockUpdated: "Stock Updated",
  expenseApproved: "Expense Approved",
  equipmentMaintenance: "Equipment Maintenance",
  
  // Alerts
  criticalStockAlert: "Critical Stock Alert",
  a4Paper: "A4 Paper",
  sheetsLeft: "Sheets Left",
  reorderImmediately: "Reorder Immediately",
  invoicePaymentReceived: "Invoice Payment Received",
  paidInFull: "Paid in Full",
};