"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  Download,
  Printer,
  Building,
  Calculator,
  Wallet,
  DollarSign,
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

// Real Balance Sheet data for Omani printing company (January 2024)
const balanceSheetData = {
  period: "يناير 2024",
  assets: {
    currentAssets: {
      cash: 125600,
      accountsReceivable: 87400,
      inventory: 45200,
      prepaidExpenses: 12800,
      total: 271000,
    },
    fixedAssets: {
      equipment: 185600,
      accumulatedDepreciation: -38400,
      netEquipment: 147200,
      furniture: 35600,
      accumulatedDepreciationFurniture: -8900,
      netFurniture: 26700,
      building: 450000,
      accumulatedDepreciationBuilding: -67500,
      netBuilding: 382500,
      total: 556400,
    },
    totalAssets: 827400,
  },
  liabilities: {
    currentLiabilities: {
      accountsPayable: 56800,
      accrued: 23400,
      shortTermDebt: 45000,
      total: 125200,
    },
    longTermLiabilities: {
      mortgage: 285000,
      equipmentLoan: 67500,
      total: 352500,
    },
    totalLiabilities: 477700,
  },
  equity: {
    capital: 200000,
    retainedEarnings: 99620,
    currentYearEarnings: 50080,
    total: 349700,
  },
}

export default function BalanceSheet() {
  const router = useRouter()
  const { t } = useI18n()
  const [selectedPeriod, setSelectedPeriod] = useState("current")
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handlePrint = () => {
    window.print()
  }

  const handleDownload = () => {
    alert('وظيفة التنزيل ستكون متاحة قريباً')
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            رجوع
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">الميزانية العمومية</h2>
            <p className="text-muted-foreground">
              الوضع المالي كما في {balanceSheetData.period}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current">الفترة الحالية</SelectItem>
              <SelectItem value="previous">الفترة السابقة</SelectItem>
              <SelectItem value="ytd">من بداية العام</SelectItem>
              <SelectItem value="quarterly">ربع سنوي</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            طباعة
          </Button>
          <Button onClick={handleDownload}>
            <Download className="mr-2 h-4 w-4" />
            تحميل PDF
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="bg-slate-50/50 dark:bg-slate-900/20 rounded-lg p-6 border border-slate-200/60 dark:border-slate-700/60">
        <div className="grid gap-4 md:grid-cols-3">
          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الأصول</CardTitle>
              <Building className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{formatCurrency(balanceSheetData.assets.totalAssets)}</div>
              <div className="text-xs text-muted-foreground">
                الأصول المتداولة + الثابتة
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الخصوم</CardTitle>
              <Calculator className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{formatCurrency(balanceSheetData.liabilities.totalLiabilities)}</div>
              <div className="text-xs text-muted-foreground">
                الخصوم المتداولة + طويلة الأجل
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">حقوق الملكية</CardTitle>
              <Wallet className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{formatCurrency(balanceSheetData.equity.total)}</div>
              <div className="text-xs text-muted-foreground">
                رأس المال + الأرباح المحتجزة
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Balance Sheet Statement */}
      <Card className="bg-white dark:bg-slate-800 shadow-sm">
        <CardHeader>
          <CardTitle className="text-xl font-bold">الميزانية العمومية</CardTitle>
          <p className="text-muted-foreground">{balanceSheetData.period}</p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            {/* Assets Column */}
            <div>
              <Table>
                <TableBody>
                  {/* Assets Header */}
                  <TableRow className="border-t-2 border-blue-300 font-medium bg-blue-50 dark:bg-blue-800">
                    <TableCell className="font-bold text-lg text-blue-800 dark:text-blue-200" colSpan={2}>
                      الأصول
                    </TableCell>
                  </TableRow>

                  {/* Current Assets */}
                  <TableRow className="bg-slate-50 dark:bg-slate-800">
                    <TableCell className="font-semibold" colSpan={2}>
                      الأصول المتداولة
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">النقدية والودائع</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.assets.currentAssets.cash)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">الذمم المدينة</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.assets.currentAssets.accountsReceivable)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">المخزون</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.assets.currentAssets.inventory)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">المصروفات المدفوعة مقدماً</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.assets.currentAssets.prepaidExpenses)}</TableCell>
                  </TableRow>
                  <TableRow className="border-t border-slate-200 font-semibold bg-blue-50 dark:bg-blue-900/20">
                    <TableCell className="font-bold">إجمالي الأصول المتداولة</TableCell>
                    <TableCell className="text-right font-bold text-blue-600">{formatCurrency(balanceSheetData.assets.currentAssets.total)}</TableCell>
                  </TableRow>

                  {/* Fixed Assets */}
                  <TableRow className="bg-slate-50 dark:bg-slate-800">
                    <TableCell className="font-semibold" colSpan={2}>
                      الأصول الثابتة
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">المعدات</TableCell>
                    <TableCell className="text-right">{formatCurrency(balanceSheetData.assets.fixedAssets.equipment)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-8 text-sm text-muted-foreground">ناقص: الاستهلاك المتراكم</TableCell>
                    <TableCell className="text-right text-red-600">{formatCurrency(balanceSheetData.assets.fixedAssets.accumulatedDepreciation)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6 font-medium">صافي المعدات</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.assets.fixedAssets.netEquipment)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">الأثاث والتجهيزات</TableCell>
                    <TableCell className="text-right">{formatCurrency(balanceSheetData.assets.fixedAssets.furniture)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-8 text-sm text-muted-foreground">ناقص: الاستهلاك المتراكم</TableCell>
                    <TableCell className="text-right text-red-600">{formatCurrency(balanceSheetData.assets.fixedAssets.accumulatedDepreciationFurniture)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6 font-medium">صافي الأثاث والتجهيزات</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.assets.fixedAssets.netFurniture)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">المباني</TableCell>
                    <TableCell className="text-right">{formatCurrency(balanceSheetData.assets.fixedAssets.building)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-8 text-sm text-muted-foreground">ناقص: الاستهلاك المتراكم</TableCell>
                    <TableCell className="text-right text-red-600">{formatCurrency(balanceSheetData.assets.fixedAssets.accumulatedDepreciationBuilding)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6 font-medium">صافي المباني</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.assets.fixedAssets.netBuilding)}</TableCell>
                  </TableRow>
                  <TableRow className="border-t border-slate-200 font-semibold bg-blue-50 dark:bg-blue-900/20">
                    <TableCell className="font-bold">إجمالي الأصول الثابتة</TableCell>
                    <TableCell className="text-right font-bold text-blue-600">{formatCurrency(balanceSheetData.assets.fixedAssets.total)}</TableCell>
                  </TableRow>

                  {/* Total Assets */}
                  <TableRow className="border-t-2 border-blue-400 font-bold bg-blue-100 dark:bg-blue-900/30">
                    <TableCell className="font-bold text-lg">إجمالي الأصول</TableCell>
                    <TableCell className="text-right font-bold text-blue-600 text-lg">{formatCurrency(balanceSheetData.assets.totalAssets)}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            {/* Liabilities & Equity Column */}
            <div>
              <Table>
                <TableBody>
                  {/* Liabilities Header */}
                  <TableRow className="border-t-2 border-red-300 font-medium bg-red-50 dark:bg-red-800">
                    <TableCell className="font-bold text-lg text-red-800 dark:text-red-200" colSpan={2}>
                      الخصوم وحقوق الملكية
                    </TableCell>
                  </TableRow>

                  {/* Current Liabilities */}
                  <TableRow className="bg-slate-50 dark:bg-slate-800">
                    <TableCell className="font-semibold" colSpan={2}>
                      الخصوم المتداولة
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">الذمم الدائنة</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.liabilities.currentLiabilities.accountsPayable)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">المستحقات</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.liabilities.currentLiabilities.accrued)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">القروض قصيرة الأجل</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.liabilities.currentLiabilities.shortTermDebt)}</TableCell>
                  </TableRow>
                  <TableRow className="border-t border-slate-200 font-semibold bg-red-50 dark:bg-red-900/20">
                    <TableCell className="font-bold">إجمالي الخصوم المتداولة</TableCell>
                    <TableCell className="text-right font-bold text-red-600">{formatCurrency(balanceSheetData.liabilities.currentLiabilities.total)}</TableCell>
                  </TableRow>

                  {/* Long-term Liabilities */}
                  <TableRow className="bg-slate-50 dark:bg-slate-800">
                    <TableCell className="font-semibold" colSpan={2}>
                      الخصوم طويلة الأجل
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">القرض العقاري</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.liabilities.longTermLiabilities.mortgage)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">قرض المعدات</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.liabilities.longTermLiabilities.equipmentLoan)}</TableCell>
                  </TableRow>
                  <TableRow className="border-t border-slate-200 font-semibold bg-red-50 dark:bg-red-900/20">
                    <TableCell className="font-bold">إجمالي الخصوم طويلة الأجل</TableCell>
                    <TableCell className="text-right font-bold text-red-600">{formatCurrency(balanceSheetData.liabilities.longTermLiabilities.total)}</TableCell>
                  </TableRow>

                  {/* Total Liabilities */}
                  <TableRow className="border-t-2 border-red-400 font-bold bg-red-100 dark:bg-red-900/30">
                    <TableCell className="font-bold text-lg">إجمالي الخصوم</TableCell>
                    <TableCell className="text-right font-bold text-red-600 text-lg">{formatCurrency(balanceSheetData.liabilities.totalLiabilities)}</TableCell>
                  </TableRow>

                  {/* Equity */}
                  <TableRow className="border-t-2 border-green-300 font-medium bg-green-50 dark:bg-green-800">
                    <TableCell className="font-bold text-lg text-green-800 dark:text-green-200" colSpan={2}>
                      حقوق الملكية
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">رأس المال</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.equity.capital)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">الأرباح المحتجزة</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.equity.retainedEarnings)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="pl-6">أرباح السنة الجارية</TableCell>
                    <TableCell className="text-right font-medium">{formatCurrency(balanceSheetData.equity.currentYearEarnings)}</TableCell>
                  </TableRow>
                  <TableRow className="border-t border-slate-200 font-semibold bg-green-50 dark:bg-green-900/20">
                    <TableCell className="font-bold">إجمالي حقوق الملكية</TableCell>
                    <TableCell className="text-right font-bold text-green-600">{formatCurrency(balanceSheetData.equity.total)}</TableCell>
                  </TableRow>

                  {/* Total Liabilities & Equity */}
                  <TableRow className="border-t-2 border-green-400 font-bold bg-green-100 dark:bg-green-900/30">
                    <TableCell className="font-bold text-lg">إجمالي الخصوم وحقوق الملكية</TableCell>
                    <TableCell className="text-right font-bold text-green-600 text-lg">{formatCurrency(balanceSheetData.liabilities.totalLiabilities + balanceSheetData.equity.total)}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 