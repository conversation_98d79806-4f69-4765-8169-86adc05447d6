import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { whatsappService } from '@/lib/whatsapp'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const customerId = searchParams.get('customerId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const where: Record<string, unknown> = {}

    if (search) {
      where.OR = [
        { number: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (customerId) {
      where.customerId = customerId
    }

    const [quotations, total] = await Promise.all([
      prisma.quotation.findMany({
        where,
        skip,
        take: limit,
        include: {
          customer: true,
          user: true,
          items: {
            include: {
              product: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.quotation.count({ where }),
    ])

    return NextResponse.json({
      quotations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching quotations:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      customerId,
      validUntil,
      items,
      taxAmount,
      discount,
      notes,
    } = body

    if (!customerId || !items || items.length === 0) {
      return NextResponse.json(
        { error: 'Customer and items are required' },
        { status: 400 }
      )
    }

    // Generate quotation number
    const lastQuotation = await prisma.quotation.findFirst({
      orderBy: { createdAt: 'desc' },
    })

    const nextNumber = lastQuotation
      ? `QUO-${String(parseInt(lastQuotation.number.split('-')[1]) + 1).padStart(3, '0')}`
      : 'QUO-001'

    // Calculate totals
    const subtotal = items.reduce((sum: number, item: { quantity: string; unitPrice: string }) =>
      sum + (parseFloat(item.quantity) * parseFloat(item.unitPrice)), 0
    )

    const calculatedTaxAmount = taxAmount || (subtotal * 0.15) // Default 15% tax
    const calculatedDiscount = discount || 0
    const total = subtotal + calculatedTaxAmount - calculatedDiscount

    const quotation = await prisma.quotation.create({
      data: {
        number: nextNumber,
        customerId,
        userId: session.user?.id || '',
        validUntil: validUntil ? new Date(validUntil) : null,
        subtotal,
        taxAmount: calculatedTaxAmount,
        discount: calculatedDiscount,
        total,
        notes,
        items: {
          create: items.map((item: { description: string; quantity: string; unitPrice: string; productId?: string }) => ({
            description: item.description,
            quantity: parseFloat(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            total: parseFloat(item.quantity) * parseFloat(item.unitPrice),
            productId: item.productId || null,
          })),
        },
      },
      include: {
        customer: true,
        user: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    // Send WhatsApp notification to customer
    if (quotation.customer?.phone) {
      await whatsappService.sendQuotationNotification(
        quotation.customer.phone,
        quotation.number,
        total,
        validUntil ? new Date(validUntil).toLocaleDateString() : 'No expiry',
        'en'
      )
    }

    return NextResponse.json(quotation, { status: 201 })
  } catch (error) {
    console.error('Error creating quotation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
