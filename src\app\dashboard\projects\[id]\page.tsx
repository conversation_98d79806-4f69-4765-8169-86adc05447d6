"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { 
  ArrowLeft, 
  Edit, 
  Calendar, 
  User, 
  DollarSign, 
  Target, 
  Clock, 
  Users,
  FileText,
  CheckCircle,
  AlertTriangle,
  Trash2,
  Eye,
  BarChart3,
  Settings,
  Home,
  Receipt,
  Upload,
  Download,
  FolderPlus,
  Search,
  Filter,
  MoreHorizontal,
  MessageSquare
} from "lucide-react"
import Link from "next/link"
import { formatCurrency } from "@/lib/localization"
import { TeamMembersManager } from "@/components/projects/team-members-manager"
import { ProjectTasksManager } from "@/components/projects/project-tasks-manager"
import { ProjectFinancialOverview } from "@/components/projects/project-financial-overview"

import { ProjectInvoicesManager } from "@/components/projects/project-invoices-manager"
import { ProjectExpensesManager } from "@/components/projects/project-expenses-manager"
import { ProjectDocumentsManager } from "@/components/projects/project-documents-manager"
import { ProjectCommentsManager } from "@/components/projects/project-comments-manager"

interface Project {
  id: string
  code: string
  name: string
  nameAr?: string
  description?: string
  status: string
  priority: string
  startDate: string
  endDate?: string
  budget?: number
  actualCost?: number
  progress: number
  notes?: string
  createdAt: string
  updatedAt: string
  client?: {
    id: string
    name: string
    company?: string
  }
  manager?: {
    id: string
    name: string
    email: string
  }
  createdBy?: {
    id: string
    name: string
    email: string
  }
  tasks?: any[]
  teamMembers?: any[]
  _count?: {
    tasks: number
    teamMembers: number
  }
}

const statusColors = {
  PLANNING: "bg-blue-100 text-blue-800",
  IN_PROGRESS: "bg-yellow-100 text-yellow-800",
  ON_HOLD: "bg-orange-100 text-orange-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
}

const priorityColors = {
  LOW: "bg-gray-100 text-gray-800",
  MEDIUM: "bg-blue-100 text-blue-800",
  HIGH: "bg-orange-100 text-orange-800",
  URGENT: "bg-red-100 text-red-800",
}

export default function ProjectDetailPage() {
  const router = useRouter()
  const params = useParams()
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)
  
  // Persistent state for comments and documents across tabs
  const [projectComments, setProjectComments] = useState<any[]>([])
  
  const [projectDocuments, setProjectDocuments] = useState<any[]>([])
  const [projectFolders, setProjectFolders] = useState<any[]>([])
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())

  // Financial statistics state
  const [financialStats, setFinancialStats] = useState({
    invoices: {
      total: 0,
      totalAmount: 0,
      paidAmount: 0,
      outstandingAmount: 0,
      count: { paid: 0, unpaid: 0, partial: 0, overdue: 0 }
    },
    expenses: {
      total: 0,
      totalAmount: 0,
      pendingAmount: 0,
      approvedAmount: 0,
      processedAmount: 0,
      count: { pending: 0, approved: 0, rejected: 0, processed: 0 }
    },
    payments: {
      totalReceived: 0,
      recentPaymentsCount: 0,
      collectionRate: 0
    }
  })
  const [financialLoading, setFinancialLoading] = useState(true)

  useEffect(() => {
    const loadProject = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/projects/${params.id}`)
        if (response.ok) {
          const data = await response.json()
          setProject(data)
          // Load financial stats after project is loaded
          if (data.id) {
            loadFinancialStats(data.id)
          }
        } else {
          alert('Failed to load project data')
          router.push('/dashboard/projects')
        }
      } catch (error) {
        console.error('Error loading project:', error)
        alert('Failed to load project data')
        router.push('/dashboard/projects')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      loadProject()
    }
  }, [params.id, router])

  const loadFinancialStats = async (projectId: string) => {
    try {
      setFinancialLoading(true)
      
      // Fetch invoices and expenses in parallel
      const [invoicesResponse, expensesResponse] = await Promise.all([
        fetch(`/api/invoices?projectId=${projectId}&limit=1000`),
        fetch(`/api/expenses?projectId=${projectId}&limit=1000`)
      ])

      const invoicesData = invoicesResponse.ok ? await invoicesResponse.json() : { invoices: [] }
      const expensesData = expensesResponse.ok ? await expensesResponse.json() : { expenses: [] }

      const invoices = invoicesData.invoices || []
      const expenses = expensesData.expenses || []

      // Calculate invoice stats with payments
      let totalInvoiced = 0
      let totalPaid = 0
      let paidCount = 0, unpaidCount = 0, partialCount = 0, overdueCount = 0
      let totalPaymentsReceived = 0

      // Process each invoice to get payment data
      for (const invoice of invoices) {
        const invoiceTotal = Number(invoice.total || 0)
        totalInvoiced += invoiceTotal
        
        // Fetch payments for this invoice
        try {
          const paymentsResponse = await fetch(`/api/invoices/${invoice.id}/payments`)
          if (paymentsResponse.ok) {
            const paymentsData = await paymentsResponse.json()
            const payments = paymentsData.payments || []
            const invoicePaidAmount = payments.reduce((sum: number, payment: any) => sum + Number(payment.amount), 0)
            totalPaid += invoicePaidAmount
            totalPaymentsReceived += invoicePaidAmount
          }
        } catch (error) {
          console.error(`Error fetching payments for invoice ${invoice.id}:`, error)
        }

        // Count by status
        switch (invoice.status) {
          case 'PAID': paidCount++; break
          case 'UNPAID': unpaidCount++; break
          case 'PARTIAL': partialCount++; break
          case 'OVERDUE': overdueCount++; break
        }
      }

      // Calculate expense stats
      const totalExpenses = expenses.length
      const totalExpenseAmount = expenses.reduce((sum: number, exp: any) => sum + Number(exp.amount || 0), 0)
      
      const pendingExpenses = expenses.filter((exp: any) => exp.status === 'PENDING')
      const approvedExpenses = expenses.filter((exp: any) => exp.status === 'APPROVED')
      const rejectedExpenses = expenses.filter((exp: any) => exp.status === 'REJECTED')
      const processedExpenses = expenses.filter((exp: any) => exp.status === 'PAID')

      const pendingAmount = pendingExpenses.reduce((sum: number, exp: any) => sum + Number(exp.amount || 0), 0)
      const approvedAmount = approvedExpenses.reduce((sum: number, exp: any) => sum + Number(exp.amount || 0), 0)
      const processedAmount = processedExpenses.reduce((sum: number, exp: any) => sum + Number(exp.amount || 0), 0)

      setFinancialStats({
        invoices: {
          total: invoices.length,
          totalAmount: totalInvoiced,
          paidAmount: totalPaid,
          outstandingAmount: totalInvoiced - totalPaid,
          count: { paid: paidCount, unpaid: unpaidCount, partial: partialCount, overdue: overdueCount }
        },
        expenses: {
          total: totalExpenses,
          totalAmount: totalExpenseAmount,
          pendingAmount,
          approvedAmount,
          processedAmount,
          count: { 
            pending: pendingExpenses.length, 
            approved: approvedExpenses.length, 
            rejected: rejectedExpenses.length, 
            processed: processedExpenses.length 
          }
        },
        payments: {
          totalReceived: totalPaymentsReceived,
          recentPaymentsCount: invoices.reduce((count: number, inv: any) => {
            return count + (inv.payments?.length || 0)
          }, 0),
          collectionRate: totalInvoiced > 0 ? (totalPaid / totalInvoiced) * 100 : 0
        }
      })
    } catch (error) {
      console.error('Error loading financial stats:', error)
    } finally {
      setFinancialLoading(false)
    }
  }

  const handleDeleteProject = async () => {
    if (!project) return
    
    if (confirm(`Are you sure you want to delete project "${project.name}"?`)) {
      try {
        const response = await fetch(`/api/projects/${project.id}`, {
          method: 'DELETE'
        })
        
        if (response.ok) {
          alert(`Project "${project.name}" deleted successfully!`)
          router.push('/dashboard/projects')
        } else {
          const error = await response.json()
          alert(`Failed to delete project: ${error.error}`)
        }
      } catch (error) {
        console.error('Error deleting project:', error)
        alert('Failed to delete project')
      }
    }
  }

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return "bg-green-500"
    if (progress >= 60) return "bg-blue-500"
    if (progress >= 40) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getDaysRemaining = (endDate: string) => {
    const now = new Date()
    const end = new Date(endDate)
    const diffTime = end.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h3 className="text-lg font-medium">Project not found</h3>
            <p className="text-muted-foreground">The project you&apos;re looking for doesn&apos;t exist.</p>
            <Button onClick={() => router.push('/dashboard/projects')} className="mt-4">
              Back to Projects
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const daysRemaining = project.endDate ? getDaysRemaining(project.endDate) : null

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Project Details</h2>
            <p className="text-muted-foreground">
              View project information and progress
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            onClick={handleDeleteProject}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
          <Button onClick={() => router.push(`/dashboard/projects/${project.id}/edit`)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Project
          </Button>
        </div>
      </div>

      {/* Project Header */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold">{project.name}</h1>
                <Badge className={statusColors[project.status as keyof typeof statusColors]}>
                  {project.status.replace('_', ' ')}
                </Badge>
                <Badge className={priorityColors[project.priority as keyof typeof priorityColors]}>
                  {project.priority}
                </Badge>
              </div>
              {project.nameAr && (
                <p className="text-lg text-muted-foreground mb-2" dir="rtl">{project.nameAr}</p>
              )}
              <p className="text-muted-foreground mb-4">{project.description}</p>
              <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <FileText className="mr-1 h-4 w-4" />
                  {project.code}
                </div>
                <div className="flex items-center">
                  <Calendar className="mr-1 h-4 w-4" />
                  Started {new Date(project.startDate).toLocaleDateString()}
                </div>
                {project.endDate && (
                  <div className="flex items-center">
                    <Clock className="mr-1 h-4 w-4" />
                    {daysRemaining !== null && daysRemaining > 0 
                      ? `${daysRemaining} days remaining`
                      : daysRemaining === 0 
                      ? 'Due today'
                      : `${Math.abs(daysRemaining!)} days overdue`
                    }
                  </div>
                )}
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold mb-1">{project.progress}%</div>
              <div className="text-sm text-muted-foreground mb-2">Complete</div>
              <Progress 
                value={project.progress} 
                className="w-32"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabbed Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-9">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Home className="h-4 w-4" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          <TabsTrigger value="tasks" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            <span className="hidden sm:inline">Tasks</span>
          </TabsTrigger>
          <TabsTrigger value="team" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="hidden sm:inline">Team</span>
          </TabsTrigger>
          <TabsTrigger value="invoices" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">Invoices</span>
          </TabsTrigger>
          <TabsTrigger value="expenses" className="flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            <span className="hidden sm:inline">Expenses</span>
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">Documents</span>
          </TabsTrigger>
          <TabsTrigger value="comments" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span className="hidden sm:inline">Notes</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <span className="hidden sm:inline">Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span className="hidden sm:inline">Settings</span>
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Budget & Cost */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5" />
                    <span>Budget & Cost</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-900">
                        {formatCurrency(Number(project.budget) || 0)}
                      </div>
                      <div className="text-sm text-blue-600">Budget</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-900">
                        {formatCurrency(Number(project.actualCost) || 0)}
                      </div>
                      <div className="text-sm text-orange-600">Actual Cost</div>
                    </div>
                  </div>
                  {project.budget && (
                    <div className="mt-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Budget Usage</span>
                        <span>{((Number(project.actualCost) || 0) / Number(project.budget) * 100).toFixed(1)}%</span>
                      </div>
                      <Progress 
                        value={(Number(project.actualCost) || 0) / Number(project.budget) * 100} 
                        className="h-2"
                      />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5" />
                    <span>Timeline</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Start Date</span>
                      <span className="font-medium">{new Date(project.startDate).toLocaleDateString()}</span>
                    </div>
                    {project.endDate && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">End Date</span>
                        <span className="font-medium">{new Date(project.endDate).toLocaleDateString()}</span>
                      </div>
                    )}
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Created</span>
                      <span className="font-medium">{new Date(project.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Last Updated</span>
                      <span className="font-medium">{new Date(project.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Financial Overview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5" />
                    <span>Financial Overview</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {financialLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* Invoice Statistics */}
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          Invoices & Payments
                        </h4>
                        <div className="grid grid-cols-2 gap-3">
                          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="text-lg font-bold text-blue-900">
                              {formatCurrency(financialStats.invoices.totalAmount)}
                            </div>
                            <div className="text-xs text-blue-600">Total Invoiced</div>
                            <div className="text-xs text-muted-foreground">{financialStats.invoices.total} invoice(s)</div>
                          </div>
                          <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                            <div className="text-lg font-bold text-green-900">
                              {formatCurrency(financialStats.invoices.paidAmount)}
                            </div>
                            <div className="text-xs text-green-600">Paid</div>
                            <div className="text-xs text-muted-foreground">{financialStats.invoices.count.paid + financialStats.invoices.count.partial} payment(s)</div>
                          </div>
                          <div className="p-3 bg-red-50 rounded-lg border border-red-200">
                            <div className="text-lg font-bold text-red-900">
                              {formatCurrency(financialStats.invoices.outstandingAmount)}
                            </div>
                            <div className="text-xs text-red-600">Outstanding</div>
                            <div className="text-xs text-muted-foreground">{financialStats.invoices.count.unpaid + financialStats.invoices.count.overdue + financialStats.invoices.count.partial} pending</div>
                          </div>
                          <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                            <div className="text-lg font-bold text-purple-900">
                              {financialStats.payments.collectionRate.toFixed(1)}%
                            </div>
                            <div className="text-xs text-purple-600">Collection Rate</div>
                            <div className="text-xs text-muted-foreground">of total invoiced</div>
                          </div>
                        </div>

                        {/* Invoice Status Breakdown */}
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <div className="text-xs font-medium text-muted-foreground mb-2">Invoice Status</div>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-green-500"></div>
                              <span>Paid: {financialStats.invoices.count.paid}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                              <span>Partial: {financialStats.invoices.count.partial}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-red-500"></div>
                              <span>Unpaid: {financialStats.invoices.count.unpaid}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-red-700"></div>
                              <span>Overdue: {financialStats.invoices.count.overdue}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Expense Statistics */}
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                          <Receipt className="h-4 w-4" />
                          Expenses
                        </h4>
                        <div className="grid grid-cols-2 gap-3">
                          <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                            <div className="text-lg font-bold text-orange-900">
                              {formatCurrency(financialStats.expenses.totalAmount)}
                            </div>
                            <div className="text-xs text-orange-600">Total Expenses</div>
                            <div className="text-xs text-muted-foreground">{financialStats.expenses.total} expense(s)</div>
                          </div>
                          <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                            <div className="text-lg font-bold text-green-900">
                              {formatCurrency(financialStats.expenses.processedAmount)}
                            </div>
                            <div className="text-xs text-green-600">Processed</div>
                            <div className="text-xs text-muted-foreground">{financialStats.expenses.count.processed} expense(s)</div>
                          </div>
                          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="text-lg font-bold text-blue-900">
                              {formatCurrency(financialStats.expenses.approvedAmount)}
                            </div>
                            <div className="text-xs text-blue-600">Approved</div>
                            <div className="text-xs text-muted-foreground">{financialStats.expenses.count.approved} expense(s)</div>
                          </div>
                          <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                            <div className="text-lg font-bold text-yellow-900">
                              {formatCurrency(financialStats.expenses.pendingAmount)}
                            </div>
                            <div className="text-xs text-yellow-600">Pending</div>
                            <div className="text-xs text-muted-foreground">{financialStats.expenses.count.pending} expense(s)</div>
                          </div>
                        </div>

                        {/* Expense Status Breakdown */}
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <div className="text-xs font-medium text-muted-foreground mb-2">Expense Status</div>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-green-500"></div>
                              <span>Processed: {financialStats.expenses.count.processed}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                              <span>Approved: {financialStats.expenses.count.approved}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                              <span>Pending: {financialStats.expenses.count.pending}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-red-500"></div>
                              <span>Rejected: {financialStats.expenses.count.rejected}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Financial Health Summary */}
                      <div className="border-t pt-4">
                        <h4 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                          <BarChart3 className="h-4 w-4" />
                          Financial Health
                        </h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between items-center">
                            <span className="text-muted-foreground">Revenue (Invoiced)</span>
                            <span className="font-medium text-green-600">{formatCurrency(financialStats.invoices.totalAmount)}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-muted-foreground">Costs (Expenses)</span>
                            <span className="font-medium text-red-600">{formatCurrency(financialStats.expenses.totalAmount)}</span>
                          </div>
                          <div className="flex justify-between items-center border-t pt-2">
                            <span className="font-medium">Net Profit</span>
                            <span className={`font-bold ${(financialStats.invoices.totalAmount - financialStats.expenses.totalAmount) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {formatCurrency(financialStats.invoices.totalAmount - financialStats.expenses.totalAmount)}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-muted-foreground">Profit Margin</span>
                            <span className={`font-medium ${financialStats.invoices.totalAmount > 0 ? (((financialStats.invoices.totalAmount - financialStats.expenses.totalAmount) / financialStats.invoices.totalAmount) * 100) >= 0 ? 'text-green-600' : 'text-red-600' : 'text-gray-500'}`}>
                              {financialStats.invoices.totalAmount > 0 
                                ? `${(((financialStats.invoices.totalAmount - financialStats.expenses.totalAmount) / financialStats.invoices.totalAmount) * 100).toFixed(1)}%`
                                : 'N/A'
                              }
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Notes */}
              {project.notes && (
                <Card>
                  <CardHeader>
                    <CardTitle>Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground whitespace-pre-wrap">{project.notes}</p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Client Information */}
              {project.client && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <User className="h-5 w-5" />
                      <span>Client</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${project.client.name}`} />
                        <AvatarFallback>
                          {project.client.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{project.client.name}</div>
                        {project.client.company && (
                          <div className="text-sm text-muted-foreground">{project.client.company}</div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Project Manager */}
              {project.manager && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Users className="h-5 w-5" />
                      <span>Project Manager</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-3">
                      <Avatar>
                        <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${project.manager.name}`} />
                        <AvatarFallback>
                          {project.manager.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{project.manager.name}</div>
                        <div className="text-sm text-muted-foreground">{project.manager.email}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Project Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Target className="h-5 w-5" />
                    <span>Project Stats</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Tasks</span>
                      <span className="font-medium">{project.tasks?.length || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Team Members</span>
                      <span className="font-medium">{project._count?.teamMembers || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Progress</span>
                      <span className="font-medium">{project.progress}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Invoices Tab */}
        <TabsContent value="invoices" className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <ProjectInvoicesManager projectId={project.id} isEditable={true} />
            </CardContent>
          </Card>
          

        </TabsContent>

        {/* Expenses Tab */}
        <TabsContent value="expenses" className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <ProjectExpensesManager projectId={project.id} isEditable={true} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents" className="space-y-6">
          <ProjectDocumentsManager 
            projectId={project.id} 
            isEditable={true}
            documents={projectDocuments}
            setDocuments={setProjectDocuments}
            folders={projectFolders}
            setFolders={setProjectFolders}
            expandedFolders={expandedFolders}
            setExpandedFolders={setExpandedFolders}
          />
        </TabsContent>

        {/* Comments/Notes Tab */}
        <TabsContent value="comments" className="space-y-6">
          <ProjectCommentsManager 
            projectId={project.id} 
            isEditable={true}
            comments={projectComments}
            setComments={setProjectComments}
          />
        </TabsContent>

        {/* Tasks Tab */}
        <TabsContent value="tasks" className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <ProjectTasksManager projectId={project.id} isEditable={true} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Team Tab */}
        <TabsContent value="team" className="space-y-6">
          <Card>
            <CardContent className="pt-6">
              <TeamMembersManager projectId={project.id} isEditable={true} />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Financial Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ProjectFinancialOverview 
                projectId={project.id} 
                projectBudget={project.budget || 0}
                projectActualCost={project.actualCost || 0}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Project Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Actions</h3>
                  <div className="space-y-3">
                    <Button className="w-full justify-start" asChild>
                      <Link href={`/dashboard/projects/${project.id}/edit`}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Project Details
                      </Link>
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <FileText className="mr-2 h-4 w-4" />
                      Export Project Report
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Receipt className="mr-2 h-4 w-4" />
                      Generate Invoice Summary
                    </Button>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-red-600">Danger Zone</h3>
                  <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                    <h4 className="font-medium text-red-800 mb-2">Delete Project</h4>
                    <p className="text-sm text-red-600 mb-4">
                      This action cannot be undone. This will permanently delete the project and all associated data.
                    </p>
                    <Button 
                      variant="destructive" 
                      onClick={handleDeleteProject}
                      className="w-full"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Project
                    </Button>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Project Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Project ID:</span>
                    <span className="ml-2 font-mono">{project.id}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Created:</span>
                    <span className="ml-2">{new Date(project.createdAt).toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Last Updated:</span>
                    <span className="ml-2">{new Date(project.updatedAt).toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Created By:</span>
                    <span className="ml-2">{project.createdBy?.name || 'Unknown'}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
