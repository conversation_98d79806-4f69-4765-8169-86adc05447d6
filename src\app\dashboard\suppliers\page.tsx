"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Edit, Trash2, Phone, Mail, Building, MoreHorizontal, Eye, BarChart3, Receipt, Package, DollarSign } from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

interface Supplier {
  id: string
  name: string
  email?: string
  mobile?: string
  phone?: string
  contactPerson?: string
  taxNumber?: string
  address?: string
  isActive?: boolean
  createdAt?: string
  updatedAt?: string
  totalPurchases?: number
  outstandingPayments?: number
  purchaseOrderCount?: number
}

export default function SuppliersPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    mobile: '',
    contactPerson: '',
    taxNumber: '',
    address: ''
  })

  // Fetch suppliers from API
  const fetchSuppliers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/suppliers')
      if (response.ok) {
        const data = await response.json()
        setSuppliers(data.suppliers || [])
      } else {
        console.error('Failed to fetch suppliers')
        toast.error('Failed to load suppliers')
      }
    } catch (error) {
      console.error('Error fetching suppliers:', error)
      toast.error('Error loading suppliers')
    } finally {
      setLoading(false)
    }
  }

  // Load suppliers on component mount
  useEffect(() => {
    fetchSuppliers()
  }, [])

  const filteredSuppliers = suppliers.filter(supplier =>
    supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (supplier.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (supplier.contactPerson || '').toLowerCase().includes(searchTerm.toLowerCase())
  )

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      mobile: '',
      contactPerson: '',
      taxNumber: '',
      address: ''
    })
  }

  const populateForm = (supplier: Supplier) => {
    setFormData({
      name: supplier.name || '',
      email: supplier.email || '',
      phone: supplier.phone || '',
      mobile: supplier.mobile || '',
      contactPerson: supplier.contactPerson || '',
      taxNumber: supplier.taxNumber || '',
      address: supplier.address || ''
    })
  }

  const handleAddSupplier = () => {
    setEditingSupplier(null)
    resetForm()
    setIsDialogOpen(true)
  }

  const handleViewSupplier = (supplier: Supplier) => {
    router.push(`/dashboard/suppliers/${supplier.id}`)
  }

  const handleEditSupplier = (supplier: Supplier) => {
    setEditingSupplier(supplier)
    populateForm(supplier)
    setIsDialogOpen(true)
  }

  const handleDeleteSupplier = async (supplier: Supplier) => {
    if (confirm(`${t('suppliers.confirmDelete')}\n\n${supplier.name}`)) {
      try {
        const response = await fetch(`/api/suppliers/${supplier.id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          setSuppliers(suppliers.filter(s => s.id !== supplier.id))
          toast.success(`🗑️ ${t('suppliers.supplierDeleted')}: ${supplier.name}`)
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to delete supplier')
        }
      } catch (error) {
        console.error('Error deleting supplier:', error)
        toast.error('Error deleting supplier')
      }
    }
  }

  const handleCreatePurchaseOrder = (supplier: Supplier) => {
    router.push(`/dashboard/purchases/create?supplier=${supplier.id}`)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSaveSupplier = async () => {
    if (!formData.name || !formData.mobile) {
      toast.error(`${t('forms.required')}: ${t('suppliers.supplierName')}, Mobile`)
      return
    }

    try {
      const method = editingSupplier ? 'PUT' : 'POST'
      const url = editingSupplier ? `/api/suppliers/${editingSupplier.id}` : '/api/suppliers'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const savedSupplier = await response.json()

        if (editingSupplier) {
          // Update existing supplier in state
          setSuppliers(suppliers.map(supplier =>
            supplier.id === editingSupplier.id ? savedSupplier : supplier
          ))
          toast.success(`✅ ${t('suppliers.supplierUpdated')}: ${savedSupplier.name}`)
        } else {
          // Add new supplier to state
          setSuppliers([...suppliers, savedSupplier])
          toast.success(`✅ ${t('suppliers.supplierAdded')}: ${savedSupplier.name}`)
        }

        handleCloseDialog()
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to save supplier')
      }
    } catch (error) {
      console.error('Error saving supplier:', error)
      toast.error('Error saving supplier')
    }
  }

  const handleCloseDialog = () => {
    setIsDialogOpen(false)
    setEditingSupplier(null)
    resetForm()
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('suppliers.title')}</h2>
          <p className="text-muted-foreground">
            {t('nav.suppliers')} - إدارة شبكة الموردين وعلاقات المشتريات
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/suppliers/dashboard')}>
            <BarChart3 className="mr-2 h-4 w-4" />
            {t('suppliers.dashboard')}
          </Button>
          <Button onClick={handleAddSupplier}>
            <Plus className="mr-2 h-4 w-4" />
            {t('suppliers.addSupplier')}
          </Button>
          <Dialog open={isDialogOpen} onOpenChange={handleCloseDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>{editingSupplier ? t('suppliers.editSupplier') : t('suppliers.addSupplier')}</DialogTitle>
              <DialogDescription>
                {editingSupplier ? 'تحديث تفاصيل المورد أدناه.' : 'أدخل تفاصيل المورد أدناه.'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  {t('suppliers.supplierName')} *
                </Label>
                <Input
                  id="name"
                  className="col-span-3"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t('suppliers.supplierName')}
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  {t('suppliers.supplierEmail')}
                </Label>
                <Input
                  id="email"
                  type="email"
                  className="col-span-3"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="mobile" className="text-right">
                  Mobile *
                </Label>
                <Input
                  id="mobile"
                  className="col-span-3"
                  value={formData.mobile}
                  onChange={(e) => handleInputChange('mobile', e.target.value)}
                  placeholder="+968 9123 4567"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone" className="text-right">
                  {t('suppliers.supplierPhone')}
                </Label>
                <Input
                  id="phone"
                  className="col-span-3"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+968 2456 7890"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="contactPerson" className="text-right">
                  {t('suppliers.contactPerson')}
                </Label>
                <Input
                  id="contactPerson"
                  className="col-span-3"
                  value={formData.contactPerson}
                  onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                  placeholder={t('suppliers.contactPerson')}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="taxNumber" className="text-right">
                  {t('suppliers.taxNumber')}
                </Label>
                <Input
                  id="taxNumber"
                  className="col-span-3"
                  value={formData.taxNumber}
                  onChange={(e) => handleInputChange('taxNumber', e.target.value)}
                  placeholder="OM123456789"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="address" className="text-right">
                  {t('suppliers.supplierAddress')}
                </Label>
                <Textarea
                  id="address"
                  className="col-span-3"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder={t('suppliers.supplierAddress')}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={handleCloseDialog}>
                {t('common.cancel')}
              </Button>
              <Button type="submit" onClick={handleSaveSupplier}>
                {editingSupplier ? t('suppliers.editSupplier') : t('common.save')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <div className="rounded-lg border p-4 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">{suppliers.length}</div>
          <div className="text-sm text-blue-600 dark:text-blue-400">{t('suppliers.totalSuppliers')}</div>
        </div>
        <div className="rounded-lg border p-4 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
          <div className="text-2xl font-bold text-green-700 dark:text-green-300">{suppliers.filter(s => s.isActive).length}</div>
          <div className="text-sm text-green-600 dark:text-green-400">{t('suppliers.activeSuppliers')}</div>
        </div>
        <div className="rounded-lg border p-4 bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">{suppliers.reduce((sum, s) => sum + (s.purchaseOrderCount || 0), 0)}</div>
          <div className="text-sm text-purple-600 dark:text-purple-400">{t('suppliers.purchaseOrders')}</div>
        </div>
        <div className="rounded-lg border p-4 bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <div className="text-2xl font-bold text-orange-700 dark:text-orange-300">{formatCurrency(suppliers.reduce((sum, s) => sum + (s.totalPurchases || 0), 0))}</div>
          <div className="text-sm text-orange-600 dark:text-orange-400">{t('suppliers.totalPurchases')}</div>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('suppliers.searchSuppliers')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="table-container">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="font-semibold text-foreground">{t('suppliers.supplierName')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('common.contact')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('suppliers.totalPurchases')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('suppliers.outstandingPayments')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('suppliers.purchaseOrders')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('common.status')}</TableHead>
              <TableHead className="text-right font-semibold text-foreground">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-12 text-muted-foreground">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="text-4xl">⏳</div>
                    <div className="text-lg font-medium">Loading suppliers...</div>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredSuppliers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-12 text-muted-foreground">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="text-4xl">🏢</div>
                    <div className="text-lg font-medium">{t('suppliers.noSuppliersFound')}</div>
                    <div className="text-sm">{searchTerm ? 'جرب مصطلح بحث مختلف' : 'ابدأ بإضافة مورد جديد'}</div>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredSuppliers.map((supplier) => (
                <TableRow key={supplier.id} className="hover:bg-muted/30 transition-colors">
                  <TableCell>
                    <div>
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium text-primary hover:text-primary/80 hover:underline"
                        onClick={() => handleViewSupplier(supplier)}
                      >
                        {supplier.name}
                      </Button>
                      {supplier.contactPerson && (
                        <div className="text-sm text-muted-foreground">{supplier.contactPerson}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {supplier.mobile && (
                        <div className="flex items-center text-sm">
                          <Phone className="mr-1 h-3 w-3 text-muted-foreground" />
                          <span className="font-medium">Mobile:</span>&nbsp;{supplier.mobile}
                        </div>
                      )}
                      {supplier.phone && (
                        <div className="flex items-center text-sm">
                          <Phone className="mr-1 h-3 w-3 text-muted-foreground" />
                          <span className="font-medium">Phone:</span>&nbsp;{supplier.phone}
                        </div>
                      )}
                      {supplier.email && (
                        <div className="flex items-center text-sm">
                          <Mail className="mr-1 h-3 w-3 text-muted-foreground" />
                          {supplier.email}
                        </div>
                      )}
                    </div>
                  </TableCell>
                <TableCell className="font-medium text-purple-700 dark:text-purple-400">
                  {formatCurrency(supplier.totalPurchases || 0)}
                </TableCell>
                <TableCell>
                  {(supplier.outstandingPayments || 0) > 0 ? (
                    <span className="text-orange-600 font-medium bg-orange-50 dark:bg-orange-950 px-2 py-1 rounded-md">
                      {formatCurrency(supplier.outstandingPayments || 0)}
                    </span>
                  ) : (
                    <span className="text-green-600 bg-green-50 dark:bg-green-950 px-2 py-1 rounded-md font-medium">{t('customers.paid')}</span>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="bg-purple-50 dark:bg-purple-950 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-800">
                    {supplier.purchaseOrderCount || 0} {t('suppliers.purchaseOrders')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={(supplier.isActive !== false) ? "default" : "secondary"}>
                    {(supplier.isActive !== false) ? t('common.active') : t('common.inactive')}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewSupplier(supplier)}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t('suppliers.viewDetails')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditSupplier(supplier)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('suppliers.editSupplier')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleCreatePurchaseOrder(supplier)}>
                        <Receipt className="mr-2 h-4 w-4" />
                        {t('suppliers.createPurchaseOrder')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => router.push(`/dashboard/supplier-payouts/create?supplierId=${supplier.id}`)}>
                        <DollarSign className="mr-2 h-4 w-4" />
                        Create Payout
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteSupplier(supplier)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('suppliers.deleteSupplier')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
