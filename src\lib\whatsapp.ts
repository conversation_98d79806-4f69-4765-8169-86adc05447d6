import axios from 'axios'
import { formatCurrency, formatOmaniPhone, getCurrentMuscatTime, isBusinessHours } from './localization'

interface WhatsAppMessage {
  to: string
  message: string
  type?: 'text' | 'document' | 'image'
  locale?: string
}

interface WhatsAppConfig {
  apiUrl: string
  accountKey: string
  secretKey: string
}

class WhatsAppService {
  private config: WhatsAppConfig

  constructor() {
    this.config = {
      apiUrl: process.env.WHATSAPP_API_URL || '',
      accountKey: process.env.WHATSAPP_ACCOUNT_KEY || '',
      secretKey: process.env.WHATSAPP_SECRET_KEY || ''
    }
  }

  async sendMessage({ to, message, type = 'text', locale = 'en' }: WhatsAppMessage): Promise<boolean> {
    try {
      if (!this.config.apiUrl || !this.config.accountKey || !this.config.secretKey) {
        console.warn('WhatsApp configuration is incomplete')
        return false
      }

      const response = await axios.post(
        `${this.config.apiUrl}/send`,
        {
          to,
          message,
          type
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.secretKey}`,
            'Account-Key': this.config.accountKey,
            'Content-Type': 'application/json'
          }
        }
      )

      return response.status === 200
    } catch (error) {
      console.error('WhatsApp send error:', error)
      return false
    }
  }

  // Enhanced Omani-specific message templates
  async sendTaskAssignedNotification(employeePhone: string, taskTitle: string, locale: string = 'en'): Promise<boolean> {
    const formattedPhone = formatOmaniPhone(employeePhone)
    const currentTime = getCurrentMuscatTime()

    const message = locale === 'ar'
      ? `🔔 مهمة جديدة\n\nتم تعيين مهمة جديدة لك:\n"${taskTitle}"\n\nالوقت: ${currentTime.toLocaleString('ar-OM', { timeZone: 'Asia/Muscat' })}\n\nيرجى تسجيل الدخول لعرض التفاصيل.\n\n📱 خدمات المكاتب المحترفة`
      : `🔔 New Task Assigned\n\nYou have been assigned a new task:\n"${taskTitle}"\n\nTime: ${currentTime.toLocaleString('en-OM', { timeZone: 'Asia/Muscat' })}\n\nPlease log in to view details.\n\n📱 Office Services Pro`

    return this.sendMessage({ to: formattedPhone, message, locale })
  }

  async sendTaskStartedNotification(phone: string, taskTitle: string, locale: string = 'en'): Promise<boolean> {
    const formattedPhone = formatOmaniPhone(phone)

    const message = locale === 'ar'
      ? `✅ بدء المهمة\n\nتم بدء العمل في المهمة:\n"${taskTitle}"\n\nسيتم إشعارك عند الانتهاء.\n\n📱 خدمات المكاتب المحترفة`
      : `✅ Task Started\n\nWork has begun on your task:\n"${taskTitle}"\n\nYou will be notified when completed.\n\n📱 Office Services Pro`

    return this.sendMessage({ to: formattedPhone, message, locale })
  }

  async sendTaskCompletedNotification(phone: string, taskTitle: string, locale: string = 'en'): Promise<boolean> {
    const formattedPhone = formatOmaniPhone(phone)
    const currentTime = getCurrentMuscatTime()

    const message = locale === 'ar'
      ? `🎉 تم إنجاز المهمة\n\nتم الانتهاء من المهمة:\n"${taskTitle}"\n\nوقت الإنجاز: ${currentTime.toLocaleString('ar-OM', { timeZone: 'Asia/Muscat' })}\n\nشكراً لثقتكم بنا!\n\n📱 خدمات المكاتب المحترفة`
      : `🎉 Task Completed\n\nYour task has been completed:\n"${taskTitle}"\n\nCompleted at: ${currentTime.toLocaleString('en-OM', { timeZone: 'Asia/Muscat' })}\n\nThank you for your business!\n\n📱 Office Services Pro`

    return this.sendMessage({ to: formattedPhone, message, locale })
  }

  async sendInvoiceNotification(customerPhone: string, invoiceNumber: string, amount: number, locale: string = 'en'): Promise<boolean> {
    const formattedPhone = formatOmaniPhone(customerPhone)
    const formattedAmount = formatCurrency(amount, locale)

    const message = locale === 'ar'
      ? `📄 فاتورة جديدة\n\nرقم الفاتورة: ${invoiceNumber}\nالمبلغ: ${formattedAmount}\n\nيمكنكم تحميل الفاتورة من النظام أو زيارة مكتبنا.\n\nساعات العمل:\nالأحد-الخميس: 8ص-6م\nالجمعة: 2م-6م\nالسبت: مغلق\n\n📱 خدمات المكاتب المحترفة`
      : `📄 New Invoice\n\nInvoice #: ${invoiceNumber}\nAmount: ${formattedAmount}\n\nYou can download the invoice from our system or visit our office.\n\nBusiness Hours:\nSun-Thu: 8AM-6PM\nFri: 2PM-6PM\nSat: Closed\n\n📱 Office Services Pro`

    return this.sendMessage({ to: formattedPhone, message, locale })
  }

  async sendQuotationNotification(customerPhone: string, quotationNumber: string, amount: number, validUntil: string, locale: string = 'en'): Promise<boolean> {
    const formattedPhone = formatOmaniPhone(customerPhone)
    const formattedAmount = formatCurrency(amount, locale)

    const message = locale === 'ar'
      ? `💰 عرض سعر جديد\n\nرقم العرض: ${quotationNumber}\nالمبلغ: ${formattedAmount}\nصالح حتى: ${validUntil}\n\nيرجى مراجعة العرض والرد في أقرب وقت.\n\nللاستفسار: +968 24 123 456\n\n📱 خدمات المكاتب المحترفة`
      : `💰 New Quotation\n\nQuotation #: ${quotationNumber}\nAmount: ${formattedAmount}\nValid until: ${validUntil}\n\nPlease review and respond at your earliest convenience.\n\nFor inquiries: +968 24 123 456\n\n📱 Office Services Pro`

    return this.sendMessage({ to: formattedPhone, message, locale })
  }

  async sendPaymentReminderNotification(customerPhone: string, invoiceNumber: string, amount: number, dueDate: string, locale: string = 'en'): Promise<boolean> {
    const formattedPhone = formatOmaniPhone(customerPhone)
    const formattedAmount = formatCurrency(amount, locale)

    const message = locale === 'ar'
      ? `⏰ تذكير بالدفع\n\nفاتورة رقم: ${invoiceNumber}\nالمبلغ المستحق: ${formattedAmount}\nتاريخ الاستحقاق: ${dueDate}\n\nيرجى سداد المبلغ في أقرب وقت ممكن.\n\nطرق الدفع المتاحة:\n💳 نقداً في المكتب\n🏦 تحويل بنكي\n\nشكراً لتفهمكم\n\n📱 خدمات المكاتب المحترفة`
      : `⏰ Payment Reminder\n\nInvoice #: ${invoiceNumber}\nAmount due: ${formattedAmount}\nDue date: ${dueDate}\n\nPlease settle the payment at your earliest convenience.\n\nPayment methods:\n💳 Cash at office\n🏦 Bank transfer\n\nThank you for your understanding\n\n📱 Office Services Pro`

    return this.sendMessage({ to: formattedPhone, message, locale })
  }

  async sendPaymentConfirmation(customerPhone: string, invoiceNumber: string, amount: number, status: string, locale: string = 'en'): Promise<boolean> {
    const formattedPhone = formatOmaniPhone(customerPhone)
    const formattedAmount = formatCurrency(amount, locale)
    const currentTime = getCurrentMuscatTime()

    const statusText = locale === 'ar'
      ? (status === 'PAID' ? 'مدفوع بالكامل' : 'مدفوع جزئياً')
      : (status === 'PAID' ? 'Fully Paid' : 'Partially Paid')

    const message = locale === 'ar'
      ? `✅ تأكيد الدفع\n\nتم استلام دفعة للفاتورة رقم: ${invoiceNumber}\nالمبلغ المدفوع: ${formattedAmount}\nالحالة: ${statusText}\nوقت الاستلام: ${currentTime.toLocaleString('ar-OM', { timeZone: 'Asia/Muscat' })}\n\nشكراً لكم!\n\n📱 خدمات المكاتب المحترفة`
      : `✅ Payment Confirmation\n\nPayment received for Invoice #: ${invoiceNumber}\nAmount paid: ${formattedAmount}\nStatus: ${statusText}\nReceived at: ${currentTime.toLocaleString('en-OM', { timeZone: 'Asia/Muscat' })}\n\nThank you!\n\n📱 Office Services Pro`

    return this.sendMessage({ to: formattedPhone, message, locale })
  }

  async sendBusinessHoursNotification(phone: string, locale: string = 'en'): Promise<boolean> {
    const formattedPhone = formatOmaniPhone(phone)
    const isOpen = isBusinessHours()

    const message = locale === 'ar'
      ? `🕐 ساعات العمل\n\n${isOpen ? '✅ مفتوح الآن' : '❌ مغلق حالياً'}\n\nساعات العمل:\nالأحد - الخميس: 8:00 ص - 6:00 م\nالجمعة: 2:00 م - 6:00 م (بعد صلاة الجمعة)\nالسبت: مغلق\n\nللطوارئ: +968 99 123 456\n\n📍 الخوير، مسقط، عُمان\n📱 خدمات المكاتب المحترفة`
      : `🕐 Business Hours\n\n${isOpen ? '✅ Currently Open' : '❌ Currently Closed'}\n\nBusiness Hours:\nSunday - Thursday: 8:00 AM - 6:00 PM\nFriday: 2:00 PM - 6:00 PM (After Jummah)\nSaturday: Closed\n\nEmergency: +968 99 123 456\n\n📍 Al Khuwair, Muscat, Oman\n📱 Office Services Pro`

    return this.sendMessage({ to: formattedPhone, message, locale })
  }

  async sendDocumentMessage(to: string, message: string, documentUrl: string): Promise<boolean> {
    try {
      const response = await axios.post(
        `${this.config.apiUrl}/send-document`,
        {
          to,
          message,
          document_url: documentUrl
        },
        {
          headers: {
            'Authorization': `Bearer ${this.config.secretKey}`,
            'Account-Key': this.config.accountKey,
            'Content-Type': 'application/json'
          }
        }
      )

      return response.status === 200
    } catch (error) {
      console.error('WhatsApp document send error:', error)
      return false
    }
  }
}

export const whatsappService = new WhatsAppService()
