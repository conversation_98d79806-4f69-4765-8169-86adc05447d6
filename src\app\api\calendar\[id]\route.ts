import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/calendar/[id] - Get single calendar event
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (!prisma.calendarEvent) {
      return NextResponse.json({ error: 'Calendar model not available' }, { status: 503 })
    }

    const { id } = await params
    const event = await prisma.calendarEvent.findUnique({
      where: { id },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 })
    }

    return NextResponse.json(event)
  } catch (error) {
    console.error('Error fetching calendar event:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/calendar/[id] - Update calendar event
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (!prisma.calendarEvent) {
      return NextResponse.json({ error: 'Calendar model not available' }, { status: 503 })
    }

    const { id } = await params
    const body = await request.json()
    const {
      title,
      titleAr,
      description,
      type,
      category,
      startDate,
      endDate,
      dueDate,
      isAllDay,
      status,
      priority,
      notifyBefore,
      isRecurring,
      recurringType,
      recurringInterval,
      relatedEntityType,
      relatedEntityId,
      notes,
      assignedToId,
    } = body

    const event = await prisma.calendarEvent.update({
      where: { id },
      data: {
        title,
        titleAr,
        description,
        type,
        category,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : null,
        dueDate: dueDate ? new Date(dueDate) : null,
        isAllDay,
        status,
        priority,
        notifyBefore,
        isRecurring,
        recurringType,
        recurringInterval,
        relatedEntityType,
        relatedEntityId,
        notes,
        assignedToId: assignedToId || null,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return NextResponse.json(event)
  } catch (error) {
    console.error('Error updating calendar event:', error)
    return NextResponse.json(
      { error: 'Failed to update calendar event' },
      { status: 500 }
    )
  }
}

// DELETE /api/calendar/[id] - Delete calendar event
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    if (!prisma.calendarEvent) {
      return NextResponse.json({ error: 'Calendar model not available' }, { status: 503 })
    }

    const { id } = await params
    await prisma.calendarEvent.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Event deleted successfully' })
  } catch (error) {
    console.error('Error deleting calendar event:', error)
    return NextResponse.json(
      { error: 'Failed to delete calendar event' },
      { status: 500 }
    )
  }
}
