"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth, useRoleCheck } from "@/hooks/use-auth"
import { Loader2 } from "lucide-react"

interface AuthGuardProps {
  children: React.ReactNode
  requiredRole?: string | string[]
  fallback?: React.ReactNode
  redirectTo?: string
}

export function AuthGuard({ 
  children, 
  requiredRole, 
  fallback,
  redirectTo = "/auth/login" 
}: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const { hasRole } = useRoleCheck()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo)
    }
  }, [isAuthenticated, isLoading, router, redirectTo])

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  // Not authenticated
  if (!isAuthenticated) {
    return fallback || null
  }

  // Check role requirements
  if (requiredRole && !hasRole(requiredRole)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You don&apos;t have permission to access this page.</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// Specific guards for common use cases
export function AdminGuard({ children, fallback }: { children: React.ReactNode, fallback?: React.ReactNode }) {
  return (
    <AuthGuard requiredRole="ADMIN" fallback={fallback}>
      {children}
    </AuthGuard>
  )
}

export function ManagerGuard({ children, fallback }: { children: React.ReactNode, fallback?: React.ReactNode }) {
  return (
    <AuthGuard requiredRole={["ADMIN", "MANAGER"]} fallback={fallback}>
      {children}
    </AuthGuard>
  )
}

export function EmployeeGuard({ children, fallback }: { children: React.ReactNode, fallback?: React.ReactNode }) {
  return (
    <AuthGuard requiredRole={["ADMIN", "MANAGER", "EMPLOYEE"]} fallback={fallback}>
      {children}
    </AuthGuard>
  )
}
