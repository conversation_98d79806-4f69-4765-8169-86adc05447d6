export const tasks = {
  // Page titles and descriptions
  title: "Tasks",
  description: "Manage and track work assignments",
  addTask: "Add Task",
  createTask: "Create Task",
  editTask: "Edit Task",
  viewDetails: "View Details",
  
  // Task properties
  task: "Task",
  taskTitle: "Task Title",
  taskDescription: "Task Description",
  customer: "Customer",
  assignedTo: "Assigned To",
  priority: "Priority",
  status: "Status",
  estHours: "Est. Hours",
  estimatedHours: "Estimated Hours",
  actualHours: "Actual Hours",
  dueDate: "Due Date",
  startDate: "Start Date",
  completedDate: "Completed Date",
  
  // Task status
  new: "New",
  inProgress: "In Progress",
  completed: "Completed",
  cancelled: "Cancelled",
  paused: "Paused",
  onHold: "On Hold",
  
  // Task priority
  low: "Low",
  medium: "Medium",
  high: "High",
  urgent: "Urgent",
  
  // Task actions
  startTask: "Start Task",
  pauseTask: "Pause Task",
  resumeTask: "Resume Task",
  completeTask: "Complete Task",
  cancelTask: "Cancel Task",
  delete: "Delete",
  actions: "Actions",
  
  // Search and filters
  searchTasks: "Search tasks...",
  searchPlaceholder: "Search tasks, customer, or assignee...",
  allStatus: "All Status",
  allPriority: "All Priority",
  filterByStatus: "Filter by Status",
  filterByPriority: "Filter by Priority",
  filterByAssignee: "Filter by Assignee",
  
  // Statistics
  totalTasks: "Total Tasks",
  newTasks: "New Tasks",
  tasksInProgress: "Tasks in Progress",
  completedTasks: "Completed Tasks",
  overdueTasks: "Overdue Tasks",
  
  // Messages and confirmations
  taskStartedSuccessfully: "Task started successfully",
  taskPausedSuccessfully: "Task paused successfully",
  taskCompletedSuccessfully: "Task completed successfully",
  taskCancelledSuccessfully: "Task cancelled successfully",
  taskDeletedSuccessfully: "Task deleted successfully",
  taskCreatedSuccessfully: "Task created successfully",
  taskUpdatedSuccessfully: "Task updated successfully",
  
  // Error messages
  failedToStartTask: "Failed to start task",
  failedToPauseTask: "Failed to pause task",
  failedToCompleteTask: "Failed to complete task",
  failedToCancelTask: "Failed to cancel task",
  failedToDeleteTask: "Failed to delete task",
  failedToCreateTask: "Failed to create task",
  failedToUpdateTask: "Failed to update task",
  errorStartingTask: "Error starting task",
  errorPausingTask: "Error pausing task",
  errorCompletingTask: "Error completing task",
  errorCancellingTask: "Error cancelling task",
  errorDeletingTask: "Error deleting task",
  
  // Confirmations
  confirmStartTask: "Are you sure you want to start this task?",
  confirmPauseTask: "Are you sure you want to pause this task?",
  confirmCompleteTask: "Are you sure you want to complete this task?",
  confirmCancelTask: "Are you sure you want to cancel task",
  confirmDeleteTask: "Are you sure you want to delete task",
  
  // Form placeholders
  taskTitlePlaceholder: "Enter task title...",
  taskDescriptionPlaceholder: "Describe the task requirements and objectives...",
  selectCustomer: "Select customer...",
  selectAssignee: "Select assignee...",
  selectPriority: "Select priority...",
  selectStatus: "Select status...",
  
  // Empty states
  noTasksFound: "No tasks found",
  noTasksFoundSearch: "No tasks found matching your search",
  createFirstTask: "Create your first task",
  noCustomer: "No Customer",
  unassigned: "Unassigned",
  
  // Task details
  taskDetails: "Task Details",
  taskInformation: "Task Information",
  timeTracking: "Time Tracking",
  taskProgress: "Task Progress",
  taskHistory: "Task History",
  taskComments: "Task Comments",
  taskAttachments: "Task Attachments",
  
  // Time tracking
  timeSpent: "Time Spent",
  timeRemaining: "Time Remaining",
  progressPercentage: "Progress Percentage",
  startTime: "Start Time",
  endTime: "End Time",
  
  // Task categories
  development: "Development",
  design: "Design",
  testing: "Testing",
  documentation: "Documentation",
  meeting: "Meeting",
  research: "Research",
  maintenance: "Maintenance",
  support: "Support",
  
  // Loading states
  loadingTasks: "Loading tasks...",
  savingTask: "Saving task...",
  updatingTask: "Updating task...",
  deletingTask: "Deleting task...",
  
  // Validation
  taskTitleRequired: "Task title is required",
  customerRequired: "Customer is required",
  assigneeRequired: "Assignee is required",
  dueDateRequired: "Due date is required",
  estimatedHoursRequired: "Estimated hours is required",
  invalidEstimatedHours: "Estimated hours must be a positive number"
}; 