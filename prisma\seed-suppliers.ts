import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedSuppliers() {
  console.log('Seeding suppliers...')

  const suppliers = [
    {
      name: 'Al-Rashid Paper Supply',
      nameAr: 'مؤسسة الراشد لتوريد الورق',
      email: '<EMAIL>',
      phone: '+968 2456 7890',
      mobile: '+968 9123 4567',
      address: 'Building 12, Industrial Area, Rusayl, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Al-Rashid Paper Supply LLC',
      taxNumber: 'TAX-001-2024',
      contactPerson: '<PERSON>',
      notes: 'Main supplier for printing paper and cardboard materials',
    },
    {
      name: 'Gulf Printing Inks',
      nameAr: 'أحبار الخليج للطباعة',
      email: '<EMAIL>',
      phone: '+968 2567 8901',
      mobile: '+968 9234 5678',
      address: 'Warehouse 8, Sohar Industrial Zone, Sohar',
      city: 'Sohar',
      country: 'Oman',
      company: 'Gulf Printing Inks SAOC',
      taxNumber: 'TAX-002-2024',
      contactPerson: 'Fatima Al-Zahra',
      notes: 'Specialized in high-quality printing inks and chemicals',
    },
    {
      name: 'Modern Equipment Trading',
      nameAr: 'تجارة المعدات الحديثة',
      email: '<EMAIL>',
      phone: '+968 2678 9012',
      mobile: '+968 9345 6789',
      address: 'Shop 25, Al-Khuwair Commercial Complex, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Modern Equipment Trading LLC',
      taxNumber: 'TAX-003-2024',
      contactPerson: 'Mohammed Al-Balushi',
      notes: 'Supplier of printing machinery and spare parts',
    },
    {
      name: 'Digital Solutions Oman',
      nameAr: 'الحلول الرقمية عمان',
      email: '<EMAIL>',
      phone: '+968 2789 0123',
      mobile: '+968 9456 7890',
      address: 'Office 15, Knowledge Oasis Muscat, Al-Rusayl',
      city: 'Muscat',
      country: 'Oman',
      company: 'Digital Solutions Oman LLC',
      taxNumber: 'TAX-004-2024',
      contactPerson: 'Sarah Al-Kindi',
      notes: 'Digital printing solutions and software provider',
    },
    {
      name: 'Al-Noor Packaging',
      nameAr: 'النور للتغليف',
      email: '<EMAIL>',
      phone: '+968 2890 1234',
      mobile: '+968 9567 8901',
      address: 'Factory 3, Nizwa Industrial Area, Nizwa',
      city: 'Nizwa',
      country: 'Oman',
      company: 'Al-Noor Packaging Industries',
      taxNumber: 'TAX-005-2024',
      contactPerson: 'Omar Al-Hinai',
      notes: 'Packaging materials and custom boxes manufacturer',
    },
    {
      name: 'Elite Finishing Materials',
      nameAr: 'مواد التشطيب المتميزة',
      email: '<EMAIL>',
      phone: '+968 2901 2345',
      mobile: '+968 9678 9012',
      address: 'Showroom 7, Ruwi Business District, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Elite Finishing Materials SAOG',
      taxNumber: 'TAX-006-2024',
      contactPerson: 'Aisha Al-Mamari',
      notes: 'Lamination, binding, and finishing materials supplier',
    },
  ]

  for (const supplierData of suppliers) {
    try {
      await prisma.supplier.create({
        data: supplierData,
      })
      console.log(`Created supplier: ${supplierData.name}`)
    } catch (error) {
      console.error(`Error creating supplier ${supplierData.name}:`, error)
    }
  }

  console.log('Suppliers seeding completed!')
}

seedSuppliers()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
