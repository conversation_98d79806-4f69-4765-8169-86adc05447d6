export const purchases = {
  // Page titles and descriptions
  title: "أوامر الشراء",
  description: "إدارة أوامر شراء الموردين والمشتريات",
  addPurchase: "إضافة أمر شراء",
  createPurchase: "إنشاء أمر شراء",
  editPurchase: "تعديل أمر الشراء",
  viewDetails: "عرض التفاصيل",
  duplicatePurchase: "نسخ أمر الشراء",
  
  // Purchase order properties
  purchaseOrderId: "معرف أمر الشراء",
  purchaseOrderNumber: "رقم أمر الشراء",
  purchaseDate: "تاريخ الشراء",
  expectedDeliveryDate: "تاريخ التسليم المتوقع",
  deliveryDate: "تاريخ التسليم",
  supplier: "المورد",
  selectSupplier: "اختر المورد",
  requestedBy: "طلب من",
  approvedBy: "وافق عليه",
  receivedBy: "استلم من",
  priority: "الأولوية",
  status: "الحالة",
  paymentTerms: "شروط الدفع",
  paymentMethod: "طريقة الدفع",
  
  // Purchase order details
  items: "العناصر",
  itemDescription: "الوصف",
  product: "المنتج",
  quantity: "الكمية",
  unitPrice: "سعر الوحدة",
  discount: "الخصم",
  amount: "المبلغ",
  addItem: "إضافة عنصر",
  removeItem: "إزالة عنصر",
  
  // Calculations
  subtotal: "المجموع الفرعي",
  tax: "الضريبة",
  taxRate: "معدل الضريبة",
  shippingCost: "تكلفة الشحن",
  totalAmount: "المبلغ الإجمالي",
  totalPaid: "المبلغ المدفوع",
  totalDue: "المبلغ المستحق",
  
  // Purchase order status
  draft: "مسودة",
  pending: "معلق",
  approved: "موافق عليه",
  ordered: "مطلوب",
  partiallyReceived: "مستلم جزئياً",
  received: "مستلم",
  completed: "مكتمل",
  cancelled: "ملغي",
  
  // Priority levels
  low: "منخفضة",
  medium: "متوسطة",
  high: "عالية",
  urgent: "عاجل",
  
  // Actions
  saveDraft: "حفظ كمسودة",
  submit: "إرسال",
  approve: "موافقة",
  reject: "رفض",
  order: "طلب",
  receive: "استلام",
  complete: "إكمال",
  cancel: "إلغاء",
  print: "طباعة",
  download: "تحميل",
  send: "إرسال",
  duplicate: "نسخ",
  delete: "حذف",
  
  // Receiving
  receiveOrder: "استلام الطلب",
  partialReceive: "استلام جزئي",
  receivedQuantity: "الكمية المستلمة",
  remainingQuantity: "الكمية المتبقية",
  qualityCheck: "فحص الجودة",
  receiptNotes: "ملاحظات الاستلام",
  
  // Payment information
  payOnDelivery: "دفع عند التسليم",
  net30: "صافي 30",
  net60: "صافي 60",
  prepaid: "مدفوع مسبقاً",
  creditCard: "بطاقة ائتمان",
  bankTransfer: "تحويل بنكي",
  cash: "نقداً",
  check: "شيك",
  
  // Search and filters
  searchPurchases: "البحث في أوامر الشراء...",
  searchPlaceholder: "البحث برقم الطلب أو المورد أو المنتج...",
  allStatus: "جميع الحالات",
  allPriorities: "جميع الأولويات",
  filterByStatus: "تصفية حسب الحالة",
  filterByPriority: "تصفية حسب الأولوية",
  filterBySupplier: "تصفية حسب المورد",
  filterByRequestedBy: "تصفية حسب الطالب",
  dateRange: "نطاق التاريخ",
  
  // Statistics
  totalPurchases: "إجمالي أوامر الشراء",
  pendingPurchases: "الطلبات المعلقة",
  approvedPurchases: "الطلبات الموافق عليها",
  completedPurchases: "الطلبات المكتملة",
  totalPurchaseValue: "إجمالي قيمة المشتريات",
  averageOrderValue: "متوسط قيمة الطلب",
  
  // Messages and confirmations
  purchaseCreatedSuccessfully: "تم إنشاء أمر الشراء بنجاح",
  purchaseUpdatedSuccessfully: "تم تحديث أمر الشراء بنجاح",
  purchaseDeletedSuccessfully: "تم حذف أمر الشراء بنجاح",
  purchaseApprovedSuccessfully: "تم الموافقة على أمر الشراء بنجاح",
  purchaseRejectedSuccessfully: "تم رفض أمر الشراء بنجاح",
  purchaseOrderedSuccessfully: "تم وضع أمر الشراء بنجاح",
  purchaseReceivedSuccessfully: "تم استلام أمر الشراء بنجاح",
  purchaseCompletedSuccessfully: "تم إكمال أمر الشراء بنجاح",
  purchaseCancelledSuccessfully: "تم إلغاء أمر الشراء بنجاح",
  
  // Error messages
  failedToCreatePurchase: "فشل في إنشاء أمر الشراء",
  failedToUpdatePurchase: "فشل في تحديث أمر الشراء",
  failedToDeletePurchase: "فشل في حذف أمر الشراء",
  failedToApprovePurchase: "فشل في الموافقة على أمر الشراء",
  failedToRejectPurchase: "فشل في رفض أمر الشراء",
  failedToOrderPurchase: "فشل في وضع أمر الشراء",
  failedToReceivePurchase: "فشل في استلام أمر الشراء",
  failedToCompletePurchase: "فشل في إكمال أمر الشراء",
  failedToCancelPurchase: "فشل في إلغاء أمر الشراء",
  
  // Confirmations
  confirmApprovePurchase: "هل أنت متأكد من الموافقة على أمر الشراء هذا؟",
  confirmRejectPurchase: "هل أنت متأكد من رفض أمر الشراء هذا؟",
  confirmOrderPurchase: "هل أنت متأكد من وضع أمر الشراء هذا؟",
  confirmReceivePurchase: "هل أنت متأكد من تعيين أمر الشراء هذا كمستلم؟",
  confirmCompletePurchase: "هل أنت متأكد من إكمال أمر الشراء هذا؟",
  confirmCancelPurchase: "هل أنت متأكد من إلغاء أمر الشراء هذا؟",
  confirmDeletePurchase: "هل أنت متأكد من حذف أمر الشراء هذا؟",
  
  // Form placeholders
  purchaseOrderNumberPlaceholder: "أدخل رقم أمر الشراء...",
  selectSupplierPlaceholder: "اختر المورد...",
  selectProductPlaceholder: "اختر المنتج...",
  notesPlaceholder: "أضف ملاحظات أو تعليمات خاصة...",
  receiptNotesPlaceholder: "أضف ملاحظات الاستلام...",
  
  // Empty states
  noPurchasesFound: "لم يتم العثور على أوامر شراء",
  noPurchasesFoundSearch: "لم يتم العثور على أوامر شراء تطابق بحثك",
  createFirstPurchase: "أنشئ أمر الشراء الأول",
  noSupplier: "لم يتم اختيار مورد",
  noItems: "لم يتم إضافة عناصر",
  
  // Purchase order details
  purchaseDetails: "تفاصيل أمر الشراء",
  purchaseInformation: "معلومات الشراء",
  itemsOrdered: "العناصر المطلوبة",
  deliveryInformation: "معلومات التسليم",
  paymentInformation: "معلومات الدفع",
  approvalHistory: "تاريخ الموافقة",
  receiptHistory: "تاريخ الاستلام",
  
  // Reports
  purchaseReport: "تقرير المشتريات",
  supplierReport: "تقرير الموردين",
  purchaseAnalysis: "تحليل المشتريات",
  costAnalysis: "تحليل التكلفة",
  deliveryPerformance: "أداء التسليم",
  
  // Validation
  supplierRequired: "المورد مطلوب",
  purchaseDateRequired: "تاريخ الشراء مطلوب",
  expectedDeliveryDateRequired: "تاريخ التسليم المتوقع مطلوب",
  itemsRequired: "عنصر واحد على الأقل مطلوب",
  quantityRequired: "الكمية مطلوبة",
  unitPriceRequired: "سعر الوحدة مطلوب",
  invalidQuantity: "يجب أن تكون الكمية رقماً موجباً",
  invalidUnitPrice: "يجب أن يكون سعر الوحدة رقماً موجباً",
  deliveryDateBeforePurchaseDate: "لا يمكن أن يكون تاريخ التسليم قبل تاريخ الشراء",
  
  // Loading states
  loadingPurchases: "جاري تحميل أوامر الشراء...",
  savingPurchase: "جاري حفظ أمر الشراء...",
  deletingPurchase: "جاري حذف أمر الشراء...",
  approvingPurchase: "جاري الموافقة على أمر الشراء...",
  rejectingPurchase: "جاري رفض أمر الشراء...",
  orderingPurchase: "جاري وضع أمر الشراء...",
  receivingPurchase: "جاري استلام أمر الشراء...",
  
  // Workflow
  approval: "الموافقة",
  approvalRequired: "الموافقة مطلوبة",
  approvalPending: "الموافقة معلقة",
  approvedStatus: "موافق عليه",
  rejectedStatus: "مرفوض",
  workflow: "سير العمل",
  
  // Delivery
  delivery: "التسليم",
  deliveryAddress: "عنوان التسليم",
  deliveryInstructions: "تعليمات التسليم",
  shippingMethod: "طريقة الشحن",
  trackingNumber: "رقم التتبع",
  
  // Quality control
  qualityControl: "مراقبة الجودة",
  qualityApproved: "جودة موافق عليها",
  qualityRejected: "جودة مرفوضة",
  qualityNotes: "ملاحظات الجودة",
  
  // Inventory integration
  updateInventory: "تحديث المخزون",
  addToInventory: "إضافة إلى المخزون",
  inventoryUpdated: "تم تحديث المخزون بنجاح",
  
  dashboard: "طلبات الشراء",
  dashboardDescription: "إدارة أوامر الشراء وعلاقات الموردين",
  createPurchaseOrder: "إنشاء أمر شراء",
  totalOrders: "إجمالي الطلبات",
  allPurchaseOrders: "جميع أوامر الشراء",
  pendingOrders: "الطلبات المعلقة",
  pendingValue: "قيمة معلقة",
  inTransit: "قيد الشحن",
  ordersShipped: "طلبات تم شحنها",
  totalValue: "إجمالي القيمة",
  ordersReceived: "طلبات تم استلامها",
  searchPurchases: "بحث في أوامر الشراء...",
  filterByStatus: "تصفية حسب الحالة",
  allStatuses: "جميع الحالات",
  draft: "مسودة",
  pending: "معلق",
  approved: "معتمد",
  received: "تم الاستلام",
  cancelled: "ملغي",
  purchaseNumber: "رقم الشراء",
  date: "التاريخ",
  payoutStatus: "حالة الدفع",
  total: "الإجمالي",
  paid: "المدفوع",
  actions: "الإجراءات",
  loadingPurchases: "جاري تحميل أوامر الشراء...",
  noPurchasesFoundMatchingSearch: "لم يتم العثور على أوامر شراء مطابقة للبحث.",
  noPurchasesFoundCreateFirstPurchaseOrder: "لم يتم العثور على أوامر شراء. أنشئ أول أمر شراء!",
  unknownSupplier: "مورد غير معروف",
  openMenu: "فتح القائمة",
  editPurchase: "تعديل الشراء",
  printPDF: "طباعة/ PDF",
  emailPurchase: "إرسال بالبريد",
  download: "تنزيل",
  createPayout: "إنشاء دفعة",
  markAsReceived: "تأكيد الاستلام",
  delete: "حذف",
  // Create form specific keys
  create: {
    title: "إنشاء أمر شراء",
    subtitle: "إنشاء أمر شراء جديد من المورد",
    saveDraft: "حفظ كمسودة",
    createPurchaseOrder: "إنشاء أمر شراء",
    purchaseOrderInformation: "معلومات أمر الشراء",
    supplier: "المورد",
    expectedDeliveryDate: "تاريخ التسليم المتوقع",
    purchaseOrderStatus: "حالة أمر الشراء",
    notes: "ملاحظات",
    pleaseDeliverDuringBusinessHours: "يرجى التسليم خلال ساعات العمل.",
    purchaseItems: "عناصر أمر الشراء",
    addItem: "إضافة عنصر",
    description: "الوصف",
    product: "المنتج",
    quantity: "الكمية",
    unitPrice: "سعر الوحدة",
    total: "الإجمالي",
    action: "إجراء",
    itemDescription: "وصف العنصر...",
    discountSettings: "إعدادات الخصم",
    discount: "الخصم",
    purchaseOrderSummary: "ملخص أمر الشراء",
    subtotal: "المجموع الفرعي",
    items: "العناصر",
    status: "الحالة",
    saveAsDraft: "حفظ كمسودة",
    noSupplierFound: "لم يتم العثور على مورد",
    addNewSupplier: "إضافة مورد جديد",
    supplierName: "اسم المورد",
    enterSupplierName: "أدخل اسم المورد...",
    mobileNumber: "رقم الجوال",
    email: "البريد الإلكتروني",
    address: "العنوان",
    supplierAddress: "عنوان المورد...",
    addSupplier: "إضافة مورد",
    supplierExists: "المورد موجود",
    selectSupplierFromDropdown: "يرجى اختيار المورد من القائمة المنسدلة.",
    // Placeholders for search and inputs
    searchSuppliersPlaceholder: "البحث في الموردين بالاسم أو رقم الجوال...",
    searchProductsPlaceholder: "البحث في المنتجات...",
    selectSupplierPlaceholder: "اختر المورد...",
    selectProductPlaceholder: "اختر المنتج",
    noProductFound: "لم يتم العثور على منتج.",
    quantityPlaceholder: "1",
    unitPricePlaceholder: "0.000",
    discountPlaceholder: "0.00 ريال",
    discountPercentagePlaceholder: "0.00%",
    // VAT and discount info
    enterPercentage: "أدخل النسبة المئوية (0-100%)",
    enterAmountInOMR: "أدخل المبلغ بالريال العماني",
    defaultRateFromSettings: "المعدل الافتراضي من الإعدادات",
    vatRatePercent: "معدل ضريبة القيمة المضافة (%)",
    // Status options
    statusOptions: {
      draft: "مسودة",
      pending: "معلق",
      approved: "معتمد", 
      ordered: "تم الطلب"
    },
    // Discount type options
    discountTypeOptions: {
      amount: "مبلغ",
      percentage: "نسبة مئوية"
    }
  },

  // View/Edit page
  loading: "جاري التحميل...",
  notFound: "أمر الشراء غير موجود",
  purchaseOrder: "أمر شراء ",
  viewAndManagePurchaseOrderDetails: "عرض وإدارة تفاصيل أمر الشراء",
  orderDate: "تاريخ الطلب",
  expectedDelivery: "تاريخ التسليم المتوقع",
  purchaseSummary: "ملخص الشراء",
  vat: "ضريبة القيمة المضافة",
  quickActions: "إجراءات سريعة",
  selectSupplier: "اختر المورد",
  purchaseDate: "تاريخ الشراء",
  expectedDate: "التاريخ المتوقع",
  additionalNotesOptional: "ملاحظات إضافية (اختياري)",
  summary: "الملخص",
  taxAmount: "قيمة الضريبة",
  discountAmount: "قيمة الخصم",
  updating: "جاري التحديث...",
  updatePurchase: "تحديث الشراء"
}; 