"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  Edit,
  Phone,
  Mail,
  Building,
  MapPin,
  Calendar,
  Star,
  MoreHorizontal,
  Trash2,
  UserPlus,
  MessageSquare,
  FileText,
  Clock,
  Target,
  TrendingUp
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"

interface Lead {
  id: string
  name: string
  nameAr?: string
  email?: string
  mobile: string
  phone?: string
  address?: string
  city?: string
  country?: string
  company?: string
  source: string
  status: string
  notes?: string
  estimatedValue?: number
  assignedToId?: string
  assignedTo?: {
    id: string
    name: string
    email: string
  }
  convertedAt?: string
  customerId?: string
  createdAt: string
  updatedAt: string
}

interface Activity {
  id: string
  type: 'CALL' | 'EMAIL' | 'MEETING' | 'NOTE' | 'SMS' | 'WHATSAPP'
  title: string
  description: string
  date: string
  time: string
  user: {
    id: string
    name: string
    email: string
  }
  createdAt: string
}

interface FollowUp {
  id: string
  type: 'CALL' | 'EMAIL' | 'MEETING' | 'PROPOSAL' | 'VISIT' | 'QUOTE'
  description: string
  scheduledDate: string
  scheduledTime: string
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED' | 'OVERDUE'
  assignedTo: {
    id: string
    name: string
    email: string
  }
  createdAt: string
}

const leadStatuses = ["NEW", "QUALIFIED", "PROPOSAL", "NEGOTIATION", "WON", "LOST"]
const priorities = ["LOW", "MEDIUM", "HIGH"]

export default function LeadDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const [lead, setLead] = useState<Lead | null>(null)
  const [activities, setActivities] = useState<Activity[]>([])
  const [followUps, setFollowUps] = useState<FollowUp[]>([])
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)
  const [isActivityDialogOpen, setIsActivityDialogOpen] = useState(false)
  const [isFollowUpDialogOpen, setIsFollowUpDialogOpen] = useState(false)
  const [isConvertDialogOpen, setIsConvertDialogOpen] = useState(false)
  const [activityForm, setActivityForm] = useState({
    type: 'NOTE',
    title: '',
    description: '',
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5)
  })
  const [followUpForm, setFollowUpForm] = useState({
    type: 'CALL',
    description: '',
    scheduledDate: new Date().toISOString().split('T')[0],
    scheduledTime: '09:00',
    assignedToId: ''
  })

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    const fetchData = async () => {
      if (!params.id) return
      
      try {
        setLoading(true)
        
        // Fetch lead data
        const leadResponse = await fetch(`/api/leads/${params.id}`)
        if (!leadResponse.ok) {
          throw new Error('Failed to fetch lead')
        }
        const leadData = await leadResponse.json()
        setLead(leadData)

        // Fetch activities
        const activitiesResponse = await fetch(`/api/leads/${params.id}/activities`)
        if (activitiesResponse.ok) {
          const activitiesData = await activitiesResponse.json()
          setActivities(activitiesData)
        }

        // Fetch follow-ups
        const followUpsResponse = await fetch(`/api/leads/${params.id}/followups`)
        if (followUpsResponse.ok) {
          const followUpsData = await followUpsResponse.json()
          setFollowUps(followUpsData)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        setLead(null)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [params.id])

  const handleEdit = () => {
    router.push(`/dashboard/leads/${lead?.id}/edit`)
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this lead?')) {
      console.log('Deleting lead:', lead?.name)
      alert('Delete functionality would be implemented here')
      router.push('/dashboard/leads')
    }
  }

  const handleConvertToCustomer = () => {
    console.log('Converting lead to customer:', lead?.name)
    alert('Convert to customer functionality would be implemented here')
    setIsConvertDialogOpen(false)
    // This would typically:
    // 1. Create a new customer record
    // 2. Update lead status to "WON"
    // 3. Transfer lead data to customer
    // 4. Redirect to customer details
  }

  const handleAddActivity = async () => {
    try {
      const response = await fetch(`/api/leads/${params.id}/activities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(activityForm),
      })

      if (response.ok) {
        const newActivity = await response.json()
        setActivities([newActivity, ...activities])
        setIsActivityDialogOpen(false)
        setActivityForm({
          type: 'NOTE',
          title: '',
          description: '',
          date: new Date().toISOString().split('T')[0],
          time: new Date().toTimeString().slice(0, 5)
        })
      } else {
        const error = await response.json()
        alert(`Failed to add activity: ${error.error}`)
      }
    } catch (error) {
      console.error('Error adding activity:', error)
      alert('Failed to add activity')
    }
  }

  const handleScheduleFollowUp = async () => {
    try {
      const response = await fetch(`/api/leads/${params.id}/followups`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(followUpForm),
      })

      if (response.ok) {
        const newFollowUp = await response.json()
        setFollowUps([...followUps, newFollowUp])
        setIsFollowUpDialogOpen(false)
        setFollowUpForm({
          type: 'CALL',
          description: '',
          scheduledDate: new Date().toISOString().split('T')[0],
          scheduledTime: '09:00',
          assignedToId: ''
        })
      } else {
        const error = await response.json()
        alert(`Failed to schedule follow-up: ${error.error}`)
      }
    } catch (error) {
      console.error('Error scheduling follow-up:', error)
      alert('Failed to schedule follow-up')
    }
  }

  const getStatusColor = (status: string) => {
    const colors = {
      NEW: "bg-blue-100 text-blue-800",
      QUALIFIED: "bg-green-100 text-green-800",
      PROPOSAL: "bg-yellow-100 text-yellow-800",
      NEGOTIATION: "bg-orange-100 text-orange-800",
      WON: "bg-emerald-100 text-emerald-800",
      LOST: "bg-red-100 text-red-800"
    }
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      LOW: "bg-gray-100 text-gray-800",
      MEDIUM: "bg-yellow-100 text-yellow-800",
      HIGH: "bg-red-100 text-red-800"
    }
    return colors[priority as keyof typeof colors] || "bg-gray-100 text-gray-800"
  }

  const getActivityIcon = (type: string) => {
    const icons = {
      CALL: Phone,
      EMAIL: Mail,
      MEETING: Calendar,
      NOTE: MessageSquare,
      SMS: MessageSquare,
      WHATSAPP: MessageSquare
    }
    return icons[type as keyof typeof icons] || MessageSquare
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Loading...</h2>
          </div>
        </div>
      </div>
    )
  }

  if (!lead) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Lead Not Found</h2>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <h2 className="text-3xl font-bold tracking-tight">{lead.name}</h2>
              <div className="flex items-center space-x-1">
                <Star className={`h-5 w-5 ${(lead.leadScore || 75) >= 80 ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`} />
                <span className="text-sm font-medium">{lead.leadScore || 75}</span>
              </div>
            </div>
            <p className="text-muted-foreground">{lead.nameAr || lead.nameEn || ''}</p>
            <p className="text-muted-foreground">{lead.company}</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge className={getStatusColor(lead.status)}>
            {lead.status}
          </Badge>

          <Badge className={getPriorityColor(lead.priority || 'MEDIUM')}>
            {lead.priority || 'MEDIUM'}
          </Badge>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Lead
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {(lead.status === 'QUALIFIED' || lead.status === 'PROPOSAL' || lead.status === 'NEGOTIATION') && (
                <DropdownMenuItem onClick={() => setIsConvertDialogOpen(true)}>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Convert to Customer
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Lead
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Lead Information */}
          <Card>
            <CardHeader>
              <CardTitle>Lead Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Contact Information</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{lead.mobile || lead.phone || 'N/A'}</span>
                      </div>
                      {lead.phone && lead.phone !== lead.mobile && (
                        <div className="flex items-center space-x-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span>{lead.phone}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span>{lead.email || 'N/A'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Company Information</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span>{lead.company}</span>
                      </div>
                      {lead.companyEn && <div className="text-sm text-muted-foreground">{lead.companyEn}</div>}
                      <div className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-muted-foreground" />
                        <span>{lead.industry || 'General'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Lead Details</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Source:</span>
                        <Badge variant="outline">{lead.source}</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Assigned To:</span>
                        <span className="text-sm">{lead.assignedTo?.name || lead.assignedTo || 'Unassigned'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Lead Score:</span>
                        <div className="flex items-center space-x-1">
                          <Star className={`h-4 w-4 ${lead.leadScore >= 80 ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`} />
                          <span className="text-sm font-medium">{lead.leadScore}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Address</Label>
                    <div className="mt-1 flex items-start space-x-2">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <span className="text-sm">{lead.address}</span>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Timeline</Label>
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Created:</span>
                        <span className="text-sm">{new Date(lead.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Last Contact:</span>
                        <span className="text-sm">{lead.lastContact ? new Date(lead.lastContact).toLocaleDateString() : new Date(lead.updatedAt || lead.createdAt).toLocaleDateString()}</span>
                      </div>
                      {lead.nextFollowUp && (
                        <div className="flex items-center justify-between">
                          <span className="text-sm">Next Follow-up:</span>
                          <span className="text-sm font-medium text-orange-600">
                            {new Date(lead.nextFollowUp).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Estimated Value</Label>
                    <div className="mt-1">
                      <span className="text-2xl font-bold text-green-600">
                        {formatCurrency(lead.estimatedValue || 0)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {lead.notes && (
                <div className="mt-6">
                  <Label className="text-sm font-medium text-muted-foreground">Notes</Label>
                  <p className="text-sm text-muted-foreground mt-1">{lead.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Activities and Follow-ups Tabs */}
          <Card>
            <CardContent className="p-0">
              <Tabs defaultValue="activities" className="w-full">
                <div className="px-6 pt-6">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="activities">Activities</TabsTrigger>
                    <TabsTrigger value="followups">Follow-ups</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="activities" className="px-6 pb-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Activity Timeline</h3>
                      <Dialog open={isActivityDialogOpen} onOpenChange={setIsActivityDialogOpen}>
                        <DialogTrigger asChild>
                          <Button size="sm">
                            <MessageSquare className="mr-2 h-4 w-4" />
                            Add Activity
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add Activity</DialogTitle>
                            <DialogDescription>
                              Record a new activity for this lead.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="grid gap-4 py-4">
                            <div className="space-y-2">
                              <Label htmlFor="activityType">Activity Type</Label>
                              <Select value={activityForm.type} onValueChange={(value) => setActivityForm({...activityForm, type: value})}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="CALL">Phone Call</SelectItem>
                                  <SelectItem value="EMAIL">Email</SelectItem>
                                  <SelectItem value="MEETING">Meeting</SelectItem>
                                  <SelectItem value="NOTE">Note</SelectItem>
                                  <SelectItem value="SMS">SMS</SelectItem>
                                  <SelectItem value="WHATSAPP">WhatsApp</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="activityTitle">Title</Label>
                              <Input 
                                id="activityTitle" 
                                placeholder="Activity title" 
                                value={activityForm.title}
                                onChange={(e) => setActivityForm({...activityForm, title: e.target.value})}
                              />
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="activityDate">Date</Label>
                                <Input 
                                  id="activityDate" 
                                  type="date" 
                                  value={activityForm.date}
                                  onChange={(e) => setActivityForm({...activityForm, date: e.target.value})}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="activityTime">Time</Label>
                                <Input 
                                  id="activityTime" 
                                  type="time" 
                                  value={activityForm.time}
                                  onChange={(e) => setActivityForm({...activityForm, time: e.target.value})}
                                />
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="activityDescription">Description</Label>
                              <Textarea 
                                id="activityDescription" 
                                placeholder="Activity description" 
                                rows={3} 
                                value={activityForm.description}
                                onChange={(e) => setActivityForm({...activityForm, description: e.target.value})}
                              />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button variant="outline" onClick={() => setIsActivityDialogOpen(false)}>
                              Cancel
                            </Button>
                            <Button onClick={handleAddActivity}>
                              Add Activity
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>

                    <div className="space-y-4">
                      {activities.map((activity) => {
                        const IconComponent = getActivityIcon(activity.type)
                        return (
                          <div key={activity.id} className="flex space-x-4 p-4 border rounded-lg">
                            <div className="flex-shrink-0">
                              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <IconComponent className="h-4 w-4 text-blue-600" />
                              </div>
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <h4 className="font-medium">{activity.title}</h4>
                                <div className="text-sm text-muted-foreground">
                                  {new Date(activity.date).toLocaleDateString()} at {activity.time}
                                </div>
                              </div>
                              <p className="text-sm text-muted-foreground mt-1">{activity.description}</p>
                              <p className="text-xs text-muted-foreground mt-2">by {activity.user.name}</p>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="followups" className="px-6 pb-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Scheduled Follow-ups</h3>
                      <Dialog open={isFollowUpDialogOpen} onOpenChange={setIsFollowUpDialogOpen}>
                        <DialogTrigger asChild>
                          <Button size="sm">
                            <Calendar className="mr-2 h-4 w-4" />
                            Schedule Follow-up
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Schedule Follow-up</DialogTitle>
                            <DialogDescription>
                              Schedule a follow-up activity for this lead.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="grid gap-4 py-4">
                            <div className="space-y-2">
                              <Label htmlFor="followUpType">Follow-up Type</Label>
                              <Select value={followUpForm.type} onValueChange={(value) => setFollowUpForm({...followUpForm, type: value})}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="CALL">Phone Call</SelectItem>
                                  <SelectItem value="EMAIL">Email</SelectItem>
                                  <SelectItem value="MEETING">Meeting</SelectItem>
                                  <SelectItem value="PROPOSAL">Send Proposal</SelectItem>
                                  <SelectItem value="VISIT">Site Visit</SelectItem>
                                  <SelectItem value="QUOTE">Send Quote</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="followUpDate">Date</Label>
                                <Input 
                                  id="followUpDate" 
                                  type="date" 
                                  value={followUpForm.scheduledDate}
                                  onChange={(e) => setFollowUpForm({...followUpForm, scheduledDate: e.target.value})}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="followUpTime">Time</Label>
                                <Input 
                                  id="followUpTime" 
                                  type="time" 
                                  value={followUpForm.scheduledTime}
                                  onChange={(e) => setFollowUpForm({...followUpForm, scheduledTime: e.target.value})}
                                />
                              </div>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="followUpDescription">Description</Label>
                              <Textarea 
                                id="followUpDescription" 
                                placeholder="Follow-up description" 
                                rows={3} 
                                value={followUpForm.description}
                                onChange={(e) => setFollowUpForm({...followUpForm, description: e.target.value})}
                              />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button variant="outline" onClick={() => setIsFollowUpDialogOpen(false)}>
                              Cancel
                            </Button>
                            <Button onClick={handleScheduleFollowUp}>
                              Schedule Follow-up
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>

                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Type</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Scheduled</TableHead>
                          <TableHead>Assigned To</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {followUps.map((followUp) => (
                          <TableRow key={followUp.id}>
                            <TableCell>
                              <Badge variant="outline">{followUp.type}</Badge>
                            </TableCell>
                            <TableCell>{followUp.description}</TableCell>
                            <TableCell>
                              <div className="text-sm">
                                <div>{new Date(followUp.scheduledDate).toLocaleDateString()}</div>
                                <div className="text-muted-foreground">{followUp.scheduledTime}</div>
                              </div>
                            </TableCell>
                            <TableCell>{followUp.assignedTo.name}</TableCell>
                            <TableCell>
                              <Badge variant={followUp.status === 'PENDING' ? 'default' : followUp.status === 'COMPLETED' ? 'secondary' : 'destructive'}>
                                {followUp.status}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Lead Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Lead Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                <Badge className={getStatusColor(lead.status)}>
                  {lead.status}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Priority:</span>
                <Badge className={getPriorityColor(lead.priority)}>
                  {lead.priority}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Lead Score:</span>
                <div className="flex items-center space-x-1">
                  <Star className={`h-4 w-4 ${lead.leadScore >= 80 ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'}`} />
                  <span className="font-medium">{lead.leadScore}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Estimated Value:</span>
                <span className="font-medium text-green-600">{formatCurrency(lead.estimatedValue)}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Source:</span>
                <Badge variant="outline">{lead.source}</Badge>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Assigned To:</span>
                <span className="text-sm">{lead.assignedTo}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Industry:</span>
                <span className="text-sm">{lead.industry}</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Lead
              </Button>

              {(lead.status === 'QUALIFIED' || lead.status === 'PROPOSAL' || lead.status === 'NEGOTIATION') && (
                <Button variant="outline" className="w-full" onClick={() => setIsConvertDialogOpen(true)}>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Convert to Customer
                </Button>
              )}

              <Button variant="outline" className="w-full" onClick={() => setIsActivityDialogOpen(true)}>
                <MessageSquare className="mr-2 h-4 w-4" />
                Add Activity
              </Button>

              <Button variant="outline" className="w-full" onClick={() => setIsFollowUpDialogOpen(true)}>
                <Calendar className="mr-2 h-4 w-4" />
                Schedule Follow-up
              </Button>

              <Button variant="outline" className="w-full" onClick={() => window.open(`mailto:${lead.email}`)}>
                <Mail className="mr-2 h-4 w-4" />
                Send Email
              </Button>

              <Button variant="outline" className="w-full" onClick={() => window.open(`tel:${lead.phone}`)}>
                <Phone className="mr-2 h-4 w-4" />
                Call Lead
              </Button>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Timeline</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">Created</span>
                </div>
                <span className="text-sm font-medium">{new Date(lead.createdAt).toLocaleDateString()}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Last Contact</span>
                </div>
                <span className="text-sm font-medium">{new Date(lead.lastContact).toLocaleDateString()}</span>
              </div>

              {lead.nextFollowUp && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                    <span className="text-sm">Next Follow-up</span>
                  </div>
                  <span className="text-sm font-medium text-orange-600">
                    {new Date(lead.nextFollowUp).toLocaleDateString()}
                  </span>
                </div>
              )}

              <Separator />

              <div className="text-center">
                <p className="text-sm text-muted-foreground">
                  Lead age: {mounted ? Math.floor((new Date().getTime() - new Date(lead.createdAt).getTime()) / (1000 * 60 * 60 * 24)) : 0} days
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Convert to Customer Dialog */}
      <Dialog open={isConvertDialogOpen} onOpenChange={setIsConvertDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Convert Lead to Customer</DialogTitle>
            <DialogDescription>
              This will create a new customer record and mark the lead as won.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium">Lead Information</h4>
                <div className="mt-2 space-y-1 text-sm">
                  <p><strong>Name:</strong> {lead.name} ({lead.nameEn})</p>
                  <p><strong>Company:</strong> {lead.company}</p>
                  <p><strong>Email:</strong> {lead.email}</p>
                  <p><strong>Phone:</strong> {lead.phone}</p>
                  <p><strong>Estimated Value:</strong> {formatCurrency(lead.estimatedValue)}</p>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                <p>The following actions will be performed:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Create a new customer record with lead information</li>
                  <li>Update lead status to &quot;WON&quot;</li>
                  <li>Transfer all lead activities to customer history</li>
                  <li>Create initial customer opportunity</li>
                </ul>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsConvertDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleConvertToCustomer}>
              Convert to Customer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}