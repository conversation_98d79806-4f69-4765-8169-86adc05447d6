"use client"

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import { Palette, Check } from 'lucide-react'
import { themes, applyTheme, getStoredTheme } from '@/lib/theme'

export function ColorThemeSelector() {
  const [currentTheme, setCurrentTheme] = useState('blue')

  useEffect(() => {
    const storedTheme = getStoredTheme()
    setCurrentTheme(storedTheme)
    applyTheme(storedTheme)
  }, [])

  const handleThemeChange = (themeName: string) => {
    setCurrentTheme(themeName)
    applyTheme(themeName)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <Palette className="h-4 w-4" />
          <span className="sr-only">Change theme color</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Choose Theme Color</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {Object.entries(themes).map(([key, theme]) => (
          <DropdownMenuItem
            key={key}
            onClick={() => handleThemeChange(key)}
            className="flex items-center justify-between cursor-pointer"
          >
            <div className="flex items-center space-x-2">
              <div
                className="w-4 h-4 rounded-full border border-gray-300"
                style={{ backgroundColor: theme.primary }}
              />
              <span>{theme.displayName}</span>
            </div>
            {currentTheme === key && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Compact theme selector for smaller spaces
export function CompactColorThemeSelector() {
  const [currentTheme, setCurrentTheme] = useState('blue')

  useEffect(() => {
    const storedTheme = getStoredTheme()
    setCurrentTheme(storedTheme)
  }, [])

  const handleThemeChange = (themeName: string) => {
    setCurrentTheme(themeName)
    applyTheme(themeName)
  }

  return (
    <div className="flex items-center space-x-1 p-2 bg-white rounded-lg border shadow-sm">
      <Palette className="h-4 w-4 text-gray-500" />
      <div className="flex space-x-1">
        {Object.entries(themes).map(([key, theme]) => (
          <button
            key={key}
            onClick={() => handleThemeChange(key)}
            className={`w-6 h-6 rounded-full border-2 transition-all hover:scale-110 ${
              currentTheme === key 
                ? 'border-gray-800 ring-2 ring-gray-300' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            style={{ backgroundColor: theme.primary }}
            title={theme.displayName}
          />
        ))}
      </div>
    </div>
  )
}

// Theme preview component
export function ThemePreview({ themeName }: { themeName: string }) {
  const theme = themes[themeName]
  
  return (
    <div className="p-4 border rounded-lg space-y-3" style={{ borderColor: theme.border }}>
      <div className="flex items-center space-x-2">
        <div
          className="w-4 h-4 rounded-full"
          style={{ backgroundColor: theme.primary }}
        />
        <span className="font-medium">{theme.displayName}</span>
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div className="space-y-1">
          <div 
            className="h-6 rounded flex items-center justify-center text-white font-medium"
            style={{ background: theme.gradient }}
          >
            Primary
          </div>
          <div 
            className="h-6 rounded flex items-center justify-center"
            style={{ backgroundColor: theme.secondary, color: theme.primary }}
          >
            Secondary
          </div>
        </div>
        <div className="space-y-1">
          <div 
            className="h-6 rounded flex items-center justify-center text-white"
            style={{ backgroundColor: theme.accent }}
          >
            Accent
          </div>
          <div 
            className="h-6 rounded flex items-center justify-center text-gray-600"
            style={{ backgroundColor: theme.muted }}
          >
            Muted
          </div>
        </div>
      </div>
    </div>
  )
}
