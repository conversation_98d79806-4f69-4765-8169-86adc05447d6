export const expenses = {
  title: "Expenses",
  description: "Manage business expenses and reimbursements",
  manageTypes: "Manage Types",
  addExpense: "Add Expense",
  searchExpenses: "Search expenses...",
  status: "Status",
  allStatuses: "All Statuses",
  pending: "Pending",
  approved: "Approved",
  rejected: "Rejected",
  paid: "Paid",
  type: "Type",
  allCategories: "All Categories",
  totalExpenses: "Total Expenses",
  pendingExpenses: "Pending Expenses",
  expense: "Expense",
  paymentMethod: "Payment Method",
  receipt: "Receipt",
  viewExpense: "View Expense",
  editExpense: "Edit Expense",
  approve: "Approve",
  reject: "Reject",
  markAsPaid: "Mark as Paid",

  // Create form specific keys
  create: {
    title: "Add New Expense",
    subtitle: "Record a new business expense",
    back: "Back",
    expenseDetails: "Expense Details",
    date: "Date",
    dateRequired: "Date *",
    amount: "Amount",
    amountRequired: "Amount *",
    amountPlaceholder: "0.00",
    description: "Description",
    descriptionRequired: "Description *",
    descriptionPlaceholder: "Enter expense description",
    expenseType: "Expense Type",
    expenseTypeRequired: "Expense Type *",
    selectExpenseTypePlaceholder: "Select expense type",
    paymentMethodRequired: "Payment Method *",
    notes: "Notes",
    notesPlaceholder: "Additional notes (optional)",
    receiptSection: "Receipt",
    uploadReceipt: "Upload receipt",
    receiptFormats: "PNG, JPG, GIF or PDF up to 5MB",
    receiptAttached: "Attached",
    receiptNotAttached: "Not attached",
    summary: "Summary",
    summaryAmount: "Amount:",
    summaryType: "Type:",
    summaryPayment: "Payment:",
    summaryReceipt: "Receipt:",
    typeNotSelected: "Not selected",
    actions: "Actions",
    saveAsDraft: "Save as Draft",
    submitForApproval: "Submit for Approval",
    // Payment method options
    paymentMethods: {
      cash: "Cash",
      card: "Card", 
      bankTransfer: "Bank Transfer",
      cheque: "Cheque"
    },
    // Expense types
    expenseTypes: {
      officeSupplies: "Office Supplies",
      travel: "Travel & Transportation",
      utilities: "Utilities", 
      marketing: "Marketing & Advertising",
      equipment: "Equipment & Maintenance",
      professional: "Professional Services",
      meals: "Meals & Entertainment",
      software: "Software & Subscriptions",
      insurance: "Insurance",
      rent: "Rent & Facilities"
    },
    // Validation messages
    pleaseEnterDescription: "Please enter a description",
    pleaseEnterValidAmount: "Please enter a valid amount",
    pleaseSelectExpenseType: "Please select an expense type",
    fileSizeLimit: "File size must be less than 5MB",
    allowedFileTypes: "Only JPEG, PNG, GIF, and PDF files are allowed",
    // Success messages
    expenseSavedAsDraft: "Expense saved as draft successfully!",
    expenseSubmittedForApproval: "Expense submitted for approval successfully!",
    // Error messages
    failedToSaveExpense: "Failed to save expense. Please try again."
  },

  // Expense Types page
  types: {
    title: "Expense Types",
    subtitle: "Manage expense categories for better expense tracking",
    addExpenseType: "Add Expense Type",
    editExpenseType: "Edit Expense Type",
    addNewExpenseType: "Add New Expense Type",
    searchExpenseTypes: "Search expense types...",
    // Form fields
    name: "Name",
    nameRequired: "Name *",
    namePlaceholder: "Enter expense type name",
    arabicName: "Arabic Name",
    arabicNamePlaceholder: "Enter Arabic name (optional)",
    description: "Description",
    descriptionPlaceholder: "Enter description (optional)",
    active: "Active",
    // Table headers
    status: "Status",
    created: "Created",
    actions: "Actions",
    // Status badges
    activeStatus: "Active",
    inactiveStatus: "Inactive",
    noDescription: "No description",
    // Actions
    edit: "Edit",
    activate: "Activate",
    deactivate: "Deactivate",
    delete: "Delete",
    cancel: "Cancel",
    create: "Create",
    update: "Update",
    openMenu: "Open menu",
    // Validation messages
    pleaseEnterExpenseTypeName: "Please enter an expense type name",
    // Confirmation messages
    confirmDeleteExpenseType: "Are you sure you want to delete",
    // Success messages
    expenseTypeCreatedSuccessfully: "created successfully!",
    expenseTypeUpdatedSuccessfully: "updated successfully!",
    expenseTypeDeletedSuccessfully: "deleted successfully!",
    expenseTypeActivatedSuccessfully: "activated successfully!",
    expenseTypeDeactivatedSuccessfully: "deactivated successfully!",
    // Error messages
    failedToCreateExpenseType: "Failed to create expense type. Please try again.",
    failedToUpdateExpenseType: "Failed to update expense type. Please try again.",
    failedToDeleteExpenseType: "Failed to delete expense type. It may have associated expenses.",
    failedToUpdateExpenseTypeStatus: "Failed to update expense type status. Please try again."
  },

  // Statistics
  expensesLabel: "expenses",
  averageLabel: "Avg:",
  pendingLabel: "pending",
  approvedLabel: "approved",
  paidLabel: "paid",
  noReceipt: "No receipt",
  
  // Status translations
  statusLabels: {
    PENDING: "Pending",
    APPROVED: "Approved", 
    REJECTED: "Rejected",
    PAID: "Paid"
  },
  
  // Payment method translations
  paymentMethodLabels: {
    CASH: "Cash",
    CARD: "Card",
    BANK_TRANSFER: "Bank Transfer", 
    CHEQUE: "Cheque",
    CHECK: "Check",
    OTHER: "Other"
  },

  // View expense page
  view: {
    title: "Expense",
    createdOn: "Created on",
    back: "Back",
    actions: "Actions",
    editExpense: "Edit Expense",
    downloadReceipt: "Download Receipt",
    approve: "Approve",
    reject: "Reject",
    markAsPaid: "Mark as Paid",
    delete: "Delete",
    
    // Overview cards
    amount: "Amount",
    expenseAmount: "Expense amount",
    status: "Status",
    currentStatus: "Current status",
    type: "Type",
    expenseCategory: "Expense category",
    method: "Method",
    paymentMethod: "Payment method",
    
    // Details section
    expenseDetails: "Expense Details",
    description: "Description",
    date: "Date", 
    createdBy: "Created By",
    expenseType: "Expense Type",
    project: "Project",
    code: "Code",
    notes: "Notes",
    receipt: "Receipt",
    
    // Timeline
    activityTimeline: "Activity Timeline",
    expenseCreated: "Expense created",
    expenseUpdated: "Expense updated",
    by: "by",
    
    // Loading and error states
    loadingExpense: "Loading expense...",
    expenseNotFound: "Expense not found",
    
    // Alert messages
    expenseApprovedSuccess: "✅ Expense approved successfully!",
    expenseApprovedError: "❌ Failed to approve expense",
    pleaseEnterRejectionReason: "Please enter rejection reason:",
    expenseRejected: "✅ Expense rejected",
    expenseRejectedError: "❌ Failed to reject expense",
    expenseMarkedAsPaidSuccess: "✅ Expense marked as paid!",
    expenseMarkedAsPaidError: "❌ Failed to mark expense as paid",
    confirmDeleteExpense: "Are you sure you want to delete expense",
    expenseDeletedSuccess: "✅ Expense deleted successfully!",
    expenseDeletedError: "❌ Failed to delete expense",
    receiptDownloadInfo: "Receipt download functionality would be implemented here",
    noReceiptAvailable: "No receipt available for this expense"
  },

  // Edit expense page  
  edit: {
    title: "Edit Expense",
    subtitle: "Update expense",
    back: "Back",
    
    // Loading and error states
    loadingExpense: "Loading expense...",
    expenseNotFound: "Expense not found",
    expenseNotFoundDescription: "The expense you're looking for doesn't exist.",
    backToExpenses: "Back to Expenses",
    
    // Form sections
    expenseDetails: "Expense Details",
    summary: "Summary",
    actions: "Actions",
    
    // Form fields
    date: "Date",
    dateRequired: "Date *",
    amount: "Amount",
    amountRequired: "Amount *",
    amountPlaceholder: "0.000",
    description: "Description",
    descriptionRequired: "Description *",
    descriptionPlaceholder: "Enter expense description",
    expenseType: "Expense Type",
    expenseTypeRequired: "Expense Type *",
    selectExpenseTypePlaceholder: "Select expense type",
    paymentMethodRequired: "Payment Method *",
    status: "Status",
    notes: "Notes",
    notesPlaceholder: "Additional notes (optional)",
    
    // Summary labels
    expenseNumber: "Expense #:",
    summaryAmount: "Amount:",
    summaryType: "Type:",
    summaryPayment: "Payment:",
    summaryStatus: "Status:",
    notSelected: "Not selected",
    
    // Action buttons
    updateExpense: "Update Expense",
    updating: "Updating...",
    cancel: "Cancel",
    
    // Validation messages
    pleaseEnterDescription: "Please enter a description",
    pleaseEnterValidAmount: "Please enter a valid amount", 
    pleaseSelectExpenseType: "Please select an expense type",
    
    // Success/error messages
    expenseUpdatedSuccess: "Expense updated successfully!",
    expenseUpdateError: "Failed to update expense. Please try again.",
    failedToLoadExpenseData: "Failed to load expense data"
  }
}; 