"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  ArrowLeft,
  Upload,
  X
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

interface ExpenseFormData {
  date: string
  description: string
  amount: number
  expenseTypeId: string
  paymentMethod: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHEQUE'
  notes: string
  receipt?: File
}

interface ExpenseType {
  id: string
  name: string
  nameAr?: string
  description?: string
  isActive: boolean
}

export default function CreateExpensePage() {
  const router = useRouter()
  const { t } = useI18n()
  
  // State for expense types from database
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([])
  const [loadingTypes, setLoadingTypes] = useState(true)

  const [formData, setFormData] = useState<ExpenseFormData>({
    date: new Date().toISOString().split('T')[0],
    description: "",
    amount: 0,
    expenseTypeId: "",
    paymentMethod: "CASH",
    notes: "",
  })
  const [receiptFile, setReceiptFile] = useState<File | null>(null)
  const [receiptPreview, setReceiptPreview] = useState<string | null>(null)

  // Fetch expense types from database
  useEffect(() => {
    const fetchExpenseTypes = async () => {
      try {
        const response = await fetch('/api/expense-types')
        if (response.ok) {
          const data = await response.json()
          // Only show active expense types
          const activeTypes = data.filter((type: ExpenseType) => type.isActive)
          setExpenseTypes(activeTypes)
        } else {
          console.error('Failed to fetch expense types')
        }
      } catch (error) {
        console.error('Error fetching expense types:', error)
      } finally {
        setLoadingTypes(false)
      }
    }

    fetchExpenseTypes()
  }, [])

  const handleInputChange = (field: keyof ExpenseFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleReceiptUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert(t("expenses.create.fileSizeLimit"))
        return
      }

      // Check file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']
      if (!allowedTypes.includes(file.type)) {
        alert(t("expenses.create.allowedFileTypes"))
        return
      }

      setReceiptFile(file)

      // Create preview for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          setReceiptPreview(e.target?.result as string)
        }
        reader.readAsDataURL(file)
      } else {
        setReceiptPreview(null)
      }
    }
  }

  const handleRemoveReceipt = () => {
    setReceiptFile(null)
    setReceiptPreview(null)
    // Reset file input
    const fileInput = document.getElementById('receipt') as HTMLInputElement
    if (fileInput) {
      fileInput.value = ''
    }
  }

  const handleSave = async (status: 'draft' | 'submitted') => {
    // Validation
    if (!formData.description.trim()) {
      alert(t("expenses.create.pleaseEnterDescription"))
      return
    }

    if (formData.amount <= 0) {
      alert(t("expenses.create.pleaseEnterValidAmount"))
      return
    }

    if (!formData.expenseTypeId) {
      alert(t("expenses.create.pleaseSelectExpenseType"))
      return
    }

    try {
      // Create expense via API
      const expenseData = {
        description: formData.description,
        amount: formData.amount,
        expenseTypeId: formData.expenseTypeId,
        paymentMethod: formData.paymentMethod,
        status: status === 'draft' ? 'PENDING' : 'PENDING',
        notes: formData.notes,
        date: formData.date,
      }

      const response = await fetch('/api/expenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(expenseData),
      })

      if (response.ok) {
        const successMessage = status === 'draft' 
          ? t("expenses.create.expenseSavedAsDraft")
          : t("expenses.create.expenseSubmittedForApproval")
        alert(successMessage)
        router.push('/dashboard/expenses')
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error saving expense:', error)
      alert(t("expenses.create.failedToSaveExpense"))
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("expenses.create.back")}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t("expenses.create.title")}</h2>
            <p className="text-muted-foreground">
              {t("expenses.create.subtitle")}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t("expenses.create.expenseDetails")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">{t("expenses.create.dateRequired")}</Label>
                  <Input
                    id="date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="amount">{t("expenses.create.amountRequired")}</Label>
                  <Input
                    id="amount"
                    type="number"
                    min="0"
                    step="0.01"
                    placeholder={t("expenses.create.amountPlaceholder")}
                    value={formData.amount || ''}
                    onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t("expenses.create.descriptionRequired")}</Label>
                <Input
                  id="description"
                  placeholder={t("expenses.create.descriptionPlaceholder")}
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expenseType">{t("expenses.create.expenseTypeRequired")}</Label>
                  <Select
                    value={formData.expenseTypeId}
                    onValueChange={(value) => handleInputChange('expenseTypeId', value)}
                    disabled={loadingTypes}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={loadingTypes ? "Loading..." : t("expenses.create.selectExpenseTypePlaceholder")} />
                    </SelectTrigger>
                    <SelectContent>
                      {expenseTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="paymentMethod">{t("expenses.create.paymentMethodRequired")}</Label>
                  <Select
                    value={formData.paymentMethod}
                    onValueChange={(value) => handleInputChange('paymentMethod', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CASH">{t("expenses.create.paymentMethods.cash")}</SelectItem>
                      <SelectItem value="CARD">{t("expenses.create.paymentMethods.card")}</SelectItem>
                      <SelectItem value="BANK_TRANSFER">{t("expenses.create.paymentMethods.bankTransfer")}</SelectItem>
                      <SelectItem value="CHEQUE">{t("expenses.create.paymentMethods.cheque")}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">{t("expenses.create.notes")}</Label>
                <Textarea
                  id="notes"
                  placeholder={t("expenses.create.notesPlaceholder")}
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  className="min-h-[80px]"
                />
              </div>
            </CardContent>
          </Card>

          {/* Receipt Upload */}
          <Card>
            <CardHeader>
              <CardTitle>{t("expenses.create.receiptSection")}</CardTitle>
            </CardHeader>
            <CardContent>
              {!receiptFile ? (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <Label htmlFor="receipt" className="cursor-pointer">
                      <span className="mt-2 block text-sm font-medium text-gray-900">
                        {t("expenses.create.uploadReceipt")}
                      </span>
                      <span className="mt-1 block text-sm text-gray-500">
                        {t("expenses.create.receiptFormats")}
                      </span>
                    </Label>
                    <Input
                      id="receipt"
                      type="file"
                      accept="image/*,.pdf"
                      onChange={handleReceiptUpload}
                      className="hidden"
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {receiptPreview ? (
                          <img
                            src={receiptPreview}
                            alt="Receipt preview"
                            className="h-10 w-10 object-cover rounded"
                          />
                        ) : (
                          <div className="h-10 w-10 bg-gray-200 rounded flex items-center justify-center">
                            📄
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{receiptFile.name}</p>
                        <p className="text-sm text-gray-500">
                          {(receiptFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRemoveReceipt}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  {receiptPreview && (
                    <div className="mt-4">
                      <img
                        src={receiptPreview}
                        alt="Receipt preview"
                        className="max-w-full h-auto rounded-lg border"
                      />
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Expense Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t("expenses.create.summary")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>{t("expenses.create.summaryAmount")}</span>
                <span className="font-medium">{formatCurrency(formData.amount)}</span>
              </div>
              
              <div className="flex justify-between">
                <span>{t("expenses.create.summaryType")}</span>
                <span className="text-sm">
                  {formData.expenseTypeId 
                    ? expenseTypes.find(type => type.id === formData.expenseTypeId)?.name 
                    : t("expenses.create.typeNotSelected")
                  }
                </span>
              </div>
              
              <div className="flex justify-between">
                <span>{t("expenses.create.summaryPayment")}</span>
                <span className="text-sm">
                  {formData.paymentMethod === 'CASH' && t("expenses.create.paymentMethods.cash")}
                  {formData.paymentMethod === 'CARD' && t("expenses.create.paymentMethods.card")}
                  {formData.paymentMethod === 'BANK_TRANSFER' && t("expenses.create.paymentMethods.bankTransfer")}
                  {formData.paymentMethod === 'CHEQUE' && t("expenses.create.paymentMethods.cheque")}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span>{t("expenses.create.summaryReceipt")}</span>
                <span className="text-sm">
                  {receiptFile ? t("expenses.create.receiptAttached") : t("expenses.create.receiptNotAttached")}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t("expenses.create.actions")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                className="w-full"
                onClick={() => handleSave('draft')}
                disabled={loadingTypes}
              >
                {t("expenses.create.saveAsDraft")}
              </Button>
              <Button
                className="w-full"
                onClick={() => handleSave('submitted')}
                disabled={loadingTypes}
              >
                {t("expenses.create.submitForApproval")}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
