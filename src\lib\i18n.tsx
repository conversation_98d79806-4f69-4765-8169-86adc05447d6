"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { en, type TranslationKeys } from './translations/en'
import { ar } from './translations/ar'
import { getStorageItem, setStorageItem, STORAGE_KEYS } from './storage'

export type Language = 'en' | 'ar'
export type Direction = 'ltr' | 'rtl'

interface I18nContextType {
  language: Language
  direction: Direction
  setLanguage: (lang: Language) => void
  t: (key: string) => string
  formatCurrency: (amount: number) => string
}

const I18nContext = createContext<I18nContextType | undefined>(undefined)

const translations = {
  en,
  ar,
}

// Helper function to get nested translation value
function getNestedValue(obj: any, path: string): string {
  return path.split('.').reduce((current, key) => current?.[key], obj) || path
}

// Currency formatting based on language
function formatCurrencyByLanguage(amount: number, language: Language): string {
  const currency = 'OMR' // Omani Rial

  // Handle null, undefined, or invalid values
  const validAmount = (amount === null || amount === undefined || isNaN(amount)) ? 0 : Number(amount)

  if (language === 'ar') {
    // Arabic formatting: ر.ع. 123.45
    return `ر.ع. ${validAmount.toFixed(2)}`
  } else {
    // English formatting: OMR 123.45
    return `${currency} ${validAmount.toFixed(2)}`
  }
}

interface I18nProviderProps {
  children: ReactNode
}

export function I18nProvider({ children }: I18nProviderProps) {
  const [language, setLanguageState] = useState<Language>('en')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    // Load saved language from storage
    const savedLanguage = getStorageItem('app_language') as Language
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {
      setLanguageState(savedLanguage)
    }
    setMounted(true)
  }, [])

  useEffect(() => {
    if (mounted) {
      // Update document direction and language
      document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
      document.documentElement.lang = language
      
      // Add/remove RTL class for styling
      if (language === 'ar') {
        document.documentElement.classList.add('rtl')
      } else {
        document.documentElement.classList.remove('rtl')
      }
    }
  }, [language, mounted])

  const setLanguage = (lang: Language) => {
    setLanguageState(lang)
    setStorageItem('app_language', lang)
  }

  const t = (key: string): string => {
    return getNestedValue(translations[language], key)
  }

  const formatCurrency = (amount: number): string => {
    return formatCurrencyByLanguage(amount, language)
  }

  const direction: Direction = language === 'ar' ? 'rtl' : 'ltr'

  const value: I18nContextType = {
    language,
    direction,
    setLanguage,
    t,
    formatCurrency,
  }

  // Don't render until mounted to avoid hydration mismatch
  if (!mounted) {
    return null
  }

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  )
}

export function useI18n() {
  const context = useContext(I18nContext)
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider')
  }
  return context
}

// Hook for translation only (shorter syntax)
export function useTranslation() {
  const { t } = useI18n()
  return { t }
}

// Hook for currency formatting
export function useCurrency() {
  const { formatCurrency } = useI18n()
  return { formatCurrency }
}

// Utility function for components that need translation without hooks
export function getTranslation(key: string, language: Language = 'en'): string {
  return getNestedValue(translations[language], key)
}

// RTL-aware class names utility
export function rtlClass(ltrClass: string, rtlClass: string, direction: Direction): string {
  return direction === 'rtl' ? rtlClass : ltrClass
}

// Direction-aware margin/padding utilities
export function directionClass(direction: Direction) {
  return {
    // Margin
    ml: direction === 'rtl' ? 'mr' : 'ml', // margin-left becomes margin-right in RTL
    mr: direction === 'rtl' ? 'ml' : 'mr', // margin-right becomes margin-left in RTL
    // Padding  
    pl: direction === 'rtl' ? 'pr' : 'pl', // padding-left becomes padding-right in RTL
    pr: direction === 'rtl' ? 'pl' : 'pr', // padding-right becomes padding-left in RTL
    // Text alignment
    textLeft: direction === 'rtl' ? 'text-right' : 'text-left',
    textRight: direction === 'rtl' ? 'text-left' : 'text-right',
    // Flex direction
    flexRow: direction === 'rtl' ? 'flex-row-reverse' : 'flex-row',
    // Border radius
    roundedL: direction === 'rtl' ? 'rounded-r' : 'rounded-l',
    roundedR: direction === 'rtl' ? 'rounded-l' : 'rounded-r',
    roundedTL: direction === 'rtl' ? 'rounded-tr' : 'rounded-tl',
    roundedTR: direction === 'rtl' ? 'rounded-tl' : 'rounded-tr',
    roundedBL: direction === 'rtl' ? 'rounded-br' : 'rounded-bl',
    roundedBR: direction === 'rtl' ? 'rounded-bl' : 'rounded-br',
  }
}

// Number formatting for Arabic
export function formatNumber(num: number, language: Language): string {
  if (language === 'ar') {
    // Convert to Arabic-Indic numerals
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩']
    return num.toString().replace(/\d/g, (digit) => arabicNumerals[parseInt(digit)])
  }
  return num.toString()
}

// Date formatting based on language
export function formatDate(date: Date | string, language: Language): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (language === 'ar') {
    return dateObj.toLocaleDateString('ar-OM', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  } else {
    return dateObj.toLocaleDateString('en-OM', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }
}

// Time formatting based on language
export function formatTime(date: Date | string, language: Language): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  if (language === 'ar') {
    return dateObj.toLocaleTimeString('ar-OM', {
      hour: '2-digit',
      minute: '2-digit',
    })
  } else {
    return dateObj.toLocaleTimeString('en-OM', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }
}
