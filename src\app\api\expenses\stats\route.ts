import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { subDays, startOfYear } from 'date-fns'

// GET /api/expenses/stats - Get expense statistics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'all'

    let whereClause: any = {}
    const now = new Date()

    if (period === '30d') {
      whereClause.createdAt = { gte: subDays(now, 30) }
    } else if (period === '90d') {
      whereClause.createdAt = { gte: subDays(now, 90) }
    } else if (period === 'ytd') {
      whereClause.createdAt = { gte: startOfYear(now) }
    }

    // Get basic counts and totals
    const [
      totalExpenses,
      totalAmount,
      statusBreakdown,
      expensesByType,
      monthlyExpenses,
      recentExpenses,
    ] = await Promise.all([
      prisma.expense.count({ where: whereClause }),
      prisma.expense.aggregate({ where: whereClause, _sum: { amount: true } }),
      prisma.expense.groupBy({
        by: ['status'],
        where: whereClause,
        _count: { status: true },
        _sum: { amount: true },
      }),
      prisma.expense.groupBy({
        by: ['expenseTypeId'],
        where: whereClause,
        _sum: { amount: true },
        _count: true,
        orderBy: { _sum: { amount: 'desc' } },
      }),
      prisma.expense.findMany({
        where: whereClause,
        select: { amount: true, createdAt: true },
        orderBy: { createdAt: 'asc' },
      }),
      prisma.expense.findMany({
        where: whereClause,
        take: 10,
        include: { expenseType: true, project: true },
        orderBy: { createdAt: 'desc' },
      }),
    ])

    // Fetch expense type names
    const expenseTypeIds = expensesByType.map(e => e.expenseTypeId);
    const expenseTypes = await prisma.expenseType.findMany({
      where: { id: { in: expenseTypeIds } },
      select: { id: true, name: true, nameAr: true },
    })
    const expenseTypeMap = new Map(expenseTypes.map(et => [et.id, et]));
    
    const expensesByTypeWithName = expensesByType.map(e => {
      const typeInfo = expenseTypeMap.get(e.expenseTypeId);
      return {
        ...e,
        name: typeInfo?.name || 'Unknown',
        nameAr: typeInfo?.nameAr || 'غير معروف',
        amount: Number(e._sum.amount || 0)
      }
    });

    const monthlyBreakdown: { [key: string]: number } = {};
    monthlyExpenses.forEach(exp => {
      const month = exp.createdAt.toISOString().slice(0, 7); // YYYY-MM
      if (!monthlyBreakdown[month]) {
        monthlyBreakdown[month] = 0;
      }
      monthlyBreakdown[month] += Number(exp.amount);
    });

    const expensesByMonth = Object.entries(monthlyBreakdown).map(([month, amount]) => ({
      month,
      amount,
    }));

    const stats = {
      overview: {
        totalExpenses,
        totalAmount: Number(totalAmount._sum.amount || 0),
        avgExpenseAmount: totalExpenses > 0 ? Number(totalAmount._sum.amount || 0) / totalExpenses : 0,
        topCategory: expensesByTypeWithName[0] || null
      },
      statusBreakdown: statusBreakdown.reduce((acc, curr) => {
        acc[curr.status] = {
          count: curr._count.status,
          amount: Number(curr._sum.amount || 0),
        }
        return acc;
      }, {} as any),
      expensesByType: expensesByTypeWithName,
      expensesByMonth,
      recentExpenses,
      period,
    }

    return NextResponse.json(stats)
  } catch (error: any) {
    console.error('Error fetching expense statistics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch expense statistics', details: error.message },
      { status: 500 }
    )
  }
}
