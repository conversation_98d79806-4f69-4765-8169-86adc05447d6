"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  ArrowLeft,
  Edit,
  Printer,
  Mail,
  Download,
  Receipt,
  MoreHorizontal,
  Trash2,
  Copy,
  Send
} from "lucide-react"
import { useI18n, formatDate } from "@/lib/i18n"
import { QuotationTemplate } from "@/components/quotations/quotation-template"
import { toast } from "sonner"

interface QuotationItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  total: number
}

interface Quotation {
  id: string
  number: string
  date: string
  validUntil: string
  customer: {
    name: string
    mobile: string
    email?: string
  }
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED'
  subtotal: number
  taxAmount: number
  discount: number
  total: number
  items: QuotationItem[]
  notes?: string
  taskTitle?: string
}

const statusColors: { [key: string]: string } = {
  PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
  APPROVED: "bg-green-100 text-green-800 border-green-200",
  REJECTED: "bg-red-100 text-red-800 border-red-200",
  EXPIRED: "bg-gray-100 text-gray-800 border-gray-200",
  CONVERTED: "bg-blue-100 text-blue-800 border-blue-200",
}

export default function QuotationDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const searchParams = useSearchParams()
  const { t, formatCurrency, language } = useI18n()

  const [quotation, setQuotation] = useState<Quotation | null>(null)
  const [loading, setLoading] = useState(true)
  const [isPrintView, setIsPrintView] = useState(false)
  const [companyData, setCompanyData] = useState({
    name: 'Print & Copy Services',
    nameAr: 'خدمات الطباعة والنسخ',
    address: '123 Business Street, Muscat, Sultanate of Oman',
    addressAr: '123 شارع الأعمال، مسقط، سلطنة عمان',
    phone: '+968 2234 5678',
    email: '<EMAIL>',
    taxNumber: 'OM123456789',
    logo: '',
    termsConditions: '',
    termsConditionsAr: '',
    signature: '',
    stamp: ''
  })

  useEffect(() => {
    const fetchQuotation = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/quotations/${params.id}`)
        if (response.ok) {
          const quotationData = await response.json()
          setQuotation(quotationData)
        } else {
          toast.error(t('quotations.failedToFetch'))
          setQuotation(null)
        }
      } catch (error) {
        console.error('Error fetching quotation:', error)
        toast.error(t('quotations.loadingError'))
        setQuotation(null)
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
        fetchQuotation()
    }

    const savedCompanyData = localStorage.getItem('companyData')
    const savedLogo = localStorage.getItem('companyLogo')

    if (savedCompanyData) {
      const parsedData = JSON.parse(savedCompanyData)
      setCompanyData(prev => ({
        ...prev,
        name: parsedData.companyName || prev.name,
        nameAr: parsedData.companyNameAr || prev.nameAr,
        address: parsedData.companyAddress || prev.address,
        addressAr: parsedData.companyAddressAr || prev.addressAr,
        phone: parsedData.companyPhone || prev.phone,
        email: parsedData.companyEmail || prev.email,
        taxNumber: parsedData.taxNumber || prev.taxNumber,
        termsConditions: parsedData.termsConditions || prev.termsConditions,
        termsConditionsAr: parsedData.termsConditionsAr || prev.termsConditionsAr,
        signature: parsedData.signature || prev.signature,
        stamp: parsedData.stamp || prev.stamp
      }))
    }

    if (savedLogo) {
      setCompanyData(prev => ({ ...prev, logo: savedLogo }))
    }

    const printParam = searchParams.get('print')
    if (printParam === 'true') {
      setIsPrintView(true)
      setTimeout(() => {
        window.print()
        setIsPrintView(false)
        const url = new URL(window.location.href)
        url.searchParams.delete('print')
        router.replace(url.pathname)
      }, 1000)
    }
  }, [params.id, searchParams, router, t])

  const handleEdit = () => router.push(`/dashboard/quotations/${quotation?.id}/edit`)
  const handlePrint = () => {
    setIsPrintView(true)
    setTimeout(() => window.print(), 100)
  }
  const handleDuplicate = () => router.push(`/dashboard/quotations/create?duplicate=${quotation?.id}`)
  
  const handleDelete = async () => {
    if (confirm(t('quotations.details.confirmDelete'))) {
        try {
            const res = await fetch(`/api/quotations/${quotation?.id}`, { method: 'DELETE' });
            if (res.ok) {
                toast.success(t('quotations.quotationDeletedSuccessfully'));
                router.push('/dashboard/quotations');
            } else {
                toast.error(t('quotations.failedToDeleteQuotation'));
            }
        } catch (error) {
            toast.error(t('quotations.failedToDeleteQuotation'));
        }
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('quotations.details.backButton')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('quotations.details.loading')}</h2>
          </div>
        </div>
      </div>
    )
  }

  if (!quotation) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('quotations.details.backButton')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('quotations.details.notFound')}</h2>
          </div>
        </div>
      </div>
    )
  }

  const isExpired = new Date(quotation.validUntil) < new Date()
  const canConvert = quotation.status === 'APPROVED' && !isExpired

  if (isPrintView) {
    return (
      <div className="print:block">
        <QuotationTemplate
          quotation={quotation}
          company={companyData}
        />
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('quotations.details.backButton')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('quotations.details.title').replace('{number}', quotation.number)}</h2>
            <p className="text-muted-foreground">
              {t('quotations.details.description')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge className={`${statusColors[quotation.status]} hover:bg-opacity-80`}>
             {t(`quotations.statusLabels.${quotation.status}`)}
          </Badge>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                {t('quotations.details.actions')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t('quotations.editQuotation')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDuplicate}>
                <Copy className="mr-2 h-4 w-4" />
                {t('quotations.details.duplicate')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handlePrint}>
                <Printer className="mr-2 h-4 w-4" />
                {t('quotations.details.printPDF')}
              </DropdownMenuItem>
              <DropdownMenuItem disabled>
                <Mail className="mr-2 h-4 w-4" />
                {t('quotations.emailQuotation')}
              </DropdownMenuItem>
              <DropdownMenuItem disabled>
                <Download className="mr-2 h-4 w-4" />
                {t('quotations.details.download')}
              </DropdownMenuItem>
              <DropdownMenuItem disabled>
                <Send className="mr-2 h-4 w-4" />
                {t('quotations.sendToCustomer')}
              </DropdownMenuItem>
              {canConvert && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => router.push(`/dashboard/invoices/create?quotation=${quotation?.id}`)}
                    className="text-green-600 focus:text-green-600"
                  >
                    <Receipt className="mr-2 h-4 w-4" />
                    {t('quotations.convertToInvoice')}
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {t('quotations.details.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('quotations.details.infoCard.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('quotations.details.infoCard.customer')}</Label>
                    <p className="text-lg font-medium">{quotation.customer?.name}</p>
                    <p className="text-sm text-muted-foreground">{quotation.customer?.mobile}</p>
                    {quotation.customer?.email && (
                      <p className="text-sm text-muted-foreground">{quotation.customer?.email}</p>
                    )}
                  </div>

                  {quotation.taskTitle && (
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">{t('quotations.details.infoCard.linkedTask')}</Label>
                      <p className="text-base">{quotation.taskTitle}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('quotations.details.infoCard.quotationDate')}</Label>
                    <p className="text-base">{formatDate(quotation.date, language)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font--medium text-muted-foreground">{t('quotations.details.infoCard.validUntil')}</Label>
                    <p className="text-base">{formatDate(quotation.validUntil, language)}</p>
                    {isExpired && (
                      <p className="text-sm text-red-600">{t('quotations.details.infoCard.expired')}</p>
                    )}
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('quotations.details.infoCard.status')}</Label>
                    <div className="mt-1">
                      <Badge className={`${statusColors[quotation.status]} hover:bg-opacity-80`}>
                        {t(`quotations.statusLabels.${quotation.status}`)}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('quotations.details.itemsCard.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('quotations.details.itemsCard.headers.description')}</TableHead>
                    <TableHead className="text-right">{t('quotations.details.itemsCard.headers.quantity')}</TableHead>
                    <TableHead className="text-right">{t('quotations.details.itemsCard.headers.unitPrice')}</TableHead>
                    <TableHead className="text-right">{t('quotations.details.itemsCard.headers.total')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {quotation.items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.description}</TableCell>
                      <TableCell className="text-right">{item.quantity}</TableCell>
                      <TableCell className="text-right">{formatCurrency(Number(item.unitPrice))}</TableCell>
                      <TableCell className="text-right font-medium">{formatCurrency(Number(item.total))}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {quotation.notes && (
            <Card>
              <CardHeader>
                <CardTitle>{t('quotations.details.notesCard.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{quotation.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('quotations.details.summaryCard.title')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>{t('quotations.details.summaryCard.subtotal')}</span>
                <span>{formatCurrency(Number(quotation.subtotal))}</span>
              </div>

              {quotation.discount > 0 && (
                <div className="flex justify-between text-red-600">
                  <span>{t('quotations.details.summaryCard.discount')}</span>
                  <span>-{formatCurrency(Number(quotation.discount))}</span>
                </div>
              )}

              <div className="flex justify-between">
                <span>{t('quotations.details.summaryCard.vat')}</span>
                <span>{formatCurrency(Number(quotation.taxAmount))}</span>
              </div>

              <Separator />

              <div className="flex justify-between font-medium text-lg">
                <span>{t('quotations.details.summaryCard.total')}</span>
                <span>{formatCurrency(Number(quotation.total))}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('quotations.details.quickActionsCard.title')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" onClick={handlePrint}>
                <Printer className="mr-2 h-4 w-4" />
                {t('quotations.details.printPDF')}
              </Button>

              <Button variant="outline" className="w-full" disabled>
                <Mail className="mr-2 h-4 w-4" />
                {t('quotations.emailQuotation')}
              </Button>

              {canConvert && (
                <Button variant="outline" className="w-full" onClick={() => router.push(`/dashboard/invoices/create?quotation=${quotation?.id}`)}>
                  <Receipt className="mr-2 h-4 w-4" />
                  {t('quotations.convertToInvoice')}
                </Button>
              )}

              <Button variant="outline" className="w-full" onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t('quotations.editQuotation')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
