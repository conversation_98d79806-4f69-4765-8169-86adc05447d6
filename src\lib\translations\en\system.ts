export const system = {
  // Success messages
  success: {
    created: "Created successfully",
    updated: "Updated successfully",
    deleted: "Deleted successfully",
    saved: "Saved successfully",
    uploaded: "Uploaded successfully",
    sent: "Sent successfully",
    approved: "Approved successfully",
    rejected: "Rejected successfully",
    completed: "Completed successfully",
    processed: "Processed successfully",
    
    // Specific success messages
    customerCreated: "Customer created successfully",
    customerUpdated: "Customer updated successfully",
    customerDeleted: "Customer deleted successfully",
    supplierCreated: "Supplier created successfully",
    supplierUpdated: "Supplier updated successfully",
    supplierDeleted: "Supplier deleted successfully",
    productCreated: "Product created successfully",
    productUpdated: "Product updated successfully",
    productDeleted: "Product deleted successfully",
    invoiceCreated: "Invoice created successfully",
    invoiceUpdated: "Invoice updated successfully",
    invoiceDeleted: "Invoice deleted successfully",
    expenseApproved: "Expense approved successfully",
    expenseRejected: "Expense rejected successfully",
    expenseMarkedAsPaid: "Expense marked as paid successfully",
    paymentRecorded: "Payment recorded successfully",
    employeeCreated: "Employee created successfully",
    employeeUpdated: "Employee updated successfully",
    employeeDeleted: "Employee deleted successfully",
    settingsSaved: "Settings saved successfully",
    imageUploaded: "Image uploaded successfully",
    fileUploaded: "File uploaded successfully",
    linkCopied: "Link copied successfully",
  },

  // Error messages
  error: {
    general: "An error occurred. Please try again",
    networkError: "Network error. Please check your connection",
    serverError: "Server error. Please try again later",
    unauthorized: "Unauthorized access",
    forbidden: "Access forbidden",
    notFound: "Not found",
    validationError: "Validation error",
    
    // Specific error messages
    failedToCreate: "Failed to create",
    failedToUpdate: "Failed to update",
    failedToDelete: "Failed to delete",
    failedToSave: "Failed to save",
    failedToUpload: "Failed to upload",
    failedToSend: "Failed to send",
    failedToApprove: "Failed to approve",
    failedToReject: "Failed to reject",
    failedToComplete: "Failed to complete",
    failedToProcess: "Failed to process",
    
    // Entity specific errors
    failedToCreateCustomer: "Failed to create customer",
    failedToUpdateCustomer: "Failed to update customer",
    failedToDeleteCustomer: "Failed to delete customer",
    failedToCreateSupplier: "Failed to create supplier",
    failedToUpdateSupplier: "Failed to update supplier",
    failedToDeleteSupplier: "Failed to delete supplier",
    failedToCreateProduct: "Failed to create product",
    failedToUpdateProduct: "Failed to update product",
    failedToDeleteProduct: "Failed to delete product",
    failedToCreateInvoice: "Failed to create invoice",
    failedToUpdateInvoice: "Failed to update invoice",
    failedToDeleteInvoice: "Failed to delete invoice",
    failedToApproveExpense: "Failed to approve expense",
    failedToRejectExpense: "Failed to reject expense",
    failedToMarkExpenseAsPaid: "Failed to mark expense as paid",
    failedToRecordPayment: "Failed to record payment",
    failedToCreateEmployee: "Failed to create employee",
    failedToUpdateEmployee: "Failed to update employee",
    failedToDeleteEmployee: "Failed to delete employee",
    failedToSaveSettings: "Failed to save settings",
    failedToUploadImage: "Failed to upload image",
    failedToUploadFile: "Failed to upload file",
  },

  // Validation messages
  validation: {
    required: "This field is required",
    email: "Please enter a valid email address",
    phone: "Please enter a valid phone number",
    password: "Password must be at least 8 characters",
    passwordMismatch: "Passwords do not match",
    minLength: "Must be at least {min} characters",
    maxLength: "Must be at most {max} characters",
    minValue: "Value must be greater than {min}",
    maxValue: "Value must be less than {max}",
    invalidFormat: "Invalid format",
    invalidDate: "Invalid date",
    invalidNumber: "Invalid number",
    invalidUrl: "Invalid URL",
    
    // Specific validation messages
    nameRequired: "Name is required",
    emailRequired: "Email is required",
    phoneRequired: "Phone number is required",
    passwordRequired: "Password is required",
    amountRequired: "Amount is required",
    quantityRequired: "Quantity is required",
    priceRequired: "Price is required",
    dateRequired: "Date is required",
    categoryRequired: "Category is required",
    supplierRequired: "Supplier is required",
    customerRequired: "Customer is required",
    productRequired: "Product is required",
    descriptionRequired: "Description is required",
    
    // File validation
    fileRequired: "File is required",
    fileSizeExceeded: "File size is too large",
    invalidFileType: "File type not supported",
    imageRequired: "Image is required",
    imageSizeExceeded: "Image size is too large",
    invalidImageType: "Image type not supported",
    
    // Business logic validation
    insufficientStock: "Insufficient stock",
    insufficientBalance: "Insufficient balance",
    duplicateEntry: "This entry already exists",
    invalidQuantity: "Invalid quantity",
    invalidPrice: "Invalid price",
    invalidAmount: "Invalid amount",
    exceedsMaximum: "Exceeds maximum limit",
    belowMinimum: "Below minimum limit",
  },

  // Confirmation messages
  confirmation: {
    delete: "Are you sure you want to delete?",
    save: "Do you want to save changes?",
    cancel: "Do you want to cancel?",
    approve: "Do you want to approve?",
    reject: "Do you want to reject?",
    submit: "Do you want to submit?",
    
    // Specific confirmations
    deleteCustomer: "Are you sure you want to delete this customer?",
    deleteSupplier: "Are you sure you want to delete this supplier?",
    deleteProduct: "Are you sure you want to delete this product?",
    deleteInvoice: "Are you sure you want to delete this invoice?",
    deleteEmployee: "Are you sure you want to delete this employee?",
    approveExpense: "Do you want to approve this expense?",
    rejectExpense: "Do you want to reject this expense?",
    markAsPaid: "Do you want to mark this as paid?",
    resetSettings: "Do you want to reset settings?",
    logout: "Do you want to sign out?",
  },

  // Loading states
  loading: {
    general: "Loading...",
    saving: "Saving...",
    deleting: "Deleting...",
    uploading: "Uploading...",
    processing: "Processing...",
    sending: "Sending...",
    loading: "Loading...",
    
    // Specific loading states
    creatingCustomer: "Creating customer...",
    updatingCustomer: "Updating customer...",
    deletingCustomer: "Deleting customer...",
    creatingSupplier: "Creating supplier...",
    updatingSupplier: "Updating supplier...",
    deletingSupplier: "Deleting supplier...",
    creatingProduct: "Creating product...",
    updatingProduct: "Updating product...",
    deletingProduct: "Deleting product...",
    creatingInvoice: "Creating invoice...",
    updatingInvoice: "Updating invoice...",
    deletingInvoice: "Deleting invoice...",
    uploadingImage: "Uploading image...",
    uploadingFile: "Uploading file...",
    savingSettings: "Saving settings...",
    signingIn: "Signing in...",
    signingOut: "Signing out...",
  },

  // Status messages
  status: {
    online: "Online",
    offline: "Offline",
    connected: "Connected",
    disconnected: "Disconnected",
    syncing: "Syncing",
    synced: "Synced",
    pending: "Pending",
    processing: "Processing",
    completed: "Completed",
    failed: "Failed",
    cancelled: "Cancelled",
    expired: "Expired",
    active: "Active",
    inactive: "Inactive",
    enabled: "Enabled",
    disabled: "Disabled",
  },

  // File and upload messages
  file: {
    selectFile: "Select File",
    selectImage: "Select Image",
    dragAndDrop: "Drag and drop file here",
    clickToUpload: "Click to upload",
    uploadProgress: "Upload progress: {progress}%",
    uploadComplete: "Upload complete",
    uploadFailed: "Upload failed",
    fileNotSelected: "No file selected",
    imageNotSelected: "No image selected",
    fileSizeLimit: "File size must be less than {size}",
    allowedFormats: "Allowed formats: {formats}",
    fileDeleted: "File deleted successfully",
    folderCreated: "Folder created successfully",
    folderDeleted: "Folder deleted successfully",
  },

  // API and network messages
  api: {
    connectionError: "Connection error",
    timeoutError: "Connection timeout",
    serverUnavailable: "Server unavailable",
    badRequest: "Bad request",
    unauthorized: "Unauthorized",
    forbidden: "Forbidden",
    notFound: "Not found",
    methodNotAllowed: "Method not allowed",
    conflict: "Data conflict",
    internalServerError: "Internal server error",
    serviceUnavailable: "Service unavailable",
    rateLimitExceeded: "Rate limit exceeded",
  }
}; 