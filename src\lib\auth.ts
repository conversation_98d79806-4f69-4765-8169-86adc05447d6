import { NextAuthOptions } from 'next-auth'
import Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import { PrismaAdapter } from '@auth/prisma-adapter'
import { prisma } from './prisma'
import bcrypt from 'bcryptjs'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as any,
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        })

        if (!user || !user.isActive) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          phone: user.phone,
          avatar: user.avatar
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    // Remove maxAge to make it a session cookie that expires when browser closes
    updateAge: 60 * 60, // Update session every hour
  },
  jwt: {
    // JWT tokens should have a reasonable expiry for security
    maxAge: 8 * 60 * 60, // 8 hours (in seconds)
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.role = user.role
        token.phone = user.phone
        token.avatar = user.avatar
        token.isActive = true
        token.lastActivity = Date.now()
      }

      // Check if token is expired or user is inactive
      if (token.lastActivity && Date.now() - (token.lastActivity as number) > 8 * 60 * 60 * 1000) {
        return null // Force logout if inactive for 8 hours
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as string
        session.user.phone = token.phone as string
        session.user.avatar = token.avatar as string

        // Update last activity
        token.lastActivity = Date.now()
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // Redirect to dashboard after login
      if (url.startsWith("/")) return `${baseUrl}${url}`
      else if (new URL(url).origin === baseUrl) return url
      return `${baseUrl}/dashboard`
    }
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/login', // Redirect errors to login page
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        // Don't set maxAge - this makes it a session cookie that expires when browser closes
      }
    },
    callbackUrl: {
      name: `next-auth.callback-url`,
      options: {
        httpOnly: false,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      }
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      }
    },
  },
  events: {
    async signOut({ token }) {
      // Clear any additional data on sign out
      console.log('User signed out:', token?.id)
    },
    async session({ session, token }) {
      // Log session activity
      if (process.env.NODE_ENV === 'development') {
        console.log('Session accessed:', session?.user?.email)
      }
    },
  },
  secret: process.env.NEXTAUTH_SECRET
}
