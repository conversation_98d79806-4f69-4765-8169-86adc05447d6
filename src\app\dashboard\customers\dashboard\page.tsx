"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts"
import {
  Users,
  DollarSign,
  TrendingUp,
  Calendar,
  Plus,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  CreditCard,
  FileText,
  Clock,
  CheckCircle
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"

// Customer stats will be fetched from API

// Chart data will be fetched from API
const monthlyData: any[] = []
const customerTypeData: any[] = []
const paymentStatusData: any[] = []

interface RecentCustomer {
  id: string
  name: string
  nameAr?: string
  email?: string
  company?: string
  phone?: string
  createdAt: string
  totalInvoices: number
  totalSpent: number
}

export default function CustomerDashboard() {
  const { t } = useI18n()
  const router = useRouter()
  const [customerStats, setCustomerStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    newThisMonth: 0,
    totalRevenue: 0,
    avgOrderValue: 0,
    topCustomers: 0,
    repeatCustomers: 0,
    outstandingBalance: 0
  })
  const [recentCustomers, setRecentCustomers] = useState<RecentCustomer[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCustomerData()
  }, [])

  const fetchCustomerData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/customers/dashboard')
      if (response.ok) {
        const data = await response.json()
        setCustomerStats(data.stats || customerStats)
        setRecentCustomers(data.recentCustomers || [])
      }
    } catch (error) {
      console.error('Error fetching customer data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('customers.dashboard')}</h2>
          <p className="text-muted-foreground">
            {t('customers.dashboardDescription')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/customers')}>
            <Users className="mr-2 h-4 w-4" />
            {t('customers.viewAllCustomers')}
          </Button>
          <Button onClick={() => router.push('/dashboard/customers/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('customers.addCustomer')}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('customers.totalCustomers')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customerStats.total}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +{customerStats.newThisMonth} {t('customers.thisMonth')}
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('customers.totalRevenue')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(customerStats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +15% {t('customers.fromLastMonth')}
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('customers.avgOrderValue')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(customerStats.avgOrderValue)}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +8% {t('customers.fromLastMonth')}
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('customers.repeatCustomers')}</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{customerStats.repeatCustomers}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-gray-600 flex items-center">
                <Minus className="h-3 w-3 mr-1" />
                {t('customers.nanPercentOfTotal')}
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Customer Status Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('customers.activeCustomers')}</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{customerStats.active}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((customerStats.active / customerStats.total) * 100)}% of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('customers.inactiveCustomers')}</CardTitle>
            <Clock className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{customerStats.inactive}</div>
            <p className="text-xs text-muted-foreground">
              {t('customers.needReEngagement')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('customers.topCustomers')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{customerStats.topCustomers}</div>
            <p className="text-xs text-muted-foreground">
              {t('customers.highValueCustomers')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('customers.outstandingBalance')}</CardTitle>
            <CreditCard className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{formatCurrency(customerStats.outstandingBalance)}</div>
            <p className="text-xs text-muted-foreground">
              From 22 customers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Monthly Customer Growth */}
        <Card>
          <CardHeader>
            <CardTitle>{t('customers.monthlyCustomerGrowth')}</CardTitle>
            <CardDescription>
              {t('customers.newCustomersAndRevenueOverLast5Months')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip
                  formatter={(value, name) => [
                    name === 'customers' ? value : formatCurrency(value as number),
                    name === 'customers' ? 'New Customers' : 'Revenue'
                  ]}
                />
                <Bar yAxisId="left" dataKey="customers" fill="#3b82f6" />
                <Line yAxisId="right" type="monotone" dataKey="revenue" stroke="#10b981" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Customer Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('customers.customerTypeDistribution')}</CardTitle>
            <CardDescription>
              {t('customers.breakdownOfCustomersByType')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={customerTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {customerTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Customers */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t('customers.recentCustomers')}</CardTitle>
              <CardDescription>
                {t('customers.latestCustomers')}
              </CardDescription>
            </div>
            <Button variant="outline" onClick={() => router.push('/dashboard/customers')}>
              <Eye className="mr-2 h-4 w-4" />
              {t('customers.viewAll')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('customers.customer')}</TableHead>
                <TableHead>{t('customers.contact')}</TableHead>
                <TableHead>{t('customers.totalSpent')}</TableHead>
                <TableHead>{t('customers.invoices')}</TableHead>
                <TableHead>{t('customers.outstanding')}</TableHead>
                <TableHead>{t('customers.lastOrder')}</TableHead>
                <TableHead>{t('customers.status')}</TableHead>
                <TableHead className="text-right">{t('customers.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentCustomers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell>
                    <div>
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium"
                        onClick={() => router.push(`/dashboard/customers/${customer.id}`)}
                      >
                        {customer.name}
                      </Button>
                      <div className="text-sm text-muted-foreground">{customer.company}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">{customer.phone}</div>
                      <div className="text-sm text-muted-foreground">{customer.email}</div>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {formatCurrency(customer.totalSpent)}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{customer.invoiceCount} {t('customers.invoices')}</Badge>
                  </TableCell>
                  <TableCell>
                    {customer.outstandingBalance > 0 ? (
                      <span className="text-orange-600 font-medium">
                        {formatCurrency(customer.outstandingBalance)}
                      </span>
                    ) : (
                      <span className="text-green-600">{t('customers.paid')}</span>
                    )}
                  </TableCell>
                  <TableCell>{new Date(customer.lastOrder).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Badge
                      variant={customer.status === 'active' ? 'default' : 'secondary'}
                      className={customer.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                    >
                      {customer.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/dashboard/customers/${customer.id}`)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}