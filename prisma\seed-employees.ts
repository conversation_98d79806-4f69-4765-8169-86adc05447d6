import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding employees with sales data...')

  // Sample employee data with realistic sales performance
  const employeesData = [
    {
      name: '<PERSON> (أحمد الراشد)',
      email: '<EMAIL>',
      phone: '+968 9123 4567',
      role: 'MANAGER',
      joinDate: new Date('2022-01-15'),
      tasksCompleted: 45,
      tasksInProgress: 3,
      completionRate: 93.8,
      salesData: {
        totalSales: 125000,
        monthlyTarget: 100000,
        invoicesCount: 28,
        avgOrderValue: 4464,
        conversionRate: 85,
        commission: 6250,
        rank: 1,
        growth: 15.5
      }
    },
    {
      name: '<PERSON><PERSON> (فاطمة الزهراء)',
      email: '<EMAIL>',
      phone: '+968 9234 5678',
      role: 'EMPLOYEE',
      joinDate: new Date('2022-03-20'),
      tasksCompleted: 38,
      tasksInProgress: 5,
      completionRate: 88.4,
      salesData: {
        totalSales: 98000,
        monthlyTarget: 80000,
        invoicesCount: 22,
        avgOrderValue: 4455,
        conversionRate: 78,
        commission: 4900,
        rank: 2,
        growth: 22.5
      }
    },
    {
      name: 'Mohammed Al-Balushi (محمد البلوشي)',
      email: '<EMAIL>',
      phone: '+968 9345 6789',
      role: 'EMPLOYEE',
      joinDate: new Date('2022-06-10'),
      tasksCompleted: 32,
      tasksInProgress: 4,
      completionRate: 88.9,
      salesData: {
        totalSales: 87500,
        monthlyTarget: 75000,
        invoicesCount: 19,
        avgOrderValue: 4605,
        conversionRate: 72,
        commission: 4375,
        rank: 3,
        growth: 8.3
      }
    },
    {
      name: 'Aisha Al-Kindi (عائشة الكندي)',
      email: '<EMAIL>',
      phone: '+968 9456 7890',
      role: 'EMPLOYEE',
      joinDate: new Date('2023-01-08'),
      tasksCompleted: 29,
      tasksInProgress: 6,
      completionRate: 82.9,
      salesData: {
        totalSales: 72000,
        monthlyTarget: 70000,
        invoicesCount: 16,
        avgOrderValue: 4500,
        conversionRate: 68,
        commission: 3600,
        rank: 4,
        growth: 12.1
      }
    },
    {
      name: 'Omar Al-Hinai (عمر الهنائي)',
      email: '<EMAIL>',
      phone: '+968 9567 8901',
      role: 'EMPLOYEE',
      joinDate: new Date('2023-02-15'),
      tasksCompleted: 25,
      tasksInProgress: 7,
      completionRate: 78.1,
      salesData: {
        totalSales: 65000,
        monthlyTarget: 65000,
        invoicesCount: 14,
        avgOrderValue: 4643,
        conversionRate: 65,
        commission: 3250,
        rank: 5,
        growth: 5.8
      }
    },
    {
      name: 'Mariam Al-Siyabi (مريم السيابي)',
      email: '<EMAIL>',
      phone: '+968 9678 9012',
      role: 'EMPLOYEE',
      joinDate: new Date('2023-04-22'),
      tasksCompleted: 22,
      tasksInProgress: 8,
      completionRate: 73.3,
      salesData: {
        totalSales: 58000,
        monthlyTarget: 60000,
        invoicesCount: 13,
        avgOrderValue: 4462,
        conversionRate: 62,
        commission: 2900,
        rank: 6,
        growth: -3.2
      }
    },
    {
      name: 'Khalid Al-Mamari (خالد المعمري)',
      email: '<EMAIL>',
      phone: '+968 9789 0123',
      role: 'EMPLOYEE',
      joinDate: new Date('2023-07-10'),
      tasksCompleted: 18,
      tasksInProgress: 9,
      completionRate: 66.7,
      salesData: {
        totalSales: 52000,
        monthlyTarget: 55000,
        invoicesCount: 11,
        avgOrderValue: 4727,
        conversionRate: 58,
        commission: 2600,
        rank: 7,
        growth: -5.5
      }
    },
    {
      name: 'Noura Al-Ghafri (نورا الغافري)',
      email: '<EMAIL>',
      phone: '+968 9890 1234',
      role: 'EMPLOYEE',
      joinDate: new Date('2023-09-05'),
      tasksCompleted: 15,
      tasksInProgress: 10,
      completionRate: 60.0,
      salesData: {
        totalSales: 45000,
        monthlyTarget: 50000,
        invoicesCount: 10,
        avgOrderValue: 4500,
        conversionRate: 55,
        commission: 2250,
        rank: 8,
        growth: -10.0
      }
    }
  ]

  // Create employees
  for (const employeeData of employeesData) {
    const hashedPassword = await bcrypt.hash('password123', 10)
    
    try {
      const employee = await prisma.user.upsert({
        where: { email: employeeData.email },
        update: {
          name: employeeData.name,
          phone: employeeData.phone,
          role: employeeData.role as any,
          joinDate: employeeData.joinDate,
          tasksCompleted: employeeData.tasksCompleted,
          tasksInProgress: employeeData.tasksInProgress,
          completionRate: employeeData.completionRate,
          isActive: true,
        },
        create: {
          email: employeeData.email,
          password: hashedPassword,
          name: employeeData.name,
          phone: employeeData.phone,
          role: employeeData.role as any,
          joinDate: employeeData.joinDate,
          tasksCompleted: employeeData.tasksCompleted,
          tasksInProgress: employeeData.tasksInProgress,
          completionRate: employeeData.completionRate,
          isActive: true,
        },
      })

      // Create or update sales data
      await prisma.salesData.upsert({
        where: { userId: employee.id },
        update: {
          totalSales: employeeData.salesData.totalSales,
          monthlyTarget: employeeData.salesData.monthlyTarget,
          invoicesCount: employeeData.salesData.invoicesCount,
          avgOrderValue: employeeData.salesData.avgOrderValue,
          conversionRate: employeeData.salesData.conversionRate,
          commission: employeeData.salesData.commission,
          rank: employeeData.salesData.rank,
          growth: employeeData.salesData.growth,
        },
        create: {
          userId: employee.id,
          totalSales: employeeData.salesData.totalSales,
          monthlyTarget: employeeData.salesData.monthlyTarget,
          invoicesCount: employeeData.salesData.invoicesCount,
          avgOrderValue: employeeData.salesData.avgOrderValue,
          conversionRate: employeeData.salesData.conversionRate,
          commission: employeeData.salesData.commission,
          rank: employeeData.salesData.rank,
          growth: employeeData.salesData.growth,
        },
      })

      console.log(`✅ Created/Updated employee: ${employeeData.name}`)
    } catch (error) {
      console.error(`❌ Error creating employee ${employeeData.name}:`, error)
    }
  }

  console.log('✅ Employee seeding completed!')
  console.log('')
  console.log('👥 Employees Created:')
  employeesData.forEach((emp, index) => {
    console.log(`   ${index + 1}. ${emp.name} (${emp.email}) - ${emp.role}`)
  })
  console.log('')
  console.log('📊 Sales Performance Data:')
  console.log('   💰 Total Sales: OMR', employeesData.reduce((sum, emp) => sum + emp.salesData.totalSales, 0).toLocaleString())
  console.log('   🎯 Total Targets: OMR', employeesData.reduce((sum, emp) => sum + emp.salesData.monthlyTarget, 0).toLocaleString())
  console.log('   📋 Total Invoices:', employeesData.reduce((sum, emp) => sum + emp.salesData.invoicesCount, 0))
  console.log('   💵 Total Commission: OMR', employeesData.reduce((sum, emp) => sum + emp.salesData.commission, 0).toLocaleString())
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
