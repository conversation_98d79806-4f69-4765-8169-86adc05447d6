import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '6months'
    const category = searchParams.get('category') || 'all'

    // Test basic database connectivity first
    console.log('Testing database connectivity...')

    // Simple test query
    const testResult = await prisma.$queryRaw`SELECT 1 as test`
    console.log('Database test result:', testResult)

    // Calculate date range based on period
    const now = new Date()
    const startDate = new Date()

    switch (period) {
      case '1month':
        startDate.setMonth(now.getMonth() - 1)
        break
      case '3months':
        startDate.setMonth(now.getMonth() - 3)
        break
      case '6months':
        startDate.setMonth(now.getMonth() - 6)
        break
      case '1year':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setMonth(now.getMonth() - 6)
    }

    console.log('Date range:', { startDate, endDate: now })

    // Get actual revenue data from invoices
    console.log('Fetching invoices...')
    const invoices = await prisma.invoice.findMany({
      where: {
        date: {
          gte: startDate,
          lte: now
        }
      },
      include: {
        items: true
      }
    })
    console.log('Found invoices:', invoices.length)

    // Get actual expense data from expenses
    console.log('Fetching expenses...')
    const expenses = await prisma.expense.findMany({
      where: {
        date: {
          gte: startDate,
          lte: now
        }
      },
      include: {
        category: true
      }
    })
    console.log('Found expenses:', expenses.length)

    // Calculate actual totals
    const actualRevenue = invoices.reduce((sum, invoice) => sum + invoice.total.toNumber(), 0)
    const actualExpenses = expenses.reduce((sum, expense) => sum + expense.amount.toNumber(), 0)

    // Sample budget data (in a real app, this would come from a budget table)
    const budgetData = {
      revenue: 180000, // 6 months budget
      expenses: {
        operatingExpenses: 45000,
        marketing: 15000,
        salaries: 60000,
        utilities: 8000,
        rent: 18000,
        supplies: 5000,
        travel: 3000,
        other: 6000
      }
    }

    const totalBudgetExpenses = Object.values(budgetData.expenses).reduce((sum, val) => sum + val, 0)

    // Calculate expense breakdown by category
    const expensesByCategory = expenses.reduce((acc, expense) => {
      const categoryName = expense.category?.name || 'Other'
      const categoryKey = getCategoryKey(categoryName)
      acc[categoryKey] = (acc[categoryKey] || 0) + expense.amount.toNumber()
      return acc
    }, {} as Record<string, number>)

    // Create budget variance analysis
    const budgetVarianceData = [
      {
        category: 'Revenue',
        budget: budgetData.revenue,
        actual: actualRevenue,
        variance: actualRevenue - budgetData.revenue,
        variancePercent: ((actualRevenue - budgetData.revenue) / budgetData.revenue) * 100,
        status: actualRevenue >= budgetData.revenue ? 'favorable' : 'unfavorable'
      }
    ]

    // Add expense categories
    Object.entries(budgetData.expenses).forEach(([category, budgetAmount]) => {
      const actualAmount = expensesByCategory[category] || 0
      const variance = budgetAmount - actualAmount // For expenses, under budget is favorable
      budgetVarianceData.push({
        category: category.charAt(0).toUpperCase() + category.slice(1),
        budget: budgetAmount,
        actual: actualAmount,
        variance: variance,
        variancePercent: budgetAmount > 0 ? (variance / budgetAmount) * 100 : 0,
        status: variance >= 0 ? 'favorable' : 'unfavorable'
      })
    })

    // Calculate monthly trend data
    const monthlyData = []
    for (let i = 5; i >= 0; i--) {
      const monthDate = new Date()
      monthDate.setMonth(now.getMonth() - i)
      const monthName = monthDate.toLocaleDateString('en-US', { month: 'short' })
      
      // Sample monthly budget vs actual (in real app, calculate from actual data)
      const monthlyBudgetRevenue = budgetData.revenue / 6
      const monthlyActualRevenue = actualRevenue / 6 * (0.8 + Math.random() * 0.4) // Simulate variance
      const monthlyBudgetExpenses = totalBudgetExpenses / 6
      const monthlyActualExpenses = actualExpenses / 6 * (0.8 + Math.random() * 0.4)

      monthlyData.push({
        month: monthName,
        budgetRevenue: monthlyBudgetRevenue,
        actualRevenue: monthlyActualRevenue,
        budgetExpenses: monthlyBudgetExpenses,
        actualExpenses: monthlyActualExpenses,
        budgetNet: monthlyBudgetRevenue - monthlyBudgetExpenses,
        actualNet: monthlyActualRevenue - monthlyActualExpenses
      })
    }

    // Calculate summary statistics
    const totalBudget = budgetData.revenue - totalBudgetExpenses
    const totalActual = actualRevenue - actualExpenses
    const totalVariance = totalActual - totalBudget
    const favorableVariances = budgetVarianceData.filter(item => item.status === 'favorable').length
    const unfavorableVariances = budgetVarianceData.filter(item => item.status === 'unfavorable').length
    const averageVariancePercent = budgetVarianceData.reduce((sum, item) => sum + Math.abs(item.variancePercent), 0) / budgetVarianceData.length

    const summary = {
      totalBudget,
      totalActual,
      totalVariance,
      variancePercentage: totalBudget !== 0 ? (totalVariance / Math.abs(totalBudget)) * 100 : 0,
      totalCategories: budgetVarianceData.length,
      favorableVariances,
      unfavorableVariances,
      averageVariancePercent,
      budgetUtilization: totalBudget !== 0 ? (totalActual / totalBudget) * 100 : 0
    }

    const response = {
      summary,
      budgetVarianceData,
      monthlyTrend: monthlyData,
      period,
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString()
      }
    }

    return NextResponse.json(response)

  } catch (error: any) {
    console.error('Error fetching budget variance data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch budget variance data', details: error.message },
      { status: 500 }
    )
  }
}

// Helper function to map expense category names to budget keys
function getCategoryKey(categoryName: string): string {
  const mapping: Record<string, string> = {
    'Marketing': 'marketing',
    'Salaries': 'salaries', 
    'Salary': 'salaries',
    'Utilities': 'utilities',
    'Rent': 'rent',
    'Supplies': 'supplies',
    'Travel': 'travel',
    'Operating': 'operatingExpenses',
    'Operations': 'operatingExpenses'
  }
  
  return mapping[categoryName] || 'other'
}
