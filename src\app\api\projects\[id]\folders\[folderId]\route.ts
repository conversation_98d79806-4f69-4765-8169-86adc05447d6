import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function DELETE(
  request: NextRequest, 
  { params }: { params: Promise<{ id: string; folderId: string }> }
) {
  try {
    const { folderId } = await params

    // Delete all documents in the folder first
    await prisma.projectDocument.deleteMany({
      where: {
        parentFolderId: folderId
      }
    })

    // Then delete the folder
    await prisma.projectFolder.delete({
      where: {
        id: folderId
      }
    })

    return NextResponse.json({ message: 'Folder deleted successfully' })
  } catch (error) {
    console.error('Error deleting project folder:', error)
    return NextResponse.json({ error: 'Failed to delete folder' }, { status: 500 })
  }
} 