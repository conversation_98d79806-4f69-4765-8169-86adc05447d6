export const pos = {
  // Page title and description
  title: "نقطة البيع",
  description: "معالجة معاملات البيع وإدارة طلبات العملاء",
  
  // Product search and categories
  searchProducts: "البحث في المنتجات...",
  allCategories: "جميع الفئات",
  category: "الفئة",
  
  // Product information
  product: "المنتج",
  products: "المنتجات",
  name: "الاسم",
  price: "السعر",
  stock: "المخزون",
  sku: "رمز المنتج",
  unit: "الوحدة",
  inStock: "متوفر في المخزون",
  outOfStock: "نفد من المخزون",
  lowStock: "مخزون منخفض",
  
  // Cart management
  cart: "السلة",
  addToCart: "إضافة إلى السلة",
  removeFromCart: "إزالة من السلة",
  clearCart: "مسح السلة",
  quantity: "الكمية",
  total: "المجموع",
  subtotal: "المجموع الفرعي",
  tax: "الضريبة",
  discount: "الخصم",
  grandTotal: "المجموع الإجمالي",
  
  // Customer management
  customer: "العميل",
  selectCustomer: "اختر العميل",
  addCustomer: "إضافة عميل",
  newCustomer: "عميل جديد",
  customerName: "اسم العميل",
  customerPhone: "هاتف العميل",
  customerEmail: "بريد العميل الإلكتروني",
  company: "الشركة",
  searchCustomers: "البحث في العملاء...",
  noCustomerSelected: "لم يتم اختيار عميل",
  walkInCustomer: "عميل عابر",
  
  // Payment processing
  payment: "الدفع",
  paymentMethod: "طريقة الدفع",
  cash: "نقداً",
  card: "بطاقة",
  creditCard: "بطاقة ائتمان",
  debitCard: "بطاقة خصم",
  bankTransfer: "تحويل بنكي",
  amountPaid: "المبلغ المدفوع",
  change: "الباقي",
  processPayment: "معالجة الدفع",
  completeTransaction: "إكمال المعاملة",
  
  // Transaction status
  transactionCompleted: "تمت المعاملة بنجاح",
  transactionFailed: "فشلت المعاملة",
  paymentSuccessful: "تم الدفع بنجاح",
  insufficientPayment: "دفع غير كافي",
  
  // Actions
  checkout: "الدفع",
  print: "طباعة",
  printReceipt: "طباعة الإيصال",
  emailReceipt: "إرسال الإيصال بالبريد",
  newTransaction: "معاملة جديدة",
  hold: "تعليق",
  recall: "استدعاء",
  
  // Receipt
  receipt: "الإيصال",
  receiptNumber: "رقم الإيصال",
  date: "التاريخ",
  time: "الوقت",
  cashier: "أمين الصندوق",
  thankYou: "شكراً لك على تعاملك معنا!",
  
  // Validation messages
  selectAtLeastOneItem: "يرجى اختيار عنصر واحد على الأقل",
  enterValidAmount: "يرجى إدخال مبلغ صحيح",
  insufficientStock: "مخزون غير كافي",
  customerRequired: "اختيار العميل مطلوب",
  
  // Error messages
  failedToProcessPayment: "فشل في معالجة الدفع",
  failedToAddProduct: "فشل في إضافة المنتج إلى السلة",
  failedToUpdateQuantity: "فشل في تحديث الكمية",
  productNotFound: "المنتج غير موجود",
  
  // Success messages
  productAddedToCart: "تم إضافة المنتج إلى السلة",
  quantityUpdated: "تم تحديث الكمية",
  customerAdded: "تم إضافة العميل بنجاح",
  transactionSaved: "تم حفظ المعاملة بنجاح",
  
  // Empty states
  cartEmpty: "السلة فارغة",
  noProductsFound: "لم يتم العثور على منتجات",
  noCustomersFound: "لم يتم العثور على عملاء",
  
  // Buttons
  add: "إضافة",
  remove: "إزالة",
  update: "تحديث",
  save: "حفظ",
  cancel: "إلغاء",
  close: "إغلاق",
  confirm: "تأكيد",
  
  // Numbers and calculations
  items: "العناصر",
  itemCount: "عدد العناصر",
  totalItems: "إجمالي العناصر",
  
  // Categories (common ones for printing business)
  officeSupplies: "مستلزمات المكتب",
  printingServices: "خدمات الطباعة",
  writingSupplies: "مستلزمات الكتابة",
  officeEquipment: "معدات المكتب",
  designServices: "خدمات التصميم",
  
  // Units
  piece: "قطعة",
  ream: "رزمة",
  set: "مجموعة",
  design: "تصميم",
  
  // Status indicators
  processing: "جاري المعالجة...",
  loading: "جاري التحميل...",
  saving: "جاري الحفظ...",
  
  // Quick actions
  quickAdd: "إضافة سريعة",
  quickPay: "دفع سريع",
  exactAmount: "المبلغ الدقيق",
  
  // Keyboard shortcuts
  shortcuts: "الاختصارات",
  enterToSearch: "Enter للبحث",
  escToCancel: "Esc للإلغاء",
  
  // Till management
  openTill: "فتح الصندوق",
  closeTill: "إغلاق الصندوق",
  tillBalance: "رصيد الصندوق",
  cashDrawer: "درج النقد"
}; 