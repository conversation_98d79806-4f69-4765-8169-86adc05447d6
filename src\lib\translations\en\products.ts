export const products = {
  // Page titles and descriptions
  title: "Products",
  dashboard: "Products Dashboard",
  manageProducts: "Manage Products",
  createProductTitle: "Create New Product",
  createProductDescription: "Add a new product or service to your inventory",
  
  // Table headers and display
  productName: "Product Name",
  sku: "SKU",
  category: "Category", 
  type: "Type",
  price: "Price",
  stock: "Stock",
  supplier: "Supplier",
  
  // Product types
  physical: "Physical Product",
  service: "Service",
  digitalProduct: "Digital Product",
  
  // Product information
  productInformation: "Product Information",
  basicProductDetails: "Basic Product Details",
  productServiceNameRequired: "Product or service name is required",
  enterProductName: "Enter product name",
  nameArabic: "Name in Arabic",
  productNameArabicPlaceholder: "Enter product name in Arabic",
  productSku: "Product SKU",
  selectType: "Select Type",
  physicalProduct: "Physical Product",
  selectCategory: "Select Category",
  searchCategories: "Search categories",
  noCategoryFound: "No category found",
  addNewCategory: "Add New Category",
  selectSupplier: "Select Supplier",
  searchSuppliers: "Search suppliers",
  noSupplierFound: "No supplier found",
  addNewSupplier: "Add New Supplier",
  description: "Description",
  productDescription: "Product Description",
  
  // Actions
  addProduct: "Add Product",
  editProduct: "Edit Product",
  viewProduct: "View Product",
  deleteProduct: "Delete Product",
  confirmDelete: "Are you sure you want to delete this product?",
  productDeleted: "Product deleted successfully",
  cancel: "Cancel",
  uploadingImage: "Uploading image...",
  creating: "Creating...",
  createProductButton: "Create Product",
  
  // Product image
  productImage: "Product Image",
  noImageSelected: "No image selected",
  clickToUpload: "Click to upload",
  dragAndDrop: "or drag and drop",
  imageFormats: "PNG, JPG, GIF up to 10MB",
  pleaseSelectImageFile: "Please select an image file",
  imageSizeMustBeLess: "Image size must be less than 10MB",
  imageUploadedSuccessfully: "Image uploaded successfully",
  failedToUploadImage: "Failed to upload image",
  
  // Pricing and stock
  pricingStock: "Pricing & Stock",
  sellingPriceRequired: "Selling price is required",
  costPrice: "Cost Price",
  currentStock: "Current Stock",
  minStockAlert: "Min Stock Alert",
  selectUnit: "Select Unit",
  searchUnits: "Search units",
  noUnitFound: "No unit found",
  addNewUnit: "Add New Unit",
  lowStockAlert: "Low Stock Alert",
  
  // Search and filters
  searchProducts: "Search products",
  uploadExcel: "Upload Excel",
  
  // Messages
  productNamePriceRequired: "Product name and price are required",
  productCreatedSuccessfully: "Product created successfully",
  failedToCreateProduct: "Failed to create product",
  errorCreatingProduct: "Error creating product",
  failedToLoadCategoriesSuppliers: "Failed to load categories and suppliers",
  
  // Category management
  categoryNameRequired: "Category name is required",
  categoryCreatedSuccessfully: "Category created successfully",
  failedToCreateCategory: "Failed to create category",
  errorCreatingCategory: "Error creating category",
  addNewCategoryTitle: "Add New Category",
  addNewCategoryDescription: "Create a new category to organize your products",
  enterCategoryName: "Enter category name",
  categoryNameArabic: "Category Name in Arabic",
  categoryNameArabicPlaceholder: "Enter category name in Arabic",
  categoryDescription: "Category Description",
  categoryDescriptionPlaceholder: "Enter category description",
  createCategory: "Create Category",
  
  // Supplier management
  supplierNameMobileRequired: "Supplier name and mobile number are required",
  supplierCreatedSuccessfully: "Supplier created successfully",
  failedToCreateSupplier: "Failed to create supplier",
  errorCreatingSupplier: "Error creating supplier",
  
  // Unit management
  unitNameSymbolRequired: "Unit name and symbol are required",
  unitCreatedSuccessfully: "Unit created successfully",
  failedToCreateUnit: "Failed to create unit",
  errorCreatingUnit: "Error creating unit",

  dashboardTitle: "Product Dashboard",
  dashboardOverview: "Overview of your product catalog and inventory performance",
  viewAllProducts: "View All Products",
  totalProducts: "Total Products",
  activeProducts: "Active Products",
  totalRevenue: "Total Revenue",
  lowStockItems: "Low Stock Items",
  fromLastMonth: "from last month",
  fewerThanLastMonth: "fewer than last month",
  monthlyPerformance: "Monthly Performance",
  productTypeDistribution: "Product Type Distribution",
  physicalProducts: "Physical Products",
  services: "Services",
  digitalProducts: "Digital Products",
  topPerformingProducts: "Top Performing Products",
  product: "Product",
  sold: "Sold",
  revenue: "Revenue",
  actions: "Actions",
  current: "Current",
  minStock: "Min Stock"
}; 