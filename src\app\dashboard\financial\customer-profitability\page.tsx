"use client"

import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>Left, TrendingUp, TrendingDown, Target } from "lucide-react"
import { useI18n } from '@/lib/i18n'
import { useEffect, useState } from 'react'
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  Cell,
} from 'recharts'
import { formatCurrency } from '@/lib/localization'

interface ProfitabilityData {
  customerId: string;
  customerName: string;
  customerNameAr?: string;
  totalRevenue: number;
  totalCost: number;
  netProfit: number;
  profitMargin: number;
}

export default function CustomerProfitability() {
  const router = useRouter()
  const { t, language } = useI18n()
  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<ProfitabilityData[]>([])
  const [mostProfitable, setMostProfitable] = useState<ProfitabilityData | null>(null)
  const [leastProfitable, setLeastProfitable] = useState<ProfitabilityData | null>(null)
  const [avgMargin, setAvgMargin] = useState(0)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        const res = await fetch('/api/financial-reports/customer-profitability')
        
        if (!res.ok) {
          const errorData = await res.json()
          throw new Error(errorData.details || 'Failed to load profitability data')
        }

        const profitabilityData: ProfitabilityData[] = await res.json()
        
        setData(profitabilityData)

        if (profitabilityData.length > 0) {
          setMostProfitable(profitabilityData[0])
          setLeastProfitable(profitabilityData[profitabilityData.length - 1])
          const totalMargin = profitabilityData.reduce((sum, d) => sum + d.profitMargin, 0)
          setAvgMargin(totalMargin / profitabilityData.length)
        }
      } catch (err) {
        console.error("Customer Profitability Error:", err)
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [])

  const getCustomerName = (item: ProfitabilityData) => {
    return language === 'ar' ? (item.customerNameAr || item.customerName) : item.customerName
  }

  const chartData = data.slice(0, 10).map(d => ({ ...d, name: getCustomerName(d) }))

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        {t('finance.loadingReports') || 'جاري تحميل التقرير...'}
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-red-500">
        <h3 className="text-lg font-bold mb-2">Error Loading Report</h3>
        <p>{error}</p>
        <Button onClick={() => fetchData()} className="mt-4">Try Again</Button>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('finance.financial.buttons.back') || 'رجوع'}
        </Button>
        <h2 className="text-3xl font-bold tracking-tight">
          {t('finance.financial.reports.reportTypes.customerprofitability.name') || 'ربحية العملاء'}
        </h2>
      </div>
      <p className="text-muted-foreground">
        {t('finance.financial.reports.reportTypes.customerprofitability.description')}
      </p>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأكثر ربحية</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold text-green-600">{mostProfitable ? getCustomerName(mostProfitable) : 'N/A'}</div>
            <p className="text-xs text-muted-foreground">
              {mostProfitable ? formatCurrency(mostProfitable.netProfit) : ''}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">الأقل ربحية</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold text-red-600">{leastProfitable ? getCustomerName(leastProfitable) : 'N/A'}</div>
            <p className="text-xs text-muted-foreground">
              {leastProfitable ? formatCurrency(leastProfitable.netProfit) : ''}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">متوسط هامش الربح</CardTitle>
            <Target className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{avgMargin.toFixed(2)}%</div>
          </CardContent>
        </Card>
      </div>
      
      {/* Profitability Chart */}
      <Card>
        <CardHeader>
          <CardTitle>أعلى 10 عملاء ربحية</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={350}>
            <BarChart data={chartData}>
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip formatter={(value) => formatCurrency(value as number)} />
              <Bar dataKey="netProfit">
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.netProfit >= 0 ? '#10b981' : '#ef4444'} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Full Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>تفاصيل ربحية العملاء</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>العميل</TableHead>
                <TableHead className="text-right">إجمالي الإيرادات</TableHead>
                <TableHead className="text-right">إجمالي التكاليف</TableHead>
                <TableHead className="text-right">صافي الربح</TableHead>
                <TableHead className="text-right">هامش الربح</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((item) => (
                <TableRow key={item.customerId}>
                  <TableCell>{getCustomerName(item)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(item.totalRevenue)}</TableCell>
                  <TableCell className="text-right text-red-500">{formatCurrency(item.totalCost)}</TableCell>
                  <TableCell className={`text-right font-bold ${item.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>{formatCurrency(item.netProfit)}</TableCell>
                  <TableCell className={`text-right ${item.profitMargin >= 0 ? 'text-green-500' : 'text-red-500'}`}>{item.profitMargin.toFixed(2)}%</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
} 