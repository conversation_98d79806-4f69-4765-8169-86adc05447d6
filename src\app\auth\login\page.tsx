"use client"

import { useState, useEffect } from "react"
import { signIn, useSession } from "next-auth/react"
import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { Loader2, Eye, EyeOff, Mail, Lock, Building2, Globe } from "lucide-react"
import { useCompanySettings } from "@/hooks/use-company-settings"
import { useI18n } from "@/lib/i18n"
import Link from "next/link"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const { logo, companyName, companyNameAr } = useCompanySettings()
  const { data: session, status } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { t, language, setLanguage } = useI18n()

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (status === "authenticated") {
      const callbackUrl = searchParams.get("callbackUrl") || "/dashboard"
      router.push(callbackUrl)
    }
  }, [status, router, searchParams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const callbackUrl = searchParams.get("callbackUrl") || "/dashboard"

      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
        callbackUrl,
      })

      if (result?.error) {
        setError(t('auth.login.invalidCredentials'))
      } else if (result?.ok) {
        // Successful login - redirect to callback URL or dashboard
        router.push(callbackUrl)
      }
    } catch (error) {
      setError(t('auth.login.loginError'))
    } finally {
      setIsLoading(false)
    }
  }

  const toggleLanguage = () => {
    setLanguage(language === 'ar' ? 'en' : 'ar')
  }

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Branding */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div 
            className="absolute inset-0" 
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>
        
        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center items-center text-white p-12 text-center">
          <div className="mb-8">
            {logo ? (
              <img
                src={logo}
                alt="Company Logo"
                className="h-20 w-auto max-w-[250px] object-contain mb-6 filter brightness-0 invert"
              />
            ) : (
              <div className="flex items-center justify-center mb-6">
                <Building2 className="h-16 w-16 mr-4" />
                <h1 className="text-4xl font-bold">
                  {language === 'ar' ? companyNameAr : companyName}
                </h1>
              </div>
            )}
          </div>
          
          <h2 className="text-3xl font-bold mb-4">
            {t('auth.login.welcomeBack')}
          </h2>
          <p className="text-xl text-blue-100 max-w-md leading-relaxed">
            {language === 'ar' 
              ? "نظام إدارة شامل لأعمالك مع دعم كامل للغة العربية"
              : "Comprehensive business management system with full Arabic support"
            }
          </p>
          
          {/* Features */}
          <div className="mt-12 grid grid-cols-1 gap-4 text-left max-w-sm">
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
              <span className="text-blue-100">
                {language === 'ar' ? "إدارة العملاء والموردين" : "Customer & Supplier Management"}
              </span>
            </div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
              <span className="text-blue-100">
                {language === 'ar' ? "نظام الفواتير والمبيعات" : "Invoicing & Sales System"}
              </span>
            </div>
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="w-2 h-2 bg-blue-300 rounded-full"></div>
              <span className="text-blue-100">
                {language === 'ar' ? "تقارير مالية شاملة" : "Comprehensive Financial Reports"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="flex-1 flex items-center justify-center bg-gray-50 p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-md">
          {/* Language Toggle */}
          <div className="flex justify-end mb-6">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleLanguage}
              className="flex items-center space-x-2 rtl:space-x-reverse"
            >
              <Globe className="h-4 w-4" />
              <span>{language === 'ar' ? 'English' : 'العربية'}</span>
            </Button>
          </div>

          <Card className="shadow-xl border-0">
            <CardHeader className="space-y-4 pb-6">
              {/* Mobile Logo */}
              <div className="flex justify-center lg:hidden">
                {logo ? (
                  <img
                    src={logo}
                    alt="Company Logo"
                    className="h-12 w-auto max-w-[200px] object-contain"
                  />
                ) : (
                  <div className="flex items-center">
                    <Building2 className="h-8 w-8 mr-2 text-blue-600" />
                    <h1 className="text-xl font-bold text-blue-600">
                      {language === 'ar' ? companyNameAr : companyName}
                    </h1>
                  </div>
                )}
              </div>

              <div className="space-y-2 text-center">
                <CardTitle className="text-2xl font-bold text-gray-900">
                  {t('auth.login.title')}
                </CardTitle>
                <CardDescription className="text-gray-600">
                  {t('auth.login.subtitle')}
                </CardDescription>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-5">
                {error && (
                  <Alert variant="destructive" className="border-red-200 bg-red-50">
                    <AlertDescription className="text-red-800">{error}</AlertDescription>
                  </Alert>
                )}

                {/* Email Field */}
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                    {t('auth.login.email')}
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      placeholder={t('auth.login.emailPlaceholder')}
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="pl-10 rtl:pl-3 rtl:pr-10 h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      required
                    />
                  </div>
                </div>

                {/* Password Field */}
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                    {t('auth.login.password')}
                  </Label>
                  <div className="relative">
                    <Lock className="absolute left-3 rtl:left-auto rtl:right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder={t('auth.login.passwordPlaceholder')}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="pl-10 pr-10 rtl:pl-10 rtl:pr-10 h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-2 rtl:right-auto rtl:left-2 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Remember Me & Forgot Password */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <Checkbox
                      id="remember"
                      checked={rememberMe}
                      onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                    />
                    <Label htmlFor="remember" className="text-sm text-gray-600 cursor-pointer">
                      {t('auth.login.rememberMe')}
                    </Label>
                  </div>
                  <Link
                    href="/auth/forgot-password"
                    className="text-sm text-blue-600 hover:text-blue-500 font-medium"
                  >
                    {t('auth.login.forgotPassword')}
                  </Link>
                </div>

                {/* Submit Button */}
                <Button 
                  type="submit" 
                  className="w-full h-11 bg-blue-600 hover:bg-blue-700 text-white font-medium"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 rtl:mr-0 rtl:ml-2 h-4 w-4 animate-spin" />
                      {t('auth.login.signingIn')}
                    </>
                  ) : (
                    t('auth.login.signInButton')
                  )}
                </Button>
              </form>

              {/* Sign Up Link */}
              <div className="text-center pt-4 border-t border-gray-200">
                <p className="text-sm text-gray-600">
                  {t('auth.login.noAccount')}{' '}
                  <Link
                    href="/auth/register"
                    className="text-blue-600 hover:text-blue-500 font-medium"
                  >
                    {t('auth.login.createAccount')}
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500">
              {language === 'ar' 
                ? `© ${new Date().getFullYear()} ${companyName}. جميع الحقوق محفوظة.`
                : `© ${new Date().getFullYear()} ${companyName}. All rights reserved.`
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
