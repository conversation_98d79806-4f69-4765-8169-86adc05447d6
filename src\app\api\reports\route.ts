import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '6months'

    // Calculate date range
    const now = new Date()
    const startDate = new Date()
    
    switch (period) {
      case '1month':
        startDate.setMonth(now.getMonth() - 1)
        break
      case '3months':
        startDate.setMonth(now.getMonth() - 3)
        break
      case '6months':
        startDate.setMonth(now.getMonth() - 6)
        break
      case '1year':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setMonth(now.getMonth() - 6)
    }

    // Get sales data from invoices
    const invoices = await prisma.invoice.findMany({
      where: {
        date: {
          gte: startDate,
          lte: now
        }
      },
      include: {
        customer: true,
        items: true,
        user: true
      }
    })

    // Get customers data
    const customers = await prisma.customer.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: now
        }
      }
    })

    // Get products data
    const products = await prisma.product.findMany({
      include: {
        category: true,
        invoiceItems: {
          where: {
            invoice: {
              date: {
                gte: startDate,
                lte: now
              }
            }
          }
        }
      }
    })

    // Get expenses data
    const expenses = await prisma.expense.findMany({
      where: {
        date: {
          gte: startDate,
          lte: now
        }
      },
      include: {
        expenseType: true
      }
    })

    // Calculate monthly sales data
    const salesData = []
    for (let i = 11; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
      
      const monthInvoices = invoices.filter(inv => {
        const invDate = new Date(inv.date)
        return invDate >= monthStart && invDate <= monthEnd
      })
      
      const monthSales = monthInvoices.reduce((sum, inv) => sum + Number(inv.total), 0)
      const monthInvoiceCount = monthInvoices.length
      
      salesData.push({
        month: monthStart.toLocaleDateString('en-US', { month: 'short' }),
        sales: monthSales,
        invoices: monthInvoiceCount
      })
    }

    // Calculate customer growth
    const customerGrowthData = []
    for (let i = 11; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
      
      const monthCustomers = customers.filter(customer => {
        const customerDate = new Date(customer.createdAt)
        return customerDate >= monthStart && customerDate <= monthEnd
      })
      
      customerGrowthData.push({
        month: monthStart.toLocaleDateString('en-US', { month: 'short' }),
        customers: monthCustomers.length
      })
    }

    // Calculate top products
    const productSales = new Map()
    invoices.forEach(invoice => {
      invoice.items.forEach(item => {
        if (item.productId) {
          const current = productSales.get(item.productId) || { quantity: 0, revenue: 0 }
          productSales.set(item.productId, {
            quantity: current.quantity + Number(item.quantity),
            revenue: current.revenue + Number(item.total)
          })
        }
      })
    })

    const topProducts = Array.from(productSales.entries())
      .map(([productId, sales]) => {
        const product = products.find(p => p.id === productId)
        return {
          id: productId,
          name: product?.name || 'Unknown Product',
          category: product?.category?.name || 'Uncategorized',
          quantity: sales.quantity,
          revenue: sales.revenue
        }
      })
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10)

    // Calculate top customers
    const customerSales = new Map()
    invoices.forEach(invoice => {
      if (invoice.customerId) {
        const current = customerSales.get(invoice.customerId) || { orders: 0, revenue: 0 }
        customerSales.set(invoice.customerId, {
          orders: current.orders + 1,
          revenue: current.revenue + Number(invoice.total)
        })
      }
    })

    const topCustomers = Array.from(customerSales.entries())
      .map(([customerId, sales]) => {
        const customer = customers.find(c => c.id === customerId)
        return {
          id: customerId,
          name: customer?.name || 'Unknown Customer',
          orders: sales.orders,
          revenue: sales.revenue
        }
      })
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10)

    // Calculate expense trends
    const expenseData = []
    for (let i = 11; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
      
      const monthExpenses = expenses.filter(exp => {
        const expDate = new Date(exp.date)
        return expDate >= monthStart && expDate <= monthEnd
      })
      
      const monthExpenseTotal = monthExpenses.reduce((sum, exp) => sum + Number(exp.amount), 0)
      
      expenseData.push({
        month: monthStart.toLocaleDateString('en-US', { month: 'short' }),
        expenses: monthExpenseTotal
      })
    }

    // Calculate summary statistics
    const totalSales = invoices.reduce((sum, inv) => sum + Number(inv.total), 0)
    const totalExpenses = expenses.reduce((sum, exp) => sum + Number(exp.amount), 0)
    const totalProfit = totalSales - totalExpenses
    const totalInvoices = invoices.length
    const totalCustomers = customers.length
    const averageOrderValue = totalInvoices > 0 ? totalSales / totalInvoices : 0

    const response = {
      salesData,
      customerGrowthData,
      expenseData,
      topProducts,
      topCustomers,
      summary: {
        totalSales,
        totalExpenses,
        totalProfit,
        totalInvoices,
        totalCustomers,
        averageOrderValue,
        profitMargin: totalSales > 0 ? ((totalProfit / totalSales) * 100) : 0
      },
      period,
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString()
      }
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching reports data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch reports data' },
      { status: 500 }
    )
  }
}
