import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { Decimal } from '@prisma/client/runtime/library'

// Helper function to calculate and update project actual cost
async function updateProjectActualCost(projectId: string) {
  if (!projectId) return

  const expenses = await prisma.expense.findMany({
    where: { 
      projectId,
      status: { in: ['APPROVED', 'PAID'] } // Only count approved or paid expenses
    },
    select: { amount: true }
  })
  
  const total = expenses.reduce((sum, expense) => sum + Number(expense.amount), 0)
  const actualCost = new Decimal(total)
  
  await prisma.project.update({
    where: { id: projectId },
    data: { 
      actualCost,
      updatedAt: new Date()
    }
  })
}

// GET /api/expenses/[id] - Get expense by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const expense = await prisma.expense.findUnique({
      where: { id },
      include: {
        expenseType: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        project: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    })

    if (!expense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(expense)
  } catch (error) {
    console.error('Error fetching expense:', error)
    return NextResponse.json(
      { error: 'Failed to fetch expense' },
      { status: 500 }
    )
  }
}

// PUT /api/expenses/[id] - Update expense
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const {
      description,
      amount,
      expenseTypeId,
      paymentMethod,
      status,
      receipt,
      notes,
      date,
    } = body

    // Check if expense exists
    const existingExpense = await prisma.expense.findUnique({
      where: { id },
    })

    if (!existingExpense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      )
    }

    // Check if expense type exists (if provided)
    if (expenseTypeId) {
      const expenseType = await prisma.expenseType.findUnique({
        where: { id: expenseTypeId },
      })

      if (!expenseType) {
        return NextResponse.json(
          { error: 'Expense type not found' },
          { status: 404 }
        )
      }
    }

    const oldStatus = existingExpense.status
    const oldProjectId = existingExpense.projectId

    // Update expense
    const updatedExpense = await prisma.expense.update({
      where: { id },
      data: {
        description,
        amount: amount ? parseFloat(amount) : undefined,
        expenseTypeId,
        paymentMethod,
        status,
        receipt,
        notes,
        date: date ? new Date(date) : undefined,
        updatedAt: new Date(),
      },
      include: {
        expenseType: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        project: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    })

    // Update project actual cost if status changed to/from APPROVED or PAID
    if (oldProjectId && (
      (status && ['APPROVED', 'PAID'].includes(status) && !['APPROVED', 'PAID'].includes(oldStatus)) ||
      (!['APPROVED', 'PAID'].includes(status) && ['APPROVED', 'PAID'].includes(oldStatus)) ||
      (amount && amount !== existingExpense.amount && ['APPROVED', 'PAID'].includes(status))
    )) {
      await updateProjectActualCost(oldProjectId)
    }

    return NextResponse.json(updatedExpense)
  } catch (error) {
    console.error('Error updating expense:', error)
    return NextResponse.json(
      { error: 'Failed to update expense' },
      { status: 500 }
    )
  }
}

// DELETE /api/expenses/[id] - Delete expense
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if expense exists
    const existingExpense = await prisma.expense.findUnique({
      where: { id },
    })

    if (!existingExpense) {
      return NextResponse.json(
        { error: 'Expense not found' },
        { status: 404 }
      )
    }

    const projectId = existingExpense.projectId

    // Delete expense
    await prisma.expense.delete({
      where: { id },
    })

    // Update project actual cost if the deleted expense was counted
    if (projectId && ['APPROVED', 'PAID'].includes(existingExpense.status)) {
      await updateProjectActualCost(projectId)
    }

    return NextResponse.json({ message: 'Expense deleted successfully' })
  } catch (error) {
    console.error('Error deleting expense:', error)
    return NextResponse.json(
      { error: 'Failed to delete expense' },
      { status: 500 }
    )
  }
}
