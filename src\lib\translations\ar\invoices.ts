export const invoices = {
  // Page titles and descriptions
  title: "الفواتير",
  description: "إدارة وتتبع فواتير العملاء",
  createInvoiceTitle: "إنشاء فاتورة",
  createInvoiceDescription: "أنشئ فاتورة جديدة لعميلك",
  editInvoice: "تعديل الفاتورة",
  viewInvoice: "عرض الفاتورة",
  duplicateInvoice: "نسخ الفاتورة",
  
  // Invoice information
  customerInformation: "معلومات الفاتورة",
  invoiceNumber: "رقم الفاتورة",
  invoiceDate: "تاريخ الفاتورة",
  dueDate: "تاريخ الاستحقاق",
  customer: "العميل",
  selectCustomer: "اختر العميل",
  billTo: "فاتورة إلى",
  shipTo: "شحن إلى",
  
  // Invoice details
  items: "العناصر",
  itemDescription: "الوصف",
  quantity: "الكمية",
  unitPrice: "سعر الوحدة",
  discount: "الخصم",
  amount: "المبلغ",
  addItem: "إضافة عنصر",
  removeItem: "إزالة عنصر",
  
  // Calculations
  subtotal: "المجموع الفرعي",
  tax: "الضريبة",
  taxRate: "معدل الضريبة",
  totalAmount: "المبلغ الإجمالي",
  amountPaid: "المبلغ المدفوع",
  amountDue: "المبلغ المستحق",
  
  // Invoice status
  draft: "مسودة",
  pending: "معلقة",
  sent: "مرسلة",
  paid: "مدفوعة",
  overdue: "متأخرة",
  cancelled: "ملغية",
  pendingPayment: "بانتظار الدفع",
  
  // Actions
  saveDraft: "حفظ كمسودة",
  saveAndSend: "حفظ وإرسال",
  print: "طباعة",
  download: "تحميل",
  send: "إرسال",
  markAsPaid: "تعيين كمدفوعة",
  createFinal: "إنشاء نهائي",
  delete: "حذف",
  viewDetails: "عرض التفاصيل",
  printPdf: "طباعة PDF",
  emailInvoice: "إرسال الفاتورة بالبريد الإلكتروني",
  
  // Payment information
  paymentTerms: "شروط الدفع",
  paymentMethod: "طريقة الدفع",
  paymentDate: "تاريخ الدفع",
  paymentReference: "مرجع الدفع",
  
  // Customer management
  pleaseEnterNameMobile: "يرجى إدخال الاسم ورقم الجوال",
  customerMobileExists: "يوجد عميل بهذا الرقم الجوال مسبقاً",
  customerAddedSuccessfully: "تم إضافة العميل بنجاح",
  failedToCreateCustomer: "فشل في إنشاء العميل",
  newCustomer: "عميل جديد",
  
  // Validation messages
  pleaseAllowPopups: "يرجى السماح بالنوافذ المنبثقة",
  pleaseSelectCustomer: "يرجى اختيار عميل",
  pleaseAddInvoiceItem: "يرجى إضافة عنصر للفاتورة",
  invoiceNumberRequired: "رقم الفاتورة مطلوب",
  dueDateRequired: "تاريخ الاستحقاق مطلوب",
  itemDescriptionRequired: "وصف العنصر مطلوب",
  quantityRequired: "الكمية مطلوبة",
  unitPriceRequired: "سعر الوحدة مطلوب",
  checkItemQuantitiesPrices: "يرجى التحقق من كميات وأسعار العناصر. جميع العناصر يجب أن تحتوي على كميات صحيحة وأسعار غير سالبة.",
  
  // Success/Error messages
  invoiceCreatedSuccessfully: "تم إنشاء الفاتورة بنجاح",
  invoiceUpdatedSuccessfully: "تم تحديث الفاتورة بنجاح",
  invoiceDeletedSuccessfully: "تم حذف الفاتورة بنجاح",
  invoiceSentSuccessfully: "تم إرسال الفاتورة بنجاح",
  paymentRecordedSuccessfully: "تم تسجيل الدفع بنجاح",
  failedToCreateInvoice: "فشل في إنشاء الفاتورة",
  failedToUpdateInvoice: "فشل في تحديث الفاتورة",
  failedToDeleteInvoice: "فشل في حذف الفاتورة",
  failedToSendInvoice: "فشل في إرسال الفاتورة",
  failedToSaveInvoice: "فشل في حفظ الفاتورة",
  invoiceSavedSuccessfully: "تم حفظ الفاتورة بنجاح",
  failedToLoadInvoiceData: "فشل تحميل بيانات الفاتورة.",
  
  // Filters and search
  searchInvoices: "البحث في الفواتير...",
  searchCustomer: "ابحث عن عميل...",
  filterByStatus: "تصفية حسب الحالة",
  filterByCustomer: "تصفية حسب العميل",
  allStatus: "جميع الحالات",
  dateRange: "نطاق التاريخ",
  
  // Statistics
  totalInvoices: "إجمالي الفواتير",
  paidInvoices: "الفواتير المدفوعة",
  pendingInvoices: "الفواتير المعلقة",
  overdueInvoices: "الفواتير المتأخرة",
  totalRevenue: "إجمالي الإيرادات",
  
  // Empty states
  noInvoicesFound: "لم يتم العثور على فواتير",
  noInvoicesFoundSearch: "لم يتم العثور على فواتير تطابق بحثك",
  createFirstInvoice: "أنشئ فاتورتك الأولى",
  
  // Confirmations
  confirmDeleteInvoice: "هل أنت متأكد من حذف هذه الفاتورة؟",
  confirmMarkAsPaid: "هل أنت متأكد من تعيين هذه الفاتورة كمدفوعة؟",
  
  // Notes
  notes: "ملاحظات",
  addNotesPlaceholder: "أضف ملاحظات أو شروط وأحكام...",
  thankYouNote: "شكراً لتعاملكم معنا!",
  terms: "الشروط والأحكام",
  footer: "تذييل",
  
  // Loading states
  loadingInvoices: "جاري تحميل الفواتير...",
  savingInvoice: "جاري حفظ الفاتورة...",
  sendingInvoice: "جاري إرسال الفاتورة...",
  deletingInvoice: "جاري حذف الفاتورة...",
  loadingInvoice: "جاري تحميل الفاتورة...",
  
  // Dashboard metrics
  pendingAmount: "المبلغ المعلق",
  nanPercentOfTotal: "NaN% من الإجمالي",
  acrossAllInvoices: "عبر جميع الفواتير",
  paidUnpaid: "مدفوع، 0 غير مدفوع",
  
  // Table headers and filters
  actions: "الإجراءات",
  balance: "الرصيد",
  source: "المصدر",
  moreFilters: "المزيد من المرشحات",
  export: "تصدير",
  allTime: "كل الأوقات",
  
  // Actions menu
  view: "عرض",
  edit: "تعديل",
  save: "حفظ",
  duplicate: "نسخ",
  recordPayment: "تسجيل دفعة",
  
  // Status messages
  manageYourInvoicesAndTrackPayments: "إدارة فواتيرك وتتبع المدفوعات",
  createInvoice: "إنشاء فاتورة",

  // Create Invoice Page - Additional translations
  // Form labels and placeholders
  dueDateLabel: "تاريخ الاستحقاق",
  linkToTask: "ربط بمهمة (اختياري)",
  selectTask: "اختر مهمة",
  searchTasks: "البحث في المهام...",
  loadingTasks: "جاري تحميل المهام...",
  noTaskFound: "لم يتم العثور على مهمة",
  taskDescriptionAutoAdd: "سيتم إضافة وصف المهمة إلى العنصر الأول تلقائياً",
  invoiceStatus: "حالة الفاتورة",
  selectStatus: "اختر الحالة",
  final: "نهائي",
  convertedFromQuotation: "محول من عرض سعر",
  fromQuotation: "من عرض السعر:",
  convertedFromQuotationDesc: "تم إنشاء هذه الفاتورة من عرض سعر معتمد",
  
  // Customer search and management
  searchCustomersByNameMobile: "البحث في العملاء بالاسم أو الجوال...",
  loadingCustomers: "جاري تحميل العملاء...",
  noCustomerFound: "لم يتم العثور على عميل",
  addNewCustomer: "إضافة عميل جديد",
  addNewCustomerTitle: "إضافة عميل جديد",
  addNewCustomerDesc: "إنشاء سجل عميل جديد. سنتحقق من وجود رقم الجوال مسبقاً",
  customerName: "اسم العميل",
  customerNameRequired: "اسم العميل *",
  enterCustomerName: "أدخل اسم العميل",
  mobileNumber: "رقم الجوال",
  mobileNumberRequired: "رقم الجوال *",
  mobilePlaceholder: "+968 9XXX XXXX",
  email: "البريد الإلكتروني",
  emailOptional: "البريد الإلكتروني (اختياري)",
  customerExists: "العميل موجود:",
  selectFromDropdown: "يمكنك اختيار هذا العميل من القائمة المنسدلة بدلاً من ذلك",
  cancel: "إلغاء",
  addCustomer: "إضافة عميل",
  
  // Invoice items section
  invoiceItems: "عناصر الفاتورة",
  itemHeader: "العنصر",
  image: "الصورة",
  productService: "المنتج/الخدمة",
  unitPriceOMR: "سعر الوحدة (ريال عماني)",
  total: "الإجمالي",
  action: "الإجراء",
  itemDescriptionPlaceholder: "وصف العنصر",
  selectProduct: "اختر منتج",
  searchProducts: "ابحث عن المنتجات",
  loadingProducts: "جاري تحميل المنتجات...",
  noProductFound: "لم يتم العثور على منتج",
  stock: "المخزون",
  inStock: "متوفر",
  outOfStock: "غير متوفر",
  
  // Discount section
  discountType: "نوع الخصم",
  discountAmount: "مبلغ الخصم",
  discountPercentage: "نسبة الخصم",
  percentage: "نسبة مئوية",
  enterPercentage: "أدخل النسبة (0-100%)",
  enterAmountOMR: "أدخل المبلغ بالريال العماني",
  currencyOMR: "ر.ع.",
  
  // Payment settings
  paymentSettings: "إعدادات الدفع",
  preferredPaymentMethod: "طريقة الدفع المفضلة",
  selectPaymentTerms: "اختر شروط الدفع",
  selectPaymentMethod: "اختر طريقة الدفع",
  recordPaymentOnCreation: "تسجيل الدفع عند الإنشاء",
  paymentAmount: "مبلغ الدفع",
  paymentAmountPlaceholder: "مبلغ الدفع",
  autoRecordPaymentDesc: "حدد لتسجيل الدفع تلقائياً عند إنشاء الفاتورة",
  
  // Payment terms
  dueImmediately: "مستحق فوراً",
  net7Days: "صافي 7 أيام",
  net15Days: "صافي 15 يوم",
  net30Days: "صافي 30 يوم",
  net60Days: "صافي 60 يوم",
  net90Days: "صافي 90 يوم",
  
  // Payment methods
  cash: "نقداً",
  bankTransfer: "تحويل بنكي",
  check: "شيك",
  creditCard: "بطاقة ائتمان",
  debitCard: "بطاقة مدى",
  
  // Invoice summary
  invoiceSummary: "ملخص الفاتورة",
  discountLabel: "الخصم",
  taxLabel: "الضريبة",
  totalLabel: "الإجمالي",
  balanceDue: "الرصيد المستحق",
  paymentMethodLabel: "طريقة الدفع",
  invoiceWillBeMarkedAsPaid: "✅ ستتم تعيين الفاتورة كمدفوعة",
  
  // Action buttons
  saveAsDraft: "حفظ كمسودة",
  savePrint: "حفظ وطباعة",
  createFinalInvoice: "إنشاء فاتورة نهائية",
  createPrintInvoice: "إنشاء وطباعة الفاتورة",
  creating: "جاري الإنشاء...",
  
  // Notes section
  notesOptional: "ملاحظات (اختياري)",
  thankYouForBusiness: "شكراً لتعاملكم معنا!",
  additionalNotesPlaceholder: "ملاحظات إضافية للفاتورة...",

  // Print Preview
  invoicePreview: "معاينة الفاتورة",
  invoiceDetailsTitle: "تفاصيل الفاتورة",
  date: "التاريخ:",
  item: "العنصر",
  qty: "الكمية",
  price: "السعر",
  status: "الحالة",
}; 