"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, Calendar } from "lucide-react"
import { getCurrentMuscatTime, isBusinessHours, getBusinessHoursText } from "@/lib/localization"

interface BusinessDay {
  day: string
  dayAr: string
  hours: string
  hoursAr: string
  isToday: boolean
  isOpen: boolean
}

export function BusinessHoursWidget() {
  const [currentTime, setCurrentTime] = useState<Date>(new Date())
  const [isOpen, setIsOpen] = useState<boolean>(false)

  useEffect(() => {
    const updateTime = () => {
      const muscatTime = getCurrentMuscatTime()
      setCurrentTime(muscatTime)
      setIsOpen(isBusinessHours())
    }

    updateTime()
    const interval = setInterval(updateTime, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [])

  const getBusinessDays = (): BusinessDay[] => {
    const today = getCurrentMuscatTime().getDay()
    
    return [
      {
        day: "Sunday",
        dayAr: "الأحد",
        hours: "8:00 AM - 6:00 PM",
        hoursAr: "8:00 ص - 6:00 م",
        isToday: today === 0,
        isOpen: today === 0 && isBusinessHours()
      },
      {
        day: "Monday",
        dayAr: "الاثنين",
        hours: "8:00 AM - 6:00 PM",
        hoursAr: "8:00 ص - 6:00 م",
        isToday: today === 1,
        isOpen: today === 1 && isBusinessHours()
      },
      {
        day: "Tuesday",
        dayAr: "الثلاثاء",
        hours: "8:00 AM - 6:00 PM",
        hoursAr: "8:00 ص - 6:00 م",
        isToday: today === 2,
        isOpen: today === 2 && isBusinessHours()
      },
      {
        day: "Wednesday",
        dayAr: "الأربعاء",
        hours: "8:00 AM - 6:00 PM",
        hoursAr: "8:00 ص - 6:00 م",
        isToday: today === 3,
        isOpen: today === 3 && isBusinessHours()
      },
      {
        day: "Thursday",
        dayAr: "الخميس",
        hours: "8:00 AM - 6:00 PM",
        hoursAr: "8:00 ص - 6:00 م",
        isToday: today === 4,
        isOpen: today === 4 && isBusinessHours()
      },
      {
        day: "Friday",
        dayAr: "الجمعة",
        hours: "2:00 PM - 6:00 PM",
        hoursAr: "2:00 م - 6:00 م",
        isToday: today === 5,
        isOpen: today === 5 && isBusinessHours()
      },
      {
        day: "Saturday",
        dayAr: "السبت",
        hours: "Closed",
        hoursAr: "مغلق",
        isToday: today === 6,
        isOpen: false
      }
    ]
  }

  const businessDays = getBusinessDays()
  const todaySchedule = businessDays.find(day => day.isToday)

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center justify-between">
          <div className="flex items-center">
            <Clock className="w-5 h-5 mr-2 text-blue-600" />
            Business Hours
          </div>
          <Badge variant={isOpen ? "default" : "secondary"} className="text-xs">
            {isOpen ? "Open" : "Closed"}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Status */}
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Current Time</span>
            <Badge variant="outline" className="text-xs">
              <Calendar className="w-3 h-3 mr-1" />
              Muscat
            </Badge>
          </div>
          <div className="text-lg font-semibold text-gray-900">
            {currentTime.toLocaleTimeString('en-OM', { 
              timeZone: 'Asia/Muscat',
              hour12: true,
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
          <div className="text-sm text-gray-600 mt-1">
            {currentTime.toLocaleDateString('en-OM', { 
              timeZone: 'Asia/Muscat',
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </div>
        </div>

        {/* Today's Schedule */}
        {todaySchedule && (
          <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-blue-900">
                  Today ({todaySchedule.day})
                </p>
                <p className="text-sm text-blue-700" dir="rtl">
                  اليوم ({todaySchedule.dayAr})
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium text-blue-900">{todaySchedule.hours}</p>
                <p className="text-sm text-blue-700" dir="rtl">{todaySchedule.hoursAr}</p>
              </div>
            </div>
          </div>
        )}

        {/* Weekly Schedule */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Weekly Schedule</h4>
          {businessDays.map((day, index) => (
            <div 
              key={index} 
              className={`flex items-center justify-between py-2 px-3 rounded ${
                day.isToday ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center space-x-2">
                <span className={`text-sm ${day.isToday ? 'font-medium text-blue-900' : 'text-gray-700'}`}>
                  {day.day}
                </span>
                <span className={`text-xs ${day.isToday ? 'text-blue-700' : 'text-gray-500'}`} dir="rtl">
                  ({day.dayAr})
                </span>
                {day.isToday && (
                  <Badge variant="secondary" className="text-xs">Today</Badge>
                )}
              </div>
              <div className="text-right">
                <span className={`text-sm ${
                  day.hours === 'Closed' ? 'text-red-600' : 
                  day.isToday ? 'font-medium text-blue-900' : 'text-gray-600'
                }`}>
                  {day.hours}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Special Notes */}
        <div className="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
          <p className="text-xs text-yellow-800 mb-1">
            <strong>Note:</strong> Friday hours are after Jummah prayer
          </p>
          <p className="text-xs text-yellow-700" dir="rtl">
            <strong>ملاحظة:</strong> ساعات العمل يوم الجمعة بعد صلاة الجمعة
          </p>
        </div>

        {/* Contact Info */}
        <div className="text-center pt-2 border-t">
          <p className="text-xs text-gray-500">
            For urgent matters outside business hours
          </p>
          <p className="text-xs text-gray-600 font-medium">
            Emergency: +968 99 123 456
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
