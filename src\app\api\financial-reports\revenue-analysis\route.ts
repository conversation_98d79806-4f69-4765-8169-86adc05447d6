import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { subDays, startOfYear } from 'date-fns'
import { Decimal } from '@prisma/client/runtime/library'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'all'

    let whereClause: any = {
      status: { in: ['PAID', 'PARTIAL'] }
    }
    const now = new Date()

    if (period === '30d') {
      whereClause.createdAt = { gte: subDays(now, 30) }
    } else if (period === '90d') {
      whereClause.createdAt = { gte: subDays(now, 90) }
    } else if (period === 'ytd') {
      whereClause.createdAt = { gte: startOfYear(now) }
    }

    const invoices = await prisma.invoice.findMany({
      where: whereClause,
      include: {
        items: { include: { product: true } },
        customer: true,
      },
      orderBy: { createdAt: 'desc' },
    })

    const totalRevenue = invoices.reduce((sum, inv) => sum.add(inv.total), new Decimal(0))
    const totalInvoices = invoices.length
    const avgRevenuePerInvoice = totalInvoices > 0 ? totalRevenue.div(totalInvoices) : new Decimal(0)

    const revenueByProduct: { [key: string]: { name: string; nameAr: string; amount: Decimal } } = {}
    invoices.forEach(inv => {
      inv.items.forEach(item => {
        const productId = item.productId || item.description
        const productName = item.product?.name || item.description
        const productNameAr = item.product?.nameAr || productName
        if (!revenueByProduct[productId]) {
          revenueByProduct[productId] = { name: productName, nameAr: productNameAr, amount: new Decimal(0) }
        }
        revenueByProduct[productId].amount = revenueByProduct[productId].amount.add(item.total)
      })
    })
    const topProducts = Object.values(revenueByProduct)
      .sort((a, b) => b.amount.comparedTo(a.amount))
      .map(p => ({ ...p, amount: p.amount.toNumber() }));

    const revenueByCustomer: { [key: string]: { name: string; nameAr: string; amount: Decimal } } = {}
    invoices.forEach(inv => {
      if (inv.customer) {
        const customerId = inv.customer.id
        if (!revenueByCustomer[customerId]) {
          revenueByCustomer[customerId] = { name: inv.customer.name, nameAr: inv.customer.nameAr || inv.customer.name, amount: new Decimal(0) }
        }
        revenueByCustomer[customerId].amount = revenueByCustomer[customerId].amount.add(inv.total)
      }
    })
    const topCustomers = Object.values(revenueByCustomer)
      .sort((a, b) => b.amount.comparedTo(a.amount))
      .map(c => ({ ...c, amount: c.amount.toNumber() }));

    const monthlyBreakdown: { [key: string]: Decimal } = {}
    invoices.forEach(inv => {
      const month = inv.createdAt.toISOString().slice(0, 7)
      if (!monthlyBreakdown[month]) {
        monthlyBreakdown[month] = new Decimal(0)
      }
      monthlyBreakdown[month] = monthlyBreakdown[month].add(inv.total)
    })
    const revenueByMonth = Object.entries(monthlyBreakdown)
      .map(([month, amount]) => ({ month, amount: amount.toNumber() }))
      .sort((a, b) => a.month.localeCompare(b.month));

    const stats = {
      overview: {
        totalRevenue: totalRevenue.toNumber(),
        totalInvoices,
        avgRevenuePerInvoice: avgRevenuePerInvoice.toNumber(),
        topProduct: topProducts[0] || null,
      },
      revenueByProduct: topProducts,
      revenueByCustomer: topCustomers,
      revenueByMonth,
      recentInvoices: invoices.slice(0, 10).map(inv => ({
        id: inv.id,
        number: inv.number,
        customerName: inv.customer?.name,
        customerNameAr: inv.customer?.nameAr,
        total: inv.total.toNumber(),
        date: inv.createdAt
      })),
      period,
    }

    return NextResponse.json(stats);

  } catch (error: any) {
    console.error('Error fetching revenue analysis:', error);
    return NextResponse.json(
      { error: 'Failed to fetch revenue analysis data', details: error.message },
      { status: 500 }
    );
  }
} 