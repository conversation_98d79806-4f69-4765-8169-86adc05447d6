export const stock = {
  // Page titles and descriptions
  title: "Stock Management",
  description: "Monitor and manage your inventory levels",
  stockReport: "Stock Report",
  
  // Stock status
  outOfStock: "Out of Stock",
  lowStock: "Low Stock", 
  inStock: "In Stock",
  
  // Stock actions
  stockIncreased: "Stock increased",
  stockDecreased: "Stock decreased",
  fillAllRequiredFields: "Please fill in all required fields",
  failedToSaveStockAdjustment: "Failed to save stock adjustment",
  
  // Stock data
  loadingStockData: "Loading stock data...",
  noStockDataAvailable: "No stock data available",
  totalProducts: "Total Products",
  activeInventoryItems: "Active inventory items",
  products: "Products",
  
  // Stock levels
  currentStock: "Current Stock",
  minStock: "Minimum Stock",
  maxStock: "Maximum Stock",
  reorderLevel: "Reorder Level",
  stockValue: "Stock Value",
  averageCost: "Average Cost",
  
  // Stock movements
  stockIn: "Stock In",
  stockOut: "Stock Out",
  stockAdjustment: "Stock Adjustment",
  stockTransfer: "Stock Transfer",
  
  // Stock alerts
  lowStockAlert: "Low Stock Alert",
  outOfStockAlert: "Out of Stock Alert",
  reorderAlert: "Reorder Alert",
  
  // Stock reports
  stockMovementReport: "Stock Movement Report",
  stockValuationReport: "Stock Valuation Report",
  lowStockReport: "Low Stock Report",
  
  // Actions
  adjustStock: "Adjust Stock",
  addStock: "Add Stock",
  removeStock: "Remove Stock",
  updateStock: "Update Stock",
  transferStock: "Transfer Stock",
  
  // Messages
  stockUpdatedSuccessfully: "Stock updated successfully",
  stockAdjustmentSaved: "Stock adjustment saved successfully",
  failedToUpdateStock: "Failed to update stock",
  invalidStockQuantity: "Invalid stock quantity",
  stockCannotBeNegative: "Stock cannot be negative",
  
  // Filters and search
  filterByStatus: "Filter by Status",
  filterByCategory: "Filter by Category",
  searchProducts: "Search products...",
  allStatuses: "All Statuses",
  
  // Inventory tracking
  lastUpdated: "Last Updated",
  updatedBy: "Updated By",
  stockMovements: "Stock Movements",
  movementHistory: "Movement History",
  
  // Warehouse management
  warehouse: "Warehouse",
  location: "Location",
  bin: "Bin",
  shelf: "Shelf",
  zone: "Zone"
}; 