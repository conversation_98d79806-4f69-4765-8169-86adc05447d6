"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Plus,
  Trash2,
  ArrowLeft,
  Save,
  Send,
  FileText,
  Calculator,
  Search,
  Check,
  ChevronsUpDown,
  UserPlus,
  Package,
  Printer,
  Eye,
  Download,
  Edit,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  MoreHorizontal,
  Copy,
  Archive,
  CreditCard
} from "lucide-react"
import { cn } from "@/lib/utils"
import { formatCurrency } from "@/lib/localization"
import { getStorageItem, STORAGE_KEYS } from "@/lib/storage"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

interface InvoiceItem {
  id: string
  description: string
  productId: string
  quantity: number
  unitPrice: number
  total: number
  image?: string
}

interface Product {
  id: string
  name: string
  nameAr?: string
  price: number
  unit: string
  category?: {
    id: string
    name: string
    nameAr?: string
  }
  image?: string
  type: 'PHYSICAL' | 'SERVICE'
  stock?: number
  sku?: string
}

interface Customer {
  id: string
  name: string
  mobile: string
  email?: string
}

export default function EditInvoicePage() {
  const router = useRouter()
  const params = useParams()
  const { id: invoiceId } = params
  const { t } = useI18n()
  
  const [invoiceItems, setInvoiceItems] = useState<InvoiceItem[]>([])
  const [formData, setFormData] = useState({
    customerId: "",
    customerName: "",
    customerMobile: "",
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    taxRate: 5,
    discount: 0,
    discountType: "amount",
    notes: "",
    status: "draft",
  })

  const [totals, setTotals] = useState({
    subtotal: 0,
    discountAmount: 0,
    taxAmount: 0,
    total: 0
  })

  const [customers, setCustomers] = useState<Customer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [customerSearchOpen, setCustomerSearchOpen] = useState(false)
  const [productSearchStates, setProductSearchStates] = useState<{[key: string]: {open: boolean, value: string}}>({})

  useEffect(() => {
    const fetchInvoiceData = async () => {
      try {
        const response = await fetch(`/api/invoices/${invoiceId}`)
        if (!response.ok) {
          throw new Error('Failed to fetch invoice data')
        }
        const data = await response.json()
        
        setFormData({
          customerId: data.customer.id,
          customerName: data.customer.name,
          customerMobile: data.customer.phone,
          dueDate: data.dueDate,
          taxRate: data.taxAmount > 0 ? (data.taxAmount / (data.subtotal - data.discount)) * 100 : 5,
          discount: data.discount,
          discountType: "amount", // Assuming amount, might need to be dynamic
          notes: data.notes,
          status: data.status,
        })
        
        setInvoiceItems(data.items.map((item: any) => ({
          id: item.id,
          description: item.description,
          productId: item.product?.id || "",
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total,
        })))
      } catch (error) {
        console.error(error)
        toast.error(t("failedToLoadInvoiceData"))
        router.push('/dashboard/invoices')
      }
    }

    const fetchDropdownData = async () => {
        const customersResponse = await fetch('/api/customers?limit=1000')
        const productsResponse = await fetch('/api/products?limit=1000')
        const customersData = await customersResponse.json()
        const productsData = await productsResponse.json()
        setCustomers(customersData.customers || [])
        setProducts(productsData.products || [])
    }
    
    Promise.all([fetchInvoiceData(), fetchDropdownData()]).finally(() => setIsLoading(false))
  }, [invoiceId, router, t])

  useEffect(() => {
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total, 0)
    const discountAmount = formData.discountType === "percentage"
      ? (subtotal * formData.discount) / 100
      : formData.discount
    const taxableAmount = subtotal - discountAmount
    const taxAmount = (taxableAmount * formData.taxRate) / 100
    const total = taxableAmount + taxAmount

    setTotals({ subtotal, discountAmount, taxAmount, total })
  }, [invoiceItems, formData.taxRate, formData.discount, formData.discountType])

  const addInvoiceItem = () => {
    setInvoiceItems([...invoiceItems, { id: Date.now().toString(), description: "", productId: "", quantity: 1, unitPrice: 0, total: 0 }])
  }

  const removeInvoiceItem = (id: string) => {
    if (invoiceItems.length > 1) {
      setInvoiceItems(invoiceItems.filter(item => item.id !== id))
    }
  }

  const updateInvoiceItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setInvoiceItems(items => items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value }
        if (field === 'productId' && value) {
          const product = products.find(p => p.id === value)
          if (product) {
            updatedItem.unitPrice = product.price
            updatedItem.description = product.name
            updatedItem.total = Number((updatedItem.quantity * product.price).toFixed(2))
          }
        }
        if (field === 'quantity' || field === 'unitPrice') {
          updatedItem.total = Number((updatedItem.quantity * updatedItem.unitPrice).toFixed(2))
        }
        return updatedItem
      }
      return item
    }))
  }
  
  const handleCustomerSelect = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    if (customer) {
      setFormData(prev => ({ ...prev, customerId: customer.id, customerName: customer.name, customerMobile: customer.mobile }))
    }
  }
  
  const setProductSearchState = (itemId: string, state: {open?: boolean, value?: string}) => {
    setProductSearchStates(prev => ({ ...prev, [itemId]: { ...prev[itemId], ...state } }))
  }

  const getProductSearchState = (itemId: string) => {
    return productSearchStates[itemId] || { open: false, value: "" }
  }

  const handleSave = async () => {
    if (!formData.customerId) {
      toast.error(t('pleaseSelectCustomer'))
      return
    }

    try {
      const invoiceData = {
        ...formData,
        items: invoiceItems.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          productId: item.productId,
        })),
        taxAmount: totals.taxAmount,
        discount: totals.discountAmount
      }

      const response = await fetch(`/api/invoices/${invoiceId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceData),
      })

      if (response.ok) {
        toast.success(t('invoiceUpdatedSuccessfully'))
        router.push('/dashboard/invoices')
      } else {
        const error = await response.json()
        toast.error(`${t('failedToUpdateInvoice')}: ${error.error}`)
      }
    } catch (error) {
      console.error('Error updating invoice:', error)
      toast.error(t('failedToUpdateInvoice'))
    }
  }

  if (isLoading) {
    return <div>{t('loadingInvoice')}</div>
  }

  return (
    <div className="flex flex-col h-full">
      <header className="bg-background border-b p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-xl font-semibold">{t('editInvoice')}</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            {t('save')}
          </Button>
        </div>
      </header>

      <main className="flex-1 overflow-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>{t('customerInformation')}</CardTitle>
              </CardHeader>
              <CardContent>
                  <Label htmlFor="customer">{t('customer')}</Label>
                  <Popover open={customerSearchOpen} onOpenChange={setCustomerSearchOpen}>
                    <PopoverTrigger asChild>
                      <Button variant="outline" role="combobox" className="w-full justify-between">
                        {formData.customerId ? customers.find((c) => c.id === formData.customerId)?.name : t('selectCustomer')}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                      <Command>
                        <CommandInput placeholder={t('searchCustomer')} />
                        <CommandList>
                          <CommandEmpty>{t('noCustomerFound')}</CommandEmpty>
                          <CommandGroup>
                            {customers.map((customer) => (
                              <CommandItem key={customer.id} value={customer.id} onSelect={(value) => { handleCustomerSelect(value); setCustomerSearchOpen(false); }}>
                                <Check className={cn("mr-2 h-4 w-4", formData.customerId === customer.id ? "opacity-100" : "opacity-0")} />
                                {customer.name} ({customer.mobile})
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('invoiceItems')}</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-1/2">{t('itemHeader')}</TableHead>
                      <TableHead>{t('quantity')}</TableHead>
                      <TableHead>{t('unitPrice')}</TableHead>
                      <TableHead>{t('total')}</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoiceItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                           <Popover open={getProductSearchState(item.id).open} onOpenChange={(open) => setProductSearchState(item.id, { open })}>
                            <PopoverTrigger asChild>
                              <Button variant="outline" className="w-full justify-between">
                                {item.productId ? products.find(p => p.id === item.productId)?.name : t('selectProduct')}
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                               <Command>
                                <CommandInput placeholder={t('searchProducts')} />
                                <CommandList>
                                  <CommandEmpty>{t('noProductFound')}</CommandEmpty>
                                  <CommandGroup>
                                    {products.map((product) => (
                                      <CommandItem key={product.id} value={product.id} onSelect={(value) => { updateInvoiceItem(item.id, 'productId', value); setProductSearchState(item.id, { open: false }); }}>
                                        <Check className={cn("mr-2 h-4 w-4", item.productId === product.id ? "opacity-100" : "opacity-0")} />
                                        {product.name}
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                          <Textarea placeholder={t('itemDescription')} value={item.description} onChange={(e) => updateInvoiceItem(item.id, 'description', e.target.value)} className="mt-2" />
                        </TableCell>
                        <TableCell><Input type="number" value={item.quantity} onChange={(e) => updateInvoiceItem(item.id, 'quantity', parseFloat(e.target.value))} /></TableCell>
                        <TableCell><Input type="number" value={item.unitPrice} onChange={(e) => updateInvoiceItem(item.id, 'unitPrice', parseFloat(e.target.value))} /></TableCell>
                        <TableCell>{formatCurrency(item.total)}</TableCell>
                        <TableCell><Button variant="ghost" size="icon" onClick={() => removeInvoiceItem(item.id)}><Trash2 className="h-4 w-4" /></Button></TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <Button onClick={addInvoiceItem} variant="outline" className="mt-4"><Plus className="h-4 w-4 mr-2" /> {t('addItem')}</Button>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader><CardTitle>{t('invoiceDetailsTitle')}</CardTitle></CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="dueDate">{t('dueDate')}</Label>
                  <Input id="dueDate" type="date" value={formData.dueDate} onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))} />
                </div>
                <div>
                  <Label htmlFor="status">{t('status')}</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                    <SelectTrigger><SelectValue placeholder={t('selectStatus')} /></SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">{t('draft')}</SelectItem>
                      <SelectItem value="pending">{t('pendingPayment')}</SelectItem>
                      <SelectItem value="paid">{t('paid')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader><CardTitle>{t('invoiceSummary')}</CardTitle></CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between"><span>{t('subtotal')}</span><span>{formatCurrency(totals.subtotal)}</span></div>
                  <div className="flex justify-between items-center">
                     <span>{t('discount')}</span>
                    <div className="flex gap-2 w-1/2">
                       <Input type="number" value={formData.discount} onChange={(e) => setFormData(prev => ({ ...prev, discount: Number(e.target.value) }))} />
                       <Select value={formData.discountType} onValueChange={(v) => setFormData(prev => ({ ...prev, discountType: v as "amount" | "percentage" }))}>
                         <SelectTrigger><SelectValue/></SelectTrigger>
                         <SelectContent>
                           <SelectItem value="amount">{t('currencyOMR')}</SelectItem>
                           <SelectItem value="percentage">%</SelectItem>
                         </SelectContent>
                       </Select>
                    </div>
                   </div>
                  <div className="flex justify-between"><span>{t('tax')} ({formData.taxRate}%)</span><span>{formatCurrency(totals.taxAmount)}</span></div>
                  <Separator />
                  <div className="flex justify-between font-bold text-lg"><span>{t('total')}</span><span>{formatCurrency(totals.total)}</span></div>
                </div>
              </CardContent>
            </Card>

            <Card>
               <CardHeader><CardTitle>{t('notes')}</CardTitle></CardHeader>
               <CardContent><Textarea value={formData.notes} onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))} placeholder={t('addNotesPlaceholder')} /></CardContent>
             </Card>
          </div>
        </div>
      </main>
    </div>
  )
}