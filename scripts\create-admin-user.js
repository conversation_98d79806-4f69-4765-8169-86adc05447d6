const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createAdminUser() {
  try {
    console.log('🔐 Creating admin user...')

    const hashedPassword = await bcrypt.hash('123456', 10)

    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: hashedPassword,
        name: 'BZ Point Admin',
        role: 'ADMIN',
        phone: '+968 9123 4567',
        isActive: true,
      },
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'BZ Point Admin',
        role: 'ADMIN',
        phone: '+968 9123 4567',
        isActive: true,
        joinDate: new Date(),
        tasksCompleted: 0,
        tasksInProgress: 0,
        completionRate: 0,
      },
    })

    console.log('✅ Admin user created successfully!')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: 123456')
    console.log('👤 User ID:', user.id)
    console.log('🎯 Role:', user.role)

  } catch (error) {
    console.error('❌ Error creating admin user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAdminUser()
