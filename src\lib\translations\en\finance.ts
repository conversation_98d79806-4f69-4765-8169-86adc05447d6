export const finance = {
  // Page titles and descriptions
  title: "Finance",
  description: "Comprehensive financial management and reporting",
  financialDashboard: "Financial Dashboard",
  financialReports: "Financial Reports",
  
  // Main sections
  accounting: "Accounting",
  budgeting: "Budgeting",
  cashFlow: "Cash Flow",
  profitLoss: "Profit & Loss",
  balanceSheet: "Balance Sheet",
  trialBalance: "Trial Balance",
  generalLedger: "General Ledger",
  journalEntries: "Journal Entries",
  
  // Financial metrics
  revenue: "Revenue",
  income: "Income",
  expenses: "Expenses",
  profit: "Profit",
  loss: "Loss",
  netIncome: "Net Income",
  grossProfit: "Gross Profit",
  grossLoss: "Gross Loss",
  operatingProfit: "Operating Profit",
  operatingLoss: "Operating Loss",
  
  // Assets and liabilities
  assets: "Assets",
  liabilities: "Liabilities",
  equity: "Equity",
  currentAssets: "Current Assets",
  fixedAssets: "Fixed Assets",
  currentLiabilities: "Current Liabilities",
  longTermLiabilities: "Long-term Liabilities",
  
  // Account types
  accountTypes: "Account Types",
  cashAccount: "Cash Account",
  bankAccount: "Bank Account",
  creditCardAccount: "Credit Card Account",
  receivablesAccount: "Accounts Receivable",
  payablesAccount: "Accounts Payable",
  inventoryAccount: "Inventory Account",
  
  // Transactions
  transaction: "Transaction",
  transactions: "Transactions",
  transactionDate: "Transaction Date",
  transactionType: "Transaction Type",
  transactionAmount: "Transaction Amount",
  transactionDescription: "Transaction Description",
  reference: "Reference",
  debit: "Debit",
  credit: "Credit",
  
  // Transaction types
  incomeTransaction: "Income",
  expenseTransaction: "Expense",
  transfer: "Transfer",
  adjustment: "Adjustment",
  deposit: "Deposit",
  withdrawal: "Withdrawal",
  payment: "Payment",
  receipt: "Receipt",
  
  // Budgeting
  budget: "Budget",
  budgets: "Budgets",
  budgetPeriod: "Budget Period",
  budgetAmount: "Budget Amount",
  actualAmount: "Actual Amount",
  variance: "Variance",
  budgetVsActual: "Budget vs Actual",
  
  // Cash flow
  cashInflow: "Cash Inflow",
  cashOutflow: "Cash Outflow",
  netCashFlow: "Net Cash Flow",
  cashPosition: "Cash Position",
  cashFlowForecast: "Cash Flow Forecast",
  operatingCashFlow: "Operating Cash Flow",
  investingCashFlow: "Investing Cash Flow",
  financingCashFlow: "Financing Cash Flow",
  
  // Financial ratios
  ratios: "Financial Ratios",
  profitMargin: "Profit Margin",
  currentRatio: "Current Ratio",
  quickRatio: "Quick Ratio",
  debtToEquity: "Debt to Equity",
  returnOnAssets: "Return on Assets",
  returnOnEquity: "Return on Equity",
  
  // Periods
  daily: "Daily",
  weekly: "Weekly",
  monthly: "Monthly",
  quarterly: "Quarterly",
  yearly: "Yearly",
  
  // Date ranges
  thisMonth: "This Month",
  lastMonth: "Last Month",
  thisQuarter: "This Quarter",
  lastQuarter: "Last Quarter",
  thisYear: "This Year",
  lastYear: "Last Year",
  custom: "Custom",
  
  // Actions
  addTransaction: "Add Transaction",
  editTransaction: "Edit Transaction",
  deleteTransaction: "Delete Transaction",
  reconcile: "Reconcile",
  generateReport: "Generate Report",
  exportData: "Export Data",
  importData: "Import Data",
  
  // Bank reconciliation
  bankReconciliation: "Bank Reconciliation",
  reconcileAccount: "Reconcile Account",
  reconciledTransactions: "Reconciled Transactions",
  unreconciledTransactions: "Unreconciled Transactions",
  reconciliationDate: "Reconciliation Date",
  bankStatement: "Bank Statement",
  
  // Financial reports
  incomeStatement: "Income Statement",
  cashFlowStatement: "Cash Flow Statement",
  financialSummary: "Financial Summary",
  expenseReport: "Expense Report",
  revenueReport: "Revenue Report",
  profitabilityReport: "Profitability Report",
  
  // Tax and compliance
  tax: "Tax",
  taxes: "Taxes",
  taxRate: "Tax Rate",
  taxAmount: "Tax Amount",
  taxReport: "Tax Report",
  vatReport: "VAT Report",
  taxPeriod: "Tax Period",
  
  // Currency and formatting
  currency: "Currency",
  amount: "Amount",
  percentage: "Percentage",
  total: "Total",
  subtotal: "Subtotal",
  grandTotal: "Grand Total",
  
  // Status
  active: "Active",
  inactive: "Inactive",
  pending: "Pending",
  completed: "Completed",
  approved: "Approved",
  rejected: "Rejected",
  
  // Search and filters
  searchTransactions: "Search transactions...",
  filterByAccount: "Filter by Account",
  filterByType: "Filter by Type",
  filterByPeriod: "Filter by Period",
  allAccounts: "All Accounts",
  allTypes: "All Types",
  
  // Messages
  transactionCreatedSuccessfully: "Transaction created successfully",
  transactionUpdatedSuccessfully: "Transaction updated successfully",
  transactionDeletedSuccessfully: "Transaction deleted successfully",
  reportGeneratedSuccessfully: "Report generated successfully",
  dataExportedSuccessfully: "Data exported successfully",
  reconciliationCompletedSuccessfully: "Reconciliation completed successfully",
  
  // Error messages
  failedToCreateTransaction: "Failed to create transaction",
  failedToUpdateTransaction: "Failed to update transaction",
  failedToDeleteTransaction: "Failed to delete transaction",
  failedToGenerateReport: "Failed to generate report",
  failedToExportData: "Failed to export data",
  
  // Validation
  accountRequired: "Account is required",
  amountRequired: "Amount is required",
  dateRequired: "Date is required",
  descriptionRequired: "Description is required",
  invalidAmount: "Amount must be a valid number",
  invalidDate: "Please enter a valid date",
  
  // Empty states
  noTransactionsFound: "No transactions found",
  noAccountsConfigured: "No accounts configured",
  noDataAvailable: "No data available for the selected period",
  
  // Loading states
  loadingTransactions: "Loading transactions...",
  loadingReports: "Loading reports...",
  generatingReport: "Generating report...",
  exportingData: "Exporting data...",
  
  // Charts and analytics
  charts: "Charts",
  analytics: "Analytics",
  trends: "Trends",
  comparison: "Comparison",
  breakdown: "Breakdown",
  summary: "Summary",
  
  // Account management
  accounts: "Accounts",
  accountName: "Account Name",
  accountNumber: "Account Number",
  accountBalance: "Account Balance",
  openingBalance: "Opening Balance",
  closingBalance: "Closing Balance",
  
  // Financial planning
  forecasting: "Forecasting",
  projections: "Projections",
  scenarios: "Scenarios",
  planning: "Financial Planning",
  
  // Notifications
  lowCashAlert: "Low Cash Alert",
  budgetExceeded: "Budget Exceeded",
  overduePayments: "Overdue Payments",
  reconciliationDue: "Reconciliation Due",

  // Financial Dashboard & Reports (nested structure)
  financial: {
    title: "Financial Dashboard",
    subtitle: "Comprehensive financial reports, profit & loss analysis, and business insights",
    period: {
      lastMonth: "Last Month",
      last3Months: "Last 3 Months",
      last6Months: "Last 6 Months",
      lastYear: "Last Year",
      ytd: "Year to Date",
      current: "Current Period",
      previous: "Previous Period",
      quarterly: "Quarterly",
      annual: "Annual"
    },
    buttons: {
      reports: "Reports",
      plStatement: "P&L Statement",
      back: "Back",
      generate: "Generate", 
      view: "View",
      download: "Download",
      print: "Print"
    },
    cards: {
      profitMargin: "Profit Margin",
      netProfit: "Net Profit",
      totalExpenses: "Total Expenses",
      totalRevenue: "Total Revenue",
      healthyMargin: "Healthy margin ratio",
      fromLastPeriod: "from last period",
      paid: "Paid",
      approved: "Approved",
      pending: "Pending",
      rejected: "Rejected",
      revenueBreakdown: "Revenue Breakdown",
      expenseBreakdown: "Expense Breakdown",
      cashFlowAnalysis: "Cash Flow Analysis",
      cashInflow: "Cash Inflow",
      cashOutflow: "Cash Outflow",
      netCashFlow: "Net Cash Flow",
      inflow: "Inflow",
      outflow: "Outflow",
      net: "Net",
      keyMetrics: "Key Metrics",
      target: "Target:"
    },
    charts: {
      profitMarginTrend: "Profit Margin Trend",
      revenueVsExpensesTrend: "Revenue vs Expenses Trend",
      cashFlow: "Cash Flow",
      revenueBreakdown: "Revenue Breakdown",
      expenseBreakdown: "Expense Breakdown",
      revenue: "Revenue",
      expenses: "Expenses",
      profit: "Profit", 
      profitMargin: "Profit Margin"
    },
    kpis: {
      revenuePerCustomer: "Revenue per Customer",
      customerAcquisitionCost: "Customer Acquisition Cost",
      averageOrderValue: "Average Order Value",
      monthlyRecurringRevenue: "Monthly Recurring Revenue"
    },
    loading: "Loading financial data...",
    noData: "No financial data available",
    
    // Financial Reports Page
    reports: {
      title: "Financial Reports",
      subtitle: "Generate and access comprehensive financial reports and analysis",
      categories: {
        allReports: "All Reports",
        financialStatements: "Financial Statements",
        performanceReports: "Performance Reports",
        budgetReports: "Budget Reports",
        analysisReports: "Analysis Reports",
        complianceReports: "Compliance Reports"
      },
      stats: {
        availableReports: "Available Reports",
        monthlyReports: "Monthly Reports",
        weeklyReports: "Weekly Reports",
        quarterlyReports: "Quarterly Reports"
      },
      table: {
        reportName: "Report Name",
        category: "Category",
        generated: "Generated",
        frequency: "Frequency",
        status: "Status",
        actions: "Actions",
        available: "Available"
      },
      lastGenerated: "Last Generated:",
      recentReports: "Recent Reports",
      frequencies: {
        monthly: "Monthly",
        weekly: "Weekly",
        quarterly: "Quarterly", 
        annual: "Annual"
      },
      reportTypes: {
        profitloss: {
          name: "Profit & Loss Statement",
          description: "Comprehensive income statement showing revenue, expenses, and net profit"
        },
        balancesheet: {
          name: "Balance Sheet",
          description: "Statement of financial position showing assets, liabilities, and equity"
        },
        cashflow: {
          name: "Cash Flow Statement",
          description: "Analysis of cash inflows and outflows from operations, investing, and financing"
        },
        revenueanalysis: {
          name: "Revenue Analysis",
          description: "Detailed breakdown of revenue by service, customer, and time period"
        },
        expenseanalysis: {
          name: "Expense Analysis",
          description: "Comprehensive analysis of expenses by category and department"
        },
        customerprofitability: {
          name: "Customer Profitability",
          description: "Analysis of profit margins and revenue contribution by customer"
        },
        budgetvariance: {
          name: "Budget vs Actual",
          description: "Comparison of actual performance against budgeted figures"
        },
        financialratios: {
          name: "Financial Ratios",
          description: "Key financial ratios and performance indicators analysis"
        },
        taxsummary: {
          name: "Tax Summary",
          description: "Summary of tax obligations and payments for compliance reporting"
        }
      }
    }
  }
}; 