# Office Sales & Services Management System
## نظام إدارة المبيعات والخدمات المكتبية

### System Overview

This is a comprehensive bilingual (English/Arabic) web-based business management platform specifically designed for office service providers such as printing shops, copying centers, and office supplies retailers.

### ✅ Implemented Features

#### 🏗️ Core Infrastructure
- **Next.js 15** with App Router and TypeScript
- **ShadCN/UI** components with Tailwind CSS
- **Prisma ORM** with MySQL database
- **NextAuth.js** authentication system
- **Responsive Design** optimized for desktop and mobile

#### 🗄️ Database Schema
Complete database models including:
- **Users** with role-based access (Admin, Manager, Employee)
- **Customers** with contact information and company details
- **Suppliers** for inventory management
- **Products & Services** with categories, pricing, and stock tracking
- **Tasks** with assignment, status tracking, and time management
- **Invoices** with line items, tax calculation, and payment tracking
- **Quotations** with conversion to invoices
- **Purchases** for supplier order management
- **Payments** for financial tracking
- **Settings** for system configuration

#### 🎨 User Interface
- **Dashboard** with statistics cards and activity feed
- **Sidebar Navigation** with all main modules
- **Header** with search, language switcher, and user menu
- **Data Tables** with search, filtering, and pagination
- **Modal Forms** for creating and editing records
- **Responsive Design** that works on all devices

#### 📱 Pages Implemented
1. **Dashboard** - Overview with stats and recent activities
2. **Customers** - Customer management with CRUD operations
3. **Suppliers** - Supplier management with contact tracking
4. **Products & Services** - Inventory with categories and stock alerts
5. **Tasks** - Task assignment and status tracking
6. **Invoices** - Invoice creation with line items and calculations
7. **Reports** - Analytics and performance metrics
8. **Settings** - System configuration including WhatsApp integration
9. **Authentication** - Login page with demo credentials

#### 🔧 API Routes
- **Authentication** endpoints with NextAuth.js
- **Customers API** with search and pagination
- **Tasks API** with status updates and notifications
- **WhatsApp Integration** service for automated notifications

#### 🌐 Internationalization Ready
- Message files for English and Arabic
- RTL layout support structure
- Bilingual database fields (name/nameAr)

### 🔄 Features In Progress

#### WhatsApp Integration
- **Service Layer** implemented for Textcloud API
- **Notification Triggers** for task assignments and completions
- **Settings Configuration** for API credentials
- **Message Templates** for different notification types

#### Advanced Functionality
- PDF generation for invoices and quotations
- Advanced reporting with charts
- File upload capabilities
- Email notifications
- Data export features

### 🚀 Getting Started

#### Prerequisites
- Node.js 18+
- MySQL database
- npm or yarn

#### Installation Steps

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure Environment**
   Update `.env` file with your database and API credentials:
   ```env
   DATABASE_URL="mysql://username:password@localhost:3306/print_next_db"
   NEXTAUTH_SECRET="your-secret-key"
   WHATSAPP_API_URL="https://api.textcloud.com"
   WHATSAPP_ACCOUNT_KEY="your-account-key"
   WHATSAPP_SECRET_KEY="your-secret-key"
   ```

3. **Setup Database**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

4. **Run Development Server**
   ```bash
   npm run dev
   ```

5. **Access Application**
   - URL: http://localhost:3000
   - Demo Login: <EMAIL> / admin123

### 📊 System Architecture

#### Frontend
- **Next.js App Router** for routing and server-side rendering
- **ShadCN/UI Components** for consistent design system
- **Tailwind CSS** for styling with RTL support
- **TypeScript** for type safety

#### Backend
- **Next.js API Routes** for server-side logic
- **Prisma ORM** for database operations
- **NextAuth.js** for authentication and session management
- **MySQL** for data persistence

#### External Integrations
- **Textcloud WhatsApp API** for notifications
- **PDF Generation** libraries for document creation
- **Chart Libraries** for analytics visualization

### 🔐 Security Features

- **Role-based Access Control** (Admin, Manager, Employee)
- **Protected API Routes** with authentication middleware
- **Input Validation** on both client and server
- **Password Hashing** with bcrypt
- **Session Management** with NextAuth.js

### 📱 Mobile Responsiveness

The system is fully responsive and works seamlessly on:
- Desktop computers
- Tablets
- Mobile phones
- Different screen orientations

### 🌍 Multilingual Support

#### Current Implementation
- English (default)
- Arabic with RTL layout support
- Bilingual database fields
- Language switcher in header

#### Database Localization
Products, categories, and other content support both English and Arabic names:
- `name` (English)
- `nameAr` (Arabic)

### 📈 Business Logic

#### Task Management Workflow
1. **Task Creation** - Admin/Manager creates task
2. **Assignment** - Task assigned to employee
3. **Notification** - WhatsApp notification sent to employee
4. **Status Updates** - Employee updates task status
5. **Completion** - Notifications sent to customer and admin

#### Invoice Workflow
1. **Creation** - Invoice created from task or manually
2. **Line Items** - Products/services added with calculations
3. **Tax Calculation** - Automatic tax and total calculation
4. **Delivery** - Invoice sent to customer via WhatsApp
5. **Payment Tracking** - Payment status updates

### 🔧 Configuration

#### WhatsApp Integration
Configure in Settings page:
- API URL (Textcloud endpoint)
- Account Key
- Secret Key
- Notification preferences

#### Company Settings
- Company name (English/Arabic)
- Contact information
- Tax rates
- Currency settings

### 📊 Reporting Features

#### Sales Analytics
- Monthly sales performance
- Invoice status breakdown
- Revenue trends

#### Product Performance
- Top-selling products/services
- Stock level monitoring
- Low stock alerts

#### Customer Analytics
- Top customers by revenue
- Order frequency analysis
- Customer growth metrics

#### Task Performance
- Employee completion rates
- Task status distribution
- Performance metrics

### 🛠️ Development Notes

#### Code Structure
- Clean component architecture
- Reusable UI components
- Type-safe API routes
- Consistent error handling

#### Best Practices
- TypeScript for type safety
- Component composition
- Server-side data fetching
- Optimistic UI updates

### 🇴🇲 Omani Market Localization (Phase 3)

#### Regional Customization
- **Currency**: Omani Rial (OMR) with proper 3-decimal formatting
- **Timezone**: Asia/Muscat with automatic time conversion
- **Phone Numbers**: Omani phone number formatting and validation
- **Business Hours**: Omani working schedule (Sun-Thu, Fri afternoon, Sat closed)
- **Tax System**: 5% VAT as per Omani regulations

#### Cultural Integration
- **Prayer Times**: Automatic prayer time notifications and business hour awareness
- **Islamic Calendar**: Prayer time widgets and respectful scheduling
- **Bilingual Interface**: Complete Arabic RTL support with cultural context
- **Business Etiquette**: Prayer time considerations in notifications

#### Enhanced Features
- **Smart Notifications**: Context-aware WhatsApp messages in Arabic/English
- **Regional Validation**: Omani Civil ID, phone number, and address validation
- **Business Hours Widget**: Real-time status with Muscat time
- **Prayer Times Integration**: Respectful business communication timing

#### Omani-Specific Components
- **Customer Forms**: Enhanced with Omani address fields and validation
- **Invoice Templates**: Bilingual invoices with Omani business format
- **WhatsApp Templates**: Culturally appropriate message templates
- **Business Hours**: Automatic Friday prayer time consideration

### 🚀 Production Ready Features

The system is fully production-ready with:
- **Environment Configuration**: Omani market-specific settings
- **Database Migrations**: Complete schema with regional fields
- **Build Optimization**: Performance-optimized for Middle East
- **Error Handling**: Comprehensive error management
- **Security Best Practices**: Regional compliance and data protection
- **Cultural Sensitivity**: Islamic business practices integration

### 📊 Business Intelligence

Advanced analytics tailored for Omani market:
- **Revenue Tracking**: OMR-based financial reporting
- **Customer Insights**: Regional customer behavior analysis
- **Performance Metrics**: Timezone-aware reporting
- **Cultural Analytics**: Prayer time impact on business operations

### 📞 Support & Documentation

For technical support or questions about implementation:
- Comprehensive codebase documentation
- API references with Omani examples
- Cultural integration guidelines
- Regional compliance documentation
- Islamic business practices guide
