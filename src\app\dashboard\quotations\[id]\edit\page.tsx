"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  ArrowLeft,
  Plus,
  Trash2,
  Check,
  ChevronsUpDown,
  UserPlus,
  Save,
  Send,
  FileText,
  Calculator,
  Package,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { getQuotationById, updateQuotation } from "@/lib/quotation-storage"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

interface Customer {
  id: string
  name: string
  mobile: string
  email?: string
}

interface Product {
  id: string
  name: string
  price: number
  unit: string
  category: { name: string } | string;
}

interface QuotationItem {
  id: string
  description: string
  productId: string | null
  quantity: number
  unitPrice: number
  total: number
}

export default function EditQuotationPage() {
  const router = useRouter()
  const params = useParams()
  const { t, formatCurrency } = useI18n()
  
  const id = params.id as string

  const [products, setProducts] = useState<Product[]>([])
  const [quotationItems, setQuotationItems] = useState<QuotationItem[]>([])

  const [formData, setFormData] = useState({
    customerId: "",
    customerName: "",
    customerMobile: "",
    validUntil: '',
    taxRate: 5,
    discount: 0,
    discountType: "amount",
    notes: '',
    status: "draft"
  })

  // Settings for VAT
  const [vatSettings, setVatSettings] = useState({
    enabled: true,
    rate: 5,
    label: "VAT"
  })

  const [totals, setTotals] = useState({
    subtotal: 0,
    discountAmount: 0,
    taxAmount: 0,
    total: 0
  })

  const [customers, setCustomers] = useState<Customer[]>([])
  const [customerSearchOpen, setCustomerSearchOpen] = useState(false)
  const [customerSearchValue, setCustomerSearchValue] = useState("")
  const [loading, setLoading] = useState(true)

  // Load customers, products, and existing quotation data
  useEffect(() => {
    const loadAllData = async () => {
      setLoading(true)
      try {
        const [customersRes, productsRes, quotationData] = await Promise.all([
          fetch('/api/customers'),
          fetch('/api/products'),
          getQuotationById(id)
        ])

        if (customersRes.ok) {
          const data = await customersRes.json()
          setCustomers(Array.isArray(data) ? data : data.customers || [])
        }
        if (productsRes.ok) {
          const data = await productsRes.json()
          setProducts(data.products || [])
        }
        if (quotationData) {
          setFormData({
            customerId: quotationData.customerId,
            customerName: quotationData.customer.name,
            customerMobile: quotationData.customer.mobile,
            validUntil: quotationData.validUntil ? new Date(quotationData.validUntil).toISOString().split('T')[0] : '',
            taxRate: Number(quotationData.taxAmount) > 0 ? (Number(quotationData.subtotal) - Number(quotationData.discount) > 0 ? (Number(quotationData.taxAmount) / (Number(quotationData.subtotal) - Number(quotationData.discount))) * 100 : 0) : 0,
            discount: Number(quotationData.discount),
            discountType: "amount", // Assuming amount for simplicity, could be enhanced
            notes: quotationData.notes || '',
            status: quotationData.status,
          })
          setQuotationItems(quotationData.items.map((item: any) => ({
            id: item.id,
            description: item.description,
            productId: item.productId,
            quantity: Number(item.quantity),
            unitPrice: Number(item.unitPrice),
            total: Number(item.total),
          })))
          setCustomerSearchValue(`${quotationData.customer.name} (${quotationData.customer.mobile})`)
        } else {
          toast.error(t('quotations.edit.alerts.notFound'))
          router.push('/dashboard/quotations')
        }
      } catch (error) {
        console.error('Error loading data:', error)
        toast.error(t('quotations.edit.alerts.loadFailed'))
      } finally {
        setLoading(false)
      }
    }
    
    if (id) {
      loadAllData()
    }
  }, [id, router, t])
  
  // Product search states
  const [productSearchStates, setProductSearchStates] = useState<{[key: string]: {open: boolean, value: string}}>({})

  // Calculate totals whenever items or rates change
  useEffect(() => {
    const subtotal = quotationItems.reduce((sum, item) => sum + item.total, 0)
    const discountAmount = formData.discountType === "percentage"
      ? (subtotal * formData.discount) / 100
      : formData.discount
    const taxableAmount = subtotal - discountAmount
    const taxAmount = vatSettings.enabled ? (taxableAmount * formData.taxRate) / 100 : 0
    const total = taxableAmount + taxAmount

    setTotals({
      subtotal,
      discountAmount,
      taxAmount,
      total
    })
  }, [quotationItems, formData.taxRate, formData.discount, formData.discountType, vatSettings.enabled])

  const updateQuotationItem = (id: string, field: keyof QuotationItem, value: any) => {
    setQuotationItems(items => items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value }

        if (field === 'productId' && value) {
          const product = products.find(p => p.id === value)
          if (product) {
            updatedItem.unitPrice = product.price
            updatedItem.description = product.name
            updatedItem.total = Number((updatedItem.quantity * product.price).toFixed(2))
          }
        }

        if (field === 'quantity' || field === 'unitPrice') {
          updatedItem.total = Number((updatedItem.quantity * updatedItem.unitPrice).toFixed(2))
        }

        return updatedItem
      }
      return item
    }))
  }

  const addQuotationItem = () => {
    const newItem: QuotationItem = {
      id: Date.now().toString(),
      description: "",
      productId: "",
      quantity: 1,
      unitPrice: 0,
      total: 0
    }
    setQuotationItems(prev => [...prev, newItem])
  }

  const removeQuotationItem = (id: string) => {
    if (quotationItems.length > 1) {
      setQuotationItems(prev => prev.filter(item => item.id !== id))
    }
  }

  const setProductSearchState = (itemId: string, state: {open?: boolean, value?: string}) => {
    setProductSearchStates(prev => ({
      ...prev,
      [itemId]: { ...prev[itemId], ...state }
    }))
  }

  const getProductSearchState = (itemId: string) => {
    return productSearchStates[itemId] || { open: false, value: "" }
  }

  const handleCustomerSelect = (customerId: string) => {
    const customer = customers.find(c => c.id === customerId)
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customerId: customer.id,
        customerName: customer.name,
        customerMobile: customer.mobile
      }))
      setCustomerSearchValue(`${customer.name} (${customer.mobile})`)
    }
  }

  const handleSave = async () => {
    if (!formData.customerId) {
      toast.error(t('quotations.create.alerts.selectCustomer'))
      return
    }

    const quotationData = {
      customerId: formData.customerId,
      validUntil: formData.validUntil,
      status: formData.status,
      subtotal: totals.subtotal,
      taxAmount: totals.taxAmount,
      discount: totals.discountAmount,
      total: totals.total,
      items: quotationItems.map(item => ({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        total: item.total,
        productId: item.productId,
      })),
      notes: formData.notes,
    };

    const updatedQuotation = await updateQuotation(id, quotationData);

    if (updatedQuotation) {
      toast.success(t('quotations.edit.alerts.updateSuccess').replace('{number}', updatedQuotation.number));
      router.push('/dashboard/quotations');
    } else {
      toast.error(t('quotations.edit.alerts.updateFailed'));
    }
  };

  if (loading) {
    return (
        <div className="flex justify-center items-center h-screen">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
        </div>
    );
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/dashboard/quotations')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('quotations.create.backButton')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('quotations.edit.title')}</h2>
            <p className="text-muted-foreground">
              {t('quotations.edit.description')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button onClick={handleSave}>
            <Save className="mr-2 h-4 w-4" />
            {t('quotations.edit.saveButton')}
          </Button>
        </div>
      </div>

      <div className="max-w-6xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              {t('quotations.create.infoSection.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="customer">{t('quotations.create.customerSection.label')}</Label>
                <div className="flex gap-2">
                  <Popover open={customerSearchOpen} onOpenChange={setCustomerSearchOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={customerSearchOpen}
                        className="flex-1 justify-between min-w-0"
                      >
                        <span className="truncate">{customerSearchValue || t('quotations.create.customerSection.placeholder')}</span>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[400px] p-0">
                      <Command>
                        <CommandInput placeholder={t('quotations.create.customerSection.searchPlaceholder')} />
                        <CommandList>
                          <CommandEmpty>{t('quotations.create.customerSection.notFound')}</CommandEmpty>
                          <CommandGroup>
                            {customers.map((customer) => (
                              <CommandItem
                                key={customer.id}
                                value={`${customer.name} ${customer.mobile} ${customer.email || ''}`}
                                onSelect={() => {
                                  handleCustomerSelect(customer.id)
                                  setCustomerSearchOpen(false)
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    formData.customerId === customer.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                <div className="flex flex-col">
                                  <span className="font-medium">{customer.name}</span>
                                  <span className="text-sm text-muted-foreground">{customer.mobile}</span>
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="validUntil">{t('quotations.create.infoSection.validUntil')}</Label>
                <Input
                  id="validUntil"
                  type="date"
                  value={formData.validUntil}
                  onChange={(e) => setFormData({...formData, validUntil: e.target.value})}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="status">{t('quotations.create.infoSection.status')}</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData({...formData, status: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PENDING">{t('quotations.statusLabels.PENDING')}</SelectItem>
                    <SelectItem value="APPROVED">{t('quotations.statusLabels.APPROVED')}</SelectItem>
                    <SelectItem value="REJECTED">{t('quotations.statusLabels.REJECTED')}</SelectItem>
                    <SelectItem value="EXPIRED">{t('quotations.statusLabels.EXPIRED')}</SelectItem>
                    <SelectItem value="CONVERTED">{t('quotations.statusLabels.CONVERTED')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">{t('quotations.create.infoSection.notes')}</Label>
                <Textarea
                  id="notes"
                  placeholder={t('quotations.create.infoSection.notesPlaceholder')}
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  className="min-h-[60px]"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                {t('quotations.create.itemsSection.title')}
              </CardTitle>
              <Button onClick={addQuotationItem} size="sm">
                <Plus className="mr-2 h-4 w-4" />
                {t('quotations.create.itemsSection.addItem')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <Table className="min-w-full">
                  <TableHeader>
                    <TableRow className="border-b">
                      <TableHead className="min-w-[300px]">{t('quotations.create.itemsSection.headers.description')}</TableHead>
                      <TableHead className="min-w-[200px]">{t('quotations.create.itemsSection.headers.product')}</TableHead>
                      <TableHead className="w-[120px] text-center">{t('quotations.create.itemsSection.headers.quantity')}</TableHead>
                      <TableHead className="w-[140px] text-right">{t('quotations.create.itemsSection.headers.unitPrice')}</TableHead>
                      <TableHead className="w-[140px] text-right">{t('quotations.create.itemsSection.headers.total')}</TableHead>
                      <TableHead className="w-[100px] text-center">{t('quotations.create.itemsSection.headers.action')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {quotationItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Textarea
                            placeholder={t('quotations.create.itemsSection.descriptionPlaceholder')}
                            value={item.description}
                            onChange={(e) => updateQuotationItem(item.id, 'description', e.target.value)}
                            className="min-h-[60px] resize-none"
                          />
                        </TableCell>
                        <TableCell>
                          <Popover
                            open={getProductSearchState(item.id).open}
                            onOpenChange={(open) => setProductSearchState(item.id, { open })}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                role="combobox"
                                className="w-full justify-between"
                              >
                                {item.productId ?
                                  products.find(p => p.id === item.productId)?.name || t('quotations.create.itemsSection.productPlaceholder')
                                  : t('quotations.create.itemsSection.productPlaceholder')
                                }
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[300px] p-0">
                              <Command>
                                <CommandInput placeholder={t('quotations.create.itemsSection.productSearchPlaceholder')} />
                                <CommandList>
                                  <CommandEmpty>{t('quotations.create.itemsSection.productNotFound')}</CommandEmpty>
                                  <CommandGroup heading={t('quotations.create.itemsSection.productsGroup')}>
                                    {products.map((product) => (
                                      <CommandItem
                                        key={product.id}
                                        value={`${product.name} ${typeof product.category === 'object' ? product.category.name : product.category} ${product.unit}`}
                                        keywords={[product.name, typeof product.category === 'object' ? product.category.name : product.category, product.unit]}
                                        onSelect={() => {
                                          updateQuotationItem(item.id, 'productId', product.id)
                                          setProductSearchState(item.id, { open: false })
                                        }}
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            item.productId === product.id ? "opacity-100" : "opacity-0"
                                          )}
                                        />
                                        <div className="flex flex-col">
                                          <span className="font-medium">{product.name}</span>
                                          <span className="text-sm text-muted-foreground">
                                            {formatCurrency(Number(product.price))} {product.unit} • {typeof product.category === 'object' ? product.category.name : product.category}
                                          </span>
                                        </div>
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                        </TableCell>
                        <TableCell className="text-center">
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.quantity}
                            onChange={(e) => updateQuotationItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                            className="w-full text-center"
                          />
                        </TableCell>
                        <TableCell className="text-right">
                          <Input
                            type="number"
                            min="0"
                            step="0.001"
                            value={item.unitPrice}
                            onChange={(e) => updateQuotationItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                            className="w-full text-right"
                          />
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="font-medium text-lg">
                            {formatCurrency(Number(item.total))}
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeQuotationItem(item.id)}
                            disabled={quotationItems.length === 1}
                            className="mx-auto"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="mr-2 h-5 w-5" />
              {t('quotations.create.summarySection.title')}
            </CardTitle>
          </CardHeader>
          <CardContent>
             <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-4">
                     <div className="flex justify-between text-lg">
                        <span>{t('quotations.create.summarySection.subtotal')}</span>
                        <span className="font-medium">{formatCurrency(totals.subtotal)}</span>
                    </div>
                     <div className="flex justify-between text-red-600">
                        <span>{t('quotations.create.summarySection.discount')}</span>
                        <span className="font-medium">-{formatCurrency(totals.discountAmount)}</span>
                    </div>
                     <div className="flex justify-between text-lg">
                        <span>{t('quotations.create.summarySection.tax').replace('{label}', vatSettings.label).replace('{rate}', String(formData.taxRate))}</span>
                        <span className="font-medium">{formatCurrency(totals.taxAmount)}</span>
                    </div>
                    <Separator />
                     <div className="flex justify-between text-2xl font-bold text-primary">
                        <span>{t('quotations.create.summarySection.total')}</span>
                        <span>{formatCurrency(totals.total)}</span>
                    </div>
                </div>
                <div className="space-y-2">
                    <Label htmlFor="discount">{t('quotations.create.settingsSection.discount')}</Label>
                    <div className="flex gap-2">
                        <Select
                            value={formData.discountType}
                            onValueChange={(value: "amount" | "percentage") => setFormData({...formData, discountType: value})}
                        >
                            <SelectTrigger className="w-32">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="amount">{t('quotations.create.settingsSection.amount')}</SelectItem>
                                <SelectItem value="percentage">{t('quotations.create.settingsSection.percentage')}</SelectItem>
                            </SelectContent>
                        </Select>
                        <Input
                            id="discount"
                            type="number"
                            min="0"
                            step="0.01"
                            value={formData.discount}
                            onChange={(e) => setFormData({...formData, discount: parseFloat(e.target.value) || 0})}
                        />
                    </div>
                </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 