"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Edit, Trash2, Package, ArrowLeft } from "lucide-react"
import { useI18n } from "@/lib/i18n"

interface Category {
  id: string
  name: string
  nameAr?: string
  description?: string
  descriptionAr?: string
  isActive: boolean
  _count?: {
    products: number
  }
}

interface CategoryFormData {
  name: string
  nameAr: string
  description: string
  descriptionAr: string
}



export default function CategoriesPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    nameAr: '',
    description: '',
    descriptionAr: ''
  })

  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/categories')
      if (response.ok) {
        const data = await response.json()
        setCategories(data.categories || [])
      } else {
        console.error('Failed to fetch categories')
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (category.nameAr && category.nameAr.includes(searchTerm)) ||
    (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const resetForm = () => {
    setFormData({
      name: '',
      nameAr: '',
      description: '',
      descriptionAr: ''
    })
  }

  const populateForm = (category: Category) => {
    setFormData({
      name: category.name || '',
      nameAr: category.nameAr || '',
      description: category.description || '',
      descriptionAr: category.descriptionAr || ''
    })
  }

  const handleAddCategory = () => {
    setEditingCategory(null)
    resetForm()
    setIsDialogOpen(true)
  }

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category)
    populateForm(category)
    setIsDialogOpen(true)
  }

  const handleDeleteCategory = async (category: Category) => {
    if (confirm(t('categories.confirmDelete'))) {
      try {
        const response = await fetch(`/api/categories/${category.id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          alert(`✅ ${t('categories.categoryDeleted')}: ${category.name}`)
          await fetchCategories() // Refresh the list
        } else {
          const error = await response.json()
          alert(`Error: ${error.error || 'Failed to delete category'}`)
        }
      } catch (error) {
        console.error('Error deleting category:', error)
        alert('Error deleting category')
      }
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSaveCategory = async () => {
    if (!formData.name) {
      alert(t('forms.required'))
      return
    }

    try {
      const method = editingCategory ? 'PUT' : 'POST'
      const url = editingCategory ? `/api/categories/${editingCategory.id}` : '/api/categories'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          nameAr: formData.nameAr,
          description: formData.description,
        }),
      })

      if (response.ok) {
        const category = await response.json()
        alert(`✅ ${editingCategory ? t('categories.categoryUpdated') : t('categories.categoryAdded')}: ${category.name}`)
        await fetchCategories() // Refresh the list
        handleCloseDialog()
      } else {
        const error = await response.json()
        alert(`Error: ${error.error || 'Failed to save category'}`)
      }
    } catch (error) {
      console.error('Error saving category:', error)
      alert('Error saving category')
    }
  }

  const handleCloseDialog = () => {
    setIsDialogOpen(false)
    setEditingCategory(null)
    resetForm()
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('categories.title')}</h2>
          <p className="text-muted-foreground">
            {t('nav.categories')} - إدارة فئات المنتجات والخدمات
          </p>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <Button onClick={handleAddCategory}>
          <Plus className="mr-2 h-4 w-4" />
          {t('categories.addCategory')}
        </Button>
        <Dialog open={isDialogOpen} onOpenChange={handleCloseDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{editingCategory ? t('categories.editCategory') : t('categories.addCategory')}</DialogTitle>
              <DialogDescription>
                {editingCategory ? 'تحديث تفاصيل الفئة أدناه.' : 'إنشاء فئة جديدة لتنظيم منتجاتك وخدماتك.'}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-6 py-4">
              {/* Category Names */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('categories.categoryName')} *</Label>
                  <Input
                    id="name"
                    placeholder={t('categories.categoryName')}
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nameAr">{t('categories.categoryNameAr')}</Label>
                  <Input
                    id="nameAr"
                    placeholder="اسم الفئة"
                    dir="rtl"
                    value={formData.nameAr}
                    onChange={(e) => handleInputChange('nameAr', e.target.value)}
                  />
                </div>
              </div>

              {/* Full Width Description */}
              <div className="space-y-2">
                <Label htmlFor="description">{t('categories.categoryDescription')}</Label>
                <Textarea
                  id="description"
                  placeholder={t('categories.categoryDescription')}
                  rows={2}
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="descriptionAr">وصف الفئة (عربي)</Label>
                <Textarea
                  id="descriptionAr"
                  placeholder="وصف الفئة"
                  dir="rtl"
                  rows={2}
                  value={formData.descriptionAr}
                  onChange={(e) => handleInputChange('descriptionAr', e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={handleCloseDialog}>
                {t('common.cancel')}
              </Button>
              <Button type="submit" onClick={handleSaveCategory}>
                {editingCategory ? t('categories.editCategory') : t('categories.addCategory')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={t('categories.searchCategories')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
      </div>

      <div className="table-container">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('categories.categoryName')}</TableHead>
              <TableHead>{t('categories.categoryDescription')}</TableHead>
              <TableHead>{t('categories.productsCount')}</TableHead>
              <TableHead>{t('common.status')}</TableHead>
              <TableHead className="text-right">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCategories.map((category) => (
              <TableRow key={category.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Package className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <button className="font-medium text-blue-600 hover:text-blue-800 text-left">
                        {category.name}
                      </button>
                      <div className="text-sm text-muted-foreground" dir="rtl">
                        {category.nameAr}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="text-sm">{category.description}</div>
                    <div className="text-xs text-muted-foreground mt-1" dir="rtl">
                      {category.descriptionAr}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {category._count?.products || 0} products
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={category.isActive ? "default" : "secondary"}>
                    {category.isActive ? t('common.active') : t('common.inactive')}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditCategory(category)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteCategory(category)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
