"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  ArrowLeft,
  Download,
  Printer,
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calculator,
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

// Real P&L data for Omani printing company (January 2024)
const profitLossData = {
  period: "يناير 2024",
  revenue: {
    printingServices: 285600,
    designServices: 98400,
    officeSupplies: 67200,
    consulting: 34400,
    total: 485600,
  },
  costOfGoodsSold: {
    materials: 145600,
    directLabor: 42800,
    manufacturingOverhead: 18200,
    total: 206600,
  },
  grossProfit: 279000,
  operatingExpenses: {
    salariesAndBenefits: 89200,
    rentAndUtilities: 42800,
    equipmentMaintenance: 28400,
    marketingAdvertising: 18800,
    insurance: 12600,
    professionalServices: 8400,
    officeExpenses: 6200,
    depreciation: 11800,
    total: 218200,
  },
  operatingIncome: 60800,
  otherIncomeExpenses: {
    interestIncome: 2400,
    interestExpense: -1800,
    otherIncome: 1200,
    total: 1800,
  },
  netIncomeBeforeTax: 62600,
  incomeTax: 12520,
  netIncome: 50080,
  margins: {
    grossMargin: 57.458,
    operatingMargin: 12.525,
    netMargin: 10.312,
  },
}

const comparisonData = {
  previousPeriod: "ديسمبر 2023",
  revenue: {
    current: 485600,
    previous: 448200,
    change: 8.348,
  },
  grossProfit: {
    current: 279000,
    previous: 251800,
    change: 10.819,
  },
  operatingIncome: {
    current: 60800,
    previous: 52400,
    change: 16.031,
  },
  netIncome: {
    current: 50080,
    previous: 41200,
    change: 21.553,
  },
}

// Helper function to format percentages with max 3 decimal places
const formatPercentage = (value: number) => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(3)}%`
}

export default function ProfitLossStatement() {
  const router = useRouter()
  const { t } = useI18n()
  const [selectedPeriod, setSelectedPeriod] = useState("current")
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handlePrint = () => {
    window.print()
  }

  const handleDownload = () => {
    // In a real app, this would generate and download a PDF
    alert('وظيفة التنزيل ستكون متاحة قريباً')
  }

  const getChangeIcon = (change: number) => {
    return change >= 0 ? TrendingUp : TrendingDown
  }

  const getChangeColor = (change: number) => {
    return change >= 0 ? 'text-green-500' : 'text-red-500'
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back') || 'رجوع'}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('finance.financial.profitLoss.title') || 'قائمة الأرباح والخسائر'}</h2>
            <p className="text-muted-foreground">
              {t('finance.financial.profitLoss.comprehensiveIncome') || 'بيان الدخل الشامل لـ'} {profitLossData.period}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current">{t('finance.financial.period.current') || 'الفترة الحالية'}</SelectItem>
              <SelectItem value="previous">{t('finance.financial.period.previous') || 'الفترة السابقة'}</SelectItem>
              <SelectItem value="ytd">{t('finance.financial.period.ytd') || 'من بداية العام'}</SelectItem>
              <SelectItem value="quarterly">{t('finance.financial.period.quarterly') || 'ربع سنوي'}</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.print') || 'طباعة'}
          </Button>
          <Button onClick={handleDownload}>
            <Download className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.download') || 'تحميل PDF'}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="bg-slate-50/50 dark:bg-slate-900/20 rounded-lg p-6 border border-slate-200/60 dark:border-slate-700/60">
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('finance.financial.cards.totalRevenue') || 'إجمالي الإيرادات'}</CardTitle>
              <DollarSign className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{formatCurrency(profitLossData.revenue.total)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                {formatPercentage(comparisonData.revenue.change)} {t('finance.financial.profitLoss.comparison.vsLastPeriod') || 'مقابل الفترة السابقة'}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('finance.financial.profitLoss.grossProfit') || 'إجمالي الربح'}</CardTitle>
              <Calculator className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{formatCurrency(profitLossData.grossProfit)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                {formatPercentage(comparisonData.grossProfit.change)} {t('finance.financial.profitLoss.comparison.vsLastPeriod') || 'مقابل الفترة السابقة'}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('finance.financial.profitLoss.operatingIncome') || 'الدخل التشغيلي'}</CardTitle>
              <TrendingUp className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">{formatCurrency(profitLossData.operatingIncome)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                {formatPercentage(comparisonData.operatingIncome.change)} {t('finance.financial.profitLoss.comparison.vsLastPeriod') || 'مقابل الفترة السابقة'}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('finance.financial.profitLoss.netIncome') || 'صافي الدخل'}</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{formatCurrency(profitLossData.netIncome)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
                {formatPercentage(comparisonData.netIncome.change)} {t('finance.financial.profitLoss.comparison.vsLastPeriod') || 'مقابل الفترة السابقة'}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Financial Statement */}
      <Card className="bg-white dark:bg-slate-800 shadow-sm">
        <CardHeader>
          <CardTitle className="text-xl font-bold">{t('finance.financial.profitLoss.title') || 'قائمة الأرباح والخسائر'}</CardTitle>
          <p className="text-muted-foreground">{profitLossData.period}</p>
        </CardHeader>
        <CardContent>
          <Table>
            <TableBody>
              {/* Revenue Section */}
              <TableRow className="border-t-2 border-slate-300 font-medium bg-slate-50 dark:bg-slate-800">
                <TableCell className="font-bold text-lg" colSpan={2}>
                  {t('finance.financial.profitLoss.revenue.title') || 'الإيرادات'}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.revenue.printingServices') || 'خدمات الطباعة'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.revenue.printingServices)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.revenue.designServices') || 'خدمات التصميم'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.revenue.designServices)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.revenue.officeSupplies') || 'اللوازم المكتبية'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.revenue.officeSupplies)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.revenue.consulting') || 'الاستشارات'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.revenue.consulting)}</TableCell>
              </TableRow>
              <TableRow className="border-t border-slate-200 font-bold bg-green-50 dark:bg-green-900/20">
                <TableCell className="font-bold">{t('finance.financial.profitLoss.revenue.total') || 'إجمالي الإيرادات'}</TableCell>
                <TableCell className="text-right font-bold text-green-600">{formatCurrency(profitLossData.revenue.total)}</TableCell>
              </TableRow>

              {/* Cost of Goods Sold Section */}
              <TableRow className="border-t-2 border-slate-300 font-medium bg-slate-50 dark:bg-slate-800">
                <TableCell className="font-bold text-lg" colSpan={2}>
                  {t('finance.financial.profitLoss.costOfGoodsSold.title') || 'تكلفة البضائع المباعة'}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.costOfGoodsSold.materials') || 'المواد والخامات'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.costOfGoodsSold.materials)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.costOfGoodsSold.directLabor') || 'العمالة المباشرة'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.costOfGoodsSold.directLabor)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.costOfGoodsSold.manufacturingOverhead') || 'المصاريف الصناعية العامة'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.costOfGoodsSold.manufacturingOverhead)}</TableCell>
              </TableRow>
              <TableRow className="border-t border-slate-200 font-bold bg-red-50 dark:bg-red-900/20">
                <TableCell className="font-bold">{t('finance.financial.profitLoss.costOfGoodsSold.total') || 'إجمالي تكلفة البضائع المباعة'}</TableCell>
                <TableCell className="text-right font-bold text-red-600">{formatCurrency(profitLossData.costOfGoodsSold.total)}</TableCell>
              </TableRow>

              {/* Gross Profit */}
              <TableRow className="border-t-2 border-blue-300 font-bold bg-blue-50 dark:bg-blue-900/20">
                <TableCell className="font-bold text-lg">{t('finance.financial.profitLoss.grossProfit') || 'إجمالي الربح'}</TableCell>
                <TableCell className="text-right font-bold text-blue-600 text-lg">{formatCurrency(profitLossData.grossProfit)}</TableCell>
              </TableRow>

              {/* Operating Expenses Section */}
              <TableRow className="border-t-2 border-slate-300 font-medium bg-slate-50 dark:bg-slate-800">
                <TableCell className="font-bold text-lg" colSpan={2}>
                  {t('finance.financial.profitLoss.operatingExpenses.title') || 'المصروفات التشغيلية'}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.operatingExpenses.salariesAndBenefits') || 'الرواتب والمزايا'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.operatingExpenses.salariesAndBenefits)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.operatingExpenses.rentAndUtilities') || 'الإيجار والمرافق'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.operatingExpenses.rentAndUtilities)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.operatingExpenses.equipmentMaintenance') || 'صيانة المعدات'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.operatingExpenses.equipmentMaintenance)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.operatingExpenses.marketingAdvertising') || 'التسويق والإعلان'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.operatingExpenses.marketingAdvertising)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.operatingExpenses.insurance') || 'التأمين'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.operatingExpenses.insurance)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.operatingExpenses.professionalServices') || 'الخدمات المهنية'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.operatingExpenses.professionalServices)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.operatingExpenses.officeExpenses') || 'مصاريف المكتب'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.operatingExpenses.officeExpenses)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.operatingExpenses.depreciation') || 'الاستهلاك'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.operatingExpenses.depreciation)}</TableCell>
              </TableRow>
              <TableRow className="border-t border-slate-200 font-bold bg-red-50 dark:bg-red-900/20">
                <TableCell className="font-bold">{t('finance.financial.profitLoss.operatingExpenses.total') || 'إجمالي المصروفات التشغيلية'}</TableCell>
                <TableCell className="text-right font-bold text-red-600">{formatCurrency(profitLossData.operatingExpenses.total)}</TableCell>
              </TableRow>

              {/* Operating Income */}
              <TableRow className="border-t-2 border-purple-300 font-bold bg-purple-50 dark:bg-purple-900/20">
                <TableCell className="font-bold text-lg">{t('finance.financial.profitLoss.operatingIncome') || 'الدخل التشغيلي'}</TableCell>
                <TableCell className="text-right font-bold text-purple-600 text-lg">{formatCurrency(profitLossData.operatingIncome)}</TableCell>
              </TableRow>

              {/* Other Income/Expenses */}
              <TableRow className="border-t-2 border-slate-300 font-medium bg-slate-50 dark:bg-slate-800">
                <TableCell className="font-bold text-lg" colSpan={2}>
                  {t('finance.financial.profitLoss.otherIncomeExpenses.title') || 'الإيرادات والمصروفات الأخرى'}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.otherIncomeExpenses.interestIncome') || 'إيرادات الفوائد'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.otherIncomeExpenses.interestIncome)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.otherIncomeExpenses.interestExpense') || 'مصروفات الفوائد'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.otherIncomeExpenses.interestExpense)}</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.otherIncomeExpenses.otherIncome') || 'إيرادات أخرى'}</TableCell>
                <TableCell className="text-right font-medium">{formatCurrency(profitLossData.otherIncomeExpenses.otherIncome)}</TableCell>
              </TableRow>
              <TableRow className="border-t border-slate-200 font-bold">
                <TableCell className="font-bold">{t('finance.financial.profitLoss.otherIncomeExpenses.total') || 'صافي الإيرادات الأخرى'}</TableCell>
                <TableCell className="text-right font-bold">{formatCurrency(profitLossData.otherIncomeExpenses.total)}</TableCell>
              </TableRow>

              {/* Net Income Before Tax */}
              <TableRow className="border-t-2 border-slate-400 font-bold bg-slate-100 dark:bg-slate-700">
                <TableCell className="font-bold text-lg">{t('finance.financial.profitLoss.netIncomeBeforeTax') || 'صافي الدخل قبل الضريبة'}</TableCell>
                <TableCell className="text-right font-bold text-lg">{formatCurrency(profitLossData.netIncomeBeforeTax)}</TableCell>
              </TableRow>

              {/* Income Tax */}
              <TableRow>
                <TableCell className="pl-6">{t('finance.financial.profitLoss.incomeTax') || 'ضريبة الدخل'}</TableCell>
                <TableCell className="text-right font-medium text-red-600">{formatCurrency(profitLossData.incomeTax)}</TableCell>
              </TableRow>

              {/* Net Income */}
              <TableRow className="border-t-2 border-green-400 font-bold bg-green-100 dark:bg-green-900/30">
                <TableCell className="font-bold text-xl">{t('finance.financial.profitLoss.netIncome') || 'صافي الدخل'}</TableCell>
                <TableCell className="text-right font-bold text-green-600 text-xl">{formatCurrency(profitLossData.netIncome)}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Financial Margins */}
      <Card className="bg-white dark:bg-slate-800 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-bold">{t('finance.financial.profitLoss.margins.title') || 'الهوامش'}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-sm text-muted-foreground">{t('finance.financial.profitLoss.margins.grossMargin') || 'هامش الربح الإجمالي'}</div>
              <div className="text-2xl font-bold text-blue-600">{profitLossData.margins.grossMargin.toFixed(3)}%</div>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-sm text-muted-foreground">{t('finance.financial.profitLoss.margins.operatingMargin') || 'هامش الربح التشغيلي'}</div>
              <div className="text-2xl font-bold text-purple-600">{profitLossData.margins.operatingMargin.toFixed(3)}%</div>
            </div>
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-sm text-muted-foreground">{t('finance.financial.profitLoss.margins.netMargin') || 'هامش الربح الصافي'}</div>
              <div className="text-2xl font-bold text-green-600">{profitLossData.margins.netMargin.toFixed(3)}%</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
