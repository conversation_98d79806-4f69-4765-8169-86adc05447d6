import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const supplier = await prisma.supplier.findUnique({
      where: { id },
      include: {
        purchases: {
          select: {
            id: true,
            number: true,
            total: true,
            status: true,
            date: true,
          },
          orderBy: { date: 'desc' },
        },
        products: {
          select: {
            id: true,
            name: true,
            price: true,
            currentStock: true,
          },
          orderBy: { name: 'asc' },
        },
      },
    })

    if (!supplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 })
    }

    // Calculate totals
    const totalPurchases = supplier.purchases.reduce((sum, purchase) => sum + Number(purchase.total), 0)
    const outstandingPayments = supplier.purchases
      .filter(purchase => purchase.status === 'PENDING' || purchase.status === 'ORDERED')
      .reduce((sum, purchase) => sum + Number(purchase.total), 0)

    const supplierWithStats = {
      ...supplier,
      totalPurchases,
      outstandingPayments,
      purchaseOrderCount: supplier.purchases.length,
    }

    return NextResponse.json(supplierWithStats)
  } catch (error) {
    console.error('Error fetching supplier:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const { name, email, phone, address, company, taxNumber, contactPerson, notes } = body

    if (!name || !phone) {
      return NextResponse.json(
        { error: 'Name and phone are required' },
        { status: 400 }
      )
    }

    const supplier = await prisma.supplier.update({
      where: { id },
      data: {
        name,
        email,
        phone,
        address,
        company,
        taxNumber,
        contactPerson,
        notes,
      },
    })

    return NextResponse.json(supplier)
  } catch (error) {
    console.error('Error updating supplier:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    // Check if supplier has any purchases or products
    const supplier = await prisma.supplier.findUnique({
      where: { id },
      include: {
        purchases: true,
        products: true,
      },
    })

    if (!supplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 })
    }

    if (supplier.purchases.length > 0 || supplier.products.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete supplier with existing purchases or products' },
        { status: 400 }
      )
    }

    await prisma.supplier.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Supplier deleted successfully' })
  } catch (error) {
    console.error('Error deleting supplier:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
