import { PrismaClient } from '@prisma/client'
import * as bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database with comprehensive data...')

  try {
    // Create categories
    let printingCategory = await prisma.category.findFirst({
      where: { name: 'Printing Services' }
    })
    if (!printingCategory) {
      printingCategory = await prisma.category.create({
        data: {
          name: 'Printing Services',
          nameAr: 'خدمات الطباعة',
          description: 'All printing related services',
        },
      })
    }

    let suppliesCategory = await prisma.category.findFirst({
      where: { name: 'Office Supplies' }
    })
    if (!suppliesCategory) {
      suppliesCategory = await prisma.category.create({
        data: {
          name: 'Office Supplies',
          nameAr: 'المستلزمات المكتبية',
          description: 'Office supplies and materials',
        },
      })
    }

    let designCategory = await prisma.category.findFirst({
      where: { name: 'Design Services' }
    })
    if (!designCategory) {
      designCategory = await prisma.category.create({
        data: {
          name: 'Design Services',
          nameAr: 'خدمات التصميم',
          description: 'Graphic design and creative services',
        },
      })
    }

    // Create units
    const reams = await prisma.unit.upsert({
      where: { name: 'Ream' },
      update: {},
      create: {
        name: 'Ream',
        nameAr: 'رزمة',
        symbol: 'ream',
        symbolAr: 'رزمة',
        description: 'Paper ream (500 sheets)',
        descriptionAr: 'رزمة ورق (500 ورقة)',
      },
    })

    const pieces = await prisma.unit.upsert({
      where: { name: 'Piece' },
      update: {},
      create: {
        name: 'Piece',
        nameAr: 'قطعة',
        symbol: 'pc',
        symbolAr: 'قطعة',
        description: 'Individual piece',
        descriptionAr: 'قطعة واحدة',
      },
    })

    const sets = await prisma.unit.upsert({
      where: { name: 'Set' },
      update: {},
      create: {
        name: 'Set',
        nameAr: 'مجموعة',
        symbol: 'set',
        symbolAr: 'مجموعة',
        description: 'Set of items',
        descriptionAr: 'مجموعة من العناصر',
      },
    })

    // Create suppliers
    const supplier1 = await prisma.supplier.upsert({
      where: { mobile: '+968 9123 4567' },
      update: {},
      create: {
        name: 'Al Warak Trading LLC',
        nameAr: 'شركة الورق التجارية',
        email: '<EMAIL>',
        mobile: '+968 9123 4567',
        phone: '+968 24123456',
        company: 'Al Warak Trading LLC',
        address: 'Ruwi Commercial District, Muscat, Oman',
        city: 'Muscat',
        country: 'Oman',
        contactPerson: 'Ahmed Al Rashid',
        taxNumber: 'OM1234567890',
        notes: 'Main paper and office supplies distributor',
      },
    })

    const supplier2 = await prisma.supplier.upsert({
      where: { mobile: '+968 9876 5432' },
      update: {},
      create: {
        name: 'Gulf Printing Solutions',
        nameAr: 'حلول الطباعة الخليجية',
        email: '<EMAIL>',
        mobile: '+968 9876 5432',
        phone: '+968 24567890',
        company: 'Gulf Printing Solutions',
        address: 'Al Khuwair Industrial Area, Muscat, Oman',
        city: 'Muscat',
        country: 'Oman',
        contactPerson: 'Fatima Al Zahra',
        taxNumber: 'OM0987654321',
        notes: 'Specialized in printing equipment and ink supplies',
      },
    })

    // Create products
    const product1 = await prisma.product.upsert({
      where: { sku: 'A4-PAPER-001' },
      update: {},
      create: {
        name: 'A4 Paper - Premium',
        nameAr: 'ورق A4 - ممتاز',
        description: 'High quality A4 printing paper 80gsm',
        sku: 'A4-PAPER-001',
        barcode: '1234567890123',
        type: 'PHYSICAL',
        price: 2.500,
        costPrice: 1.800,
        currentStock: 1000,
        minStock: 100,
        maxStock: 2000,
        location: 'A1-B1',
        unit: 'ream',
        categoryId: suppliesCategory.id,
        supplierId: supplier1.id,
        unitId: reams.id,
        lastRestocked: new Date('2024-01-15'),
      },
    })

    const product2 = await prisma.product.upsert({
      where: { sku: 'BC-PRINT-001' },
      update: {},
      create: {
        name: 'Business Card Printing',
        nameAr: 'طباعة بطاقات العمل',
        description: 'Professional business card printing service (500 cards)',
        sku: 'BC-PRINT-001',
        type: 'SERVICE',
        price: 15.000,
        costPrice: 8.000,
        currentStock: 0,
        minStock: 0,
        maxStock: 0,
        location: 'SERVICE',
        unit: 'set',
        categoryId: printingCategory.id,
        unitId: sets.id,
      },
    })

    const product3 = await prisma.product.upsert({
      where: { sku: 'LOGO-DESIGN-001' },
      update: {},
      create: {
        name: 'Logo Design Service',
        nameAr: 'خدمة تصميم الشعار',
        description: 'Professional logo design with 3 concepts',
        sku: 'LOGO-DESIGN-001',
        type: 'SERVICE',
        price: 50.000,
        costPrice: 25.000,
        currentStock: 0,
        minStock: 0,
        unit: 'project',
        categoryId: designCategory.id,
        unitId: pieces.id,
      },
    })

    // Create customers
    const customer1 = await prisma.customer.upsert({
      where: { mobile: '+968 9111 2222' },
      update: {},
      create: {
        name: 'Muscat Trading Company LLC',
        nameAr: 'شركة مسقط التجارية',
        email: '<EMAIL>',
        mobile: '+968 9111 2222',
        phone: '+968 24111222',
        company: 'Muscat Trading Company LLC',
        address: 'Al Khuwair, Way 3018, Building 145, Muscat, Oman',
        city: 'Muscat',
        country: 'Oman',
        taxNumber: 'OM1001234567',
        notes: 'Regular corporate customer, bulk printing orders',
      },
    })

    const customer2 = await prisma.customer.upsert({
      where: { mobile: '+968 9912 3456' },
      update: {},
      create: {
        name: 'Ahmed Al Kindi',
        email: '<EMAIL>',
        mobile: '+968 9912 3456',
        phone: '+968 99123456',
        company: 'Individual',
        address: 'Al Mawaleh, Muscat, Oman',
        city: 'Muscat',
        country: 'Oman',
        notes: 'Individual customer, occasional printing needs',
      },
    })

    // Create expense types
    const officeExpenseType = await prisma.expenseType.upsert({
      where: { name: 'Office Expenses' },
      update: {},
      create: {
        name: 'Office Expenses',
        nameAr: 'مصاريف المكتب',
        description: 'General office expenses',
      },
    })

    const travelExpenseType = await prisma.expenseType.upsert({
      where: { name: 'Travel & Transportation' },
      update: {},
      create: {
        name: 'Travel & Transportation',
        nameAr: 'السفر والمواصلات',
        description: 'Travel and transportation costs',
      },
    })

    console.log('✅ Comprehensive data seeded successfully!')
    console.log('')
    console.log('📊 Sample Data Created:')
    console.log('   📁 Categories: 3 (Printing, Supplies, Design)')
    console.log('   📏 Units: 3 (Ream, Piece, Set)')
    console.log('   🏢 Suppliers: 2 (Omani companies)')
    console.log('   📦 Products: 3 (Physical & Service items)')
    console.log('   👥 Customers: 2 (Omani businesses & individuals)')
    console.log('   💸 Expense Types: 2 (Office, Travel)')
    console.log('')
    console.log('💰 Currency: Omani Rial (OMR)')
    console.log('🌍 Timezone: Asia/Muscat')
    console.log('📍 Location: Muscat, Sultanate of Oman')

  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
