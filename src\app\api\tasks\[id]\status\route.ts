import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { whatsappService } from '@/lib/whatsapp'
import { TaskStatus } from '@prisma/client'

// Helper function to calculate and update project progress
async function updateProjectProgress(projectId: string) {
  if (!projectId) return

  const tasks = await prisma.task.findMany({
    where: { projectId },
    select: { status: true }
  })
  
  if (tasks.length === 0) return
  
  const completedTasks = tasks.filter(task => task.status === 'COMPLETED').length
  const progress = Math.round((completedTasks / tasks.length) * 100)
  
  // Get current project to check status
  const currentProject = await prisma.project.findUnique({
    where: { id: projectId },
    select: { status: true }
  })
  
  const updateData: any = {
    progress,
    updatedAt: new Date()
  }
  
  // Auto-complete project when all tasks are done
  if (progress === 100 && currentProject?.status === 'IN_PROGRESS') {
    updateData.status = 'COMPLETED'
  }
  
  await prisma.project.update({
    where: { id: projectId },
    data: updateData
  })
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { status, completionNotes, attachments, actualHours } = await request.json()
    const { id: taskId } = await params

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      )
    }

    // Get the current task
    const currentTask = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        customer: true,
        assignedTo: true,
      },
    })

    if (!currentTask) {
      return NextResponse.json(
        { error: 'Task not found' },
        { status: 404 }
      )
    }

    // Update the task status
    const updateData: {
      status: TaskStatus;
      startTime?: Date;
      endTime?: Date;
      completionNotes?: string;
      attachments?: any;
      actualHours?: number;
    } = { status: status as TaskStatus }

    if (status === 'IN_PROGRESS' && !currentTask.startTime) {
      updateData.startTime = new Date()
    }

    if (status === 'COMPLETED') {
      if (!currentTask.endTime) {
        updateData.endTime = new Date()
      }
      if (completionNotes) {
        updateData.completionNotes = completionNotes
      }
      if (attachments) {
        updateData.attachments = attachments
      }
      if (actualHours) {
        updateData.actualHours = parseFloat(actualHours)
      }
    }

    const updatedTask = await prisma.task.update({
      where: { id: taskId },
      data: updateData,
      include: {
        customer: true,
        assignedTo: true,
        createdBy: true,
      },
    })

    // Update project progress when task status changes
    if (currentTask.projectId) {
      await updateProjectProgress(currentTask.projectId)
    }

    // Send WhatsApp notifications based on status change
    if (status === 'IN_PROGRESS' && currentTask.customer?.phone) {
      await whatsappService.sendTaskStartedNotification(
        currentTask.customer.phone,
        currentTask.title
      )
    }

    if (status === 'COMPLETED') {
      // Notify customer
      if (currentTask.customer?.phone) {
        await whatsappService.sendTaskCompletedNotification(
          currentTask.customer.phone,
          currentTask.title
        )
      }

      // Notify admin/manager
      const admins = await prisma.user.findMany({
        where: {
          role: { in: ['ADMIN', 'MANAGER'] },
          isActive: true,
        },
      })

      for (const admin of admins) {
        if (admin.phone) {
          await whatsappService.sendTaskCompletedNotification(
            admin.phone,
            currentTask.title
          )
        }
      }
    }

    return NextResponse.json(updatedTask)
  } catch (error) {
    console.error('Error updating task status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
