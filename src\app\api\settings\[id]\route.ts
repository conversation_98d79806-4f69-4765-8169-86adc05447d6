import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/settings/[id] - Get setting by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const setting = await prisma.setting.findUnique({
      where: { id },
    })

    if (!setting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(setting)
  } catch (error) {
    console.error('Error fetching setting:', error)
    return NextResponse.json(
      { error: 'Failed to fetch setting' },
      { status: 500 }
    )
  }
}

// PUT /api/settings/[id] - Update setting
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { key, value, description } = body

    // Check if setting exists
    const existingSetting = await prisma.setting.findUnique({
      where: { id },
    })

    if (!existingSetting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      )
    }

    // Check if key already exists for other settings (if key is being changed)
    if (key && key !== existingSetting.key) {
      const keyExists = await prisma.setting.findFirst({
        where: {
          key,
          id: { not: id },
        },
      })

      if (keyExists) {
        return NextResponse.json(
          { error: 'Setting key already exists' },
          { status: 400 }
        )
      }
    }

    // Update setting
    const updatedSetting = await prisma.setting.update({
      where: { id },
      data: {
        key,
        value,
        description,
        updatedAt: new Date(),
      },
    })

    return NextResponse.json(updatedSetting)
  } catch (error) {
    console.error('Error updating setting:', error)
    return NextResponse.json(
      { error: 'Failed to update setting' },
      { status: 500 }
    )
  }
}

// DELETE /api/settings/[id] - Delete setting
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if setting exists
    const existingSetting = await prisma.setting.findUnique({
      where: { id },
    })

    if (!existingSetting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      )
    }

    // Delete setting
    await prisma.setting.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Setting deleted successfully' })
  } catch (error) {
    console.error('Error deleting setting:', error)
    return NextResponse.json(
      { error: 'Failed to delete setting' },
      { status: 500 }
    )
  }
}
