const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function seedProjects() {
  try {
    console.log('🌱 Seeding projects...')

    // First, let's get some customers and users for relationships
    const customers = await prisma.customer.findMany({ take: 3 })
    const users = await prisma.user.findMany({ take: 3 })

    // If no users exist, create a default admin user
    let defaultUser = users[0]
    if (!defaultUser) {
      console.log('No users found, creating default admin user...')
      defaultUser = await prisma.user.create({
        data: {
          name: 'Admin User',
          email: '<EMAIL>',
          phone: '+968 9123 4567',
          role: 'ADMIN',
          password: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', // hashed "password"
          isActive: true,
          joinDate: new Date(),
          tasksCompleted: 0,
          tasksInProgress: 0,
          completionRate: 0,
        }
      })
      console.log('✅ Created default admin user')
    }

    const projects = [
      {
        code: 'PRJ-0001',
        name: 'E-commerce Website Development',
        nameAr: 'تطوير موقع التجارة الإلكترونية',
        description: 'Complete development of a modern e-commerce platform with payment integration, inventory management, and customer portal.',
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-06-15'),
        budget: 50000,
        actualCost: 25000,
        progress: 60,
        notes: 'Project is progressing well. Frontend development completed, working on backend integration.',
        clientId: customers[0]?.id || null,
        managerId: users[0]?.id || defaultUser.id,
        createdById: defaultUser.id,
      },
      {
        code: 'PRJ-0002',
        name: 'Mobile App Development',
        nameAr: 'تطوير تطبيق الهاتف المحمول',
        description: 'Native mobile application for iOS and Android with real-time notifications and offline capabilities.',
        status: 'PLANNING',
        priority: 'MEDIUM',
        startDate: new Date('2024-03-01'),
        endDate: new Date('2024-08-01'),
        budget: 75000,
        actualCost: 5000,
        progress: 15,
        notes: 'Requirements gathering completed. UI/UX design in progress.',
        clientId: customers[1]?.id || null,
        managerId: users[1]?.id || defaultUser.id,
        createdById: defaultUser.id,
      },
      {
        code: 'PRJ-0003',
        name: 'Digital Marketing Campaign',
        nameAr: 'حملة التسويق الرقمي',
        description: 'Comprehensive digital marketing strategy including SEO, social media, and content marketing.',
        status: 'COMPLETED',
        priority: 'LOW',
        startDate: new Date('2023-10-01'),
        endDate: new Date('2024-01-31'),
        budget: 20000,
        actualCost: 18500,
        progress: 100,
        notes: 'Campaign completed successfully. Achieved 150% of target engagement.',
        clientId: customers[2]?.id || null,
        managerId: users[2]?.id || defaultUser.id,
        createdById: defaultUser.id,
      },
      {
        code: 'PRJ-0004',
        name: 'Cloud Infrastructure Migration',
        nameAr: 'ترحيل البنية التحتية السحابية',
        description: 'Migration of existing on-premise infrastructure to AWS cloud with improved security and scalability.',
        status: 'ON_HOLD',
        priority: 'URGENT',
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-05-01'),
        budget: 100000,
        actualCost: 30000,
        progress: 35,
        notes: 'Project on hold due to client budget constraints. Awaiting approval for phase 2.',
        clientId: customers[0]?.id || null,
        managerId: users[1]?.id || defaultUser.id,
        createdById: defaultUser.id,
      },
      {
        code: 'PRJ-0005',
        name: 'Data Analytics Dashboard',
        nameAr: 'لوحة تحليل البيانات',
        description: 'Business intelligence dashboard with real-time analytics, reporting, and data visualization.',
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-04-30'),
        budget: 35000,
        actualCost: 20000,
        progress: 75,
        notes: 'Dashboard development nearly complete. Testing and deployment scheduled for next week.',
        clientId: customers[1]?.id || null,
        managerId: users[2]?.id || defaultUser.id,
        createdById: defaultUser.id,
      }
    ]

    // Create projects
    for (const projectData of projects) {
      try {
        const project = await prisma.project.create({
          data: projectData,
          include: {
            client: {
              select: {
                id: true,
                name: true,
                company: true,
              },
            },
            manager: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        })
        console.log(`✅ Created project: ${project.name} (${project.code})`)
      } catch (error) {
        console.error(`❌ Failed to create project ${projectData.name}:`, error.message)
      }
    }

    console.log('🎉 Projects seeding completed!')

    // Display summary
    const totalProjects = await prisma.project.count()
    console.log(`📊 Total projects in database: ${totalProjects}`)

  } catch (error) {
    console.error('❌ Error seeding projects:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seed function
seedProjects()
  .catch((error) => {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  })
