import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/settings - Get all settings
export async function GET() {
  try {
    const settings = await prisma.setting.findMany({
      orderBy: {
        key: 'asc',
      },
    })

    // Convert to key-value object for easier frontend usage
    const settingsObject = settings.reduce((acc, setting) => {
      acc[setting.key] = {
        id: setting.id,
        value: setting.value,
        description: setting.description,
        createdAt: setting.createdAt,
        updatedAt: setting.updatedAt,
      }
      return acc
    }, {} as Record<string, any>)

    return NextResponse.json({
      settings,
      settingsObject,
    })
  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

// POST /api/settings - Create new setting
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { key, value, description } = body

    // Validate required fields
    if (!key || value === undefined || value === null) {
      return NextResponse.json(
        { error: 'Key and value are required' },
        { status: 400 }
      )
    }

    // Check if key already exists
    const existingSetting = await prisma.setting.findUnique({
      where: { key },
    })

    if (existingSetting) {
      return NextResponse.json(
        { error: 'Setting key already exists' },
        { status: 400 }
      )
    }

    // Create setting
    const setting = await prisma.setting.create({
      data: {
        key,
        value,
        description,
      },
    })

    return NextResponse.json(setting, { status: 201 })
  } catch (error) {
    console.error('Error creating setting:', error)
    return NextResponse.json(
      { error: 'Failed to create setting' },
      { status: 500 }
    )
  }
}

// PUT /api/settings - Update setting
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, key, value, description } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Setting ID is required' },
        { status: 400 }
      )
    }

    // Check if setting exists
    const existingSetting = await prisma.setting.findUnique({
      where: { id },
    })

    if (!existingSetting) {
      return NextResponse.json(
        { error: 'Setting not found' },
        { status: 404 }
      )
    }

    // Check if key already exists for other settings (if key is being changed)
    if (key && key !== existingSetting.key) {
      const keyExists = await prisma.setting.findFirst({
        where: {
          key,
          id: { not: id },
        },
      })

      if (keyExists) {
        return NextResponse.json(
          { error: 'Setting key already exists' },
          { status: 400 }
        )
      }
    }

    // Update setting
    const updatedSetting = await prisma.setting.update({
      where: { id },
      data: {
        key,
        value,
        description,
        updatedAt: new Date(),
      },
    })

    return NextResponse.json(updatedSetting)
  } catch (error) {
    console.error('Error updating setting:', error)
    return NextResponse.json(
      { error: 'Failed to update setting' },
      { status: 500 }
    )
  }
}
