"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  ArrowLeft,
  Edit,
  Printer,
  Mail,
  Download,
  CheckCircle,
  MoreHorizontal,
  Trash2,
  Copy,
  Truck
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

interface PurchaseItem {
  id: string
  description: string
  quantity: number
  unitPrice: number
  total: number
}

interface Purchase {
  id: string
  number: string
  date: string
  expectedDate: string
  supplier: string
  supplierMobile: string
  supplierEmail?: string
  supplierAddress?: string
  status: 'PENDING' | 'ORDERED' | 'RECEIVED' | 'CANCELLED'
  subtotal: number
  taxAmount: number
  discount: number
  total: number
  items: PurchaseItem[]
  notes?: string
}

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
  ORDERED: "bg-blue-100 text-blue-800 border-blue-200",
  RECEIVED: "bg-green-100 text-green-800 border-green-200",
  CANCELLED: "bg-red-100 text-red-800 border-red-200",
}

// Mock data - in real app this would come from API
const mockPurchase: Purchase = {
  id: "1",
  number: "PO-001",
  date: "2024-01-15",
  expectedDate: "2024-01-22",
  supplier: "Paper Supply Co.",
  supplierMobile: "+968 9111 1111",
  supplierEmail: "<EMAIL>",
  supplierAddress: "Muscat Industrial Area",
  status: "ORDERED",
  subtotal: 500.00,
  taxAmount: 25.00,
  discount: 0,
  total: 525.00,
  notes: "Please deliver during business hours.",
  items: [
    {
      id: "1",
      description: "A4 Paper - High quality white paper for printing and copying",
      quantity: 10000,
      unitPrice: 0.05,
      total: 500.00
    }
  ]
}

export default function PurchaseDetailsPage() {
  const router = useRouter()
  const params = useParams()
  const { t } = useI18n()
  const [purchase, setPurchase] = useState<Purchase | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchPurchase = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/purchases/${params.id}`)

        if (!response.ok) {
          throw new Error('Failed to fetch purchase')
        }

        const purchaseData = await response.json()

        // Transform API data to match component interface
        const transformedPurchase: Purchase = {
          id: purchaseData.id,
          number: purchaseData.number,
          date: purchaseData.date,
          expectedDate: purchaseData.expectedDate,
          supplier: purchaseData.supplier?.name || 'Unknown Supplier',
          supplierMobile: purchaseData.supplier?.mobile || '',
          supplierEmail: purchaseData.supplier?.email || '',
          supplierAddress: purchaseData.supplier?.address || '',
          status: purchaseData.status,
          subtotal: Number(purchaseData.subtotal),
          taxAmount: Number(purchaseData.taxAmount),
          discount: Number(purchaseData.discountAmount || 0),
          total: Number(purchaseData.total),
          items: purchaseData.items?.map((item: any) => ({
            id: item.id,
            description: item.description,
            quantity: item.quantity,
            unitPrice: Number(item.unitPrice),
            total: Number(item.total),
          })) || [],
          notes: purchaseData.notes || '',
        }

        setPurchase(transformedPurchase)
      } catch (error) {
        console.error('Error fetching purchase:', error)
        // Fallback to mock data for now
        setPurchase(mockPurchase)
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchPurchase()
    }
  }, [params.id])

  const handleEdit = () => {
    router.push(`/dashboard/purchases/${purchase?.id}/edit`)
  }

  const handlePrint = () => {
    window.print()
  }

  const handleEmail = () => {
    console.log('Emailing purchase order:', purchase?.number)
    alert('Email functionality would be implemented here')
  }

  const handleDownload = () => {
    console.log('Downloading purchase order:', purchase?.number)
    alert('PDF download functionality would be implemented here')
  }

  const handleMarkAsReceived = () => {
    console.log('Marking purchase as received:', purchase?.number)
    alert('Mark as received functionality would be implemented here')
  }

  const handleDuplicate = () => {
    router.push(`/dashboard/purchases/create?duplicate=${purchase?.id}`)
  }

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this purchase order?')) {
      console.log('Deleting purchase order:', purchase?.number)
      alert('Delete functionality would be implemented here')
      router.push('/dashboard/purchases')
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('purchases.loading')}</h2>
          </div>
        </div>
      </div>
    )
  }

  if (!purchase) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('purchases.notFound')}</h2>
          </div>
        </div>
      </div>
    )
  }

  const canMarkAsReceived = purchase.status === 'ORDERED'

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('purchases.purchaseOrder') + purchase.number}</h2>
            <p className="text-muted-foreground">
              {t('purchases.viewAndManagePurchaseOrderDetails')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge className={statusColors[purchase.status]}>
            {purchase.status}
          </Badge>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Purchase
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDuplicate}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handlePrint}>
                <Printer className="mr-2 h-4 w-4" />
                Print/PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEmail}>
                <Mail className="mr-2 h-4 w-4" />
                Email Purchase
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDownload}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </DropdownMenuItem>
              {canMarkAsReceived && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={handleMarkAsReceived}
                    className="text-green-600 focus:text-green-600"
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Mark as Received
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('purchases.purchaseOrderInformation')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('purchases.supplier')}</Label>
                    <p className="text-lg font-medium">{purchase.supplier}</p>
                    <p className="text-sm text-muted-foreground">{purchase.supplierMobile}</p>
                    {purchase.supplierEmail && (
                      <p className="text-sm text-muted-foreground">{purchase.supplierEmail}</p>
                    )}
                    {purchase.supplierAddress && (
                      <p className="text-sm text-muted-foreground">{purchase.supplierAddress}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('purchases.orderDate')}</Label>
                    <p className="text-base">{new Date(purchase.date).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('purchases.expectedDelivery')}</Label>
                    <p className="text-base">{new Date(purchase.expectedDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">{t('purchases.status')}</Label>
                    <div className="mt-1">
                      <Badge className={statusColors[purchase.status]}>
                        {purchase.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Purchase Items */}
          <Card>
            <CardHeader>
              <CardTitle>{t('purchases.purchaseItems')}</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('purchases.description')}</TableHead>
                    <TableHead className="text-right">{t('purchases.quantity')}</TableHead>
                    <TableHead className="text-right">{t('purchases.unitPrice')}</TableHead>
                    <TableHead className="text-right">{t('purchases.total')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {purchase.items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.description}</TableCell>
                      <TableCell className="text-right">{item.quantity.toLocaleString()}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
                      <TableCell className="text-right font-medium">{formatCurrency(item.total)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Notes */}
          {purchase.notes && (
            <Card>
              <CardHeader>
                <CardTitle>{t('purchases.notes')}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{purchase.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Purchase Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t('purchases.purchaseSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>{t('purchases.subtotal')}:</span>
                <span>{formatCurrency(purchase.subtotal)}</span>
              </div>
              
              {purchase.discount > 0 && (
                <div className="flex justify-between text-red-600">
                  <span>{t('purchases.discount')}:</span>
                  <span>-{formatCurrency(purchase.discount)}</span>
                </div>
              )}
              
              <div className="flex justify-between">
                <span>{t('purchases.vat')}:</span>
                <span>{formatCurrency(purchase.taxAmount)}</span>
              </div>
              
              <Separator />
              
              <div className="flex justify-between font-medium text-lg">
                <span>{t('purchases.total')}:</span>
                <span>{formatCurrency(purchase.total)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('purchases.quickActions')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full" onClick={handlePrint}>
                <Printer className="mr-2 h-4 w-4" />
                Print/PDF
              </Button>
              
              <Button variant="outline" className="w-full" onClick={handleEmail}>
                <Mail className="mr-2 h-4 w-4" />
                Email Purchase
              </Button>
              
              {canMarkAsReceived && (
                <Button variant="outline" className="w-full" onClick={handleMarkAsReceived}>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Mark as Received
                </Button>
              )}
              
              <Button variant="outline" className="w-full" onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit Purchase
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
