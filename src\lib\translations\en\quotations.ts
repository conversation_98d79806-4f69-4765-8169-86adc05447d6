export const quotations = {
  // Page titles and descriptions
  title: "Quotations",
  dashboard: "Quotation Dashboard",
  dashboardDescription: "Overview of your quotation performance and metrics",
  createQuotation: "Create Quotation",
  editQuotation: "Edit Quotation",
  quotationDetails: "Quotation Details",
  
  // Search and filters
  searchQuotations: "Search quotations...",
  
  // Status
  pending: "Pending",
  approved: "Approved",
  rejected: "Rejected",
  expired: "Expired",
  
  // Table headers
  quotationNumber: "Quotation #",
  quotationDate: "Quotation Date",
  validUntil: "Valid Until",
  customer: "Customer",
  total: "Total",
  status: "Status",
  
  // Actions
  printQuotation: "Print Quotation",
  emailQuotation: "Email Quotation",
  downloadQuotation: "Download Quotation",
  sendToCustomer: "Send to Customer",
  convertToInvoice: "Convert to Invoice",
  deleteQuotation: "Delete Quotation",
  
  // Form fields
  quotationInformation: "Quotation Information",
  customerRequired: "Customer is required",
  quotationDateRequired: "Quotation date is required",
  validUntilRequired: "Valid until date is required",
  
  // Messages
  quotationCreatedSuccessfully: "Quotation created successfully",
  quotationUpdatedSuccessfully: "Quotation updated successfully",
  quotationDeletedSuccessfully: "Quotation deleted successfully",
  quotationSentSuccessfully: "Quotation sent successfully",
  quotationConvertedSuccessfully: "Quotation converted to invoice successfully",
  
  // Error messages
  failedToCreateQuotation: "Failed to create quotation",
  failedToUpdateQuotation: "Failed to update quotation",
  failedToDeleteQuotation: "Failed to delete quotation",
  failedToSendQuotation: "Failed to send quotation",
  failedToConvertQuotation: "Failed to convert quotation",
  
  // Empty states
  noQuotationsFound: "No quotations found",
  createFirstQuotation: "Create your first quotation",
  
  // Loading states
  loadingQuotations: "Loading quotations...",
  savingQuotation: "Saving quotation...",
  sendingQuotation: "Sending quotation...",
  convertingQuotation: "Converting quotation...",
  
  // New keys from the code block
  viewAllQuotations: "View All Quotations",
  totalQuotations: "Total Quotations",
  totalValue: "Total Value",
  fromLastMonth: "from last month",
  conversionRate: "Conversion Rate",
  avgQuotationValue: "Avg. Quotation Value",
  noChange: "No change",
  monthlyTrend: "Monthly Quotation Trend",
  monthlyTrendDescription: "Number of quotations and total value over the last 5 months",
  quotations: "Quotations",
  value: "Value",
  statusDistribution: "Quotation Status Distribution",
  statusDistributionDescription: "Current breakdown of quotation statuses",
  statusLabels: {
    PENDING: "Pending",
    APPROVED: "Approved",
    REJECTED: "Rejected",
    EXPIRED: "Expired",
    CONVERTED: "Converted"
  },
  recentQuotations: "Recent Quotations",
  recentQuotationsDescription: "Latest quotations and their current status",
  viewAll: "View All",
  date: "Date",
  amount: "Amount",
  actions: "Actions",

  list: {
    description: "Manage your quotations and convert them to invoices",
    filterByStatus: "Filter by status",
    allStatuses: "All Statuses",
    loading: "Loading quotations...",
    emptySearch: "No quotations found matching your search.",
    empty: "No quotations found. Create your first quotation!",
    confirmDelete: "Are you sure you want to delete quotation {number}?",
    deleteSuccess: "Quotation {number} deleted successfully!",
    deleteFailed: "Failed to delete quotation: {error}",
    deleteFailedGeneric: "Failed to delete quotation. Please try again.",
    openMenu: "Open menu"
  },

  details: {
    title: "Quotation {number}",
    description: "View and manage quotation details",
    loading: "Loading...",
    notFound: "Quotation Not Found",
    backButton: "Back",
    actions: "Actions",
    duplicate: "Duplicate",
    printPDF: "Print/PDF",
    download: "Download",
    delete: "Delete",
    confirmDelete: "Are you sure you want to delete this quotation?",
    
    infoCard: {
      title: "Quotation Information",
      customer: "Customer",
      linkedTask: "Linked Task",
      quotationDate: "Quotation Date",
      validUntil: "Valid Until",
      status: "Status",
      expired: "Expired"
    },

    itemsCard: {
      title: "Quotation Items",
      headers: {
        description: "Description",
        quantity: "Quantity",
        unitPrice: "Unit Price",
        total: "Total"
      }
    },

    notesCard: {
      title: "Notes"
    },

    summaryCard: {
      title: "Quotation Summary",
      subtotal: "Subtotal:",
      discount: "Discount:",
      vat: "VAT:",
      total: "Total:"
    },

    quickActionsCard: {
      title: "Quick Actions"
    }
  },

  // Create/Edit Page
  create: {
    title: "Create Quotation",
    description: "Create a new quotation for your customer",
    backButton: "Back",
    saveDraft: "Save Draft",
    createFinal: "Create Final Quotation",
    
    customerSection: {
      title: "Customer Information",
      label: "Customer *",
      placeholder: "Select customer...",
      searchPlaceholder: "Search customers by name or mobile...",
      notFound: "No customer found.",
      addNew: "Add New Customer",
    },

    infoSection: {
      title: "Quotation Information",
      validUntil: "Valid Until",
      status: "Quotation Status",
      notes: "Notes",
      notesPlaceholder: "Thank you for considering our services!",
      statusOptions: {
        draft: "Draft",
        final: "Final",
        sent: "Sent"
      }
    },

    itemsSection: {
      title: "Quotation Items",
      addItem: "Add Item",
      headers: {
        description: "Description",
        product: "Product/Service",
        quantity: "Quantity",
        unitPrice: "Unit Price (OMR)",
        total: "Total (OMR)",
        action: "Action"
      },
      descriptionPlaceholder: "Item description",
      productPlaceholder: "Select product",
      productSearchPlaceholder: "Search products...",
      productNotFound: "No product found.",
      productsGroup: "Products",
    },

    settingsSection: {
      title: "{label} & Discount Settings",
      rateLabel: "{label} Rate (%)",
      defaultRate: "Default rate from settings: {rate}%",
      discount: "Discount",
      amount: "Amount",
      percentage: "Percentage",
      percentageHint: "Enter percentage (0-100%)",
      amountHint: "Enter amount in OMR"
    },

    summarySection: {
      title: "Quotation Summary",
      subtotal: "Subtotal:",
      discount: "Discount ({details}):",
      tax: "{label} ({rate}%):",
      total: "Total:",
      items: "Items",
      status: "Status"
    },

    newCustomerDialog: {
      title: "Add New Customer",
      nameLabel: "Customer Name *",
      namePlaceholder: "Enter customer name",
      mobileLabel: "Mobile Number *",
      mobilePlaceholder: "+968 9XXX XXXX",
      emailLabel: "Email (Optional)",
      emailPlaceholder: "<EMAIL>",
      customerExists: "Customer exists: {name}",
      customerExistsHint: "You can select this customer from the dropdown instead.",
      cancel: "Cancel",
      add: "Add Customer"
    },

    alerts: {
      nameAndMobileRequired: "Please enter both name and mobile number",
      customerExists: "A customer with this mobile number already exists",
      customerCreated: "Customer created successfully!",
      customerCreateFailed: "Failed to create customer. Please try again.",
      selectCustomer: "Please select a customer",
      addOneItem: "Please add at least one quotation item",
      checkItems: "Please check item quantities and prices. All items must have valid quantities and non-negative prices.",
      savedAsDraft: "Quotation {number} saved as draft successfully!",
      createdSuccessfully: "Quotation {number} created successfully!",
      saveFailed: "Failed to save quotation. Please try again."
    }
  },

  // Add these new keys for the Edit page
  edit: {
    title: 'Edit Quotation',
    description: 'Update the details of the quotation.',
    saveButton: 'Save Changes',
    alerts: {
      updateSuccess: 'Quotation {number} updated successfully.',
      updateFailed: 'Failed to update quotation.',
      notFound: 'Quotation not found.',
      loadFailed: 'Failed to load required data for editing.',
    },
  },
}; 