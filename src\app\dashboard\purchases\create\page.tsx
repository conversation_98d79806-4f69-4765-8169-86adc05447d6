"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Plus,
  Trash2,
  Check,
  ChevronsUpDown,
  UserPlus,
  Save,
  Send,
  FileText,
  Calculator,
  Package,
  Truck
} from "lucide-react"
import { cn } from "@/lib/utils"
import { formatCurrency } from "@/lib/localization"
import { addPurchase } from "@/lib/purchase-storage"
import { useI18n } from '@/lib/i18n'

interface Supplier {
  id: string
  name: string
  mobile: string
  email?: string
  address?: string
}

interface Product {
  id: string
  name: string
  price: number
  unit: string
  category: string
}

interface PurchaseItem {
  id: string
  description: string
  productId: string
  quantity: number
  unitPrice: number
  total: number
}

const products: Product[] = [
  { id: "paper", name: "A4 Paper", price: 0.05, unit: "per sheet", category: "Paper" },
  { id: "ink-black", name: "Black Ink Cartridge", price: 25.00, unit: "per cartridge", category: "Ink" },
  { id: "ink-color", name: "Color Ink Cartridge", price: 35.00, unit: "per cartridge", category: "Ink" },
  { id: "stapler", name: "Heavy Duty Stapler", price: 15.00, unit: "per piece", category: "Equipment" },
  { id: "binding-wire", name: "Binding Wire", price: 2.50, unit: "per pack", category: "Binding" },
  { id: "lamination-film", name: "Lamination Film A4", price: 0.25, unit: "per sheet", category: "Lamination" },
  { id: "toner", name: "Laser Printer Toner", price: 85.00, unit: "per cartridge", category: "Toner" },
  { id: "photo-paper", name: "Photo Paper A4", price: 0.50, unit: "per sheet", category: "Paper" },
]

const mockSuppliers: Supplier[] = [
  { id: "1", name: "Paper Supply Co.", mobile: "+968 9111 1111", email: "<EMAIL>", address: "Muscat Industrial Area" },
  { id: "2", name: "Ink Solutions Ltd.", mobile: "+968 9222 2222", email: "<EMAIL>", address: "Ruwi Business District" },
  { id: "3", name: "Office Equipment Pro", mobile: "+968 9333 3333", email: "<EMAIL>", address: "Al Khuwair" },
  { id: "4", name: "Printing Materials Inc.", mobile: "+968 9444 4444", email: "<EMAIL>", address: "Seeb Industrial Zone" },
  { id: "5", name: "Tech Supplies Oman", mobile: "+968 9555 5555", email: "<EMAIL>", address: "Ghala Industrial Area" },
]

export default function CreatePurchasePage() {
  const router = useRouter()
  const { t } = useI18n()
  const [purchaseItems, setPurchaseItems] = useState<PurchaseItem[]>([
    {
      id: "1",
      description: "",
      productId: "",
      quantity: 1,
      unitPrice: 0,
      total: 0
    }
  ])

  const [formData, setFormData] = useState({
    supplierId: "",
    supplierName: "",
    supplierMobile: "",
    expectedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
    taxRate: 5, // Default VAT rate for Oman
    discount: 0,
    discountType: "amount", // "amount" or "percentage"
    notes: "Please deliver during business hours.",
    status: "pending"
  })

  // Settings for VAT (this would come from your settings/config)
  const [vatSettings, setVatSettings] = useState({
    enabled: true,
    rate: 5, // 5% VAT for Oman
    label: "VAT"
  })

  const [totals, setTotals] = useState({
    subtotal: 0,
    discountAmount: 0,
    taxAmount: 0,
    total: 0
  })

  const [suppliers, setSuppliers] = useState<Supplier[]>(mockSuppliers)
  const [supplierSearchOpen, setSupplierSearchOpen] = useState(false)
  const [supplierSearchValue, setSupplierSearchValue] = useState("")
  const [showNewSupplierDialog, setShowNewSupplierDialog] = useState(false)
  const [newSupplierData, setNewSupplierData] = useState({ name: "", mobile: "", email: "", address: "" })
  const [mobileCheckResult, setMobileCheckResult] = useState<Supplier | null>(null)

  // Product search states
  const [productSearchStates, setProductSearchStates] = useState<{[key: string]: {open: boolean, value: string}}>({})

  // Initialize VAT rate from settings
  useEffect(() => {
    if (vatSettings.enabled) {
      setFormData(prev => ({ ...prev, taxRate: vatSettings.rate }))
    }
  }, [vatSettings])

  // Calculate totals whenever items or rates change
  useEffect(() => {
    const subtotal = purchaseItems.reduce((sum, item) => sum + item.total, 0)
    const discountAmount = formData.discountType === "percentage"
      ? (subtotal * formData.discount) / 100
      : formData.discount
    const taxableAmount = subtotal - discountAmount
    const taxAmount = vatSettings.enabled ? (taxableAmount * formData.taxRate) / 100 : 0
    const total = taxableAmount + taxAmount

    setTotals({
      subtotal,
      discountAmount,
      taxAmount,
      total
    })
  }, [purchaseItems, formData.taxRate, formData.discount, formData.discountType, vatSettings.enabled])

  const updatePurchaseItem = (id: string, field: keyof PurchaseItem, value: any) => {
    setPurchaseItems(items => items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value }

        // Auto-fill product details when product is selected
        if (field === 'productId' && value) {
          const product = products.find(p => p.id === value)
          if (product) {
            updatedItem.unitPrice = product.price
            updatedItem.description = product.name
            // Calculate total immediately when product is selected
            updatedItem.total = Number((updatedItem.quantity * product.price).toFixed(2))
          }
        }

        // Recalculate total when quantity or price changes
        if (field === 'quantity' || field === 'unitPrice') {
          updatedItem.total = Number((updatedItem.quantity * updatedItem.unitPrice).toFixed(2))
        }

        return updatedItem
      }
      return item
    }))
  }

  const addPurchaseItem = () => {
    const newItem: PurchaseItem = {
      id: Date.now().toString(),
      description: "",
      productId: "",
      quantity: 1,
      unitPrice: 0,
      total: 0
    }
    setPurchaseItems(prev => [...prev, newItem])
  }

  const removePurchaseItem = (id: string) => {
    if (purchaseItems.length > 1) {
      setPurchaseItems(prev => prev.filter(item => item.id !== id))
    }
  }

  const setProductSearchState = (itemId: string, state: {open?: boolean, value?: string}) => {
    setProductSearchStates(prev => ({
      ...prev,
      [itemId]: { ...prev[itemId], ...state }
    }))
  }

  const getProductSearchState = (itemId: string) => {
    return productSearchStates[itemId] || { open: false, value: "" }
  }

  const checkMobileExists = (mobile: string) => {
    const existing = suppliers.find(s => s.mobile === mobile)
    setMobileCheckResult(existing || null)
    return existing
  }

  const handleSupplierSelect = (supplierId: string) => {
    const supplier = suppliers.find(s => s.id === supplierId)
    if (supplier) {
      setFormData(prev => ({
        ...prev,
        supplierId: supplier.id,
        supplierName: supplier.name,
        supplierMobile: supplier.mobile
      }))
      setSupplierSearchValue(`${supplier.name} (${supplier.mobile})`)
    }
  }

  const handleNewSupplierSave = async () => {
    if (!newSupplierData.name || !newSupplierData.mobile) {
      alert('Please enter both name and mobile number')
      return
    }

    // Check if mobile already exists
    const existingSupplier = checkMobileExists(newSupplierData.mobile)
    if (existingSupplier) {
      alert('A supplier with this mobile number already exists')
      return
    }

    try {
      // Create new supplier
      const newSupplier: Supplier = {
        id: Date.now().toString(),
        name: newSupplierData.name,
        mobile: newSupplierData.mobile,
        email: newSupplierData.email || "",
        address: newSupplierData.address || ""
      }

      // In a real app, you would save to database here
      // await saveSupplierToDatabase(newSupplier)

      setSuppliers(prev => [...prev, newSupplier])

      // Auto-select the newly created supplier
      setFormData(prev => ({
        ...prev,
        supplierId: newSupplier.id,
        supplierName: newSupplier.name,
        supplierMobile: newSupplier.mobile
      }))
      setSupplierSearchValue(`${newSupplier.name} (${newSupplier.mobile})`)

      setShowNewSupplierDialog(false)
      setNewSupplierData({ name: "", mobile: "", email: "", address: "" })
      setMobileCheckResult(null)

      alert('Supplier created successfully!')
    } catch (error) {
      console.error('Error creating supplier:', error)
      alert('Failed to create supplier. Please try again.')
    }
  }

  const handleSave = async (status: string) => {
    // Validation
    if (!formData.supplierId) {
      alert('Please select a supplier')
      return
    }

    if (purchaseItems.length === 0 || purchaseItems.every(item => !item.description && !item.productId)) {
      alert('Please add at least one purchase item')
      return
    }

    // Check if any item has invalid data
    const invalidItems = purchaseItems.filter(item =>
      (item.description || item.productId) && (item.quantity <= 0 || item.unitPrice < 0)
    )

    if (invalidItems.length > 0) {
      alert('Please check item quantities and prices. All items must have valid quantities and non-negative prices.')
      return
    }

    try {
      // Prepare purchase items in the correct format
      const purchaseItemsFormatted = purchaseItems
        .filter(item => item.description || item.productId)
        .map(item => ({
          id: item.id,
          description: item.description,
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          total: item.total
        }))

      // Prepare purchase data for storage
      const purchaseData = {
        date: new Date().toISOString().split('T')[0],
        expectedDate: formData.expectedDate,
        supplier: formData.supplierName || 'Unknown Supplier',
        supplierData: {
          id: formData.supplierId,
          name: formData.supplierName,
          mobile: formData.supplierMobile
        },
        status: status === 'draft' ? 'DRAFT' : 'PENDING' as 'DRAFT' | 'PENDING',
        subtotal: totals.subtotal,
        taxAmount: totals.taxAmount,
        total: totals.total,
        items: purchaseItemsFormatted,
        notes: formData.notes,
      }

      console.log('Saving purchase order:', purchaseData)

      // Save using the shared purchase storage system
      const savedPurchase = addPurchase(purchaseData)

      // Show success message
      alert(`Purchase order ${savedPurchase.number} ${status === 'draft' ? 'saved as draft' : 'created'} successfully!`)

      // Navigate back to purchases list
      router.push('/dashboard/purchases')
    } catch (error) {
      console.error('Error saving purchase order:', error)
      alert('Failed to save purchase order. Please try again.')
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/dashboard/purchases')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('purchases.create.title')}</h2>
            <p className="text-muted-foreground">
              {t('purchases.create.subtitle')}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => handleSave('draft')}
          >
            <Save className="mr-2 h-4 w-4" />
            {t('purchases.create.saveDraft')}
          </Button>
          <Button onClick={() => handleSave('pending')}>
            <Send className="mr-2 h-4 w-4" />
            {t('purchases.create.createPurchaseOrder')}
          </Button>
        </div>
      </div>

      {/* Single Column Layout */}
      <div className="max-w-6xl mx-auto space-y-6">

        {/* 1. Basic Purchase Order Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-5 w-5" />
              {t('purchases.create.purchaseOrderInformation')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="supplier">{t('purchases.create.supplier')}</Label>
                <div className="flex gap-2">
                  <Popover open={supplierSearchOpen} onOpenChange={setSupplierSearchOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={supplierSearchOpen}
                        className="flex-1 justify-between min-w-0"
                      >
                        <span className="truncate">{supplierSearchValue || t('purchases.create.selectSupplierPlaceholder')}</span>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[400px] p-0">
                      <Command>
                        <CommandInput placeholder={t('purchases.create.searchSuppliersPlaceholder')} />
                        <CommandList>
                          <CommandEmpty>
                            <div className="p-4 text-center">
                              <p className="text-sm text-muted-foreground mb-2">{t('purchases.create.noSupplierFound')}</p>
                              <Button
                                size="sm"
                                onClick={() => {
                                  setShowNewSupplierDialog(true)
                                  setSupplierSearchOpen(false)
                                }}
                              >
                                <UserPlus className="mr-2 h-4 w-4" />
                                {t('purchases.create.addNewSupplier')}
                              </Button>
                            </div>
                          </CommandEmpty>
                          <CommandGroup>
                            {suppliers.map((supplier) => (
                              <CommandItem
                                key={supplier.id}
                                value={`${supplier.name} ${supplier.mobile} ${supplier.email || ''}`}
                                keywords={[supplier.name, supplier.mobile, supplier.email || '']}
                                onSelect={() => {
                                  handleSupplierSelect(supplier.id)
                                  setSupplierSearchOpen(false)
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    formData.supplierId === supplier.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                <div className="flex flex-col">
                                  <span className="font-medium">{supplier.name}</span>
                                  <span className="text-sm text-muted-foreground">{supplier.mobile}</span>
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setShowNewSupplierDialog(true)}
                    title={t('purchases.create.addNewSupplier')}
                  >
                    <UserPlus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="expectedDate">{t('purchases.create.expectedDeliveryDate')}</Label>
                <Input
                  id="expectedDate"
                  type="date"
                  value={formData.expectedDate}
                  onChange={(e) => setFormData({...formData, expectedDate: e.target.value})}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="status">{t('purchases.create.purchaseOrderStatus')}</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData({...formData, status: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">{t('purchases.create.statusOptions.draft')}</SelectItem>
                    <SelectItem value="pending">{t('purchases.create.statusOptions.pending')}</SelectItem>
                    <SelectItem value="approved">{t('purchases.create.statusOptions.approved')}</SelectItem>
                    <SelectItem value="ordered">{t('purchases.create.statusOptions.ordered')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">{t('purchases.create.notes')}</Label>
                <Textarea
                  id="notes"
                  placeholder={t('purchases.create.pleaseDeliverDuringBusinessHours')}
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  className="min-h-[60px]"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 2. Purchase Items */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                {t('purchases.create.purchaseItems')}
              </CardTitle>
              <Button onClick={addPurchaseItem} size="sm">
                <Plus className="mr-2 h-4 w-4" />
                {t('purchases.create.addItem')}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="overflow-x-auto">
                <Table className="min-w-full">
                  <TableHeader>
                    <TableRow className="border-b">
                      <TableHead className="min-w-[300px]">{t('purchases.create.description')}</TableHead>
                      <TableHead className="min-w-[200px]">{t('purchases.create.product')}</TableHead>
                      <TableHead className="w-[120px] text-center">{t('purchases.create.quantity')}</TableHead>
                      <TableHead className="w-[140px] text-right">{t('purchases.create.unitPrice')}</TableHead>
                      <TableHead className="w-[140px] text-right">{t('purchases.create.total')}</TableHead>
                      <TableHead className="w-[100px] text-center">{t('purchases.create.action')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {purchaseItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Textarea
                            placeholder={t('purchases.create.itemDescription')}
                            value={item.description}
                            onChange={(e) => updatePurchaseItem(item.id, 'description', e.target.value)}
                            className="min-h-[60px] resize-none"
                          />
                        </TableCell>
                        <TableCell>
                          <Popover
                            open={getProductSearchState(item.id).open}
                            onOpenChange={(open) => setProductSearchState(item.id, { open })}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                role="combobox"
                                className="w-full justify-between"
                              >
                                {item.productId ?
                                  products.find(p => p.id === item.productId)?.name || "Select product"
                                  : "Select product"
                                }
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[300px] p-0">
                              <Command>
                                <CommandInput placeholder="Search products..." />
                                <CommandList>
                                  <CommandEmpty>No product found.</CommandEmpty>
                                  <CommandGroup heading="Products">
                                    {products.map((product) => (
                                      <CommandItem
                                        key={product.id}
                                        value={`${product.name} ${product.category} ${product.unit}`}
                                        keywords={[product.name, product.category, product.unit]}
                                        onSelect={() => {
                                          updatePurchaseItem(item.id, 'productId', product.id)
                                          setProductSearchState(item.id, { open: false })
                                        }}
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            item.productId === product.id ? "opacity-100" : "opacity-0"
                                          )}
                                        />
                                        <div className="flex flex-col">
                                          <span className="font-medium">{product.name}</span>
                                          <span className="text-sm text-muted-foreground">
                                            {formatCurrency(product.price)} {product.unit} • {product.category}
                                          </span>
                                        </div>
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                        </TableCell>
                        <TableCell className="text-center">
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.quantity}
                            onChange={(e) => updatePurchaseItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                            className="w-full text-center"
                            placeholder={t('purchases.create.quantityPlaceholder')}
                          />
                        </TableCell>
                        <TableCell className="text-right">
                          <Input
                            type="number"
                            min="0"
                            step="0.001"
                            value={item.unitPrice}
                            onChange={(e) => updatePurchaseItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                            className="w-full text-right"
                            placeholder="0.000"
                          />
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="font-medium text-lg">
                            {formatCurrency(item.total)}
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removePurchaseItem(item.id)}
                            disabled={purchaseItems.length === 1}
                            className="mx-auto"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 3. VAT and Discount Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calculator className="mr-2 h-5 w-5" />
              {vatSettings.label} & {t('purchases.create.discountSettings')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {vatSettings.enabled && (
                <div className="space-y-2">
                  <Label htmlFor="taxRate">{t('purchases.create.vatRatePercent')}</Label>
                  <Input
                    id="taxRate"
                    type="number"
                    min="0"
                    max="100"
                    step="0.1"
                    value={formData.taxRate}
                    onChange={(e) => setFormData({...formData, taxRate: parseFloat(e.target.value) || 0})}
                    disabled={!vatSettings.enabled}
                  />
                  <p className="text-xs text-muted-foreground">
                    {t('purchases.create.defaultRateFromSettings')}: {vatSettings.rate}%
                  </p>
                </div>
              )}
              <div className="space-y-2">
                <Label htmlFor="discount">{t('purchases.create.discount')}</Label>
                <div className="flex gap-2">
                  <Select
                    value={formData.discountType}
                    onValueChange={(value: "amount" | "percentage") => setFormData({...formData, discountType: value})}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="amount">Amount</SelectItem>
                      <SelectItem value="percentage">Percentage</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    id="discount"
                    type="number"
                    min="0"
                    max={formData.discountType === "percentage" ? "100" : undefined}
                    step="0.01"
                    value={formData.discount}
                    onChange={(e) => setFormData({...formData, discount: parseFloat(e.target.value) || 0})}
                    placeholder={formData.discountType === "percentage" ? "0.00%" : "0.00 OMR"}
                    className="flex-1"
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  {formData.discountType === "percentage"
                    ? "Enter percentage (0-100%)"
                    : "Enter amount in OMR"
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 4. Purchase Order Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="mr-2 h-5 w-5" />
              {t('purchases.create.purchaseOrderSummary')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Summary Details */}
              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between text-lg">
                    <span>{t('purchases.create.subtotal')}:</span>
                    <span className="font-medium">{formatCurrency(totals.subtotal)}</span>
                  </div>

                  {formData.discount > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>
                        {t('purchases.create.discount')} ({formData.discountType === "percentage"
                          ? `${formData.discount}%`
                          : formatCurrency(formData.discount)
                        }):
                      </span>
                      <span className="font-medium">-{formatCurrency(totals.discountAmount)}</span>
                    </div>
                  )}

                  {vatSettings.enabled && (
                    <div className="flex justify-between text-lg">
                      <span>{vatSettings.label} ({formData.taxRate}%):</span>
                      <span className="font-medium">{formatCurrency(totals.taxAmount)}</span>
                    </div>
                  )}

                  <Separator />

                  <div className="flex justify-between text-2xl font-bold text-primary">
                    <span>{t('purchases.create.total')}:</span>
                    <span>{formatCurrency(totals.total)}</span>
                  </div>
                </div>
              </div>

              {/* Purchase Stats */}
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-muted/50 rounded-lg text-center">
                    <div className="text-2xl font-bold text-primary">{purchaseItems.length}</div>
                    <div className="text-sm text-muted-foreground">{t('purchases.create.items')}</div>
                  </div>
                  <div className="p-4 bg-muted/50 rounded-lg text-center">
                    <div className="text-2xl font-bold text-primary capitalize">{formData.status}</div>
                    <div className="text-sm text-muted-foreground">{t('purchases.create.status')}</div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => handleSave('draft')}
                    size="lg"
                  >
                    <Save className="mr-2 h-4 w-4" />
                    {t('purchases.create.saveAsDraft')}
                  </Button>
                  <Button
                    className="w-full"
                    onClick={() => handleSave('pending')}
                    size="lg"
                  >
                    <Send className="mr-2 h-4 w-4" />
                    {t('purchases.create.createPurchaseOrder')}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* New Supplier Dialog */}
      <Dialog open={showNewSupplierDialog} onOpenChange={setShowNewSupplierDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t('purchases.create.addNewSupplier')}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="supplierName">{t('purchases.create.supplierName')}</Label>
              <Input
                id="supplierName"
                placeholder={t('purchases.create.enterSupplierName')}
                value={newSupplierData.name}
                onChange={(e) => setNewSupplierData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="supplierMobile">{t('purchases.create.mobileNumber')}</Label>
              <Input
                id="supplierMobile"
                placeholder="+968 9XXX XXXX"
                value={newSupplierData.mobile}
                onChange={(e) => {
                  const mobile = e.target.value
                  setNewSupplierData(prev => ({ ...prev, mobile }))
                  if (mobile.length >= 8) {
                    checkMobileExists(mobile)
                  } else {
                    setMobileCheckResult(null)
                  }
                }}
              />
              {mobileCheckResult && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-sm text-yellow-800">
                    <strong>{t('purchases.create.supplierExists')}:</strong> {mobileCheckResult.name}
                  </p>
                  <p className="text-xs text-yellow-600 mt-1">
                    {t('purchases.create.selectSupplierFromDropdown')}
                  </p>
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="supplierEmail">{t('purchases.create.email')}</Label>
              <Input
                id="supplierEmail"
                type="email"
                placeholder="<EMAIL>"
                value={newSupplierData.email}
                onChange={(e) => setNewSupplierData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="supplierAddress">{t('purchases.create.address')}</Label>
              <Textarea
                id="supplierAddress"
                placeholder={t('purchases.create.supplierAddress')}
                value={newSupplierData.address}
                onChange={(e) => setNewSupplierData(prev => ({ ...prev, address: e.target.value }))}
                className="min-h-[60px]"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                setShowNewSupplierDialog(false)
                setNewSupplierData({ name: "", mobile: "", email: "", address: "" })
                setMobileCheckResult(null)
              }}
            >
              {t('purchases.create.cancel')}
            </Button>
            <Button
              onClick={handleNewSupplierSave}
              disabled={!newSupplierData.name || !newSupplierData.mobile || !!mobileCheckResult}
            >
              {t('purchases.create.addSupplier')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
