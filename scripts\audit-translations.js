#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to recursively find all .tsx and .ts files
function findFiles(dir, extensions = ['.tsx', '.ts']) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Skip node_modules and .next directories
      if (!['node_modules', '.next', 'dist', 'build'].includes(file)) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      if (extensions.some(ext => file.endsWith(ext))) {
        results.push(filePath);
      }
    }
  });
  
  return results;
}

// Function to extract translation keys from file content
function extractTranslationKeys(content) {
  const keys = new Set();
  
  // Match t('key') and t("key") patterns
  const regex = /t\(['"`]([^'"`]+)['"`]\)/g;
  let match;
  
  while ((match = regex.exec(content)) !== null) {
    keys.add(match[1]);
  }
  
  return Array.from(keys);
}

// Function to load existing translations
function loadExistingTranslations() {
  const translations = new Set();
  
  try {
    // Load from src/lib/translations/ar.ts
    const arTranslationsPath = path.join(__dirname, '../src/lib/translations/ar.ts');
    if (fs.existsSync(arTranslationsPath)) {
      const content = fs.readFileSync(arTranslationsPath, 'utf8');
      // Extract keys from the translation object
      const keyRegex = /(\w+):\s*["']/g;
      let match;
      while ((match = keyRegex.exec(content)) !== null) {
        translations.add(match[1]);
      }
    }
    
    // Load from messages/ar.json
    const messagesPath = path.join(__dirname, '../messages/ar.json');
    if (fs.existsSync(messagesPath)) {
      const messages = JSON.parse(fs.readFileSync(messagesPath, 'utf8'));
      function addKeys(obj, prefix = '') {
        for (const key in obj) {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          translations.add(fullKey);
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            addKeys(obj[key], fullKey);
          }
        }
      }
      addKeys(messages);
    }
  } catch (error) {
    console.error('Error loading existing translations:', error);
  }
  
  return translations;
}

// Main audit function
function auditTranslations() {
  console.log('🔍 Auditing translations...\n');
  
  // Find all component files
  const srcDir = path.join(__dirname, '../src');
  const files = findFiles(srcDir);
  
  console.log(`📁 Found ${files.length} files to scan\n`);
  
  // Extract all translation keys used in components
  const usedKeys = new Set();
  const fileKeyMap = new Map();
  
  files.forEach(file => {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const keys = extractTranslationKeys(content);
      
      if (keys.length > 0) {
        fileKeyMap.set(file, keys);
        keys.forEach(key => usedKeys.add(key));
      }
    } catch (error) {
      console.error(`Error reading file ${file}:`, error);
    }
  });
  
  // Load existing translations
  const existingTranslations = loadExistingTranslations();
  
  // Find missing translations
  const missingKeys = Array.from(usedKeys).filter(key => !existingTranslations.has(key));
  
  // Group missing keys by module
  const moduleGroups = {
    common: [],
    navigation: [],
    dashboard: [],
    customers: [],
    suppliers: [],
    products: [],
    invoices: [],
    quotations: [],
    tasks: [],
    projects: [],
    calendar: [],
    employees: [],
    reports: [],
    settings: [],
    financial: [],
    other: []
  };
  
  missingKeys.forEach(key => {
    const module = key.split('.')[0];
    if (moduleGroups[module]) {
      moduleGroups[module].push(key);
    } else {
      moduleGroups.other.push(key);
    }
  });
  
  // Generate report
  console.log('📊 TRANSLATION AUDIT REPORT');
  console.log('=' .repeat(50));
  console.log(`Total translation keys used: ${usedKeys.size}`);
  console.log(`Existing translations: ${existingTranslations.size}`);
  console.log(`Missing translations: ${missingKeys.length}`);
  console.log(`Coverage: ${((existingTranslations.size / usedKeys.size) * 100).toFixed(1)}%\n`);
  
  // Report missing keys by module
  Object.entries(moduleGroups).forEach(([module, keys]) => {
    if (keys.length > 0) {
      console.log(`🔴 ${module.toUpperCase()} MODULE (${keys.length} missing):`);
      keys.sort().forEach(key => {
        console.log(`   - ${key}`);
      });
      console.log('');
    }
  });
  
  // Show files with most missing translations
  console.log('📄 FILES WITH MISSING TRANSLATIONS:');
  console.log('-'.repeat(50));
  
  const fileMissingCount = new Map();
  fileKeyMap.forEach((keys, file) => {
    const missing = keys.filter(key => missingKeys.includes(key));
    if (missing.length > 0) {
      fileMissingCount.set(file, missing);
    }
  });
  
  // Sort files by number of missing translations
  const sortedFiles = Array.from(fileMissingCount.entries())
    .sort((a, b) => b[1].length - a[1].length);
  
  sortedFiles.forEach(([file, missing]) => {
    const relativePath = path.relative(process.cwd(), file);
    console.log(`${relativePath} (${missing.length} missing)`);
    missing.forEach(key => {
      console.log(`   - ${key}`);
    });
    console.log('');
  });
  
  // Generate task list
  console.log('✅ RECOMMENDED TASKS:');
  console.log('-'.repeat(50));
  
  const priorityModules = ['common', 'navigation', 'customers', 'suppliers'];
  const mediumModules = ['dashboard', 'products', 'invoices'];
  const lowModules = ['quotations', 'tasks', 'projects', 'calendar', 'employees', 'reports', 'settings', 'financial'];
  
  priorityModules.forEach(module => {
    if (moduleGroups[module].length > 0) {
      console.log(`🔥 HIGH PRIORITY: ${module} module (${moduleGroups[module].length} translations)`);
    }
  });
  
  mediumModules.forEach(module => {
    if (moduleGroups[module].length > 0) {
      console.log(`🟡 MEDIUM PRIORITY: ${module} module (${moduleGroups[module].length} translations)`);
    }
  });
  
  lowModules.forEach(module => {
    if (moduleGroups[module].length > 0) {
      console.log(`🟢 LOW PRIORITY: ${module} module (${moduleGroups[module].length} translations)`);
    }
  });
  
  if (moduleGroups.other.length > 0) {
    console.log(`❓ UNKNOWN MODULE: other (${moduleGroups.other.length} translations)`);
  }
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Start with HIGH PRIORITY modules');
  console.log('2. Create modular translation files');
  console.log('3. Add missing translations systematically');
  console.log('4. Test each module after completion');
}

// Run the audit
auditTranslations(); 