import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { serializeBigInt } from '@/lib/json-utils'

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: projectId } = await params

    const folders = await prisma.projectFolder.findMany({
      where: {
        projectId
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        documents: {
          select: {
            id: true,
            name: true,
            type: true,
            size: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(serializeBigInt(folders))
  } catch (error) {
    console.error('Error fetching project folders:', error)
    return NextResponse.json({ error: 'Failed to fetch folders' }, { status: 500 })
  }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: projectId } = await params
    const { name, description } = await request.json()

    if (!name?.trim()) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 })
    }

    // For now, we'll use a default user ID - in a real app, get this from authentication
    const createdById = "user_1" // Replace with actual user ID from session

    const folder = await prisma.projectFolder.create({
      data: {
        name: name.trim(),
        description,
        projectId,
        createdById
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        documents: {
          select: {
            id: true,
            name: true,
            type: true,
            size: true
          }
        }
      }
    })

    return NextResponse.json(serializeBigInt(folder), { status: 201 })
  } catch (error) {
    console.error('Error creating project folder:', error)
    return NextResponse.json({ error: 'Failed to create folder' }, { status: 500 })
  }
} 