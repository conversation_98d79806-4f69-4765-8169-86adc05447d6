import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedLeads() {
  console.log('Seeding leads...')

  // Get the first user to assign leads to
  const user = await prisma.user.findFirst()
  
  const leads = [
    {
      name: '<PERSON>',
      nameAr: 'أحمد الراشد',
      email: '<EMAIL>',
      mobile: '+968 9123 4567',
      phone: '+968 2456 7890',
      address: 'Building 12, Way 456, Al-Khuwair, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Al-Rashid Trading',
      source: 'WEBSITE',
      status: 'NEW',
      notes: 'Interested in printing services for business cards and brochures',
      assignedToId: user?.id,
    },
    {
      name: '<PERSON><PERSON> Al<PERSON>Zahra',
      nameAr: 'فاطمة الزهراء',
      email: '<EMAIL>',
      mobile: '+968 9234 5678',
      phone: '+968 2567 8901',
      address: 'Office 25, Building 8, Ruwi, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Zahra Enterprises',
      source: 'PHONE',
      status: 'CONTACTED',
      notes: 'Called regarding large format printing for exhibition banners',
      assignedToId: user?.id,
    },
    {
      name: 'Mohammed Al-Balushi',
      nameAr: 'محمد البلوشي',
      email: '<EMAIL>',
      mobile: '+968 9345 6789',
      address: 'Villa 15, Al-Hail, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Balushi Construction',
      source: 'REFERRAL',
      status: 'QUALIFIED',
      notes: 'Referred by existing customer. Needs safety signage printing',
      assignedToId: user?.id,
    },
    {
      name: 'Sarah Al-Kindi',
      nameAr: 'سارة الكندي',
      email: '<EMAIL>',
      mobile: '+968 9456 7890',
      phone: '+968 2678 9012',
      address: 'School Complex, Al-Seeb, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Al-Kindi School',
      source: 'EMAIL',
      status: 'PROPOSAL',
      notes: 'Educational institution needing student certificates and promotional materials',
      assignedToId: user?.id,
    },
    {
      name: 'Omar Al-Hinai',
      nameAr: 'عمر الهنائي',
      email: '<EMAIL>',
      mobile: '+968 9567 8901',
      address: 'Restaurant Location, Qurum, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Hinai Restaurant',
      source: 'SOCIAL_MEDIA',
      status: 'NEGOTIATION',
      notes: 'Restaurant chain looking for menu printing and promotional materials',
      assignedToId: user?.id,
    },
    {
      name: 'Aisha Al-Mamari',
      nameAr: 'عائشة المعمري',
      email: '<EMAIL>',
      mobile: '+968 9678 9012',
      phone: '+968 2789 0123',
      address: 'Event Hall, Al-Khoud, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Mamari Events',
      source: 'ADVERTISEMENT',
      status: 'NEW',
      notes: 'Event management company interested in invitation and banner printing',
      assignedToId: user?.id,
    },
    {
      name: 'Khalid Al-Busaidi',
      nameAr: 'خالد البوسعيدي',
      email: '<EMAIL>',
      mobile: '+968 9789 0123',
      address: 'Medical Center, Al-Ghubra, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Busaidi Medical Center',
      source: 'WALK_IN',
      status: 'CONTACTED',
      notes: 'Medical center needing patient forms and informational brochures',
      assignedToId: user?.id,
    },
    {
      name: 'Maryam Al-Siyabi',
      nameAr: 'مريم السيابي',
      email: '<EMAIL>',
      mobile: '+968 9890 1234',
      phone: '+968 2890 1234',
      address: 'Fashion Store, City Center, Muscat',
      city: 'Muscat',
      country: 'Oman',
      company: 'Siyabi Fashion',
      source: 'OTHER',
      status: 'LOST',
      notes: 'Fashion retailer - decided to go with competitor due to pricing',
      assignedToId: user?.id,
    },
  ]

  for (const leadData of leads) {
    try {
      await prisma.lead.create({
        data: leadData as any,
      })
      console.log(`Created lead: ${leadData.name}`)
    } catch (error) {
      console.error(`Error creating lead ${leadData.name}:`, error)
    }
  }

  console.log('Leads seeding completed!')
}

seedLeads()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
