const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

const defaultUnits = [
  {
    name: 'Piece',
    nameAr: 'قطعة',
    symbol: 'pc',
    symbolAr: 'ق',
    description: 'Individual items or units',
    descriptionAr: 'عناصر أو وحدات فردية',
  },
  {
    name: 'Page',
    nameAr: 'صفحة',
    symbol: 'pg',
    symbolAr: 'ص',
    description: 'Single page for printing services',
    descriptionAr: 'صفحة واحدة لخدمات الطباعة',
  },
  {
    name: 'Ream',
    nameAr: 'رزمة',
    symbol: 'ream',
    symbolAr: 'رزمة',
    description: '500 sheets of paper',
    descriptionAr: '500 ورقة',
  },
  {
    name: 'Hour',
    nameAr: 'ساعة',
    symbol: 'hr',
    symbolAr: 'س',
    description: 'Time-based services',
    descriptionAr: 'الخدمات القائمة على الوقت',
  },
  {
    name: 'Set',
    nameAr: 'مجموعة',
    symbol: 'set',
    symbolAr: 'مج',
    description: 'Collection of related items',
    descriptionAr: 'مجموعة من العناصر ذات الصلة',
  },
  {
    name: 'Box',
    nameAr: 'صندوق',
    symbol: 'box',
    symbolAr: 'صندوق',
    description: 'Boxed items or packages',
    descriptionAr: 'عناصر معبأة أو حزم',
  },
  {
    name: 'Kilogram',
    nameAr: 'كيلوغرام',
    symbol: 'kg',
    symbolAr: 'كغ',
    description: 'Weight measurement',
    descriptionAr: 'قياس الوزن',
  },
  {
    name: 'Meter',
    nameAr: 'متر',
    symbol: 'm',
    symbolAr: 'م',
    description: 'Length measurement',
    descriptionAr: 'قياس الطول',
  },
]

async function seedUnits() {
  console.log('Seeding units...')
  
  for (const unit of defaultUnits) {
    try {
      const existingUnit = await prisma.unit.findFirst({
        where: { name: unit.name }
      })
      
      if (!existingUnit) {
        await prisma.unit.create({
          data: unit
        })
        console.log(`✅ Created unit: ${unit.name}`)
      } else {
        console.log(`⏭️  Unit already exists: ${unit.name}`)
      }
    } catch (error) {
      console.error(`❌ Error creating unit ${unit.name}:`, error)
    }
  }
  
  console.log('Units seeding completed!')
}

seedUnits()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
