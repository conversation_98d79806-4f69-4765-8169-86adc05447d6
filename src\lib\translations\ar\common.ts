export const common = {
  // Basic actions
  save: "حفظ",
  cancel: "إلغاء",
  delete: "حذف",
  edit: "تعديل",
  add: "إضافة",
  create: "إنشاء",
  update: "تحديث",
  view: "عرض",
  search: "بحث",
  filter: "تصفية",
  export: "تصدير",
  import: "استيراد",
  print: "طباعة",
  download: "تحميل",
  upload: "رفع",
  loading: "جاري التحميل...",
  noData: "لا توجد بيانات متاحة",
  confirm: "تأكيد",
  yes: "نعم",
  no: "لا",
  back: "رجوع",
  next: "التالي",
  previous: "السابق",
  close: "إغلاق",
  submit: "إرسال",
  reset: "إعادة تعيين",
  clear: "مسح",
  select: "اختيار",
  all: "الكل",
  none: "لا شيء",

  // Status and states
  actions: "الإجراءات",
  status: "الحالة",
  active: "نشط",
  inactive: "غير نشط",
  pending: "معلق",
  approved: "موافق عليه",
  rejected: "مرفوض",
  completed: "مكتمل",
  cancelled: "ملغي",
  draft: "مسودة",
  sent: "مرسل",
  paid: "مدفوع",
  unpaid: "غير مدفوع",
  partial: "مدفوع جزئياً",
  overdue: "متأخر",

  // Data fields
  date: "التاريخ",
  time: "الوقت",
  total: "المجموع",
  subtotal: "المجموع الفرعي",
  tax: "الضريبة",
  discount: "الخصم",
  amount: "المبلغ",
  quantity: "الكمية",
  price: "السعر",
  cost: "التكلفة",
  balance: "الرصيد",
  
  // Contact information
  name: "الاسم",
  email: "البريد الإلكتروني",
  phone: "الهاتف",
  mobile: "الجوال",
  address: "العنوان",
  city: "المدينة",
  country: "البلد",
  company: "الشركة",
  contact: "جهة الاتصال",
  contactInformation: "معلومات الاتصال",

  // Business entities
  customer: "العميل",
  supplier: "المورد",
  employee: "الموظف",
  product: "المنتج",
  service: "الخدمة",
  category: "الفئة",
  invoice: "الفاتورة",
  quotation: "عرض السعر",
  task: "المهمة",
  project: "المشروع",
  report: "التقرير",

  // Dates and time
  startDate: "تاريخ البداية",
  endDate: "تاريخ النهاية",
  dueDate: "تاريخ الاستحقاق",
  createdAt: "تاريخ الإنشاء",
  updatedAt: "تاريخ التحديث",
  created: "تم الإنشاء",
  today: "اليوم",
  yesterday: "أمس",
  tomorrow: "غداً",
  thisWeek: "هذا الأسبوع",
  thisMonth: "هذا الشهر",
  thisYear: "هذا العام",

  // Priorities and levels
  priority: "الأولوية",
  low: "منخفض",
  medium: "متوسط",
  high: "عالي",
  urgent: "عاجل",
  critical: "حرج",

  // Common UI elements
  description: "الوصف",
  notes: "الملاحظات",
  comments: "التعليقات",
  attachments: "المرفقات",
  settings: "الإعدادات",
  profile: "الملف الشخصي",
  logout: "تسجيل الخروج",
  dashboard: "لوحة القيادة",
  menu: "القائمة",
  home: "الرئيسية",
  
  // Theme and appearance
  theme: "المظهر",
  light: "فاتح",
  dark: "داكن",
  system: "النظام",
  language: "اللغة",

  // Search and filters
  searchPlaceholder: "بحث...",
  filterBy: "تصفية حسب",
  sortBy: "ترتيب حسب",
  allStatuses: "جميع الحالات",
  allCategories: "جميع الفئات",
  allTypes: "جميع الأنواع",
  allPriorities: "جميع الأولويات",
  allSources: "جميع المصادر",

  // Assignment and ownership
  assignedTo: "مُكلف إلى",
  createdBy: "أنشأ بواسطة",
  updatedBy: "حُدث بواسطة",
  owner: "المالك",
  manager: "المدير",
  client: "العميل",
  unassigned: "غير مُكلف",

  // Progress and completion
  progress: "التقدم",
  percentage: "النسبة المئوية",
  budget: "الميزانية",
  spent: "المُنفق",
  remaining: "المتبقي",

  // Common messages
  success: "نجح",
  error: "خطأ",
  warning: "تحذير",
  info: "معلومات",
  required: "مطلوب",
  optional: "اختياري",
  
  // Other common terms
  source: "المصدر",
  type: "النوع",
  reference: "المرجع",
  code: "الرمز",
  number: "الرقم",
  version: "الإصدار",
  other: "أخرى",
  unknown: "غير معروف",
  
  // File and document related
  file: "الملف",
  document: "الوثيقة",
  image: "الصورة",
  folder: "المجلد",
  size: "الحجم",
  format: "التنسيق",
  comingSoon: "قريباً",
}; 