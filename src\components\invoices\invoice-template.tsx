"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { formatCurrency, formatDate, formatOmaniAddress, calculateOmaniTax } from "@/lib/localization"

interface InvoiceItem {
  id: string
  description: string
  descriptionAr?: string
  quantity: number
  unitPrice: number
  total: number
  product?: {
    name: string
    nameAr?: string
    unit: string
  }
}

interface Customer {
  name: string
  nameAr?: string
  company?: string
  companyAr?: string
  phone: string
  email?: string
  civilId?: string
  taxNumber?: string
  governorate?: string
  wilayat?: string
  area?: string
  street?: string
  building?: string
  postalCode?: string
}

interface Payment {
  id: string
  amount: number
  method: string
  date: string
  reference?: string
  notes?: string
}

interface InvoiceTemplateProps {
  invoice: {
    id: string
    number: string
    date: string
    dueDate?: string
    status: string
    subtotal: number
    taxAmount: number
    discount: number
    total: number
    notes?: string
    customer: Customer
    items: InvoiceItem[]
    payments?: Payment[]
    amountPaid?: number
    balance?: number
  }
  company: {
    name: string
    nameAr: string
    address: string
    addressAr: string
    phone: string
    email: string
    taxNumber: string
    logo?: string
    termsConditions?: string
    termsConditionsAr?: string
    signature?: string
    stamp?: string
  }
  locale?: string
}

export function InvoiceTemplate({ invoice, company, locale = "en" }: InvoiceTemplateProps) {
  const isArabic = locale === "ar"

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PAID": return "bg-green-100 text-green-800"
      case "PARTIAL": return "bg-yellow-100 text-yellow-800"
      case "UNPAID": return "bg-red-100 text-red-800"
      case "OVERDUE": return "bg-purple-100 text-purple-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusText = (status: string) => {
    if (isArabic) {
      switch (status) {
        case "PAID": return "مدفوع"
        case "PARTIAL": return "مدفوع جزئياً"
        case "UNPAID": return "غير مدفوع"
        case "OVERDUE": return "متأخر"
        default: return status
      }
    }
    return status
  }

  return (
    <div className={`max-w-4xl mx-auto p-8 bg-white ${isArabic ? 'rtl' : 'ltr'}`} dir={isArabic ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div className="flex-1">
          {company.logo && (
            <img src={company.logo} alt="Company Logo" className="h-16 mb-4" />
          )}
          <h1 className="text-2xl font-bold text-gray-900">
            {isArabic ? company.nameAr : company.name}
          </h1>
          <p className="text-gray-600 mt-2">
            {isArabic ? company.addressAr : company.address}
          </p>
          <p className="text-gray-600">{company.phone}</p>
          <p className="text-gray-600">{company.email}</p>
          {company.taxNumber && (
            <p className="text-gray-600">
              {isArabic ? "الرقم الضريبي: " : "Tax Number: "}{company.taxNumber}
            </p>
          )}
        </div>

        <div className="text-right">
          <h2 className="text-3xl font-bold text-blue-600 mb-2">
            {isArabic ? "فاتورة" : "INVOICE"}
          </h2>
          <div className="space-y-1">
            <p className="text-lg font-semibold">{invoice.number}</p>
            <Badge className={getStatusColor(invoice.status)}>
              {getStatusText(invoice.status)}
            </Badge>
          </div>
        </div>
      </div>

      {/* Invoice Details */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-900">
            {isArabic ? "تفاصيل الفاتورة" : "Invoice Details"}
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">
                {isArabic ? "تاريخ الفاتورة:" : "Invoice Date:"}
              </span>
              <span>{formatDate(invoice.date, locale)}</span>
            </div>
            {invoice.dueDate && (
              <div className="flex justify-between">
                <span className="text-gray-600">
                  {isArabic ? "تاريخ الاستحقاق:" : "Due Date:"}
                </span>
                <span>{formatDate(invoice.dueDate, locale)}</span>
              </div>
            )}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3 text-gray-900">
            {isArabic ? "بيانات العميل" : "Bill To"}
          </h3>
          <div className="space-y-1">
            <p className="font-semibold">
              {isArabic && invoice.customer.nameAr
                ? invoice.customer.nameAr
                : invoice.customer.name}
            </p>
            {invoice.customer.company && (
              <p className="text-gray-600">
                {isArabic && invoice.customer.companyAr
                  ? invoice.customer.companyAr
                  : invoice.customer.company}
              </p>
            )}
            <p className="text-gray-600">{invoice.customer.phone}</p>
            {invoice.customer.email && (
              <p className="text-gray-600">{invoice.customer.email}</p>
            )}
            {invoice.customer.civilId && (
              <p className="text-gray-600">
                {isArabic ? "الرقم المدني: " : "Civil ID: "}{invoice.customer.civilId}
              </p>
            )}
            {invoice.customer.taxNumber && (
              <p className="text-gray-600">
                {isArabic ? "الرقم الضريبي: " : "Tax Number: "}{invoice.customer.taxNumber}
              </p>
            )}
            {(invoice.customer.governorate || invoice.customer.area) && (
              <p className="text-gray-600">
                {formatOmaniAddress({
                  building: invoice.customer.building,
                  street: invoice.customer.street,
                  area: invoice.customer.area,
                  wilayat: invoice.customer.wilayat,
                  governorate: invoice.customer.governorate,
                  postalCode: invoice.customer.postalCode,
                }, locale)}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-8">
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b-2 border-gray-300">
              <th className="text-left py-3 px-2 font-semibold">
                {isArabic ? "الوصف" : "Description"}
              </th>
              <th className="text-center py-3 px-2 font-semibold w-20">
                {isArabic ? "الكمية" : "Qty"}
              </th>
              <th className="text-right py-3 px-2 font-semibold w-24">
                {isArabic ? "سعر الوحدة" : "Unit Price"}
              </th>
              <th className="text-right py-3 px-2 font-semibold w-24">
                {isArabic ? "المجموع" : "Total"}
              </th>
            </tr>
          </thead>
          <tbody>
            {invoice.items.map((item, index) => (
              <tr key={item.id} className="border-b border-gray-200">
                <td className="py-3 px-2">
                  <div>
                    <p className="font-medium">
                      {isArabic && item.descriptionAr
                        ? item.descriptionAr
                        : item.description}
                    </p>
                    {item.product && (
                      <p className="text-sm text-gray-600">
                        {isArabic && item.product.nameAr
                          ? item.product.nameAr
                          : item.product.name}
                      </p>
                    )}
                  </div>
                </td>
                <td className="py-3 px-2 text-center">
                  {item.quantity} {item.product?.unit || ''}
                </td>
                <td className="py-3 px-2 text-right">
                  {formatCurrency(item.unitPrice, locale)}
                </td>
                <td className="py-3 px-2 text-right font-medium">
                  {formatCurrency(item.total, locale)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-80">
          <div className="space-y-2">
            <div className="flex justify-between py-2">
              <span className="text-gray-600">
                {isArabic ? "المجموع الفرعي:" : "Subtotal:"}
              </span>
              <span>{formatCurrency(invoice.subtotal, locale)}</span>
            </div>

            {invoice.discount > 0 && (
              <div className="flex justify-between py-2">
                <span className="text-gray-600">
                  {isArabic ? "الخصم:" : "Discount:"}
                </span>
                <span>-{formatCurrency(invoice.discount, locale)}</span>
              </div>
            )}

            <div className="flex justify-between py-2">
              <span className="text-gray-600">
                {isArabic ? "ضريبة القيمة المضافة (5%):" : "VAT (5%):"}
              </span>
              <span>{formatCurrency(invoice.taxAmount, locale)}</span>
            </div>

            <Separator />

            <div className="flex justify-between py-3 text-lg font-bold">
              <span>{isArabic ? "المجموع الكلي:" : "Total:"}</span>
              <span className="text-blue-600">
                {formatCurrency(invoice.total, locale)}
              </span>
            </div>

            {/* Payment Information */}
            {(invoice.amountPaid !== undefined && invoice.amountPaid > 0) && (
              <>
                <Separator className="my-2" />
                <div className="flex justify-between py-2 text-green-600">
                  <span>{isArabic ? "المبلغ المدفوع:" : "Amount Paid:"}</span>
                  <span className="font-medium">
                    {formatCurrency(invoice.amountPaid, locale)}
                  </span>
                </div>
                {(invoice.balance !== undefined && invoice.balance > 0) && (
                  <div className="flex justify-between py-2 text-orange-600 font-medium">
                    <span>{isArabic ? "الرصيد المستحق:" : "Balance Due:"}</span>
                    <span>{formatCurrency(invoice.balance, locale)}</span>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Payment History */}
      {invoice.payments && invoice.payments.length > 0 && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-4">
            {isArabic ? "سجل المدفوعات:" : "Payment History:"}
          </h3>
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-50">
                <th className="border border-gray-300 py-2 px-3 text-left font-semibold">
                  {isArabic ? "التاريخ" : "Date"}
                </th>
                <th className="border border-gray-300 py-2 px-3 text-left font-semibold">
                  {isArabic ? "المبلغ" : "Amount"}
                </th>
                <th className="border border-gray-300 py-2 px-3 text-left font-semibold">
                  {isArabic ? "طريقة الدفع" : "Payment Method"}
                </th>
                <th className="border border-gray-300 py-2 px-3 text-left font-semibold">
                  {isArabic ? "المرجع" : "Reference"}
                </th>
                {invoice.payments.some(p => p.notes) && (
                  <th className="border border-gray-300 py-2 px-3 text-left font-semibold">
                    {isArabic ? "ملاحظات" : "Notes"}
                  </th>
                )}
              </tr>
            </thead>
            <tbody>
              {invoice.payments.map((payment) => (
                <tr key={payment.id}>
                  <td className="border border-gray-300 py-2 px-3">
                    {formatDate(payment.date, locale)}
                  </td>
                  <td className="border border-gray-300 py-2 px-3 text-green-600 font-medium">
                    {formatCurrency(payment.amount, locale)}
                  </td>
                  <td className="border border-gray-300 py-2 px-3">
                    {payment.method}
                  </td>
                  <td className="border border-gray-300 py-2 px-3 font-mono text-sm">
                    {payment.reference || '-'}
                  </td>
                  {invoice.payments.some(p => p.notes) && (
                    <td className="border border-gray-300 py-2 px-3 text-sm">
                      {payment.notes || '-'}
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Notes */}
      {invoice.notes && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">
            {isArabic ? "ملاحظات:" : "Notes:"}
          </h3>
          <p className="text-gray-600 bg-gray-50 p-4 rounded">
            {invoice.notes}
          </p>
        </div>
      )}

      {/* Terms & Conditions */}
      {(company.termsConditions || company.termsConditionsAr) && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-3">
            {isArabic ? "الشروط والأحكام:" : "Terms & Conditions:"}
          </h3>
          <div className="text-sm text-gray-600 bg-gray-50 p-4 rounded">
            {isArabic && company.termsConditionsAr
              ? company.termsConditionsAr
              : company.termsConditions}
          </div>
        </div>
      )}

      {/* Signature and Stamp Section */}
      {(company.signature || company.stamp) && (
        <div className="mb-8">
          <div className="flex justify-between items-end">
            {/* Signature */}
            {company.signature && (
              <div className="text-center">
                <div className="mb-2">
                  <img
                    src={company.signature}
                    alt="Authorized Signature"
                    className="h-16 w-auto max-w-[150px] object-contain mx-auto"
                  />
                </div>
                <div className="border-t border-gray-400 pt-2 w-40">
                  <p className="text-sm text-gray-600">
                    {isArabic ? "التوقيع المعتمد" : "Authorized Signature"}
                  </p>
                </div>
              </div>
            )}

            {/* Stamp */}
            {company.stamp && (
              <div className="text-center">
                <div className="mb-2">
                  <img
                    src={company.stamp}
                    alt="Company Stamp"
                    className="h-20 w-auto max-w-[100px] object-contain mx-auto"
                  />
                </div>
                <p className="text-sm text-gray-600">
                  {isArabic ? "ختم الشركة" : "Company Stamp"}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="border-t pt-6 text-center text-gray-600">
        <p className="mb-2">
          {isArabic
            ? "شكراً لك على تعاملك معنا"
            : "Thank you for your business"}
        </p>
        <p className="text-sm">
          {isArabic
            ? "هذه فاتورة مُنشأة إلكترونياً"
            : "This is a computer-generated invoice"}
        </p>
      </div>
    </div>
  )
}
