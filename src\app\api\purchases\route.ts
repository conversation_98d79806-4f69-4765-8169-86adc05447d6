import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/purchases - Get all purchases
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const purchases = await prisma.purchase.findMany({
      include: {
        supplier: true,
        user: true,
        items: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json(purchases)
  } catch (error) {
    console.error('Error fetching purchases:', error)
    return NextResponse.json(
      { error: 'Failed to fetch purchases' },
      { status: 500 }
    )
  }
}

// POST /api/purchases - Create new purchase
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      supplierId,
      expectedDate,
      status = 'DRAFT',
      items,
      notes,
      vatRate = 5,
      discountType = 'amount',
      discountValue = 0,
    } = body

    // Validate required fields
    if (!supplierId || !items || items.length === 0) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Generate purchase number
    const lastPurchase = await prisma.purchase.findFirst({
      orderBy: { createdAt: 'desc' },
    })

    const currentYear = new Date().getFullYear()
    const lastNumber = lastPurchase?.number.split('-')[2] || '0000'
    const nextNumber = (parseInt(lastNumber) + 1).toString().padStart(4, '0')
    const purchaseNumber = `PO-${currentYear}-${nextNumber}`

    // Calculate totals
    let subtotal = 0
    const processedItems = items.map((item: any) => {
      const itemTotal = item.quantity * item.unitPrice
      subtotal += itemTotal
      return {
        ...item,
        total: itemTotal,
      }
    })

    // Calculate discount
    let discountAmount = 0
    if (discountType === 'percentage') {
      discountAmount = (subtotal * discountValue) / 100
    } else {
      discountAmount = discountValue
    }

    // Calculate tax
    const taxableAmount = subtotal - discountAmount
    const taxAmount = (taxableAmount * vatRate) / 100
    const total = taxableAmount + taxAmount

    // Create purchase with items
    const purchase = await prisma.purchase.create({
      data: {
        number: purchaseNumber,
        supplierId,
        expectedDate: expectedDate ? new Date(expectedDate) : null,
        status,
        subtotal,
        taxAmount,
        discountAmount,
        total,
        notes,
        items: {
          create: processedItems.map((item: any) => ({
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            total: item.total,
            productId: item.productId || null,
          })),
        },
      },
      include: {
        supplier: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    return NextResponse.json(purchase, { status: 201 })
  } catch (error) {
    console.error('Error creating purchase:', error)
    return NextResponse.json(
      { error: 'Failed to create purchase' },
      { status: 500 }
    )
  }
}
