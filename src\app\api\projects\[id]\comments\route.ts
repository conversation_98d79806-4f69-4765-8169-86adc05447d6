import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: projectId } = await params

    const comments = await prisma.projectComment.findMany({
      where: {
        projectId
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { isPinned: 'desc' },
        { createdAt: 'desc' }
      ]
    })

    return NextResponse.json(comments)
  } catch (error) {
    console.error('Error fetching project comments:', error)
    return NextResponse.json({ error: 'Failed to fetch comments' }, { status: 500 })
  }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: projectId } = await params
    const { content, isPinned = false } = await request.json()

    if (!content?.trim()) {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 })
    }

    // For now, we'll use a default user ID - in a real app, get this from authentication
    const authorId = "user_1" // Replace with actual user ID from session

    const comment = await prisma.projectComment.create({
      data: {
        content: content.trim(),
        isPinned,
        projectId,
        authorId
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    return NextResponse.json(comment, { status: 201 })
  } catch (error) {
    console.error('Error creating project comment:', error)
    return NextResponse.json({ error: 'Failed to create comment' }, { status: 500 })
  }
} 