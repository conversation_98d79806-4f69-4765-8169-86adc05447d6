"use client"

import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts"
import {
  Package,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  ShoppingCart,
  Plus,
  Eye,
  BarChart3
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

// Product metrics will be fetched from API

const productTypeData = [
  { name: 'Physical Products', value: 89, color: '#3b82f6' },
  { name: 'Services', value: 45, color: '#10b981' },
  { name: 'Digital Products', value: 22, color: '#f59e0b' },
]

const categoryData = [
  { name: 'Office Supplies', products: 45, revenue: 35600 },
  { name: 'Printing Services', products: 28, revenue: 42800 },
  { name: 'Writing Supplies', products: 32, revenue: 18900 },
  { name: 'Office Equipment', products: 18, revenue: 28100 },
  { name: 'Binding Services', products: 15, revenue: 12400 },
  { name: 'Design Services', products: 18, revenue: 22600 },
]

const monthlyData = [
  { month: 'Sep', products: 134, revenue: 98500 },
  { month: 'Oct', products: 142, revenue: 105200 },
  { month: 'Nov', products: 148, revenue: 112800 },
  { month: 'Dec', products: 152, revenue: 118600 },
  { month: 'Jan', products: 156, revenue: 125400 },
]

const topProducts = [
  {
    id: '1',
    name: 'A4 Copy Paper',
    nameAr: 'ورق نسخ A4',
    category: 'Office Supplies',
    totalSold: 2500,
    revenue: 31250,
    stock: 1000,
    isLowStock: false,
  },
  {
    id: '2',
    name: 'Ballpoint Pens (Blue)',
    nameAr: 'أقلام حبر جاف (أزرق)',
    category: 'Writing Supplies',
    totalSold: 3600,
    revenue: 2700,
    stock: 1200,
    isLowStock: false,
  },
  {
    id: '3',
    name: 'Business Card Printing',
    nameAr: 'طباعة بطاقات العمل',
    category: 'Printing Services',
    totalSold: 150,
    revenue: 3750,
    stock: 0,
    isLowStock: false,
  },
  {
    id: '4',
    name: 'Color Ink Cartridge HP',
    nameAr: 'خرطوشة حبر ملونة HP',
    category: 'Office Supplies',
    totalSold: 85,
    revenue: 3901.5,
    stock: 8,
    isLowStock: true,
  },
  {
    id: '5',
    name: 'Document Binding Service',
    nameAr: 'خدمة تجليد المستندات',
    category: 'Printing Services',
    totalSold: 320,
    revenue: 1120,
    stock: 0,
    isLowStock: false,
  },
]

const lowStockProducts = [
  {
    id: '3',
    name: 'Color Ink Cartridge HP',
    nameAr: 'خرطوشة حبر ملونة HP',
    currentStock: 8,
    minStock: 10,
    unit: 'piece',
  },
  {
    id: '8',
    name: 'Toner Cartridge Canon',
    nameAr: 'خرطوشة حبر كانون',
    currentStock: 3,
    minStock: 5,
    unit: 'piece',
  },
  {
    id: '12',
    name: 'Premium Paper A3',
    nameAr: 'ورق فاخر A3',
    currentStock: 45,
    minStock: 50,
    unit: 'ream',
  },
]

export default function ProductDashboard() {
  const router = useRouter()
  const { t } = useI18n()
  const [productMetrics, setProductMetrics] = useState({
    totalProducts: 0,
    totalProductsGrowth: 0,
    activeProducts: 0,
    activeProductsGrowth: 0,
    totalRevenue: 0,
    totalRevenueGrowth: 0,
    lowStockItems: 0,
    lowStockItemsGrowth: 0,
  })
  const [topProducts, setTopProducts] = useState([])
  const [lowStockProducts, setLowStockProducts] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchProductData()
  }, [])

  const fetchProductData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/products/dashboard')
      if (response.ok) {
        const data = await response.json()
        setProductMetrics(data.metrics || productMetrics)
        setTopProducts(data.topProducts || [])
        setLowStockProducts(data.lowStockProducts || [])
      }
    } catch (error) {
      console.error('Error fetching product data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('products.dashboardTitle')}</h2>
          <p className="text-muted-foreground">
            {t('products.dashboardOverview')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/products')}>
            <Package className="mr-2 h-4 w-4" />
            {t('products.viewAllProducts')}
          </Button>
          <Button onClick={() => router.push('/dashboard/products/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('products.addProduct')}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('products.totalProducts')}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{productMetrics.totalProducts}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              +{productMetrics.totalProductsGrowth} {t('products.fromLastMonth')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('products.activeProducts')}</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{productMetrics.activeProducts}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              +{productMetrics.activeProductsGrowth} {t('products.fromLastMonth')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('products.totalRevenue')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(productMetrics.totalRevenue)}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              +{productMetrics.totalRevenueGrowth}% {t('products.fromLastMonth')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('products.lowStockItems')}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{productMetrics.lowStockItems}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <TrendingDown className="mr-1 h-3 w-3 text-green-500" />
              {Math.abs(productMetrics.lowStockItemsGrowth)} {t('products.fewerThanLastMonth')}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Monthly Performance */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>{t('products.monthlyPerformance')}</CardTitle>
          </CardHeader>
          <CardContent className="pl-2">
            <ResponsiveContainer width="100%" height={350}>
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip
                  formatter={(value, name) => [
                    name === 'revenue' ? formatCurrency(value as number) : value,
                    name === 'revenue' ? t('products.revenue') : t('products.product')
                  ]}
                />
                <Bar yAxisId="left" dataKey="products" fill="#3b82f6" name={t('products.product')} />
                <Line yAxisId="right" type="monotone" dataKey="revenue" stroke="#10b981" strokeWidth={2} name={t('products.revenue')} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Product Type Distribution */}
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>{t('products.productTypeDistribution')}</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={350}>
              <PieChart>
                <Pie
                  data={productTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${t('products.' + name.replace(/ /g, '').toLowerCase())}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {productTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Tables Section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Top Performing Products */}
        <Card>
          <CardHeader>
            <CardTitle>{t('products.topPerformingProducts')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('products.product')}</TableHead>
                  <TableHead>{t('products.category')}</TableHead>
                  <TableHead>{t('products.sold')}</TableHead>
                  <TableHead className="text-right">{t('products.revenue')}</TableHead>
                  <TableHead className="text-right">{t('products.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {topProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {product.isLowStock && (
                          <AlertTriangle className="h-4 w-4 text-orange-500" />
                        )}
                        <div>
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium"
                            onClick={() => router.push(`/dashboard/products/${product.id}`)}
                          >
                            {product.name}
                          </Button>
                          <div className="text-sm text-muted-foreground" dir="rtl">
                            {product.nameAr}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{product.category}</Badge>
                    </TableCell>
                    <TableCell>{product.totalSold}</TableCell>
                    <TableCell className="text-right font-medium text-green-600">
                      {formatCurrency(product.revenue)}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/dashboard/products/${product.id}`)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Low Stock Alert */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <span>{t('products.lowStockAlert')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('products.product')}</TableHead>
                  <TableHead>{t('products.current')}</TableHead>
                  <TableHead>{t('products.minStock')}</TableHead>
                  <TableHead className="text-right">{t('products.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {lowStockProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell>
                      <div>
                        <Button
                          variant="link"
                          className="p-0 h-auto font-medium"
                          onClick={() => router.push(`/dashboard/products/${product.id}`)}
                        >
                          {product.name}
                        </Button>
                        <div className="text-sm text-muted-foreground" dir="rtl">
                          {product.nameAr}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-orange-600 font-medium">
                      {product.currentStock} {product.unit}
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {product.minStock} {product.unit}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push('/dashboard/purchases/create')}
                      >
                        <ShoppingCart className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>

      {/* Category Performance */}
      <Card>
        <CardHeader>
          <CardTitle>{t('products.category')} {t('products.monthlyPerformance')}</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={categoryData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip
                formatter={(value, name) => [
                  name === 'revenue' ? formatCurrency(value as number) : value,
                  name === 'revenue' ? t('products.revenue') : t('products.product')
                ]}
              />
              <Bar yAxisId="left" dataKey="products" fill="#3b82f6" name={t('products.product')} />
              <Bar yAxisId="right" dataKey="revenue" fill="#10b981" name={t('products.revenue')} />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  )
}