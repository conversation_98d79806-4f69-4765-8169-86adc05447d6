"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Calendar,
  BarChart3
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import {
  getSupplierPayouts,
  deleteSupplierPayout,
  markPayoutAsPaid,
  approvePayout,
  rejectPayout,
  getPayoutStats,
  type SupplierPayout,
  type PayoutStats
} from "@/lib/supplier-payout-storage"

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800", 
  PAID: "bg-green-100 text-green-800",
  CANCELLED: "bg-gray-100 text-gray-800",
  REJECTED: "bg-red-100 text-red-800",
}

const methodLabels = {
  CASH: "Cash",
  CARD: "Card",
  BANK_TRANSFER: "Bank Transfer",
  CHECK: "Check",
  OTHER: "Other",
}

export default function SupplierPayoutsPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [payouts, setPayouts] = useState<SupplierPayout[]>([])
  const [stats, setStats] = useState<PayoutStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  })

  // Load payouts and statistics
  useEffect(() => {
    loadPayouts()
    loadStats()
  }, [statusFilter, pagination.page])

  const loadPayouts = async () => {
    setLoading(true)
    try {
      const params = {
        status: statusFilter === 'all' ? undefined : statusFilter,
        page: pagination.page,
        limit: pagination.limit,
      }
      const result = await getSupplierPayouts(params)
      setPayouts(result.payouts)
      setPagination(result.pagination)
    } catch (error) {
      console.error('Error loading payouts:', error)
      setPayouts([])
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const statsData = await getPayoutStats('month')
      setStats(statsData)
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const filteredPayouts = payouts.filter(payout =>
    payout.number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payout.supplier?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payout.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleCreatePayout = () => {
    router.push('/dashboard/supplier-payouts/create')
  }

  const handleViewPayout = (payout: SupplierPayout) => {
    router.push(`/dashboard/supplier-payouts/${payout.id}`)
  }

  const handleEditPayout = (payout: SupplierPayout) => {
    router.push(`/dashboard/supplier-payouts/${payout.id}/edit`)
  }

  const handleDeletePayout = async (payout: SupplierPayout) => {
    if (confirm(`Are you sure you want to delete payout ${payout.number}?`)) {
      try {
        await deleteSupplierPayout(payout.id)
        loadPayouts()
        alert('Payout deleted successfully!')
      } catch (error) {
        console.error('Delete error:', error)
        alert(`Failed to delete payout: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }

  const handleMarkAsPaid = async (payout: SupplierPayout) => {
    if (confirm(`Mark payout ${payout.number} as paid?`)) {
      try {
        await markPayoutAsPaid(payout.id)
        loadPayouts()
        loadStats()
        alert('Payout marked as paid!')
      } catch (error) {
        console.error('Error marking as paid:', error)
        alert('Failed to mark payout as paid')
      }
    }
  }

  const handleApprovePayout = async (payout: SupplierPayout) => {
    if (confirm(`Approve payout ${payout.number}?`)) {
      try {
        await approvePayout(payout.id)
        loadPayouts()
        loadStats()
        alert('Payout approved!')
      } catch (error) {
        console.error('Error approving payout:', error)
        alert('Failed to approve payout')
      }
    }
  }

  const handleRejectPayout = async (payout: SupplierPayout) => {
    const reason = prompt(`Reject payout ${payout.number}? Enter reason:`)
    if (reason !== null) {
      try {
        await rejectPayout(payout.id, reason)
        loadPayouts()
        loadStats()
        alert('Payout rejected!')
      } catch (error) {
        console.error('Error rejecting payout:', error)
        alert('Failed to reject payout')
      }
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Supplier Payouts</h2>
          <p className="text-muted-foreground">
            Manage payments to suppliers and track payout status
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/supplier-payouts/dashboard')}>
            <BarChart3 className="mr-2 h-4 w-4" />
            Dashboard
          </Button>
          <Button onClick={handleCreatePayout}>
            <Plus className="mr-2 h-4 w-4" />
            Create Payout
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Payouts</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalPayouts}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(stats.totalAmount)} total amount
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.statusCounts.PENDING}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(stats.statusAmounts.PENDING)} amount
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Paid</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.statusCounts.PAID}</div>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(stats.statusAmounts.PAID)} amount
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Overdue</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.overduePayouts}</div>
              <p className="text-xs text-muted-foreground">
                Require immediate attention
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search payouts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="PENDING">Pending</SelectItem>
            <SelectItem value="APPROVED">Approved</SelectItem>
            <SelectItem value="PAID">Paid</SelectItem>
            <SelectItem value="CANCELLED">Cancelled</SelectItem>
            <SelectItem value="REJECTED">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Payouts Table */}
      <div className="table-container">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Payout #</TableHead>
              <TableHead>Supplier</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Method</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span>Loading payouts...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredPayouts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm ? 'No payouts found matching your search.' : 'No payouts found. Create your first payout!'}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredPayouts.map((payout) => (
                <TableRow key={payout.id}>
                  <TableCell className="font-medium">
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium"
                      onClick={() => handleViewPayout(payout)}
                    >
                      {payout.number}
                    </Button>
                  </TableCell>
                  <TableCell>{payout.supplier?.name || 'Unknown Supplier'}</TableCell>
                  <TableCell className="font-medium">{formatCurrency(payout.amount)}</TableCell>
                  <TableCell>{methodLabels[payout.method as keyof typeof methodLabels]}</TableCell>
                  <TableCell>
                    <Badge className={statusColors[payout.status as keyof typeof statusColors]}>
                      {payout.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{new Date(payout.date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    {payout.dueDate ? new Date(payout.dueDate).toLocaleDateString() : '-'}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewPayout(payout)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        {payout.status !== 'PAID' && (
                          <DropdownMenuItem onClick={() => handleEditPayout(payout)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Payout
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        {payout.status === 'PENDING' && (
                          <DropdownMenuItem onClick={() => handleApprovePayout(payout)}>
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Approve
                          </DropdownMenuItem>
                        )}
                        {(payout.status === 'APPROVED' || payout.status === 'PENDING') && (
                          <DropdownMenuItem onClick={() => handleMarkAsPaid(payout)}>
                            <DollarSign className="mr-2 h-4 w-4" />
                            Mark as Paid
                          </DropdownMenuItem>
                        )}
                        {payout.status === 'PENDING' && (
                          <DropdownMenuItem onClick={() => handleRejectPayout(payout)}>
                            <XCircle className="mr-2 h-4 w-4" />
                            Reject
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        {payout.status !== 'PAID' && (
                          <DropdownMenuItem
                            onClick={() => handleDeletePayout(payout)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Payout
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} payouts
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
              disabled={pagination.page >= pagination.pages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
