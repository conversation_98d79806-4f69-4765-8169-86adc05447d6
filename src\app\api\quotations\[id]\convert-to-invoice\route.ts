import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { whatsappService } from '@/lib/whatsapp'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: quotationId } = await params

    // Get the quotation with items
    const quotation = await prisma.quotation.findUnique({
      where: { id: quotationId },
      include: {
        customer: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    if (!quotation) {
      return NextResponse.json(
        { error: 'Quotation not found' },
        { status: 404 }
      )
    }

    if (quotation.status !== 'APPROVED') {
      return NextResponse.json(
        { error: 'Only approved quotations can be converted to invoices' },
        { status: 400 }
      )
    }

    // Generate invoice number
    const lastInvoice = await prisma.invoice.findFirst({
      orderBy: { createdAt: 'desc' },
    })

    const nextNumber = lastInvoice
      ? `INV-${String(parseInt(lastInvoice.number.split('-')[1]) + 1).padStart(3, '0')}`
      : 'INV-001'

    // Create invoice from quotation
    const invoice = await prisma.invoice.create({
      data: {
        number: nextNumber,
        customerId: quotation.customerId,
        userId: session.user?.id || '',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        subtotal: quotation.subtotal,
        taxAmount: quotation.taxAmount,
        discount: quotation.discount,
        total: quotation.total,
        notes: `Converted from quotation ${quotation.number}`,
        items: {
          create: quotation.items.map((item) => ({
            description: item.description,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            total: item.total,
            productId: item.productId,
          })),
        },
      },
      include: {
        customer: true,
        user: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    // Update quotation status to indicate it's been converted
    await prisma.quotation.update({
      where: { id: quotationId },
      data: {
        status: 'APPROVED',
        notes: quotation.notes
          ? `${quotation.notes}\n\nConverted to invoice ${invoice.number}`
          : `Converted to invoice ${invoice.number}`
      },
    })

    // Send WhatsApp notification to customer
    if (invoice.customer?.phone) {
      await whatsappService.sendInvoiceNotification(
        invoice.customer.phone,
        invoice.number,
        parseFloat(quotation.total.toString()),
        'en'
      )
    }

    return NextResponse.json({
      message: 'Quotation successfully converted to invoice',
      invoice,
      quotation: {
        id: quotation.id,
        number: quotation.number,
      },
    })
  } catch (error) {
    console.error('Error converting quotation to invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
