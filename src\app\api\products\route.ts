import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const type = searchParams.get('type')
    const categoryId = searchParams.get('categoryId')
    const lowStock = searchParams.get('lowStock')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const where: Record<string, unknown> = {
      // Remove isActive filter to show all products including newly created ones
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { nameAr: { contains: search, mode: 'insensitive' } },
        { sku: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (type) {
      where.type = type
    }

    if (categoryId) {
      where.categoryId = categoryId
    }

    if (lowStock === 'true') {
      where.AND = [
        { type: 'PHYSICAL' },
        {
          currentStock: { lte: { minStock: true } },
        },
      ]
    }

    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: limit,
        include: {
          category: true,
          supplier: true,
          unitModel: true,
          stockMovements: {
            orderBy: { date: 'desc' },
            take: 3,
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.product.count({ where }),
    ])

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching products:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      nameAr,
      description,
      sku,
      barcode,
      type,
      price,
      costPrice,
      currentStock,
      minStock,
      maxStock,
      location,
      unit,
      image,
      categoryId,
      unitId,
      supplierId,
    } = body

    if (!name || !type || !price) {
      return NextResponse.json(
        { error: 'Name, type, and price are required' },
        { status: 400 }
      )
    }

    // Check if SKU already exists
    if (sku) {
      const existingProduct = await prisma.product.findUnique({
        where: { sku },
      })

      if (existingProduct) {
        return NextResponse.json(
          { error: 'SKU already exists' },
          { status: 400 }
        )
      }
    }

    const product = await prisma.product.create({
      data: {
        name,
        nameAr,
        description,
        sku,
        barcode,
        type,
        price: parseFloat(price),
        costPrice: costPrice ? parseFloat(costPrice) : null,
        currentStock: type === 'PHYSICAL' ? parseInt(currentStock || '0') : 0,
        minStock: type === 'PHYSICAL' ? parseInt(minStock || '0') : 0,
        maxStock: type === 'PHYSICAL' ? parseInt(maxStock || '100') : 0,
        location,
        unit: unit || 'piece',
        image: image || null,
        categoryId: categoryId || null,
        unitId: unitId || null,
        supplierId: supplierId || null,
        lastRestocked: currentStock > 0 ? new Date() : null,
      },
      include: {
        category: true,
        supplier: true,
        unitModel: true,
      },
    })

    // Create initial stock movement if stock > 0
    if (currentStock > 0) {
      await prisma.stockMovement.create({
        data: {
          productId: product.id,
          type: 'IN',
          quantity: parseInt(currentStock),
          reason: 'Initial Stock',
          date: new Date(),
        },
      })
    }

    return NextResponse.json(product, { status: 201 })
  } catch (error) {
    console.error('Error creating product:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      name,
      nameAr,
      description,
      sku,
      barcode,
      type,
      price,
      costPrice,
      currentStock,
      minStock,
      maxStock,
      location,
      unit,
      image,
      categoryId,
      unitId,
      supplierId,
      isActive,
    } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      )
    }

    // Check if SKU already exists for other products
    if (sku) {
      const existingProduct = await prisma.product.findFirst({
        where: {
          sku,
          id: { not: id },
        },
      })

      if (existingProduct) {
        return NextResponse.json(
          { error: 'SKU already exists' },
          { status: 400 }
        )
      }
    }

    const product = await prisma.product.update({
      where: { id },
      data: {
        name,
        nameAr,
        description,
        sku,
        barcode,
        type,
        price: price ? parseFloat(price) : undefined,
        costPrice: costPrice ? parseFloat(costPrice) : null,
        currentStock: type === 'PHYSICAL' && currentStock !== undefined ? parseInt(currentStock) : undefined,
        minStock: type === 'PHYSICAL' && minStock !== undefined ? parseInt(minStock) : undefined,
        maxStock: type === 'PHYSICAL' && maxStock !== undefined ? parseInt(maxStock) : undefined,
        location,
        unit,
        image: image !== undefined ? image : undefined,
        categoryId: categoryId || null,
        unitId: unitId || null,
        supplierId: supplierId || null,
        isActive,
      },
      include: {
        category: true,
        supplier: true,
        unitModel: true,
      },
    })

    return NextResponse.json(product)
  } catch (error) {
    console.error('Error updating product:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
