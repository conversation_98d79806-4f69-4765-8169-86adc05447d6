import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '30' // days

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get total revenue
    const totalRevenue = await prisma.invoice.aggregate({
      where: {
        status: 'PAID',
        date: {
          gte: startDate,
        },
      },
      _sum: {
        total: true,
      },
    })

    // Get pending tasks count
    const pendingTasks = await prisma.task.count({
      where: {
        status: {
          in: ['NEW', 'IN_PROGRESS'],
        },
      },
    })

    // Get overdue invoices count
    const overdueInvoices = await prisma.invoice.count({
      where: {
        status: 'UNPAID',
        dueDate: {
          lt: new Date(),
        },
      },
    })

    // Get low stock items count
    const lowStockItems = await prisma.product.count({
      where: {
        type: 'PHYSICAL',
        isActive: true,
        currentStock: {
          lte: 10, // Low stock threshold
        },
      },
    })

    // Get recent activities
    const recentTasks = await prisma.task.findMany({
      take: 5,
      orderBy: { updatedAt: 'desc' },
      include: {
        customer: true,
        assignedTo: true,
      },
    })

    const recentInvoices = await prisma.invoice.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
      },
    })

    // Get sales data for the last 6 months
    const salesData = []
    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date()
      monthStart.setMonth(monthStart.getMonth() - i)
      monthStart.setDate(1)
      monthStart.setHours(0, 0, 0, 0)
      
      const monthEnd = new Date(monthStart)
      monthEnd.setMonth(monthEnd.getMonth() + 1)
      monthEnd.setDate(0)
      monthEnd.setHours(23, 59, 59, 999)

      const monthSales = await prisma.invoice.aggregate({
        where: {
          status: 'PAID',
          date: {
            gte: monthStart,
            lte: monthEnd,
          },
        },
        _sum: {
          total: true,
        },
        _count: true,
      })

      salesData.push({
        month: monthStart.toLocaleDateString('en-US', { month: 'short' }),
        sales: monthSales._sum.total || 0,
        invoices: monthSales._count,
      })
    }

    // Get top products
    const topProducts = await prisma.invoiceItem.groupBy({
      by: ['productId'],
      where: {
        productId: {
          not: null,
        },
        invoice: {
          date: {
            gte: startDate,
          },
        },
      },
      _sum: {
        quantity: true,
        total: true,
      },
      orderBy: {
        _sum: {
          total: 'desc',
        },
      },
      take: 5,
    })

    const topProductsWithDetails = await Promise.all(
      topProducts.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId! },
        })
        return {
          name: product?.name || 'Unknown Product',
          sales: item._sum.quantity || 0,
          revenue: item._sum.total || 0,
        }
      })
    )

    // Get top customers
    const topCustomers = await prisma.invoice.groupBy({
      by: ['customerId'],
      where: {
        customerId: {
          not: null,
        },
        date: {
          gte: startDate,
        },
      },
      _sum: {
        total: true,
      },
      _count: true,
      orderBy: {
        _sum: {
          total: 'desc',
        },
      },
      take: 5,
    })

    const topCustomersWithDetails = await Promise.all(
      topCustomers.map(async (item) => {
        const customer = await prisma.customer.findUnique({
          where: { id: item.customerId! },
        })
        return {
          name: customer?.name || 'Unknown Customer',
          orders: item._count,
          revenue: item._sum.total || 0,
        }
      })
    )

    // Get task performance data
    const taskStats = await prisma.task.groupBy({
      by: ['status'],
      _count: true,
    })

    const taskPerformance = {
      completed: taskStats.find(s => s.status === 'COMPLETED')?._count || 0,
      inProgress: taskStats.find(s => s.status === 'IN_PROGRESS')?._count || 0,
      pending: taskStats.find(s => s.status === 'NEW')?._count || 0,
      cancelled: taskStats.find(s => s.status === 'CANCELLED')?._count || 0,
    }

    return NextResponse.json({
      summary: {
        totalRevenue: totalRevenue._sum.total || 0,
        pendingTasks,
        overdueInvoices,
        lowStockItems,
      },
      salesData,
      topProducts: topProductsWithDetails,
      topCustomers: topCustomersWithDetails,
      taskPerformance,
      recentActivities: {
        tasks: recentTasks,
        invoices: recentInvoices,
      },
    })
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
