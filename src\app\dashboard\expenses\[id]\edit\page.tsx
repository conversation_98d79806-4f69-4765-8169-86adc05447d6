"use client"

import { useState, useEffect } from "react"
import { useRouter, useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ArrowLeft, Loader2 } from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { getExpenseById, updateExpense, type Expense } from "@/lib/expense-storage"
import { getExpenseTypes, type ExpenseType } from "@/lib/expense-type-storage"
import { useI18n } from '@/lib/i18n'

export default function EditExpensePage() {
  const router = useRouter()
  const params = useParams()
  const { t } = useI18n()
  const expenseId = params.id as string

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [expense, setExpense] = useState<Expense | null>(null)
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([])
  
  const [formData, setFormData] = useState({
    date: "",
    description: "",
    amount: 0,
    expenseTypeId: "",
    paymentMethod: "CASH" as 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHECK' | 'OTHER',
    status: "PENDING" as 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID',
    notes: "",
  })

  // Load expense data and expense types
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      try {
        const [expenseData, typesData] = await Promise.all([
          getExpenseById(expenseId),
          getExpenseTypes()
        ])
        
        if (expenseData) {
          setExpense(expenseData)
          setFormData({
            date: new Date(expenseData.date).toISOString().split('T')[0],
            description: expenseData.description,
            amount: Number(expenseData.amount),
            expenseTypeId: expenseData.expenseTypeId,
            paymentMethod: expenseData.paymentMethod,
            status: expenseData.status,
            notes: expenseData.notes || "",
          })
        } else {
          alert(t('expenses.edit.expenseNotFound'))
          router.push('/dashboard/expenses')
        }
        
        setExpenseTypes(typesData)
      } catch (error) {
        console.error('Error loading data:', error)
        alert(t('expenses.edit.failedToLoadExpenseData'))
        router.push('/dashboard/expenses')
      } finally {
        setLoading(false)
      }
    }
    
    if (expenseId) {
      loadData()
    }
  }, [expenseId, router, t])

  const handleInputChange = (field: keyof typeof formData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    // Validation
    if (!formData.description.trim()) {
      alert(t('expenses.edit.pleaseEnterDescription'))
      return
    }

    if (formData.amount <= 0) {
      alert(t('expenses.edit.pleaseEnterValidAmount'))
      return
    }

    if (!formData.expenseTypeId) {
      alert(t('expenses.edit.pleaseSelectExpenseType'))
      return
    }

    setSaving(true)
    try {
      const updated = await updateExpense(expenseId, {
        description: formData.description,
        amount: formData.amount,
        expenseTypeId: formData.expenseTypeId,
        paymentMethod: formData.paymentMethod,
        status: formData.status,
        notes: formData.notes,
        date: formData.date,
      })

      if (updated) {
        alert(t('expenses.edit.expenseUpdatedSuccess'))
        router.push('/dashboard/expenses')
      } else {
        alert(t('expenses.edit.expenseUpdateError'))
      }
    } catch (error) {
      console.error('Error updating expense:', error)
      alert(t('expenses.edit.expenseUpdateError'))
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>{t('expenses.edit.loadingExpense')}</span>
        </div>
      </div>
    )
  }

  if (!expense) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <h2 className="text-2xl font-bold">{t('expenses.edit.expenseNotFound')}</h2>
          <p className="text-muted-foreground mt-2">{t('expenses.edit.expenseNotFoundDescription')}</p>
          <Button onClick={() => router.push('/dashboard/expenses')} className="mt-4">
            {t('expenses.edit.backToExpenses')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('expenses.edit.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('expenses.edit.title')}</h2>
            <p className="text-muted-foreground">
              {t('expenses.edit.subtitle')} #{expense.number}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t('expenses.edit.expenseDetails')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date">{t('expenses.edit.dateRequired')}</Label>
                  <Input
                    id="date"
                    type="date"
                    value={formData.date}
                    onChange={(e) => handleInputChange('date', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="amount">{t('expenses.edit.amountRequired')}</Label>
                  <Input
                    id="amount"
                    type="number"
                    min="0"
                    step="0.001"
                    placeholder={t('expenses.edit.amountPlaceholder')}
                    value={formData.amount || ''}
                    onChange={(e) => handleInputChange('amount', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">{t('expenses.edit.descriptionRequired')}</Label>
                <Input
                  id="description"
                  placeholder={t('expenses.edit.descriptionPlaceholder')}
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expenseType">{t('expenses.edit.expenseTypeRequired')}</Label>
                  <Select
                    value={formData.expenseTypeId}
                    onValueChange={(value) => handleInputChange('expenseTypeId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('expenses.edit.selectExpenseTypePlaceholder')} />
                    </SelectTrigger>
                    <SelectContent>
                      {expenseTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="paymentMethod">{t('expenses.edit.paymentMethodRequired')}</Label>
                  <Select
                    value={formData.paymentMethod}
                    onValueChange={(value) => handleInputChange('paymentMethod', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CASH">{t('expenses.paymentMethodLabels.CASH')}</SelectItem>
                      <SelectItem value="CARD">{t('expenses.paymentMethodLabels.CARD')}</SelectItem>
                      <SelectItem value="BANK_TRANSFER">{t('expenses.paymentMethodLabels.BANK_TRANSFER')}</SelectItem>
                      <SelectItem value="CHECK">{t('expenses.paymentMethodLabels.CHECK')}</SelectItem>
                      <SelectItem value="OTHER">{t('expenses.paymentMethodLabels.OTHER')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">{t('expenses.edit.status')}</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PENDING">{t('expenses.statusLabels.PENDING')}</SelectItem>
                    <SelectItem value="APPROVED">{t('expenses.statusLabels.APPROVED')}</SelectItem>
                    <SelectItem value="REJECTED">{t('expenses.statusLabels.REJECTED')}</SelectItem>
                    <SelectItem value="PAID">{t('expenses.statusLabels.PAID')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">{t('expenses.edit.notes')}</Label>
                <Textarea
                  id="notes"
                  placeholder={t('expenses.edit.notesPlaceholder')}
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  className="min-h-[80px]"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Expense Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t('expenses.edit.summary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>{t('expenses.edit.expenseNumber')}</span>
                <span className="font-medium">{expense.number}</span>
              </div>
              
              <div className="flex justify-between">
                <span>{t('expenses.edit.summaryAmount')}</span>
                <span className="font-medium">{formatCurrency(formData.amount)}</span>
              </div>
              
              <div className="flex justify-between">
                <span>{t('expenses.edit.summaryType')}</span>
                <span className="text-sm">
                  {expenseTypes.find(t => t.id === formData.expenseTypeId)?.name || t('expenses.edit.notSelected')}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span>{t('expenses.edit.summaryPayment')}</span>
                <span className="text-sm">{t(`expenses.paymentMethodLabels.${formData.paymentMethod}`)}</span>
              </div>
              
              <div className="flex justify-between">
                <span>{t('expenses.edit.summaryStatus')}</span>
                <span className="text-sm">{t(`expenses.statusLabels.${formData.status}`)}</span>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('expenses.edit.actions')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                className="w-full"
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('expenses.edit.updating')}
                  </>
                ) : (
                  t('expenses.edit.updateExpense')
                )}
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push('/dashboard/expenses')}
                disabled={saving}
              >
                {t('expenses.edit.cancel')}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
