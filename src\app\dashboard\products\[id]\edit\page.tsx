"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ArrowLeft, Save, Package, Upload, X, Image as ImageIcon, Plus, ChevronsUpDown, Check, Building, Tag } from "lucide-react"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

interface Category {
  id: string
  name: string
  nameAr?: string
  description?: string
}

interface Supplier {
  id: string
  name: string
  mobile: string
  email?: string
  address?: string
  contactPerson?: string
}

interface Unit {
  id: string
  name: string
  nameAr?: string
  symbol: string
  symbolAr?: string
  description?: string
  descriptionAr?: string
  isActive: boolean
}

export default function EditProductPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(true)
  const [categories, setCategories] = useState<Category[]>([])
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [units, setUnits] = useState<Unit[]>([])
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [currentImage, setCurrentImage] = useState<string | null>(null)
  const [uploadingImage, setUploadingImage] = useState(false)

  // Search states
  const [categorySearchOpen, setCategorySearchOpen] = useState(false)
  const [supplierSearchOpen, setSupplierSearchOpen] = useState(false)
  const [unitSearchOpen, setUnitSearchOpen] = useState(false)

  // New item dialogs
  const [showNewCategoryDialog, setShowNewCategoryDialog] = useState(false)
  const [showNewSupplierDialog, setShowNewSupplierDialog] = useState(false)
  const [showNewUnitDialog, setShowNewUnitDialog] = useState(false)

  // Form data for new items
  const [newCategoryData, setNewCategoryData] = useState({
    name: '',
    nameAr: '',
    description: ''
  })
  
  const [newSupplierData, setNewSupplierData] = useState({
    name: '',
    mobile: '',
    email: '',
    address: '',
    contactPerson: ''
  })

  const [newUnitData, setNewUnitData] = useState({
    name: '',
    nameAr: '',
    symbol: '',
    symbolAr: '',
    description: '',
    descriptionAr: ''
  })

  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    sku: '',
    type: 'PHYSICAL',
    categoryId: '',
    supplierId: '',
    unitId: '',
    price: '',
    cost: '',
    stock: '',
    minStock: '',
    unit: 'piece',
    description: '',
    image: '',
  })

  // Fetch product data and options
  useEffect(() => {
    const fetchData = async () => {
      try {
        setFetchLoading(true)
        const { id } = await params

        const [productRes, categoriesRes, suppliersRes, unitsRes] = await Promise.all([
          fetch(`/api/products/${id}`),
          fetch('/api/categories'),
          fetch('/api/suppliers'),
          fetch('/api/units')
        ])

        if (productRes.ok) {
          const product = await productRes.json()
          setFormData({
            name: product.name || '',
            nameAr: product.nameAr || '',
            sku: product.sku || '',
            type: product.type || 'PHYSICAL',
            categoryId: product.categoryId || '',
            supplierId: product.supplierId || '',
            unitId: product.unitId || '',
            price: product.price?.toString() || '',
            cost: product.costPrice?.toString() || '',
            stock: product.currentStock?.toString() || '',
            minStock: product.minStock?.toString() || '',
            unit: product.unit || 'piece',
            description: product.description || '',
            image: product.image || '',
          })

          // Set current image if exists
          if (product.image) {
            setCurrentImage(product.image)
          }
        } else {
          toast.error('Product not found')
          router.push('/dashboard/products')
          return
        }

        if (categoriesRes.ok) {
          const categoriesData = await categoriesRes.json()
          setCategories(categoriesData.categories || [])
        }

        if (suppliersRes.ok) {
          const suppliersData = await suppliersRes.json()
          setSuppliers(suppliersData.suppliers || [])
        }

        if (unitsRes.ok) {
          const unitsData = await unitsRes.json()
          setUnits(unitsData.units || [])
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        toast.error('Failed to load product data')
      } finally {
        setFetchLoading(false)
      }
    }

    fetchData()
  }, [params, router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleCategorySelect = (categoryId: string) => {
    setFormData(prev => ({ ...prev, categoryId }))
    setCategorySearchOpen(false)
  }

  const handleSupplierSelect = (supplierId: string) => {
    setFormData(prev => ({ ...prev, supplierId }))
    setSupplierSearchOpen(false)
  }

  const handleUnitSelect = (unitId: string) => {
    setFormData(prev => ({ ...prev, unitId }))
    setUnitSearchOpen(false)
  }

  const handleCreateCategory = async () => {
    if (!newCategoryData.name) {
      toast.error('Category name is required')
      return
    }

    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCategoryData)
      })

      if (response.ok) {
        const category = await response.json()
        setCategories(prev => [...prev, category])
        setFormData(prev => ({ ...prev, categoryId: category.id }))
        setNewCategoryData({ name: '', nameAr: '', description: '' })
        setShowNewCategoryDialog(false)
        toast.success(`Category "${category.name}" created successfully`)
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to create category')
      }
    } catch (error) {
      console.error('Error creating category:', error)
      toast.error('Error creating category')
    }
  }

  const handleCreateSupplier = async () => {
    if (!newSupplierData.name || !newSupplierData.mobile) {
      toast.error('Supplier name and mobile are required')
      return
    }

    try {
      const response = await fetch('/api/suppliers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSupplierData)
      })

      if (response.ok) {
        const supplier = await response.json()
        setSuppliers(prev => [...prev, supplier])
        setFormData(prev => ({ ...prev, supplierId: supplier.id }))
        setNewSupplierData({ name: '', mobile: '', email: '', address: '', contactPerson: '' })
        setShowNewSupplierDialog(false)
        toast.success(`Supplier "${supplier.name}" created successfully`)
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to create supplier')
      }
    } catch (error) {
      console.error('Error creating supplier:', error)
      toast.error('Error creating supplier')
    }
  }

  const handleCreateUnit = async () => {
    if (!newUnitData.name || !newUnitData.symbol) {
      toast.error('Unit name and symbol are required')
      return
    }

    try {
      const response = await fetch('/api/units', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newUnitData)
      })

      if (response.ok) {
        const unit = await response.json()
        setUnits(prev => [...prev, unit])
        setFormData(prev => ({ ...prev, unitId: unit.id }))
        setNewUnitData({ name: '', nameAr: '', symbol: '', symbolAr: '', description: '', descriptionAr: '' })
        setShowNewUnitDialog(false)
        toast.success(`Unit "${unit.name}" created successfully`)
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to create unit')
      }
    } catch (error) {
      console.error('Error creating unit:', error)
      toast.error('Error creating unit')
    }
  }

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file')
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image size must be less than 5MB')
        return
      }

      setImageFile(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = () => {
    setImageFile(null)
    setImagePreview(null)
    setCurrentImage(null)
    setFormData(prev => ({ ...prev, image: '' }))
  }

  const uploadImage = async (file: File): Promise<string> => {
    const formData = new FormData()
    formData.append('image', file)

    const response = await fetch('/api/products/upload-image', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error('Failed to upload image')
    }

    const { imageUrl } = await response.json()
    return imageUrl
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.name || !formData.price) {
      toast.error('Product name and price are required')
      return
    }

    try {
      setLoading(true)
      let imagePath = formData.image

      // Upload image if selected
      if (imageFile) {
        setUploadingImage(true)
        try {
          imagePath = await uploadImage(imageFile)
          toast.success('Image uploaded successfully')
        } catch (error) {
          console.error('Error uploading image:', error)
          toast.error('Failed to upload image')
          setUploadingImage(false)
          return
        }
        setUploadingImage(false)
      }

      const { id } = await params
      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        costPrice: formData.cost ? parseFloat(formData.cost) : null,
        currentStock: formData.stock ? parseInt(formData.stock) : 0,
        minStock: formData.minStock ? parseInt(formData.minStock) : 0,
        image: imagePath,
        categoryId: formData.categoryId || null,
        supplierId: formData.supplierId || null,
        unitId: formData.unitId || null,
      }

      const response = await fetch(`/api/products/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      })

      if (response.ok) {
        const product = await response.json()
        toast.success(`Product "${product.name}" updated successfully`)
        router.push('/dashboard/products')
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to update product')
      }
    } catch (error) {
      console.error('Error updating product:', error)
      toast.error('Error updating product')
    } finally {
      setLoading(false)
    }
  }

  const getSelectedCategory = () => {
    return categories.find(c => c.id === formData.categoryId)
  }

  const getSelectedSupplier = () => {
    return suppliers.find(s => s.id === formData.supplierId)
  }

  const getSelectedUnit = () => {
    return units.find(u => u.id === formData.unitId)
  }

  if (fetchLoading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading product...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Edit Product</h2>
            <p className="text-muted-foreground">Update product or service information</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Product Information */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="mr-2 h-5 w-5" />
                  Product Information
                </CardTitle>
                <CardDescription>
                  Basic product details and identification
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Product Names Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Product/Service Name *</Label>
                    <Input
                      id="name"
                      placeholder="Enter product or service name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nameAr">Name (Arabic)</Label>
                    <Input
                      id="nameAr"
                      placeholder="اسم المنتج أو الخدمة"
                      dir="rtl"
                      value={formData.nameAr}
                      onChange={(e) => handleInputChange('nameAr', e.target.value)}
                    />
                  </div>
                </div>

                {/* SKU and Type Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sku">SKU</Label>
                    <Input
                      id="sku"
                      placeholder="Product SKU"
                      value={formData.sku}
                      onChange={(e) => handleInputChange('sku', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">Type</Label>
                    <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PHYSICAL">Physical Product</SelectItem>
                        <SelectItem value="SERVICE">Service</SelectItem>
                        <SelectItem value="DIGITAL">Digital Product</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Category and Supplier Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Category</Label>
                    <div className="flex space-x-2">
                      <Popover open={categorySearchOpen} onOpenChange={setCategorySearchOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            className="flex-1 justify-between"
                          >
                            {getSelectedCategory()?.name || "Select category"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[300px] p-0">
                          <Command>
                            <CommandInput placeholder="Search categories..." />
                            <CommandList>
                              <CommandEmpty>
                                <div className="p-4 text-center">
                                  <p className="text-sm text-muted-foreground mb-2">No category found.</p>
                                  <Button
                                    size="sm"
                                    onClick={() => {
                                      setShowNewCategoryDialog(true)
                                      setCategorySearchOpen(false)
                                    }}
                                  >
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add New Category
                                  </Button>
                                </div>
                              </CommandEmpty>
                              <CommandGroup>
                                {categories.map((category) => (
                                  <CommandItem
                                    key={category.id}
                                    value={`${category.name} ${category.nameAr || ''}`}
                                    onSelect={() => handleCategorySelect(category.id)}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        formData.categoryId === category.id ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    <div className="flex flex-col">
                                      <span className="font-medium">{category.name}</span>
                                      {category.nameAr && (
                                        <span className="text-sm text-muted-foreground">{category.nameAr}</span>
                                      )}
                                    </div>
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => setShowNewCategoryDialog(true)}
                        title="Add New Category"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Supplier</Label>
                    <div className="flex space-x-2">
                      <Popover open={supplierSearchOpen} onOpenChange={setSupplierSearchOpen}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            className="flex-1 justify-between"
                          >
                            {getSelectedSupplier()?.name || "Select supplier"}
                            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-[300px] p-0">
                          <Command>
                            <CommandInput placeholder="Search suppliers..." />
                            <CommandList>
                              <CommandEmpty>
                                <div className="p-4 text-center">
                                  <p className="text-sm text-muted-foreground mb-2">No supplier found.</p>
                                  <Button
                                    size="sm"
                                    onClick={() => {
                                      setShowNewSupplierDialog(true)
                                      setSupplierSearchOpen(false)
                                    }}
                                  >
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add New Supplier
                                  </Button>
                                </div>
                              </CommandEmpty>
                              <CommandGroup>
                                {suppliers.map((supplier) => (
                                  <CommandItem
                                    key={supplier.id}
                                    value={`${supplier.name} ${supplier.mobile} ${supplier.email || ''}`}
                                    onSelect={() => handleSupplierSelect(supplier.id)}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        formData.supplierId === supplier.id ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    <div className="flex flex-col">
                                      <span className="font-medium">{supplier.name}</span>
                                      <span className="text-sm text-muted-foreground">{supplier.mobile}</span>
                                    </div>
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => setShowNewSupplierDialog(true)}
                        title="Add New Supplier"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Product or service description"
                    rows={3}
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar - Image and Pricing */}
          <div className="space-y-6">
            {/* Image Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <ImageIcon className="mr-2 h-5 w-5" />
                  Product Image
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {imagePreview || currentImage ? (
                    <div className="relative">
                      <img
                        src={imagePreview || currentImage || ''}
                        alt="Product preview"
                        className="w-full h-48 object-cover rounded-lg border"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2"
                        onClick={removeImage}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                      <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground/50" />
                      <p className="mt-2 text-sm text-muted-foreground">No image selected</p>
                    </div>
                  )}
                  <div className="flex items-center justify-center w-full">
                    <label htmlFor="image-upload" className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                      <div className="flex flex-col items-center justify-center pt-5 pb-6">
                        <Upload className="w-8 h-8 mb-4 text-gray-500" />
                        <p className="mb-2 text-sm text-gray-500">
                          <span className="font-semibold">Click to upload</span> or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                      </div>
                      <input
                        id="image-upload"
                        type="file"
                        className="hidden"
                        accept="image/*"
                        onChange={handleImageSelect}
                      />
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Pricing & Stock */}
            <Card>
              <CardHeader>
                <CardTitle>Pricing & Stock</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Selling Price *</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.001"
                      placeholder="0.000"
                      value={formData.price}
                      onChange={(e) => handleInputChange('price', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cost">Cost Price</Label>
                    <Input
                      id="cost"
                      type="number"
                      step="0.001"
                      placeholder="0.000"
                      value={formData.cost}
                      onChange={(e) => handleInputChange('cost', e.target.value)}
                    />
                  </div>
                </div>

                {formData.type === 'PHYSICAL' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="stock">Current Stock</Label>
                      <Input
                        id="stock"
                        type="number"
                        placeholder="0"
                        value={formData.stock}
                        onChange={(e) => handleInputChange('stock', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="minStock">Min Stock Alert</Label>
                      <Input
                        id="minStock"
                        type="number"
                        placeholder="0"
                        value={formData.minStock}
                        onChange={(e) => handleInputChange('minStock', e.target.value)}
                      />
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label>Unit</Label>
                  <div className="flex space-x-2">
                    <Popover open={unitSearchOpen} onOpenChange={setUnitSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          className="flex-1 justify-between"
                        >
                          {getSelectedUnit() ? `${getSelectedUnit()?.name} (${getSelectedUnit()?.symbol})` : "Select unit"}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[300px] p-0">
                        <Command>
                          <CommandInput placeholder="Search units..." />
                          <CommandList>
                            <CommandEmpty>
                              <div className="p-4 text-center">
                                <p className="text-sm text-muted-foreground mb-2">No unit found.</p>
                                <Button
                                  size="sm"
                                  onClick={() => {
                                    setShowNewUnitDialog(true)
                                    setUnitSearchOpen(false)
                                  }}
                                >
                                  <Plus className="mr-2 h-4 w-4" />
                                  Add New Unit
                                </Button>
                              </div>
                            </CommandEmpty>
                            <CommandGroup>
                              {units.map((unit) => (
                                <CommandItem
                                  key={unit.id}
                                  value={`${unit.name} ${unit.nameAr || ''} ${unit.symbol}`}
                                  onSelect={() => handleUnitSelect(unit.id)}
                                >
                                  <Check
                                    className={cn(
                                      "mr-2 h-4 w-4",
                                      formData.unitId === unit.id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  <div className="flex flex-col">
                                    <div className="flex items-center space-x-2">
                                      <span className="font-medium">{unit.name}</span>
                                      <span className="text-sm text-muted-foreground">({unit.symbol})</span>
                                    </div>
                                    {unit.nameAr && (
                                      <span className="text-sm text-muted-foreground">{unit.nameAr}</span>
                                    )}
                                  </div>
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => setShowNewUnitDialog(true)}
                      title="Add New Unit"
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={loading || uploadingImage}>
            <Save className="mr-2 h-4 w-4" />
            {uploadingImage ? 'Uploading Image...' : loading ? 'Updating...' : 'Update Product'}
          </Button>
        </div>
      </form>

      {/* New Category Dialog */}
      <Dialog open={showNewCategoryDialog} onOpenChange={setShowNewCategoryDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Tag className="mr-2 h-5 w-5" />
              Add New Category
            </DialogTitle>
            <DialogDescription>
              Create a new category to organize your products
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="new-category-name">Category Name *</Label>
              <Input
                id="new-category-name"
                placeholder="Enter category name"
                value={newCategoryData.name}
                onChange={(e) => setNewCategoryData(prev => ({ ...prev, name: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-category-name-ar">Category Name (Arabic)</Label>
              <Input
                id="new-category-name-ar"
                placeholder="اسم الفئة"
                dir="rtl"
                value={newCategoryData.nameAr}
                onChange={(e) => setNewCategoryData(prev => ({ ...prev, nameAr: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-category-description">Description</Label>
              <Textarea
                id="new-category-description"
                placeholder="Category description"
                rows={2}
                value={newCategoryData.description}
                onChange={(e) => setNewCategoryData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowNewCategoryDialog(false)
                setNewCategoryData({ name: '', nameAr: '', description: '' })
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateCategory}>
              <Plus className="mr-2 h-4 w-4" />
              Create Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New Supplier Dialog */}
      <Dialog open={showNewSupplierDialog} onOpenChange={setShowNewSupplierDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Building className="mr-2 h-5 w-5" />
              Add New Supplier
            </DialogTitle>
            <DialogDescription>
              Create a new supplier for your products
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="new-supplier-name">Supplier Name *</Label>
                <Input
                  id="new-supplier-name"
                  placeholder="Enter supplier name"
                  value={newSupplierData.name}
                  onChange={(e) => setNewSupplierData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-supplier-mobile">Mobile *</Label>
                <Input
                  id="new-supplier-mobile"
                  placeholder="+968 9XXX XXXX"
                  value={newSupplierData.mobile}
                  onChange={(e) => setNewSupplierData(prev => ({ ...prev, mobile: e.target.value }))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="new-supplier-email">Email</Label>
                <Input
                  id="new-supplier-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={newSupplierData.email}
                  onChange={(e) => setNewSupplierData(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-supplier-contact">Contact Person</Label>
                <Input
                  id="new-supplier-contact"
                  placeholder="Contact person name"
                  value={newSupplierData.contactPerson}
                  onChange={(e) => setNewSupplierData(prev => ({ ...prev, contactPerson: e.target.value }))}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-supplier-address">Address</Label>
              <Textarea
                id="new-supplier-address"
                placeholder="Supplier address"
                rows={2}
                value={newSupplierData.address}
                onChange={(e) => setNewSupplierData(prev => ({ ...prev, address: e.target.value }))}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowNewSupplierDialog(false)
                setNewSupplierData({ name: '', mobile: '', email: '', address: '', contactPerson: '' })
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateSupplier}>
              <Plus className="mr-2 h-4 w-4" />
              Create Supplier
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New Unit Dialog */}
      <Dialog open={showNewUnitDialog} onOpenChange={setShowNewUnitDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Package className="mr-2 h-5 w-5" />
              Add New Unit
            </DialogTitle>
            <DialogDescription>
              Create a new unit of measurement for your products
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="new-unit-name">Unit Name *</Label>
                <Input
                  id="new-unit-name"
                  placeholder="Enter unit name"
                  value={newUnitData.name}
                  onChange={(e) => setNewUnitData(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-unit-symbol">Symbol *</Label>
                <Input
                  id="new-unit-symbol"
                  placeholder="Unit symbol"
                  value={newUnitData.symbol}
                  onChange={(e) => setNewUnitData(prev => ({ ...prev, symbol: e.target.value }))}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="new-unit-name-ar">Unit Name (Arabic)</Label>
                <Input
                  id="new-unit-name-ar"
                  placeholder="اسم الوحدة"
                  dir="rtl"
                  value={newUnitData.nameAr}
                  onChange={(e) => setNewUnitData(prev => ({ ...prev, nameAr: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-unit-symbol-ar">Symbol (Arabic)</Label>
                <Input
                  id="new-unit-symbol-ar"
                  placeholder="رمز الوحدة"
                  dir="rtl"
                  value={newUnitData.symbolAr}
                  onChange={(e) => setNewUnitData(prev => ({ ...prev, symbolAr: e.target.value }))}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-unit-description">Description</Label>
              <Textarea
                id="new-unit-description"
                placeholder="Unit description"
                rows={2}
                value={newUnitData.description}
                onChange={(e) => setNewUnitData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-unit-description-ar">Description (Arabic)</Label>
              <Textarea
                id="new-unit-description-ar"
                placeholder="وصف الوحدة"
                dir="rtl"
                rows={2}
                value={newUnitData.descriptionAr}
                onChange={(e) => setNewUnitData(prev => ({ ...prev, descriptionAr: e.target.value }))}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowNewUnitDialog(false)
                setNewUnitData({ name: '', nameAr: '', symbol: '', symbolAr: '', description: '', descriptionAr: '' })
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateUnit}>
              <Plus className="mr-2 h-4 w-4" />
              Create Unit
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
