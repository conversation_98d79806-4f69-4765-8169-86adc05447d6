"use client"

import { useState, useEffect } from "react"
import { use<PERSON>outer } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  ArrowLeft,
  ChevronLeft, 
  ChevronRight, 
  Calendar,
  Plus,
  Clock,
  Bell,
  CalendarDays,
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import { useI18n } from "@/lib/i18n"

interface CalendarEvent {
  id: string
  title: string
  titleAr?: string
  description?: string
  type: string
  category: string
  startDate: string
  endDate?: string
  dueDate?: string
  isAllDay: boolean
  status: string
  priority: string
  assignedTo?: {
    id: string
    name: string
    email: string
  }
}

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
  IN_PROGRESS: "bg-blue-100 text-blue-800 border-blue-200",
  COMPLETED: "bg-green-100 text-green-800 border-green-200",
  CANCELLED: "bg-gray-100 text-gray-800 border-gray-200",
  OVERDUE: "bg-red-100 text-red-800 border-red-200",
}

const priorityColors = {
  LOW: "border-l-gray-400",
  MEDIUM: "border-l-blue-400",
  HIGH: "border-l-orange-400",
  URGENT: "border-l-red-400",
  CRITICAL: "border-l-red-600",
}

const typeIcons = {
  REMINDER: Bell,
  MEETING: Calendar,
  DEADLINE: Clock,
  RENEWAL: CalendarDays,
  EXPIRATION: AlertTriangle,
  TASK_DUE: CheckCircle,
  INVOICE_DUE: CheckCircle,
  PAYMENT_DUE: CheckCircle,
  HOLIDAY: Calendar,
  OTHER: Calendar,
}

export default function CalendarViewPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [currentDate, setCurrentDate] = useState(new Date())
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month')
  const [loading, setLoading] = useState(true)

  // Load events from API
  useEffect(() => {
    const loadEvents = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/calendar')
        if (response.ok) {
          const data = await response.json()
          setEvents(data)
        }
      } catch (error) {
        console.error('Error loading calendar events:', error)
      } finally {
        setLoading(false)
      }
    }

    loadEvents()
  }, [])

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }
    
    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }
    
    return days
  }

  const getEventsForDate = (date: Date) => {
    if (!date) return []
    
    const dateStr = date.toISOString().split('T')[0]
    return events.filter(event => {
      const eventDate = new Date(event.dueDate || event.startDate).toISOString().split('T')[0]
      return eventDate === dateStr
    })
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const navigateToToday = () => {
    setCurrentDate(new Date())
  }

  const formatMonthYear = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
  }

  const isToday = (date: Date) => {
    if (!date) return false
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const days = getDaysInMonth(currentDate)
  const weekDays = [
    t('calendar.weekDays.sun'),
    t('calendar.weekDays.mon'),
    t('calendar.weekDays.tue'),
    t('calendar.weekDays.wed'),
    t('calendar.weekDays.thu'),
    t('calendar.weekDays.fri'),
    t('calendar.weekDays.sat')
  ]

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>{t('calendar.back')}</span>
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('calendar.calendarViewTitle')}</h2>
            <p className="text-muted-foreground">
              {t('calendar.calendarViewDescription')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={navigateToToday}>
            {t('calendar.today')}
          </Button>
          <Button onClick={() => router.push('/dashboard/calendar/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('calendar.addEvent')}
          </Button>
        </div>
      </div>

      {/* Calendar Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => navigateMonth('prev')}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h3 className="text-xl font-semibold min-w-[200px] text-center">
              {formatMonthYear(currentDate)}
            </h3>
            <Button variant="outline" size="sm" onClick={() => navigateMonth('next')}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={viewMode} onValueChange={(value: 'month' | 'week' | 'day') => setViewMode(value)}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">{t('calendar.month')}</SelectItem>
              <SelectItem value="week">{t('calendar.week')}</SelectItem>
              <SelectItem value="day">{t('calendar.day')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Calendar Grid */}
      <Card>
        <CardContent className="p-0">
          <div className="grid grid-cols-7 border-b">
            {weekDays.map((day) => (
              <div key={day} className="p-4 text-center font-medium text-muted-foreground border-r last:border-r-0">
                {day}
              </div>
            ))}
          </div>
          <div className="grid grid-cols-7">
            {days.map((date, index) => {
              const dayEvents = date ? getEventsForDate(date) : []
              const isCurrentDay = date ? isToday(date) : false
              
              return (
                <div
                  key={index}
                  className={`min-h-[120px] p-2 border-r border-b last:border-r-0 ${
                    !date ? 'bg-gray-50' : isCurrentDay ? 'bg-blue-50' : 'bg-white'
                  }`}
                >
                  {date && (
                    <>
                      <div className={`text-sm font-medium mb-2 ${
                        isCurrentDay ? 'text-blue-600' : 'text-gray-900'
                      }`}>
                        {date.getDate()}
                      </div>
                      <div className="space-y-1">
                        {dayEvents.slice(0, 3).map((event) => {
                          const TypeIcon = typeIcons[event.type as keyof typeof typeIcons] || Calendar
                          return (
                            <div
                              key={event.id}
                              className={`text-xs p-1 rounded border-l-2 cursor-pointer hover:shadow-sm transition-shadow ${
                                statusColors[event.status as keyof typeof statusColors]
                              } ${priorityColors[event.priority as keyof typeof priorityColors]}`}
                              onClick={() => router.push(`/dashboard/calendar/${event.id}`)}
                            >
                              <div className="flex items-center space-x-1">
                                <TypeIcon className="h-3 w-3" />
                                <span className="truncate font-medium">{event.title}</span>
                              </div>
                              {event.assignedTo && (
                                <div className="text-xs text-muted-foreground truncate">
                                  {event.assignedTo.name}
                                </div>
                              )}
                            </div>
                          )
                        })}
                        {dayEvents.length > 3 && (
                          <div className="text-xs text-muted-foreground">
                            +{dayEvents.length - 3} {t('calendar.more')}
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{t('calendar.legend')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <h4 className="font-medium mb-2">{t('calendar.status')}</h4>
              <div className="space-y-1">
                {Object.entries(statusColors).map(([status, color]) => (
                  <div key={status} className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded border ${color}`}></div>
                    <span className="text-sm">{status.replace('_', ' ')}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">{t('calendar.priority')}</h4>
              <div className="space-y-1">
                {Object.entries(priorityColors).map(([priority, color]) => (
                  <div key={priority} className="flex items-center space-x-2">
                    <div className={`w-3 h-3 border-l-4 ${color}`}></div>
                    <span className="text-sm">{priority}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">{t('calendar.eventTypes')}</h4>
              <div className="space-y-1">
                {Object.entries(typeIcons).slice(0, 5).map(([type, Icon]) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Icon className="w-3 h-3" />
                    <span className="text-sm">{type.replace('_', ' ')}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-2">{t('calendar.quickActions')}</h4>
              <div className="space-y-2">
                <Button size="sm" variant="outline" onClick={() => router.push('/dashboard/calendar/create')}>
                  <Plus className="mr-1 h-3 w-3" />
                  {t('calendar.addEvent')}
                </Button>
                <Button size="sm" variant="outline" onClick={() => router.push('/dashboard/calendar')}>
                  <Calendar className="mr-1 h-3 w-3" />
                  {t('calendar.listView')}
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
