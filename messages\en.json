{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "print": "Print", "loading": "Loading...", "noData": "No data available", "confirm": "Confirm", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "view": "View", "actions": "Actions", "status": "Status", "date": "Date", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "notes": "Notes", "description": "Description", "quantity": "Quantity", "price": "Price", "amount": "Amount"}, "navigation": {"dashboard": "Dashboard", "customers": "Customers", "suppliers": "Suppliers", "products": "Products & Services", "tasks": "Tasks", "invoices": "Invoices", "quotations": "Quotations", "purchases": "Purchases", "employees": "Employees", "reports": "Reports", "settings": "Settings", "leads": "Leads", "projects": "Projects", "calendar": "Calendar & Reminders", "pos": "Point of Sale", "financial": "Financial", "expenses": "Expenses"}, "nav": {"dashboard": "Dashboard", "customers": "Customers", "suppliers": "Suppliers", "products": "Products & Services", "tasks": "Tasks", "invoices": "Invoices", "quotations": "Quotations", "purchases": "Purchases", "employees": "Employees", "reports": "Reports", "settings": "Settings", "leads": "Leads", "projects": "Projects", "calendar": "Calendar & Reminders", "pos": "Point of Sale", "financial": "Financial", "expenses": "Expenses"}, "dashboard": {"title": "Dashboard", "totalSales": "Total Sales", "pendingTasks": "Pending Tasks", "overdueInvoices": "Overdue Invoices", "lowStock": "Low Stock Items", "recentActivities": "Recent Activities", "salesChart": "Sales Overview", "taskChart": "Task Performance"}, "customers": {"title": "Customers", "addCustomer": "Add Customer", "editCustomer": "Edit Customer", "customerDetails": "Customer Details", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "company": "Company", "taxNumber": "Tax Number", "customerDetailsAndHistory": "Customer details and transaction history", "customerNotFound": "Customer Not Found", "customerInformation": "Customer Information", "contactInformation": "Contact Information", "customerSince": "Customer Since", "lastOrderDate": "Last Order", "customerSummary": "Customer Summary", "totalSpent": "Total Spent", "outstandingBalance": "Outstanding Balance", "totalInvoices": "Total Invoices", "totalQuotations": "Total Quotations", "quickActions": "Quick Actions", "createInvoice": "Create Invoice", "createQuotation": "Create Quotation", "sendEmail": "Send Email", "callCustomer": "Call Customer", "paymentStatus": "Payment Status", "paidInvoices": "Paid Invoices", "sentInvoices": "Sent Invoices", "overdueInvoices": "Overdue Invoices", "lastPayment": "Last payment", "customerInvoices": "Customer Invoices", "customerQuotations": "Customer Quotations", "paymentHistory": "Payment History", "invoiceNumber": "Invoice #", "dueDate": "Due Date", "balance": "Balance", "method": "Method", "reference": "Reference", "quotationNumber": "Quotation #", "validUntil": "<PERSON>id <PERSON>", "noInvoicesFound": "No invoices found for this customer", "noQuotationsFound": "No quotations found for this customer", "confirmDelete": "Are you sure you want to delete this customer?", "paid": "Paid"}, "tasks": {"title": "Tasks", "description": "Manage and track work assignments", "addTask": "Add Task", "searchTasks": "Search tasks...", "status": "Status", "priority": "Priority", "allStatus": "All Status", "allPriority": "All Priority", "new": "New", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>", "totalTasks": "Total Tasks", "newTasks": "New Tasks", "task": "Task", "customer": "Customer", "assignedTo": "Assigned To", "estHours": "Est. Hours", "actions": "Actions", "noCustomer": "No Customer", "unassigned": "Unassigned", "viewDetails": "View Details", "editTask": "Edit Task", "startTask": "Start Task", "pauseTask": "Pause Task", "completeTask": "Complete Task", "cancelTask": "Cancel Task", "delete": "Delete", "taskStartedSuccessfully": "Task started successfully", "failedToStartTask": "Failed to start task", "errorStartingTask": "Error starting task", "taskPausedSuccessfully": "Task paused successfully", "failedToPauseTask": "Failed to pause task", "errorPausingTask": "Error pausing task", "confirmCancelTask": "Are you sure you want to cancel task", "taskCancelledSuccessfully": "Task cancelled successfully", "failedToCancelTask": "Failed to cancel task", "errorCancellingTask": "Error cancelling task", "confirmDeleteTask": "Are you sure you want to delete task", "taskDeletedSuccessfully": "Task deleted successfully", "failedToDeleteTask": "Failed to delete task", "errorDeletingTask": "Error deleting task"}, "invoices": {"title": "Invoices", "addInvoice": "Add Invoice", "editInvoice": "Edit Invoice", "invoiceDetails": "Invoice Details", "invoiceNumber": "Invoice Number", "dueDate": "Due Date", "paymentStatus": {"paid": "Paid", "partial": "Partial", "unpaid": "Unpaid", "overdue": "Overdue"}}, "projects": {"title": "Projects", "addProject": "Add Project", "editProject": "Edit Project", "createProject": "Create Project", "updateProject": "Update Project", "deleteProject": "Delete Project", "projectDetails": "Project Details", "projectName": "Project Name", "projectNameAr": "Project Name (Arabic)", "projectCode": "Project Code", "projectDescription": "Project Description", "projectManager": "Project Manager", "projectBudget": "Project Budget", "actualCost": "Actual Cost", "projectProgress": "Project Progress", "projectStatus": "Project Status", "projectPriority": "Project Priority", "teamMembers": "Team Members", "projectTasks": "Project Tasks", "projectInvoices": "Project Invoices", "projectExpenses": "Project Expenses", "projectNotes": "Project Notes", "searchPlaceholder": "Search projects, clients...", "noProjectsFound": "No projects found", "createFirstProject": "Create your first project!", "noProjectsFoundSearch": "No projects found matching your search.", "createFirstProjectAlt": "No projects found. Create your first project!", "totalProjects": "Total Projects", "activeProjects": "Active Projects", "completedProjects": "Completed Projects", "onHoldProjects": "On Hold Projects", "cancelledProjects": "Cancelled Projects", "totalBudget": "Total Budget", "totalActualCost": "Total Actual Cost", "averageProgress": "Average Progress", "overdueProjects": "Overdue Projects", "recentProjects": "Recent Projects", "upcomingDeadlines": "Upcoming Deadlines", "highPriorityProjects": "High Priority Projects", "projectPerformance": "Project Performance", "budgetUtilization": "Budget Utilization", "timelineOverview": "Timeline Overview", "financialOverview": "Financial Overview", "addTeamMember": "Add Team Member", "removeTeamMember": "Remove Team Member", "assignManager": "Assign Manager", "changeStatus": "Change Status", "viewTasks": "View Tasks", "addTask": "Add Task", "viewInvoices": "View Invoices", "addInvoice": "Add Invoice", "viewExpenses": "View Expenses", "addExpense": "Add Expense", "projectManagement": "Project Management", "manageDescription": "Manage your projects, track progress, and collaborate with your team", "dashboard": "Dashboard", "createProjectDescription": "Set up a new project with timeline, budget, and team assignments", "projectInformation": "Project Information", "searchAndSelectClient": "Search and select client...", "searchAndSelectClientPlaceholder": "Search and select client...", "selectProjectManager": "Select Project Manager", "startDate": "Start Date", "endDate": "End Date", "dueDate": "Due Date", "estimatedDuration": "Estimated Duration", "actualDuration": "Actual Duration", "client": "Client", "clientName": "Client Name", "projectDescriptionPlaceholder": "Describe the project goals, scope, and deliverables...", "additionalNotes": "Additional Notes", "additionalNotesPlaceholder": "Any additional notes or requirements...", "projectNamePlaceholder": "Enter project name", "projectCodePlaceholder": "Enter project code", "budgetPlaceholder": "Enter project budget", "selectClient": "Select Client", "searchClients": "Search clients...", "noClientFound": "No client found", "projectCreatedSuccessfully": "Project created successfully!", "failedToCreateProject": "Failed to create project", "projectUpdatedSuccessfully": "Project updated successfully!", "failedToUpdateProject": "Failed to update project", "projectDeletedSuccessfully": "Project deleted successfully!", "failedToDeleteProject": "Failed to delete project", "confirmDeleteProject": "Are you sure you want to delete this project?", "noClient": "No Client", "noDueDate": "No Due Date", "noManager": "No Manager", "noDescription": "No Description", "notStarted": "Not Started", "archived": "Archived", "statuses": {"planning": "Planning", "inProgress": "In Progress", "onHold": "On Hold", "completed": "Completed", "cancelled": "Cancelled"}, "priorities": {"low": "Low", "medium": "Medium", "high": "High", "urgent": "<PERSON><PERSON>"}}, "products": {"title": "Products & Services", "addProduct": "Add Product", "editProduct": "Edit Product", "productDetails": "Product Details", "sku": "SKU", "category": "Category", "type": "Type", "stock": "Stock", "minStock": "Minimum Stock", "unit": "Unit", "cost": "Cost", "price": "Price", "supplier": "Supplier", "createProductTitle": "Create Product", "createProductDescription": "Add a new product or service to your inventory", "productInformation": "Product Information", "basicProductDetails": "Basic product details and identification", "productServiceNameRequired": "Product/Service Name *", "enterProductName": "Enter product or service name", "nameArabic": "Name (Arabic)", "productNameArabicPlaceholder": "اسم المنتج أو الخدمة", "productSku": "Product SKU", "selectType": "Select type", "physicalProduct": "Physical Product", "service": "Service", "digitalProduct": "Digital Product", "selectCategory": "Select category", "searchCategories": "Search categories...", "noCategoryFound": "No category found.", "addNewCategory": "Add New Category", "selectSupplier": "Select supplier", "searchSuppliers": "Search suppliers...", "noSupplierFound": "No supplier found.", "addNewSupplier": "Add New Supplier", "selectUnit": "Select unit", "searchUnits": "Search units...", "noUnitFound": "No unit found.", "addNewUnit": "Add New Unit", "pricingStock": "Pricing & Stock", "sellingPriceRequired": "Se<PERSON> (Required)", "currentStock": "Current Stock", "minStockAlert": "<PERSON>", "noImageSelected": "No image selected", "clickToUpload": "Click to upload", "dragAndDrop": "or drag and drop", "imageFormats": "PNG, JPG, GIF up to 10MB", "failedToLoadCategoriesSuppliers": "Failed to load categories and suppliers", "categoryNameRequired": "Category name is required", "categoryCreatedSuccessfully": "Category created successfully", "failedToCreateCategory": "Failed to create category", "errorCreatingCategory": "Error creating category", "supplierNameMobileRequired": "Supplier name and mobile are required", "supplierCreatedSuccessfully": "Supplier created successfully", "failedToCreateSupplier": "Failed to create supplier", "errorCreatingSupplier": "Error creating supplier", "unitNameSymbolRequired": "Unit name and symbol are required", "unitCreatedSuccessfully": "Unit created successfully", "failedToCreateUnit": "Failed to create unit", "errorCreatingUnit": "Error creating unit", "pleaseSelectImageFile": "Please select an image file", "imageSizeMustBeLess": "Image size must be less than 5MB", "productNamePriceRequired": "Product name and price are required", "imageUploadedSuccessfully": "Image uploaded successfully", "failedToUploadImage": "Failed to upload image", "productCreatedSuccessfully": "Product created successfully", "failedToCreateProduct": "Failed to create product", "errorCreatingProduct": "Error creating product", "types": {"physical": "Physical", "service": "Service"}}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginTitle": "Sign in to your account", "invalidCredentials": "Invalid email or password"}, "settings": {"title": "Settings", "general": "General Settings", "whatsapp": "WhatsApp Integration", "company": "Company Profile", "users": "User Management", "language": "Language", "companyName": "Company Name", "companyAddress": "Company Address", "companyPhone": "Company Phone", "companyEmail": "Company Email", "whatsappApiUrl": "WhatsApp API URL", "whatsappAccountKey": "Account Key", "whatsappSecretKey": "Secret Key"}, "stock": {"title": "Stock Management", "description": "Monitor and manage your inventory levels", "products": "Products", "stockReport": "Stock Report", "totalProducts": "Total Products", "lowStockAlert": "Low Stock Alert", "stockValue": "Stock Value", "potentialProfit": "Potential Profit", "activeInventoryItems": "Active inventory items", "itemsNeedRestocking": "Items need restocking", "totalInventoryCost": "Total inventory cost", "ifAllStockSold": "If all stock sold", "searchProducts": "Search products, SKU, or category...", "filterByStatus": "Filter by status", "allProducts": "All Products", "inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock", "product": "Product", "sku": "SKU", "category": "Category", "currentStock": "Current Stock", "minMaxStock": "Min/Max <PERSON>", "stockLevel": "Stock Level", "value": "Value", "location": "Location", "status": "Status", "actions": "Actions", "adjust": "Adjust", "stockAdjustment": "Stock Adjustment", "adjustStockLevels": "Adjust stock levels for", "adjustmentType": "Adjustment Type", "stockIn": "Stock In (+)", "stockOut": "Stock Out (-)", "quantity": "Quantity", "reason": "Reason *", "notes": "Notes", "selectReason": "Select reason", "purchaseOrder": "Purchase Order", "sales": "Sales", "damaged": "Damaged", "lost": "Lost", "returned": "Returned", "transfer": "Transfer", "stockCount": "Stock Count", "other": "Other", "enterQuantity": "Enter quantity", "additionalNotes": "Additional notes...", "cancel": "Cancel", "saveAdjustment": "Save Adjustment", "loadingStockData": "Loading stock data...", "noStockDataAvailable": "No stock data available", "fillAllRequiredFields": "Please fill in all required fields", "stockIncreased": "increased", "stockDecreased": "decreased", "failedToSaveStockAdjustment": "Failed to save stock adjustment", "cost": "Cost"}, "suppliers": {"title": "Suppliers", "addSupplier": "Add Supplier", "editSupplier": "Edit Supplier", "supplierDetails": "Supplier Details", "supplierName": "Supplier Name", "supplierEmail": "Supplier Email", "supplierPhone": "Supplier Phone", "supplierAddress": "Supplier Address", "supplierCompany": "Supplier Company", "contactPerson": "Contact Person", "taxNumber": "Tax Number", "searchSuppliers": "Search suppliers...", "noSuppliers": "No suppliers found", "totalSuppliers": "Total Suppliers", "activeSuppliers": "Active Suppliers", "supplierAdded": "Supplier added successfully", "supplierUpdated": "Supplier updated successfully", "supplierDeleted": "Supplier deleted successfully", "deleteSupplier": "Delete Supplier", "confirmDelete": "Are you sure you want to delete this supplier?", "totalPurchases": "Total Purchases", "outstandingPayments": "Outstanding Payments", "purchaseOrders": "Purchase Orders", "lastOrderDate": "Last Order", "viewDetails": "View Details", "createPurchaseOrder": "Create Purchase Order", "dashboard": "Suppliers Dashboard", "noSuppliersFound": "No suppliers found", "supplierDetailsAndHistory": "Supplier details and procurement history", "supplierNotFound": "Supplier Not Found", "supplierInformation": "Supplier Information", "contactInformation": "Contact Information", "supplierSince": "Supplier Since", "lastOrder": "Last Order", "supplierSummary": "Supplier Summary", "quickActions": "Quick Actions", "createPayout": "Create Payout", "sendEmail": "Send Email", "callSupplier": "Call Supplier", "paymentStatus": "Payment Status", "paidOrders": "Paid Orders", "receivedOrders": "Received Orders", "overdueOrders": "Overdue Orders", "lastPayment": "Last payment", "supplierProducts": "Supplier Products", "paymentHistory": "Payment History", "purchaseOrderNumber": "PO #", "dueDate": "Due Date", "balance": "Balance", "method": "Method", "reference": "Reference", "product": "Product", "sku": "SKU", "category": "Category", "unitPrice": "Unit Price", "stock": "Stock", "noPurchaseOrdersFound": "No purchase orders found for this supplier.", "noProductsFound": "No products found for this supplier.", "confirmDeleteSupplier": "Are you sure you want to delete this supplier?", "viewPurchaseDetails": "View Details", "editOrder": "Edit Order", "openMenu": "Open menu", "products": "Products", "payments": "Payments", "orders": "Purchase Orders", "mobile": "Mobile", "phone": "Phone", "company": "Company", "address": "Address", "notes": "Notes", "status": "Status", "active": "Active", "inactive": "Inactive", "actions": "Actions", "back": "Back", "loading": "Loading...", "amount": "Amount", "date": "Date", "total": "Total"}}