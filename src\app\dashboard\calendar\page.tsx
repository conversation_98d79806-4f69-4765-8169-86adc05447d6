"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Bell,
  CalendarDays,
} from "lucide-react"
import { useI18n } from "@/lib/i18n"

interface CalendarEvent {
  id: string
  title: string
  titleAr?: string
  description?: string
  type: string
  category: string
  startDate: string
  endDate?: string
  dueDate?: string
  isAllDay: boolean
  status: string
  priority: string
  notifyBefore?: number
  isRecurring: boolean
  recurringType?: string
  relatedEntityType?: string
  relatedEntityId?: string
  notes?: string
  createdAt: string
  updatedAt: string
  createdBy?: {
    id: string
    name: string
    email: string
  }
  assignedTo?: {
    id: string
    name: string
    email: string
  }
}

export default function CalendarPage() {
  const router = useRouter()
  const { t, language, direction } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [typeFilter, setTypeFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [loading, setLoading] = useState(true)

  // Translated status and priority options
  const statusColors = {
    PENDING: "bg-yellow-100 text-yellow-800",
    IN_PROGRESS: "bg-blue-100 text-blue-800",
    COMPLETED: "bg-green-100 text-green-800",
    CANCELLED: "bg-gray-100 text-gray-800",
    OVERDUE: "bg-red-100 text-red-800",
  }

  const priorityColors = {
    LOW: "bg-gray-100 text-gray-800",
    MEDIUM: "bg-blue-100 text-blue-800",
    HIGH: "bg-orange-100 text-orange-800",
    URGENT: "bg-red-100 text-red-800",
    CRITICAL: "bg-red-200 text-red-900",
  }

  const typeIcons = {
    REMINDER: Bell,
    MEETING: Calendar,
    DEADLINE: Clock,
    RENEWAL: CalendarDays,
    EXPIRATION: AlertTriangle,
    TASK_DUE: CheckCircle,
    INVOICE_DUE: CheckCircle,
    PAYMENT_DUE: CheckCircle,
    HOLIDAY: Calendar,
    OTHER: Calendar,
  }

  // Load events from API
  useEffect(() => {
    const loadEvents = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/calendar')
        if (response.ok) {
          const data = await response.json()
          setEvents(data)
        }
      } catch (error) {
        console.error('Error loading calendar events:', error)
      } finally {
        setLoading(false)
      }
    }

    loadEvents()
  }, [])

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.assignedTo?.name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = typeFilter === "all" || event.type.toLowerCase() === typeFilter.toLowerCase()
    const matchesCategory = categoryFilter === "all" || event.category.toLowerCase() === categoryFilter.toLowerCase()
    const matchesStatus = statusFilter === "all" || event.status.toLowerCase() === statusFilter.toLowerCase()

    return matchesSearch && matchesType && matchesCategory && matchesStatus
  })

  const handleViewEvent = (event: CalendarEvent) => {
    router.push(`/dashboard/calendar/${event.id}`)
  }

  const handleEditEvent = (event: CalendarEvent) => {
    router.push(`/dashboard/calendar/${event.id}/edit`)
  }

  const handleDeleteEvent = async (event: CalendarEvent) => {
    if (confirm(`Are you sure you want to delete "${event.title}"?`)) {
      try {
        const response = await fetch(`/api/calendar/${event.id}`, {
          method: 'DELETE'
        })
        
        if (response.ok) {
          setEvents(prev => prev.filter(e => e.id !== event.id))
          alert(`${t('calendar.eventDeletedSuccessfully') || 'Event deleted successfully!'}: "${event.title}"`)
        }
      } catch (error) {
        console.error('Error deleting event:', error)
        alert(t('calendar.failedToDeleteEvent') || 'Failed to delete event')
      }
    }
  }

  // Calculate statistics
  const totalEvents = events.length
  const upcomingEvents = events.filter(e => new Date(e.dueDate || e.startDate) > new Date()).length
  const overdueEvents = events.filter(e => e.status === 'OVERDUE').length
  const completedEvents = events.filter(e => e.status === 'COMPLETED').length

  return (
    <div className={`flex-1 space-y-6 p-8 pt-6 ${direction === 'rtl' ? 'font-arabic' : ''}`} dir={direction}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('calendar.calendarAndReminders') || 'Calendar & Reminders'}</h2>
          <p className="text-muted-foreground">
            {t('calendar.manageDescription') || 'Manage document renewals, deadlines, and important dates'}
          </p>
        </div>
        <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
          <Button variant="outline" onClick={() => router.push('/dashboard/calendar/calendar-view')}>
            <Calendar className={`${direction === 'rtl' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
            {t('calendar.calendarView') || 'Calendar View'}
          </Button>
          <Button onClick={() => router.push('/dashboard/calendar/create')}>
            <Plus className={`${direction === 'rtl' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
            {t('calendar.addEvent') || 'Add Event'}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-gradient-to-br from-blue-50 to-indigo-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">{t('calendar.totalEvents') || 'Total Events'}</p>
              <p className="text-3xl font-bold text-blue-900">{totalEvents}</p>
            </div>
            <div className="rounded-full bg-blue-100 p-3">
              <Calendar className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-green-50 to-emerald-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">{t('calendar.upcomingEvents') || 'Upcoming'}</p>
              <p className="text-3xl font-bold text-green-900">{upcomingEvents}</p>
            </div>
            <div className="rounded-full bg-green-100 p-3">
              <Clock className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-red-50 to-pink-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-600">{t('calendar.overdueEvents') || 'Overdue'}</p>
              <p className="text-3xl font-bold text-red-900">{overdueEvents}</p>
            </div>
            <div className="rounded-full bg-red-100 p-3">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-purple-50 to-pink-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">{t('calendar.completedEvents') || 'Completed'}</p>
              <p className="text-3xl font-bold text-purple-900">{completedEvents}</p>
            </div>
            <div className="rounded-full bg-purple-100 p-3">
              <CheckCircle className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('calendar.searchPlaceholder') || 'Search events...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="reminder">Reminder</SelectItem>
            <SelectItem value="meeting">Meeting</SelectItem>
            <SelectItem value="deadline">Deadline</SelectItem>
            <SelectItem value="renewal">Renewal</SelectItem>
            <SelectItem value="expiration">Expiration</SelectItem>
            <SelectItem value="task_due">Task Due</SelectItem>
            <SelectItem value="invoice_due">Invoice Due</SelectItem>
            <SelectItem value="payment_due">Payment Due</SelectItem>
          </SelectContent>
        </Select>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="document">Document</SelectItem>
            <SelectItem value="financial">Financial</SelectItem>
            <SelectItem value="legal">Legal</SelectItem>
            <SelectItem value="hr">HR</SelectItem>
            <SelectItem value="business">Business</SelectItem>
          </SelectContent>
        </Select>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Events Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('calendar.eventTitle') || 'Event'}</TableHead>
              <TableHead>{t('calendar.eventType') || 'Type'}</TableHead>
              <TableHead>{t('calendar.eventCategory') || 'Category'}</TableHead>
              <TableHead>{t('common.dueDate') || 'Due Date'}</TableHead>
              <TableHead>{t('common.status') || 'Status'}</TableHead>
              <TableHead>{t('calendar.eventPriority') || 'Priority'}</TableHead>
              <TableHead>{t('common.assignedTo') || 'Assigned To'}</TableHead>
              <TableHead className="text-right">{t('common.actions') || 'Actions'}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span>Loading events...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredEvents.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm ? 'No events found matching your search.' : 'No events found. Create your first event!'}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredEvents.map((event) => {
                const TypeIcon = typeIcons[event.type as keyof typeof typeIcons] || Calendar
                return (
                  <TableRow key={event.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <TypeIcon className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium"
                            onClick={() => handleViewEvent(event)}
                          >
                            {event.title}
                          </Button>
                          {event.titleAr && (
                            <div className="text-sm text-muted-foreground">{event.titleAr}</div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {event.type.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {event.category}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {event.dueDate ? new Date(event.dueDate).toLocaleDateString() : 
                       new Date(event.startDate).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Badge className={statusColors[event.status as keyof typeof statusColors]}>
                        {event.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={priorityColors[event.priority as keyof typeof priorityColors]}>
                        {event.priority}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {event.assignedTo?.name || 'Unassigned'}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewEvent(event)}>
                            <Eye className="mr-2 h-4 w-4" />
                            {t('common.view') || 'View Details'}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditEvent(event)}>
                            <Edit className="mr-2 h-4 w-4" />
                            {t('calendar.editEvent') || 'Edit Event'}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleDeleteEvent(event)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t('calendar.deleteEvent') || 'Delete Event'}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
