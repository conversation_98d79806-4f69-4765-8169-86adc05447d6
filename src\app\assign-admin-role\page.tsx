"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function AssignAdminRolePage() {
  const { data: session } = useSession()
  const [result, setResult] = useState<string>("")
  const [loading, setLoading] = useState(false)

  const assignSuperAdminRole = async () => {
    if (!session?.user?.id) {
      setResult("No user session found")
      return
    }

    setLoading(true)
    try {
      // First, get the Super Admin role
      const rolesResponse = await fetch('/api/roles')
      const roles = await rolesResponse.json()
      const superAdminRole = roles.find((role: any) => role.name === 'Super Admin')

      if (!superAdminRole) {
        setResult("Super Admin role not found. Please seed permissions first.")
        return
      }

      // Assign the Super Admin role to current user
      const assignResponse = await fetch(`/api/users/${session.user.id}/assign-role`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          roleId: superAdminRole.id,
        }),
      })

      const assignResult = await assignResponse.json()
      
      if (assignResponse.ok) {
        setResult(`Success! Super Admin role assigned to ${session.user.email}`)
      } else {
        setResult(`Error: ${assignResult.error}`)
      }
    } catch (error) {
      setResult(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Assign Super Admin Role</CardTitle>
          <CardDescription>
            Assign the Super Admin role to the current user to access all features
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {session?.user && (
            <div className="p-4 bg-gray-100 rounded">
              <p><strong>Current User:</strong> {session.user.email}</p>
              <p><strong>Name:</strong> {session.user.name}</p>
            </div>
          )}
          
          <Button 
            onClick={assignSuperAdminRole} 
            disabled={loading || !session?.user}
            className="w-full"
          >
            {loading ? "Assigning Role..." : "Assign Super Admin Role"}
          </Button>
          
          {result && (
            <div className={`p-4 rounded ${
              result.includes('Success') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {result}
            </div>
          )}
          
          <div className="text-sm text-gray-600">
            <p><strong>Note:</strong> This will give you full access to all features including:</p>
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>User management</li>
              <li>Role management</li>
              <li>System settings</li>
              <li>All modules and features</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
