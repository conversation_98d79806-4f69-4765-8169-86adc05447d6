"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
} from "recharts"
import {
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  TrendingUp,
  DollarSign,
  Users,
  Calendar,
  Plus,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Calculator
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

// Quotation stats will be fetched from API

// Chart data will be fetched from API
const monthlyData: { month: string; quotations: number; value: number }[] = []
const statusData: { name: string; value: number; color: string }[] = []

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
  EXPIRED: "bg-gray-100 text-gray-800",
  CONVERTED: "bg-blue-100 text-blue-800",
}

export default function QuotationDashboard() {
  const router = useRouter()
  const { t } = useI18n()
  const [quotationStats, setQuotationStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    expired: 0,
    totalValue: 0,
    approvedValue: 0,
    pendingValue: 0,
    conversionRate: 0,
    avgQuotationValue: 0
  })
  type Quotation = {
    id: string | number
    number: string
    customer: string
    date: string | Date
    validUntil: string | Date
    status: string
    total: number
  }
  const [recentQuotations, setRecentQuotations] = useState<Quotation[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchQuotationData()
  }, [])

  const fetchQuotationData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/quotations/dashboard')
      if (response.ok) {
        const data = await response.json()
        setQuotationStats(data.stats || quotationStats)
        setRecentQuotations(data.recentQuotations || [])
      }
    } catch (error) {
      console.error('Error fetching quotation data:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('quotations.dashboard')}</h2>
          <p className="text-muted-foreground">
            {t('quotations.dashboardDescription')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/quotations')}>
            <FileText className="mr-2 h-4 w-4" />
            {t('quotations.viewAllQuotations')}
          </Button>
          <Button onClick={() => router.push('/dashboard/quotations/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('quotations.createQuotation')}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('quotations.totalQuotations')}</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quotationStats.total}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +12% {t('quotations.fromLastMonth')}
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('quotations.totalValue')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(quotationStats.totalValue)}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +8% {t('quotations.fromLastMonth')}
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('quotations.conversionRate')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quotationStats.conversionRate}%</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-red-600 flex items-center">
                <ArrowDownRight className="h-3 w-3 mr-1" />
                -3% {t('quotations.fromLastMonth')}
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('quotations.avgQuotationValue')}</CardTitle>
            <Calculator className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(quotationStats.avgQuotationValue)}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-gray-600 flex items-center">
                <Minus className="h-3 w-3 mr-1" />
                {t('quotations.noChange')}
              </span>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Monthly Trend */}
        <Card>
          <CardHeader>
            <CardTitle>{t('quotations.monthlyTrend')}</CardTitle>
            <CardDescription>
              {t('quotations.monthlyTrendDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip
                  formatter={(value, name) => [
                    name === 'quotations' ? value : formatCurrency(value as number),
                    name === 'quotations' ? t('quotations.quotations') : t('quotations.value')
                  ]}
                />
                <Bar yAxisId="left" dataKey="quotations" fill="#3b82f6" />
                <Line yAxisId="right" type="monotone" dataKey="value" stroke="#10b981" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>{t('quotations.statusDistribution')}</CardTitle>
            <CardDescription>
              {t('quotations.statusDistributionDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${t('quotations.status.' + name)} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Quotations */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t('quotations.recentQuotations')}</CardTitle>
              <CardDescription>
                {t('quotations.recentQuotationsDescription')}
              </CardDescription>
            </div>
            <Button variant="outline" onClick={() => router.push('/dashboard/quotations')}>
              <Eye className="mr-2 h-4 w-4" />
              {t('quotations.viewAll')}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('quotations.quotationNumber')}</TableHead>
                <TableHead>{t('quotations.customer')}</TableHead>
                <TableHead>{t('quotations.date')}</TableHead>
                <TableHead>{t('quotations.validUntil')}</TableHead>
                <TableHead>{t('quotations.status')}</TableHead>
                <TableHead className="text-right">{t('quotations.amount')}</TableHead>
                <TableHead className="text-right">{t('quotations.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentQuotations.map((quotation) => (
                <TableRow key={quotation.id}>
                  <TableCell className="font-medium">
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium"
                      onClick={() => router.push(`/dashboard/quotations/${quotation.id}`)}
                    >
                      {quotation.number}
                    </Button>
                  </TableCell>
                  <TableCell>{quotation.customer}</TableCell>
                  <TableCell>{new Date(quotation.date).toLocaleDateString()}</TableCell>
                  <TableCell>{new Date(quotation.validUntil).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Badge className={statusColors[quotation.status as keyof typeof statusColors]}>
                      {t('quotations.status.' + quotation.status)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatCurrency(quotation.total)}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(`/dashboard/quotations/${quotation.id}`)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
