"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"

export default function SeedPermissionsPage() {
  const [result, setResult] = useState<string>("")
  const [loading, setLoading] = useState(false)

  const runSeeding = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/seed-permissions', {
        method: 'POST',
      })
      const data = await response.json()
      setResult(JSON.stringify(data, null, 2))
    } catch (error) {
      setResult(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Seed Permissions & Roles</h1>
      <Button onClick={runSeeding} disabled={loading}>
        {loading ? "Seeding..." : "Seed Database"}
      </Button>
      {result && (
        <pre className="mt-4 p-4 bg-gray-100 rounded text-sm overflow-auto">
          {result}
        </pre>
      )}
    </div>
  )
}
