"use client"

import * as React from "react"

import { cn } from "@/lib/utils"

function Table({ className, ...props }: React.ComponentProps<"table">) {
  return (
    <div
      data-slot="table-container"
      className="relative w-full overflow-x-auto"
    >
      <table
        data-slot="table"
        className={cn("w-full caption-bottom text-sm", className)}
        {...props}
      />
    </div>
  )
}

function TableHeader({ className, ...props }: React.ComponentProps<"thead">) {
  return (
    <thead
      data-slot="table-header"
      className={cn("[&_tr]:border-b bg-muted/50", className)}
      {...props}
    />
  )
}

function TableBody({ className, ...props }: React.ComponentProps<"tbody">) {
  return (
    <tbody
      data-slot="table-body"
      className={cn("[&_tr:last-child]:border-0", className)}
      {...props}
    />
  )
}

function TableFooter({ className, ...props }: React.ComponentProps<"tfoot">) {
  return (
    <tfoot
      data-slot="table-footer"
      className={cn(
        "bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",
        className
      )}
      {...props}
    />
  )
}

function TableRow({ className, ...props }: React.ComponentProps<"tr">) {
  return (
    <tr
      data-slot="table-row"
      className={cn(
        "hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-all duration-200 hover:shadow-sm",
        className
      )}
      {...props}
    />
  )
}

function TableHead({ 
  className, 
  align,
  ...props 
}: React.ComponentProps<"th"> & {
  align?: "left" | "right" | "center"
}) {
  const getAlignment = () => {
    if (!align) {
      // Auto-detect RTL and adjust alignment
      return "[dir='rtl'] &:text-right [dir='ltr'] &:text-left"
    }
    return align === "left" ? "text-left" : align === "right" ? "text-right" : "text-center"
  }

  return (
    <th
      data-slot="table-head"
      className={cn(
        "h-12 px-4 align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        "[dir='rtl'] &:text-right [dir='ltr'] &:text-left",
        getAlignment(),
        className
      )}
      {...props}
    />
  )
}

function TableCell({ 
  className, 
  align,
  ...props 
}: React.ComponentProps<"td"> & {
  align?: "left" | "right" | "center"
}) {
  const getAlignment = () => {
    if (!align) {
      return "[dir='rtl'] &:text-right [dir='ltr'] &:text-left"
    }
    return align === "left" ? "text-left" : align === "right" ? "text-right" : "text-center"
  }

  return (
    <td
      data-slot="table-cell"
      className={cn(
        "p-4 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",
        "[dir='rtl'] &:text-right [dir='ltr'] &:text-left",
        getAlignment(),
        className
      )}
      {...props}
    />
  )
}

function TableCaption({
  className,
  ...props
}: React.ComponentProps<"caption">) {
  return (
    <caption
      data-slot="table-caption"
      className={cn("text-muted-foreground mt-4 text-sm", className)}
      {...props}
    />
  )
}

export {
  Table,
  TableHeader,
  TableBody,
  TableFooter,
  TableHead,
  TableRow,
  TableCell,
  TableCaption,
}
