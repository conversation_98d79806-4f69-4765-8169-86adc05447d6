@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));
@custom-variant rtl ([dir="rtl"] &);

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* RTL Utilities for Tailwind CSS v4 */
@utility rtl-flex-row-reverse {
  flex-direction: row-reverse;
}

@utility rtl-justify-end {
  justify-content: flex-end;
}

@utility rtl-text-right {
  text-align: right;
}

@utility rtl-ml-3 {
  margin-left: 0.75rem;
}

@utility rtl-mr-3 {
  margin-right: 0.75rem;
}

@utility rtl-ml-2 {
  margin-left: 0.5rem;
}

@utility rtl-mr-2 {
  margin-right: 0.5rem;
}

@utility rtl-pl-6 {
  padding-left: 1.5rem;
}

@utility rtl-pr-6 {
  padding-right: 1.5rem;
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  /* Theme color variables - default to blue */
  --theme-primary: #3b82f6;
  --theme-primary-hover: #2563eb;
  --theme-primary-active: #1d4ed8;
  --theme-primary-foreground: #ffffff;
  --theme-secondary: #e0f2fe;
  --theme-accent: #0ea5e9;
  --theme-muted: #f1f5f9;
  --theme-border: #e2e8f0;
  --theme-ring: #3b82f6;
  --theme-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --theme-shadow: rgba(59, 130, 246, 0.25);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !important;
  }

  /* Arabic font support */
  .rtl body,
  [dir="rtl"] body,
  html.rtl body {
    font-family: var(--font-arabic), var(--font-inter), ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji" !important;
  }
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* RTL-specific styles */
.rtl .text-left,
[dir="rtl"] .text-left {
  text-align: right;
}

.rtl .text-right,
[dir="rtl"] .text-right {
  text-align: left;
}

.rtl .ml-auto,
[dir="rtl"] .ml-auto {
  margin-left: 0;
  margin-right: auto;
}

.rtl .mr-auto,
[dir="rtl"] .mr-auto {
  margin-right: 0;
  margin-left: auto;
}

/* RTL flex direction */
.rtl .flex-row,
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

/* RTL border radius */
.rtl .rounded-l,
[dir="rtl"] .rounded-l {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
}

.rtl .rounded-r,
[dir="rtl"] .rounded-r {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}

/* RTL positioning */
.rtl .left-0,
[dir="rtl"] .left-0 {
  left: auto;
  right: 0;
}

.rtl .right-0,
[dir="rtl"] .right-0 {
  right: auto;
  left: 0;
}

/* RTL transforms for icons */
.rtl .transform-rtl,
[dir="rtl"] .transform-rtl {
  transform: scaleX(-1);
}

/* Ensure proper spacing for Arabic text */
.rtl,
[dir="rtl"] {
  letter-spacing: normal;
  word-spacing: normal;
}

/* RTL table alignment */
.rtl table,
[dir="rtl"] table {
  text-align: right;
}

.rtl th,
[dir="rtl"] th,
.rtl td,
[dir="rtl"] td {
  text-align: right;
}

/* Enhanced RTL table styling */
[dir="rtl"] .table-container {
  direction: rtl;
}

/* RTL table header actions column - always on the left in RTL */
[dir="rtl"] th.text-right {
  text-align: left !important;
}

[dir="rtl"] td.text-right {
  text-align: left !important;
}

/* RTL numeric data should remain LTR for readability */
[dir="rtl"] .numeric-cell,
[dir="rtl"] .currency-cell,
[dir="rtl"] .amount-cell {
  direction: ltr;
  text-align: left !important;
  unicode-bidi: embed;
}

/* RTL table column ordering adjustments */
[dir="rtl"] table {
  transform: none; /* Don't flip the entire table */
}

/* RTL pagination in tables */
[dir="rtl"] .table-pagination {
  flex-direction: row-reverse;
}

[dir="rtl"] .table-pagination .pagination-info {
  order: 2;
}

[dir="rtl"] .table-pagination .pagination-controls {
  order: 1;
}

/* RTL form elements */
.rtl input,
[dir="rtl"] input,
.rtl textarea,
[dir="rtl"] textarea,
.rtl select,
[dir="rtl"] select {
  text-align: right;
}

/* RTL dropdown menus */
.rtl .dropdown-menu,
[dir="rtl"] .dropdown-menu {
  left: auto;
  right: 0;
}

/* RTL sidebar adjustments */
.rtl .sidebar,
[dir="rtl"] .sidebar {
  left: auto;
  right: 0;
}

/* Mobile navigation fixes */
@media (max-width: 1023px) {
  .sidebar-container {
    position: fixed !important;
    top: 0 !important;
    bottom: 0 !important;
    z-index: 50 !important;
    width: 16rem !important; /* w-64 */
    background-color: white !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100vh !important;
    max-height: 100vh !important;
  }

  /* Mobile sidebar header - fixed at top */
  .sidebar-container > div:first-child {
    flex-shrink: 0 !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 51 !important;
    background-color: inherit !important;
  }

  /* Mobile sidebar content - scrollable */
  .sidebar-container .scroll-area {
    flex: 1 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: auto !important;
    max-height: none !important;
    -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
    scrollbar-width: thin !important; /* Firefox */
  }

  /* Mobile sidebar navigation area */
  .sidebar-container nav {
    padding-bottom: 2rem !important; /* Extra padding at bottom for easier scrolling */
    min-height: 100% !important; /* Ensure nav takes full height for proper scrolling */
  }

  /* Hide scrollbar on mobile for cleaner look */
  .sidebar-container .scroll-area::-webkit-scrollbar {
    width: 4px !important;
  }

  .sidebar-container .scroll-area::-webkit-scrollbar-track {
    background: transparent !important;
  }

  .sidebar-container .scroll-area::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2) !important;
    border-radius: 2px !important;
  }

  .sidebar-container .scroll-area::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3) !important;
  }

  /* RTL mobile positioning */
  [dir="rtl"] .sidebar-container {
    right: 0 !important;
    left: auto !important;
  }

  [dir="ltr"] .sidebar-container {
    left: 0 !important;
    right: auto !important;
  }

  /* Mobile overlay */
  .fixed.inset-0.z-40 {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 40 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
  }

  /* Ensure main content doesn't interfere */
  .main-content {
    position: relative !important;
    z-index: 10 !important;
  }

  /* Mobile transform fixes */
  .sidebar-container.translate-x-0 {
    transform: translateX(0) !important;
  }

  /* RTL mobile transforms */
  [dir="rtl"] .sidebar-container.-translate-x-full {
    transform: translateX(100%) !important;
  }

  [dir="rtl"] .sidebar-container.translate-x-full {
    transform: translateX(100%) !important;
  }

  /* LTR mobile transforms */
  [dir="ltr"] .sidebar-container.-translate-x-full {
    transform: translateX(-100%) !important;
  }

  /* Transition for smooth animation */
  .sidebar-container {
    transition: transform 0.3s ease-in-out !important;
  }

  /* Touch scrolling improvements for mobile */
  .sidebar-container,
  .sidebar-container * {
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
  }

  /* Prevent horizontal scrolling on mobile */
  .sidebar-container {
    overscroll-behavior-x: none !important;
    overscroll-behavior-y: auto !important;
  }
}

/* Desktop sidebar collapse/expand */
@media (min-width: 1024px) {
  .sidebar-container {
    transition: width 0.3s ease-in-out !important;
  }
  
  /* Collapsed sidebar on desktop */
  .sidebar-container.lg\:w-16 {
    width: 4rem !important; /* 64px */
  }
  
  /* Expanded sidebar on desktop */
  .sidebar-container.lg\:w-64 {
    width: 16rem !important; /* 256px */
  }
}

/* RTL desktop layout adjustments */
@media (min-width: 1024px) {
  [dir="rtl"] .flex-row-reverse {
    flex-direction: row-reverse !important;
  }

  [dir="rtl"] .lg\:static {
    position: static !important;
  }

  [dir="rtl"] .lg\:translate-x-0 {
    transform: translateX(0) !important;
  }

  /* Force RTL sidebar positioning on desktop */
  [dir="rtl"] .sidebar-container {
    order: 2 !important;
  }

  [dir="rtl"] .main-content {
    order: 1 !important;
  }
}

/* RTL navigation menu items */
[dir="rtl"] .nav-item {
  flex-direction: row-reverse;
  text-align: right;
}

[dir="rtl"] .nav-item .icon {
  margin-left: 0.75rem;
  margin-right: 0;
}

/* RTL button spacing */
[dir="rtl"] .space-x-reverse > * + * {
  margin-left: 0;
  margin-right: var(--space-x-reverse-value, 1rem);
}

/* RTL flex row reverse - Enhanced for Tailwind v4 */
[dir="rtl"] .flex-row-reverse {
  flex-direction: row-reverse !important;
}

[dir="rtl"] .justify-end {
  justify-content: flex-end !important;
}

[dir="rtl"] .text-right {
  text-align: right !important;
}

[dir="rtl"] .text-left {
  text-align: right !important;
}

/* RTL margin and padding utilities */
[dir="rtl"] .ml-3 {
  margin-left: 0.75rem !important;
  margin-right: 0 !important;
}

[dir="rtl"] .mr-3 {
  margin-right: 0.75rem !important;
  margin-left: 0 !important;
}

[dir="rtl"] .ml-2 {
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

[dir="rtl"] .mr-2 {
  margin-right: 0.5rem !important;
  margin-left: 0 !important;
}

[dir="rtl"] .pl-6 {
  padding-left: 1.5rem !important;
  padding-right: 0 !important;
}

[dir="rtl"] .pr-6 {
  padding-right: 1.5rem !important;
  padding-left: 0 !important;
}

/* RTL sidebar navigation improvements */
[dir="rtl"] .sidebar-nav-item {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  text-align: right;
}

[dir="rtl"] .sidebar-nav-item .icon {
  order: 2;
  margin-left: 0.75rem;
  margin-right: 0;
}

[dir="rtl"] .sidebar-nav-item .text {
  order: 1;
  flex: 1;
  text-align: right;
}

/* Ensure proper RTL spacing for buttons */
[dir="rtl"] .btn-rtl {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

[dir="rtl"] .btn-rtl .icon {
  margin-left: 0.5rem;
  margin-right: 0;
}

/* Force RTL layout for all flex containers in sidebar */
[dir="rtl"] nav button {
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
  text-align: right !important;
}

[dir="rtl"] nav button span {
  text-align: right !important;
}

/* RTL gap handling for flex containers */
[dir="rtl"] .gap-0 {
  gap: 0 !important;
}

/* RTL specific button styling */
[dir="rtl"] button[dir="rtl"] {
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
}

[dir="rtl"] button[dir="rtl"] svg {
  margin-left: 0.75rem !important;
  margin-right: 0 !important;
}

[dir="rtl"] button[dir="rtl"] span {
  text-align: right !important;
  flex: 1 !important;
}

/* Enhanced RTL icon positioning for sidebar navigation */
/* Main menu item icons (first icon in button) */
[dir="rtl"] nav button > svg:first-child {
  order: 3 !important;
  margin-left: 0.75rem !important;
  margin-right: 0 !important;
}

/* Text spans in buttons */
[dir="rtl"] nav button > span {
  order: 2 !important;
  flex: 1 !important;
  text-align: right !important;
}

/* Chevron icons (last icon in button) */
[dir="rtl"] nav button > svg:last-child {
  order: 1 !important;
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

/* More specific targeting for main menu icons */
[dir="rtl"] nav button > svg:not([data-lucide*="chevron"]) {
  order: 3 !important;
  margin-left: 0.75rem !important;
  margin-right: 0 !important;
}

/* Chevron icons should stay on the left in RTL */
[dir="rtl"] nav button > svg[data-lucide*="chevron"] {
  order: 1 !important;
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

/* RTL chevron arrow corrections - Remove transforms since component handles this */
/* The Sidebar component already uses ChevronLeft for RTL and ChevronRight for LTR */

/* Force proper RTL layout for all sidebar buttons */
[dir="rtl"] .sidebar nav button,
[dir="rtl"] nav .space-y-2 button {
  display: flex !important;
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
  align-items: center !important;
}

/* RTL icon spacing override */
[dir="rtl"] .sidebar nav button svg,
[dir="rtl"] nav .space-y-2 button svg {
  margin-left: 0.75rem !important;
  margin-right: 0 !important;
}

/* RTL submenu indentation */
[dir="rtl"] .space-y-1 .pr-6 {
  padding-right: 1.5rem !important;
  padding-left: 0 !important;
}

[dir="rtl"] .space-y-1 .pl-6 {
  padding-left: 1.5rem !important;
  padding-right: 0 !important;
}

/* RTL submenu button layout */
[dir="rtl"] .space-y-1 button {
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
}

[dir="rtl"] .space-y-1 button svg {
  order: 2 !important;
  margin-left: 0.5rem !important;
  margin-right: 0 !important;
}

[dir="rtl"] .space-y-1 button span {
  order: 1 !important;
  text-align: right !important;
}

/* Table header styling for better visual distinction */
.table-header-bg {
  background-color: #f8fafc !important; /* Light gray background */
  border-bottom: 1px solid #e2e8f0 !important;
}

/* Alternative table header styling */
thead tr {
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

thead th {
  background-color: #f8fafc !important;
  font-weight: 600 !important;
  color: #374151 !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

/* Table row hover effect */
tbody tr:hover {
  background-color: #f9fafb !important;
}

/* Ensure table rows have white background by default */
tbody tr {
  background-color: white !important;
}

/* Table border styling */
.table-container {
  border: 1px solid #e5e7eb !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
  position: relative !important;
}

/* Immediate solution: Better table scrolling */
.table-container {
  overflow-x: auto !important;
  overflow-y: visible !important;
}

/* Make table take full width and be scrollable */
.table-container table {
  min-width: 100% !important;
  width: max-content !important;
}

/* Enhanced table scrolling */
.table-scroll-container {
  overflow-x: auto !important;
  overflow-y: visible !important;
  max-width: 100% !important;
  position: relative !important;
  /* Make scrollbar always visible */
  scrollbar-width: auto !important;
}

/* Alternative: Sticky scrollbar at top */
.table-container-with-top-scroll {
  position: relative !important;
}

.table-container-with-top-scroll::before {
  content: '' !important;
  position: sticky !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 15px !important;
  background: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
  overflow-x: auto !important;
  z-index: 5 !important;
}

/* Simple solution: Make all table containers have better scrolling */
.table-container > div {
  overflow-x: auto !important;
  scrollbar-width: auto !important;
}

/* Force scrollbar to be always visible and prominent */
.table-container::-webkit-scrollbar {
  height: 16px !important;
  width: 16px !important;
  background-color: #f8fafc !important;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f5f9 !important;
  border-radius: 8px !important;
  border: 1px solid #e2e8f0 !important;
}

.table-container::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8) !important;
  border-radius: 8px !important;
  border: 2px solid #f1f5f9 !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #1d4ed8, #1e40af) !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

.table-container::-webkit-scrollbar-thumb:active {
  background: linear-gradient(45deg, #1e40af, #1e3a8a) !important;
}

/* Firefox scrollbar styling */
.table-container {
  scrollbar-width: auto !important;
  scrollbar-color: #3b82f6 #f1f5f9 !important;
}

/* Add scroll hint for wide tables */
.table-container::after {
  content: '← Scroll horizontally to see more columns →' !important;
  position: absolute !important;
  bottom: 20px !important;
  right: 20px !important;
  background: rgba(59, 130, 246, 0.9) !important;
  color: white !important;
  padding: 8px 12px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  pointer-events: none !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
  z-index: 10 !important;
  white-space: nowrap !important;
}

.table-container:hover::after {
  opacity: 1 !important;
}

/* Hide scroll hint if table doesn't overflow */
.table-container:not(.overflowing)::after {
  display: none !important;
}

/* Custom scrollbar styling for better visibility */
.table-scroll-container::-webkit-scrollbar {
  height: 12px !important;
  background-color: #f1f5f9 !important;
  border-radius: 6px !important;
}

.table-scroll-container::-webkit-scrollbar-track {
  background-color: #f1f5f9 !important;
  border-radius: 6px !important;
}

.table-scroll-container::-webkit-scrollbar-thumb {
  background-color: #cbd5e1 !important;
  border-radius: 6px !important;
  border: 2px solid #f1f5f9 !important;
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8 !important;
}

/* Sticky horizontal scroll indicator */
.table-container::before {
  content: '';
  position: sticky !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg,
    rgba(59, 130, 246, 0.5) 0%,
    rgba(59, 130, 246, 0.2) 50%,
    rgba(59, 130, 246, 0.5) 100%) !important;
  z-index: 10 !important;
  display: none !important;
}

/* Show scroll indicator when table is scrollable */
.table-container.has-horizontal-scroll::before {
  display: block !important;
}

/* Table wrapper for better scroll handling */
.table-wrapper {
  position: relative !important;
  width: 100% !important;
}

/* Scroll shadow effects */
.table-scroll-container {
  background:
    linear-gradient(90deg, white 30%, rgba(255,255,255,0)),
    linear-gradient(90deg, rgba(255,255,255,0), white 70%) 0 100%,
    radial-gradient(farthest-side at 0 50%, rgba(0,0,0,.2), rgba(0,0,0,0)),
    radial-gradient(farthest-side at 100% 50%, rgba(0,0,0,.2), rgba(0,0,0,0)) 0 100% !important;
  background-repeat: no-repeat !important;
  background-color: white !important;
  background-size: 40px 100%, 40px 100%, 14px 100%, 14px 100% !important;
  background-attachment: local, local, scroll, scroll !important;
}

/* RTL modal positioning */
.rtl .modal,
[dir="rtl"] .modal {
  text-align: right;
}

/* RTL number formatting */
.rtl .number,
[dir="rtl"] .number {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

/* Theme-aware components */
.theme-primary {
  background-color: var(--theme-primary) !important;
  color: var(--theme-primary-foreground) !important;
}

.theme-primary:hover {
  background-color: var(--theme-primary-hover) !important;
}

.theme-primary:active {
  background-color: var(--theme-primary-active) !important;
}

.theme-secondary {
  background-color: var(--theme-secondary) !important;
  color: var(--theme-primary) !important;
}

.theme-accent {
  background-color: var(--theme-accent) !important;
  color: var(--theme-primary-foreground) !important;
}

.theme-gradient {
  background: var(--theme-gradient) !important;
  color: var(--theme-primary-foreground) !important;
}

.theme-border {
  border-color: var(--theme-border) !important;
}

.theme-shadow {
  box-shadow: 0 4px 12px var(--theme-shadow) !important;
}

/* Update scroll arrows to use theme colors */
.scroll-arrows-container button {
  background: var(--theme-primary) !important;
  color: var(--theme-primary-foreground) !important;
}

.scroll-arrows-container button:hover {
  background: var(--theme-primary-hover) !important;
}

.scroll-arrows-container button:active {
  background: var(--theme-primary-active) !important;
}

/* Update table scrollbar to use theme colors */
.table-container::-webkit-scrollbar-thumb {
  background: var(--theme-gradient) !important;
  border-radius: 8px !important;
  border: 2px solid #f1f5f9 !important;
  box-shadow: 0 2px 4px var(--theme-shadow) !important;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: var(--theme-primary-hover) !important;
  box-shadow: 0 4px 8px var(--theme-shadow) !important;
}

.table-container::-webkit-scrollbar-thumb:active {
  background: var(--theme-primary-active) !important;
}

/* Firefox scrollbar styling with theme colors */
.table-container {
  scrollbar-color: var(--theme-primary) #f1f5f9 !important;
}

/* Print Styles */
@media print {
  @page {
    margin: 0.5in;
    size: A4;
  }

  body {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  .print\:block {
    display: block !important;
  }

  .print\:hidden {
    display: none !important;
  }

  /* Hide all elements except print view */
  body > * {
    visibility: hidden;
  }

  .print\:block,
  .print\:block * {
    visibility: visible;
  }

  .print\:block {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }

  /* Ensure proper spacing and typography for print */
  .print\:block {
    font-size: 12pt;
    line-height: 1.4;
  }

  .print\:block h1 {
    font-size: 18pt;
  }

  .print\:block h2 {
    font-size: 16pt;
  }

  .print\:block h3 {
    font-size: 14pt;
  }

  /* Ensure tables print properly */
  .print\:block table {
    border-collapse: collapse;
    width: 100%;
  }

  .print\:block th,
  .print\:block td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }

  .print\:block th {
    background-color: #f5f5f5 !important;
    font-weight: bold;
  }

  /* Avoid page breaks inside important elements */
  .print\:block .no-break {
    page-break-inside: avoid;
  }

  /* Ensure signature and stamp areas print correctly */
  .print\:block img {
    max-width: 100%;
    height: auto;
  }
}
