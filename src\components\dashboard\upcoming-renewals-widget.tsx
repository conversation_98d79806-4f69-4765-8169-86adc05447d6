"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import {
  Calendar,
  Clock,
  AlertTriangle,
  Bell,
  CalendarDays,
  CheckCircle,
  User,
  FileText,
  CreditCard,
  ArrowRight,
} from "lucide-react"
import { useRouter } from "next/navigation"

interface UpcomingItem {
  id: string
  title: string
  type: string
  category: string
  dueDate: string
  priority: string
  relatedEntityType?: string
  relatedEntityId?: string
  assignedTo?: {
    id: string
    name: string
    email: string
  }
}

const priorityColors = {
  LOW: "bg-gray-100 text-gray-800",
  MEDIUM: "bg-blue-100 text-blue-800",
  HIGH: "bg-orange-100 text-orange-800",
  URGENT: "bg-red-100 text-red-800",
  CRITICAL: "bg-red-200 text-red-900",
}

const typeIcons = {
  REMINDER: Bell,
  MEETING: Calendar,
  DEADLINE: Clock,
  RENEWAL: CalendarDays,
  EXPIRATION: Alert<PERSON>riangle,
  TASK_DUE: CheckCircle,
  INVOICE_DUE: FileText,
  PAYMENT_DUE: CreditCard,
  HOLIDAY: Calendar,
  OTHER: Calendar,
}

const categoryColors = {
  DOCUMENT: "bg-purple-100 text-purple-800",
  FINANCIAL: "bg-green-100 text-green-800",
  LEGAL: "bg-blue-100 text-blue-800",
  HR: "bg-orange-100 text-orange-800",
  BUSINESS: "bg-indigo-100 text-indigo-800",
  PERSONAL: "bg-pink-100 text-pink-800",
  SYSTEM: "bg-gray-100 text-gray-800",
  OTHER: "bg-gray-100 text-gray-800",
}

export function UpcomingRenewalsWidget() {
  const router = useRouter()
  const [upcomingItems, setUpcomingItems] = useState<UpcomingItem[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadUpcomingItems = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/calendar/upcoming?days=30&limit=8')
        if (response.ok) {
          const data = await response.json()
          setUpcomingItems(data)
        }
      } catch (error) {
        console.error('Error loading upcoming items:', error)
      } finally {
        setLoading(false)
      }
    }

    loadUpcomingItems()
  }, [])

  const getDaysUntilDue = (dueDate: string) => {
    const now = new Date()
    const due = new Date(dueDate)
    const diffTime = due.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getDueDateColor = (daysUntil: number) => {
    if (daysUntil < 0) return "text-red-600 font-semibold" // Overdue
    if (daysUntil <= 3) return "text-red-500 font-medium" // Critical
    if (daysUntil <= 7) return "text-orange-500 font-medium" // Warning
    if (daysUntil <= 14) return "text-yellow-600" // Caution
    return "text-gray-600" // Normal
  }

  const getDueDateText = (daysUntil: number) => {
    if (daysUntil < 0) return `${Math.abs(daysUntil)} days overdue`
    if (daysUntil === 0) return "Due today"
    if (daysUntil === 1) return "Due tomorrow"
    return `Due in ${daysUntil} days`
  }

  return (
    <Card className="h-[400px]">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium">Upcoming Renewals & Deadlines</CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/dashboard/calendar')}
          className="text-xs"
        >
          View All
          <ArrowRight className="ml-1 h-3 w-3" />
        </Button>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        ) : upcomingItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-center">
            <CheckCircle className="h-8 w-8 text-green-500 mb-2" />
            <p className="text-sm text-muted-foreground">No upcoming renewals</p>
            <p className="text-xs text-muted-foreground">All documents are up to date</p>
          </div>
        ) : (
          <div className="h-[320px] overflow-y-auto">
            <div className="space-y-3">
              {upcomingItems.map((item) => {
                const TypeIcon = typeIcons[item.type as keyof typeof typeIcons] || Calendar
                const daysUntil = getDaysUntilDue(item.dueDate)
                
                return (
                  <div
                    key={item.id}
                    className="flex items-start space-x-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors cursor-pointer"
                    onClick={() => {
                      if (item.relatedEntityType === 'employee') {
                        router.push(`/dashboard/employees/${item.relatedEntityId}`)
                      } else {
                        router.push('/dashboard/calendar')
                      }
                    }}
                  >
                    <div className="flex-shrink-0">
                      <div className={`p-2 rounded-full ${
                        daysUntil <= 3 ? 'bg-red-100' : 
                        daysUntil <= 7 ? 'bg-orange-100' : 
                        'bg-blue-100'
                      }`}>
                        <TypeIcon className={`h-4 w-4 ${
                          daysUntil <= 3 ? 'text-red-600' : 
                          daysUntil <= 7 ? 'text-orange-600' : 
                          'text-blue-600'
                        }`} />
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground truncate">
                            {item.title}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge 
                              variant="secondary" 
                              className={`text-xs ${categoryColors[item.category as keyof typeof categoryColors]}`}
                            >
                              {item.category}
                            </Badge>
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${priorityColors[item.priority as keyof typeof priorityColors]}`}
                            >
                              {item.priority}
                            </Badge>
                          </div>
                          {item.assignedTo && (
                            <div className="flex items-center space-x-1 mt-1">
                              <User className="h-3 w-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">
                                {item.assignedTo.name}
                              </span>
                            </div>
                          )}
                        </div>
                        
                        <div className="text-right">
                          <p className={`text-xs ${getDueDateColor(daysUntil)}`}>
                            {getDueDateText(daysUntil)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(item.dueDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
