import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const payout = await prisma.supplierPayout.findUnique({
      where: { id },
      include: {
        supplier: true,
        purchase: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    if (!payout) {
      return NextResponse.json({ error: 'Payout not found' }, { status: 404 })
    }

    return NextResponse.json(payout)
  } catch (error) {
    console.error('Error fetching payout:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const {
      amount,
      method,
      reference,
      description,
      status,
      date,
      dueDate,
      paidDate,
      notes,
    } = body

    // Check if payout exists
    const existingPayout = await prisma.supplierPayout.findUnique({
      where: { id },
    })

    if (!existingPayout) {
      return NextResponse.json({ error: 'Payout not found' }, { status: 404 })
    }

    // Prevent editing paid payouts
    if (existingPayout.status === 'PAID' && status !== 'PAID') {
      return NextResponse.json(
        { error: 'Cannot modify paid payouts' },
        { status: 400 }
      )
    }

    const updateData: any = {}

    if (amount !== undefined) updateData.amount = parseFloat(amount)
    if (method !== undefined) updateData.method = method
    if (reference !== undefined) updateData.reference = reference
    if (description !== undefined) updateData.description = description
    if (status !== undefined) {
      updateData.status = status
      if (status === 'PAID' && !existingPayout.paidDate) {
        updateData.paidDate = new Date()
      }
    }
    if (date !== undefined) updateData.date = new Date(date)
    if (dueDate !== undefined) updateData.dueDate = dueDate ? new Date(dueDate) : null
    if (paidDate !== undefined) updateData.paidDate = paidDate ? new Date(paidDate) : null
    if (notes !== undefined) updateData.notes = notes

    const payout = await prisma.supplierPayout.update({
      where: { id },
      data: updateData,
      include: {
        supplier: true,
        purchase: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return NextResponse.json(payout)
  } catch (error) {
    console.error('Error updating payout:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if payout exists
    const existingPayout = await prisma.supplierPayout.findUnique({
      where: { id },
    })

    if (!existingPayout) {
      return NextResponse.json({ error: 'Payout not found' }, { status: 404 })
    }

    // Prevent deleting paid payouts
    if (existingPayout.status === 'PAID') {
      return NextResponse.json(
        { error: 'Cannot delete paid payouts' },
        { status: 400 }
      )
    }

    await prisma.supplierPayout.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Payout deleted successfully' })
  } catch (error) {
    console.error('Error deleting payout:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
