import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/calendar - Get calendar events
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if calendar model is available
    if (!prisma.calendarEvent) {
      console.log('CalendarEvent model not available yet, returning empty array')
      return NextResponse.json([])
    }

    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const type = searchParams.get('type')
    const category = searchParams.get('category')
    const status = searchParams.get('status')

    // Build where clause
    const where: any = {}
    
    if (startDate && endDate) {
      where.OR = [
        {
          startDate: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        },
        {
          dueDate: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        },
      ]
    }

    if (type) where.type = type
    if (category) where.category = category
    if (status) where.status = status

    const events = await prisma.calendarEvent.findMany({
      where,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: { startDate: 'asc' },
    })

    return NextResponse.json(events)
  } catch (error) {
    console.error('Error fetching calendar events:', error)
    // Return empty array if calendar model is not available
    const errorMessage = error instanceof Error ? error.message : String(error)
    if (errorMessage?.includes('calendarEvent') || errorMessage?.includes('findMany')) {
      return NextResponse.json([])
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/calendar - Create new calendar event
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if calendar model is available
    if (!prisma.calendarEvent) {
      return NextResponse.json(
        { error: 'Calendar model not available. Please restart the server.' },
        { status: 503 }
      )
    }

    const body = await request.json()
    const {
      title,
      titleAr,
      description,
      type = 'REMINDER',
      category = 'DOCUMENT',
      startDate,
      endDate,
      dueDate,
      isAllDay = false,
      status = 'PENDING',
      priority = 'MEDIUM',
      notifyBefore,
      isRecurring = false,
      recurringType,
      recurringInterval,
      relatedEntityType,
      relatedEntityId,
      notes,
      assignedToId,
    } = body

    if (!title || !startDate) {
      return NextResponse.json(
        { error: 'Title and start date are required' },
        { status: 400 }
      )
    }

    const event = await prisma.calendarEvent.create({
      data: {
        title,
        titleAr,
        description,
        type,
        category,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : null,
        dueDate: dueDate ? new Date(dueDate) : null,
        isAllDay,
        status,
        priority,
        notifyBefore,
        isRecurring,
        recurringType,
        recurringInterval,
        relatedEntityType,
        relatedEntityId,
        notes,
        createdById: session.user.id,
        assignedToId: assignedToId || null,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return NextResponse.json(event, { status: 201 })
  } catch (error) {
    console.error('Error creating calendar event:', error)
    // Return specific error if calendar model is not available
    const errorMessage = error instanceof Error ? error.message : String(error)
    if (errorMessage?.includes('calendarEvent') || errorMessage?.includes('create')) {
      return NextResponse.json(
        { error: 'Calendar model not available. Please restart the server.' },
        { status: 503 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to create calendar event' },
      { status: 500 }
    )
  }
}
