"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Receipt, 
  FileText, 
  AlertTriangle,
  CheckCircle
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"

interface ProjectFinancialOverviewProps {
  projectId: string
  projectBudget?: number
  projectActualCost?: number
}

interface FinancialData {
  budget: number
  actualCost: number
  totalRevenue: number
  totalExpenses: number
  netProfit: number
  profitMargin: number
  budgetUsage: number
  invoiceStats: {
    total: number
    totalAmount: number
    paidAmount: number
    unpaidAmount: number
  }
  expenseStats: {
    total: number
    totalAmount: number
    pendingAmount: number
    approvedAmount: number
    paidAmount: number
  }
}

export function ProjectFinancialOverview({ 
  projectId, 
  projectBudget = 0, 
  projectActualCost = 0 
}: ProjectFinancialOverviewProps) {
  const [financialData, setFinancialData] = useState<FinancialData>({
    budget: projectBudget,
    actualCost: projectActualCost,
    totalRevenue: 0,
    totalExpenses: 0,
    netProfit: 0,
    profitMargin: 0,
    budgetUsage: 0,
    invoiceStats: {
      total: 0,
      totalAmount: 0,
      paidAmount: 0,
      unpaidAmount: 0
    },
    expenseStats: {
      total: 0,
      totalAmount: 0,
      pendingAmount: 0,
      approvedAmount: 0,
      paidAmount: 0
    }
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchFinancialData()
  }, [projectId, projectBudget, projectActualCost])

  const fetchFinancialData = async () => {
    try {
      setLoading(true)
      
      // Fetch invoices and expenses in parallel
      const [invoicesResponse, expensesResponse] = await Promise.all([
        fetch(`/api/invoices?projectId=${projectId}&limit=1000`),
        fetch(`/api/expenses?projectId=${projectId}&limit=1000`)
      ])

      const invoicesData = invoicesResponse.ok ? await invoicesResponse.json() : { invoices: [] }
      const expensesData = expensesResponse.ok ? await expensesResponse.json() : { expenses: [] }

      const invoices = invoicesData.invoices || []
      const expenses = expensesData.expenses || []

      // Calculate invoice stats with proper number conversion
      const totalRevenue = invoices.reduce((sum: number, inv: any) => sum + Number(inv.total || 0), 0)
      const paidAmount = invoices
        .filter((inv: any) => inv.status === 'PAID')
        .reduce((sum: number, inv: any) => sum + Number(inv.total || 0), 0)
      const unpaidAmount = totalRevenue - paidAmount

      const invoiceStats = {
        total: invoices.length,
        totalAmount: totalRevenue,
        paidAmount,
        unpaidAmount
      }

      // Calculate expense stats with proper number conversion and include all statuses for actual cost
      const totalExpenses = expenses
        .filter((exp: any) => exp.status === 'APPROVED' || exp.status === 'PAID')
        .reduce((sum: number, exp: any) => sum + Number(exp.amount || 0), 0)
      const pendingAmount = expenses
        .filter((exp: any) => exp.status === 'PENDING')
        .reduce((sum: number, exp: any) => sum + Number(exp.amount || 0), 0)
      const approvedAmount = expenses
        .filter((exp: any) => exp.status === 'APPROVED')
        .reduce((sum: number, exp: any) => sum + Number(exp.amount || 0), 0)
      const paidExpenseAmount = expenses
        .filter((exp: any) => exp.status === 'PAID')
        .reduce((sum: number, exp: any) => sum + Number(exp.amount || 0), 0)

      const expenseStats = {
        total: expenses.length,
        totalAmount: expenses.reduce((sum: number, exp: any) => sum + Number(exp.amount || 0), 0),
        pendingAmount,
        approvedAmount,
        paidAmount: paidExpenseAmount
      }

      // Calculate derived metrics
      const netProfit = totalRevenue - totalExpenses
      const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0
      const budgetUsage = projectBudget > 0 ? (totalExpenses / projectBudget) * 100 : 0

      setFinancialData({
        budget: projectBudget,
        actualCost: totalExpenses, // Use calculated totalExpenses instead of passed actualCost
        totalRevenue,
        totalExpenses,
        netProfit,
        profitMargin,
        budgetUsage,
        invoiceStats,
        expenseStats
      })
    } catch (error) {
      console.error('Error fetching financial data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getProfitabilityStatus = () => {
    if (financialData.netProfit > 0) return { icon: TrendingUp, color: "text-green-600", bg: "bg-green-50" }
    if (financialData.netProfit < 0) return { icon: TrendingDown, color: "text-red-600", bg: "bg-red-50" }
    return { icon: Target, color: "text-gray-600", bg: "bg-gray-50" }
  }

  const getBudgetStatus = () => {
    if (financialData.budgetUsage > 100) return { color: "bg-red-500", text: "Over Budget" }
    if (financialData.budgetUsage > 80) return { color: "bg-yellow-500", text: "Near Budget" }
    return { color: "bg-green-500", text: "On Track" }
  }

  const profitStatus = getProfitabilityStatus()
  const budgetStatus = getBudgetStatus()

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Revenue */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <FileText className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(financialData.totalRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">
              {financialData.invoiceStats.total} invoice(s)
            </p>
          </CardContent>
        </Card>

        {/* Expenses */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
            <Receipt className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(financialData.totalExpenses)}
            </div>
            <p className="text-xs text-muted-foreground">
              {financialData.expenseStats.total} expense(s)
            </p>
          </CardContent>
        </Card>

        {/* Net Profit */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
            <profitStatus.icon className={`h-4 w-4 ${profitStatus.color}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${profitStatus.color}`}>
              {formatCurrency(financialData.netProfit)}
            </div>
            <p className="text-xs text-muted-foreground">
              {financialData.profitMargin.toFixed(1)}% margin
            </p>
          </CardContent>
        </Card>

        {/* Budget Usage */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Budget Usage</CardTitle>
            <Target className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {financialData.budgetUsage.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              <Badge variant="secondary" className={budgetStatus.color}>
                {budgetStatus.text}
              </Badge>
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Financial Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Budget vs Actual */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Budget vs Actual
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Budget</span>
                <span className="font-medium">{formatCurrency(financialData.budget)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Actual Expenses</span>
                <span className="font-medium">{formatCurrency(financialData.totalExpenses)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Remaining</span>
                <span className={`font-medium ${
                  financialData.budget - financialData.totalExpenses >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {formatCurrency(financialData.budget - financialData.totalExpenses)}
                </span>
              </div>
            </div>
            
            {financialData.budget > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Budget Usage</span>
                  <span>{financialData.budgetUsage.toFixed(1)}%</span>
                </div>
                <Progress 
                  value={Math.min(financialData.budgetUsage, 100)} 
                  className="h-2"
                />
                {financialData.budgetUsage > 100 && (
                  <div className="flex items-center gap-2 text-red-600 text-sm">
                    <AlertTriangle className="h-4 w-4" />
                    Over budget by {formatCurrency(financialData.totalExpenses - financialData.budget)}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Revenue Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Revenue Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Total Invoiced</span>
                <span className="font-medium">{formatCurrency(financialData.invoiceStats.totalAmount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-green-600">Paid</span>
                <span className="font-medium text-green-600">{formatCurrency(financialData.invoiceStats.paidAmount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-red-600">Outstanding</span>
                <span className="font-medium text-red-600">{formatCurrency(financialData.invoiceStats.unpaidAmount)}</span>
              </div>
            </div>
            
            {financialData.invoiceStats.totalAmount > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Collection Rate</span>
                  <span>{((financialData.invoiceStats.paidAmount / financialData.invoiceStats.totalAmount) * 100).toFixed(1)}%</span>
                </div>
                <Progress 
                  value={(financialData.invoiceStats.paidAmount / financialData.invoiceStats.totalAmount) * 100} 
                  className="h-2"
                />
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Profitability Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Profitability Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className={`p-4 rounded-lg ${profitStatus.bg}`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={`text-sm font-medium ${profitStatus.color}`}>Net Profit</p>
                  <p className={`text-2xl font-bold ${profitStatus.color}`}>
                    {formatCurrency(financialData.netProfit)}
                  </p>
                </div>
                <profitStatus.icon className={`h-8 w-8 ${profitStatus.color}`} />
              </div>
            </div>
            
            <div className="p-4 rounded-lg bg-blue-50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Profit Margin</p>
                  <p className="text-2xl font-bold text-blue-900">
                    {financialData.profitMargin.toFixed(1)}%
                  </p>
                </div>
                <Target className="h-8 w-8 text-blue-600" />
              </div>
            </div>
            
            <div className="p-4 rounded-lg bg-purple-50">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">ROI</p>
                  <p className="text-2xl font-bold text-purple-900">
                    {financialData.budget > 0 ? ((financialData.netProfit / financialData.budget) * 100).toFixed(1) : 'N/A'}%
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-purple-600" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 