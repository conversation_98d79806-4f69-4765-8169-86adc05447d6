// Import modular translations
import { common } from './ar/common';
import { customers } from './ar/customers';
import { navigation } from './ar/navigation';
import { dashboard } from './ar/dashboard';
import { forms } from './ar/forms';
import { categories } from './ar/categories';
import { units } from './ar/units';
import { products } from './ar/products';
import { stock } from './ar/stock';
import { tasks } from './ar/tasks';
import { invoices } from './ar/invoices';
import { expenses } from './ar/expenses';
import { employees } from './ar/employees';
import { quotations } from './ar/quotations';
import { calendar } from './ar/calendar';
import { settings } from './ar/settings';
import { auth } from './ar/auth';
import { system } from './ar/system';
import { projects } from './ar/projects';
import { leads } from './ar/leads';
import { suppliers } from './ar/suppliers';
import { maintenance } from './ar/maintenance';
import { purchases } from './ar/purchases';
import { finance } from './ar/finance';
import { pos } from './ar/pos';
import { reports } from './ar/reports';

// Export combined translations
export const ar = {
  common,
  customers,
  navigation,
  dashboard,
  forms,
  categories,
  units,
  products,
  stock,
  tasks,
  invoices,
  expenses,
  employees,
  quotations,
  calendar,
  settings,
  auth,
  system,
  projects,
  leads,
  suppliers,
  maintenance,
  purchases,
  finance,
  pos,
  reports
}; 