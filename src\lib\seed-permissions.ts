import { prisma } from './prisma'

// Default permissions for the application
const DEFAULT_PERMISSIONS = [
  // Dashboard
  { name: 'View Dashboard', nameAr: 'عرض لوحة التحكم', module: 'dashboard', action: 'view', description: 'Access to main dashboard' },
  
  // Customers
  { name: 'View Customers', nameAr: 'عرض العملاء', module: 'customers', action: 'view', description: 'View customer list and details' },
  { name: 'Create Customer', nameAr: 'إنشاء عميل', module: 'customers', action: 'create', description: 'Create new customers' },
  { name: 'Edit Customer', nameAr: 'تعديل عميل', module: 'customers', action: 'edit', description: 'Edit customer information' },
  { name: 'Delete Customer', nameAr: 'حذف عميل', module: 'customers', action: 'delete', description: 'Delete customers' },
  { name: 'Export Customers', nameAr: 'تصدير العملاء', module: 'customers', action: 'export', description: 'Export customer data' },

  // Suppliers
  { name: 'View Suppliers', nameAr: 'عرض الموردين', module: 'suppliers', action: 'view', description: 'View supplier list and details' },
  { name: 'Create Supplier', nameAr: 'إنشاء مورد', module: 'suppliers', action: 'create', description: 'Create new suppliers' },
  { name: 'Edit Supplier', nameAr: 'تعديل مورد', module: 'suppliers', action: 'edit', description: 'Edit supplier information' },
  { name: 'Delete Supplier', nameAr: 'حذف مورد', module: 'suppliers', action: 'delete', description: 'Delete suppliers' },

  // Products
  { name: 'View Products', nameAr: 'عرض المنتجات', module: 'products', action: 'view', description: 'View product list and details' },
  { name: 'Create Product', nameAr: 'إنشاء منتج', module: 'products', action: 'create', description: 'Create new products' },
  { name: 'Edit Product', nameAr: 'تعديل منتج', module: 'products', action: 'edit', description: 'Edit product information' },
  { name: 'Delete Product', nameAr: 'حذف منتج', module: 'products', action: 'delete', description: 'Delete products' },
  { name: 'Manage Stock', nameAr: 'إدارة المخزون', module: 'products', action: 'stock', description: 'Manage product stock levels' },

  // Categories & Units
  { name: 'View Categories', nameAr: 'عرض الفئات', module: 'categories', action: 'view', description: 'View product categories' },
  { name: 'Manage Categories', nameAr: 'إدارة الفئات', module: 'categories', action: 'manage', description: 'Create, edit, delete categories' },
  { name: 'View Units', nameAr: 'عرض الوحدات', module: 'units', action: 'view', description: 'View measurement units' },
  { name: 'Manage Units', nameAr: 'إدارة الوحدات', module: 'units', action: 'manage', description: 'Create, edit, delete units' },

  // Tasks
  { name: 'View Tasks', nameAr: 'عرض المهام', module: 'tasks', action: 'view', description: 'View task list and details' },
  { name: 'Create Task', nameAr: 'إنشاء مهمة', module: 'tasks', action: 'create', description: 'Create new tasks' },
  { name: 'Edit Task', nameAr: 'تعديل مهمة', module: 'tasks', action: 'edit', description: 'Edit task information' },
  { name: 'Delete Task', nameAr: 'حذف مهمة', module: 'tasks', action: 'delete', description: 'Delete tasks' },
  { name: 'Assign Tasks', nameAr: 'تعيين المهام', module: 'tasks', action: 'assign', description: 'Assign tasks to users' },

  // Invoices
  { name: 'View Invoices', nameAr: 'عرض الفواتير', module: 'invoices', action: 'view', description: 'View invoice list and details' },
  { name: 'Create Invoice', nameAr: 'إنشاء فاتورة', module: 'invoices', action: 'create', description: 'Create new invoices' },
  { name: 'Edit Invoice', nameAr: 'تعديل فاتورة', module: 'invoices', action: 'edit', description: 'Edit invoice information' },
  { name: 'Delete Invoice', nameAr: 'حذف فاتورة', module: 'invoices', action: 'delete', description: 'Delete invoices' },
  { name: 'Print Invoice', nameAr: 'طباعة فاتورة', module: 'invoices', action: 'print', description: 'Print and export invoices' },

  // Quotations
  { name: 'View Quotations', nameAr: 'عرض عروض الأسعار', module: 'quotations', action: 'view', description: 'View quotation list and details' },
  { name: 'Create Quotation', nameAr: 'إنشاء عرض سعر', module: 'quotations', action: 'create', description: 'Create new quotations' },
  { name: 'Edit Quotation', nameAr: 'تعديل عرض سعر', module: 'quotations', action: 'edit', description: 'Edit quotation information' },
  { name: 'Delete Quotation', nameAr: 'حذف عرض سعر', module: 'quotations', action: 'delete', description: 'Delete quotations' },

  // Purchases
  { name: 'View Purchases', nameAr: 'عرض المشتريات', module: 'purchases', action: 'view', description: 'View purchase orders' },
  { name: 'Create Purchase', nameAr: 'إنشاء مشتريات', module: 'purchases', action: 'create', description: 'Create purchase orders' },
  { name: 'Edit Purchase', nameAr: 'تعديل مشتريات', module: 'purchases', action: 'edit', description: 'Edit purchase orders' },
  { name: 'Delete Purchase', nameAr: 'حذف مشتريات', module: 'purchases', action: 'delete', description: 'Delete purchase orders' },

  // Expenses
  { name: 'View Expenses', nameAr: 'عرض المصروفات', module: 'expenses', action: 'view', description: 'View expense list and details' },
  { name: 'Create Expense', nameAr: 'إنشاء مصروف', module: 'expenses', action: 'create', description: 'Create new expenses' },
  { name: 'Edit Expense', nameAr: 'تعديل مصروف', module: 'expenses', action: 'edit', description: 'Edit expense information' },
  { name: 'Delete Expense', nameAr: 'حذف مصروف', module: 'expenses', action: 'delete', description: 'Delete expenses' },
  { name: 'Approve Expenses', nameAr: 'الموافقة على المصروفات', module: 'expenses', action: 'approve', description: 'Approve or reject expenses' },

  // Financial
  { name: 'View Financial Reports', nameAr: 'عرض التقارير المالية', module: 'financial', action: 'view', description: 'View financial reports and analytics' },
  { name: 'Export Financial Data', nameAr: 'تصدير البيانات المالية', module: 'financial', action: 'export', description: 'Export financial reports' },

  // Employees
  { name: 'View Employees', nameAr: 'عرض الموظفين', module: 'employees', action: 'view', description: 'View employee list and details' },
  { name: 'Create Employee', nameAr: 'إنشاء موظف', module: 'employees', action: 'create', description: 'Create new employees' },
  { name: 'Edit Employee', nameAr: 'تعديل موظف', module: 'employees', action: 'edit', description: 'Edit employee information' },
  { name: 'Delete Employee', nameAr: 'حذف موظف', module: 'employees', action: 'delete', description: 'Delete employees' },

  // Reports
  { name: 'View Reports', nameAr: 'عرض التقارير', module: 'reports', action: 'view', description: 'View system reports' },
  { name: 'Export Reports', nameAr: 'تصدير التقارير', module: 'reports', action: 'export', description: 'Export reports' },

  // Settings
  { name: 'View Settings', nameAr: 'عرض الإعدادات', module: 'settings', action: 'view', description: 'View system settings' },
  { name: 'Edit Settings', nameAr: 'تعديل الإعدادات', module: 'settings', action: 'edit', description: 'Modify system settings' },

  // User Management
  { name: 'View Users', nameAr: 'عرض المستخدمين', module: 'users', action: 'view', description: 'View user accounts' },
  { name: 'Create User', nameAr: 'إنشاء مستخدم', module: 'users', action: 'create', description: 'Create new user accounts' },
  { name: 'Edit User', nameAr: 'تعديل مستخدم', module: 'users', action: 'edit', description: 'Edit user accounts' },
  { name: 'Delete User', nameAr: 'حذف مستخدم', module: 'users', action: 'delete', description: 'Delete user accounts' },

  // Role Management
  { name: 'View Roles', nameAr: 'عرض الأدوار', module: 'roles', action: 'view', description: 'View user roles' },
  { name: 'Create Role', nameAr: 'إنشاء دور', module: 'roles', action: 'create', description: 'Create new roles' },
  { name: 'Edit Role', nameAr: 'تعديل دور', module: 'roles', action: 'edit', description: 'Edit roles and permissions' },
  { name: 'Delete Role', nameAr: 'حذف دور', module: 'roles', action: 'delete', description: 'Delete roles' },

  // POS
  { name: 'Access POS', nameAr: 'الوصول لنقطة البيع', module: 'pos', action: 'access', description: 'Access point of sale system' },
  { name: 'Process Sales', nameAr: 'معالجة المبيعات', module: 'pos', action: 'sell', description: 'Process sales transactions' },

  // Leads
  { name: 'View Leads', nameAr: 'عرض العملاء المحتملين', module: 'leads', action: 'view', description: 'View potential customers' },
  { name: 'Create Lead', nameAr: 'إنشاء عميل محتمل', module: 'leads', action: 'create', description: 'Create new leads' },
  { name: 'Edit Lead', nameAr: 'تعديل عميل محتمل', module: 'leads', action: 'edit', description: 'Edit lead information' },
  { name: 'Convert Lead', nameAr: 'تحويل عميل محتمل', module: 'leads', action: 'convert', description: 'Convert leads to customers' },
]

// Default roles with their permissions
const DEFAULT_ROLES = [
  {
    name: 'Super Admin',
    nameAr: 'مدير عام',
    description: 'Full system access with all permissions',
    isSystem: true,
    permissions: 'ALL', // Special case for all permissions
  },
  {
    name: 'Manager',
    nameAr: 'مدير',
    description: 'Management level access with most permissions',
    isSystem: true,
    permissions: [
      'dashboard.view', 'customers.view', 'customers.create', 'customers.edit', 'customers.export',
      'suppliers.view', 'suppliers.create', 'suppliers.edit',
      'products.view', 'products.create', 'products.edit', 'products.stock',
      'categories.view', 'categories.manage', 'units.view', 'units.manage',
      'tasks.view', 'tasks.create', 'tasks.edit', 'tasks.assign',
      'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.print',
      'quotations.view', 'quotations.create', 'quotations.edit',
      'purchases.view', 'purchases.create', 'purchases.edit',
      'expenses.view', 'expenses.create', 'expenses.edit', 'expenses.approve',
      'financial.view', 'financial.export',
      'employees.view', 'employees.create', 'employees.edit',
      'reports.view', 'reports.export',
      'settings.view',
      'pos.access', 'pos.sell',
      'leads.view', 'leads.create', 'leads.edit', 'leads.convert',
    ],
  },
  {
    name: 'Sales Representative',
    nameAr: 'مندوب مبيعات',
    description: 'Sales focused permissions',
    isSystem: true,
    permissions: [
      'dashboard.view',
      'customers.view', 'customers.create', 'customers.edit',
      'products.view',
      'tasks.view', 'tasks.create', 'tasks.edit',
      'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.print',
      'quotations.view', 'quotations.create', 'quotations.edit',
      'pos.access', 'pos.sell',
      'leads.view', 'leads.create', 'leads.edit', 'leads.convert',
    ],
  },
  {
    name: 'Employee',
    nameAr: 'موظف',
    description: 'Basic employee access',
    isSystem: true,
    permissions: [
      'dashboard.view',
      'customers.view',
      'products.view',
      'tasks.view',
      'invoices.view',
      'quotations.view',
      'expenses.view', 'expenses.create',
    ],
  },
]

export async function seedPermissionsAndRoles() {
  try {
    console.log('🌱 Seeding permissions and roles...')

    // Create permissions
    console.log('Creating permissions...')
    const createdPermissions = []
    
    for (const permission of DEFAULT_PERMISSIONS) {
      const existing = await prisma.permission.findFirst({
        where: {
          module: permission.module,
          action: permission.action,
          resource: permission.resource || null,
        },
      })

      if (!existing) {
        const created = await prisma.permission.create({
          data: permission,
        })
        createdPermissions.push(created)
        console.log(`✅ Created permission: ${permission.name}`)
      } else {
        console.log(`⏭️  Permission already exists: ${permission.name}`)
      }
    }

    // Get all permissions for role assignment
    const allPermissions = await prisma.permission.findMany()
    const permissionMap = allPermissions.reduce((acc, p) => {
      const key = p.resource ? `${p.module}.${p.action}.${p.resource}` : `${p.module}.${p.action}`
      acc[key] = p.id
      return acc
    }, {} as Record<string, string>)

    // Create roles
    console.log('Creating roles...')
    for (const roleData of DEFAULT_ROLES) {
      const existing = await prisma.role.findUnique({
        where: { name: roleData.name },
      })

      if (!existing) {
        // Determine permission IDs for this role
        let permissionIds: string[] = []
        
        if (roleData.permissions === 'ALL') {
          permissionIds = allPermissions.map(p => p.id)
        } else {
          permissionIds = (roleData.permissions as string[])
            .map(permKey => permissionMap[permKey])
            .filter(Boolean)
        }

        const role = await prisma.role.create({
          data: {
            name: roleData.name,
            nameAr: roleData.nameAr,
            description: roleData.description,
            isSystem: roleData.isSystem,
            permissions: {
              create: permissionIds.map(permissionId => ({
                permissionId,
                granted: true,
              })),
            },
          },
        })

        console.log(`✅ Created role: ${roleData.name} with ${permissionIds.length} permissions`)
      } else {
        console.log(`⏭️  Role already exists: ${roleData.name}`)
      }
    }

    console.log('🎉 Permissions and roles seeded successfully!')
    return true
  } catch (error) {
    console.error('❌ Error seeding permissions and roles:', error)
    return false
  }
}
