"use client"

import { useState, useEffect } from "react"
import {
  getExpenses,
  addExpense,
  updateExpense,
  deleteExpense,
  type Expense,
  type ExpenseFilters
} from "@/lib/expense-storage"
import {
  getExpenseTypes,
  type ExpenseType
} from "@/lib/expense-type-storage"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Search,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  Receipt,
  Download,
  Filter,
  Calendar,
  DollarSign,
  CreditCard,
  Clock,
  CheckCircle,
  TrendingUp
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"



const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
  APPROVED: "bg-green-100 text-green-800 border-green-200",
  REJECTED: "bg-red-100 text-red-800 border-red-200",
  PAID: "bg-blue-100 text-blue-800 border-blue-200",
}

const paymentMethodColors = {
  CASH: "bg-green-50 text-green-700",
  CARD: "bg-blue-50 text-blue-700",
  BANK_TRANSFER: "bg-purple-50 text-purple-700",
  CHECK: "bg-orange-50 text-orange-700",
  OTHER: "bg-gray-50 text-gray-700",
}

interface ExpenseStats {
  overview: {
    totalExpenses: number
    totalAmount: number
    avgExpenseAmount: number
    approvalRate: number
  }
  statusBreakdown: {
    pending: { count: number; amount: number }
    approved: { count: number; amount: number }
    rejected: { count: number; amount: number }
    paid: { count: number; amount: number }
  }
}

export default function ExpensesPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([])
  const [stats, setStats] = useState<ExpenseStats | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [loading, setLoading] = useState(true)

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      try {
        const [expensesData, typesData, statsData] = await Promise.all([
          getExpenses({ limit: 100 }),
          getExpenseTypes(),
          fetch('/api/expenses/stats').then(res => res.json())
        ])
        setExpenses(expensesData.expenses)
        setExpenseTypes(typesData)
        setStats(statsData)
      } catch (error) {
        console.error('Error loading data:', error)
      } finally {
        setLoading(false)
      }
    }
    loadData()
  }, [])

  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.expenseType.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || expense.status === statusFilter
    const matchesType = typeFilter === "all" || expense.expenseTypeId === typeFilter

    return matchesSearch && matchesStatus && matchesType
  })

  const handleViewExpense = (expense: Expense) => {
    router.push(`/dashboard/expenses/${expense.id}`)
  }

  const handleEditExpense = (expense: Expense) => {
    router.push(`/dashboard/expenses/${expense.id}/edit`)
  }

  const handleApproveExpense = async (expense: Expense) => {
    const updated = await updateExpense(expense.id, { status: 'APPROVED' })
    if (updated) {
      setExpenses(prev => prev.map(e =>
        e.id === expense.id ? updated : e
      ))
      alert(`Expense ${expense.number} approved successfully`)
    } else {
      alert('Failed to approve expense. Please try again.')
    }
  }

  const handleRejectExpense = async (expense: Expense) => {
    const reason = prompt('Please enter rejection reason:')
    if (reason) {
      const updated = await updateExpense(expense.id, {
        status: 'REJECTED',
        notes: reason
      })
      if (updated) {
        setExpenses(prev => prev.map(e =>
          e.id === expense.id ? updated : e
        ))
        alert(`Expense ${expense.number} rejected`)
      } else {
        alert('Failed to reject expense. Please try again.')
      }
    }
  }

  const handleMarkAsPaid = async (expense: Expense) => {
    const updated = await updateExpense(expense.id, { status: 'PAID' })
    if (updated) {
      setExpenses(prev => prev.map(e =>
        e.id === expense.id ? updated : e
      ))
      alert(`Expense ${expense.number} marked as paid`)
    } else {
      alert('Failed to mark expense as paid. Please try again.')
    }
  }

  const handleDownloadReceipt = (expense: Expense) => {
    if (expense.receipt) {
      console.log('Downloading receipt:', expense.receipt)
      alert('Receipt download functionality would be implemented here')
    } else {
      alert('No receipt available for this expense')
    }
  }

  const handleDeleteExpense = async (expense: Expense) => {
    if (confirm(`Are you sure you want to delete expense ${expense.number}?`)) {
      try {
        const deleted = await deleteExpense(expense.id)
        if (deleted) {
          setExpenses(prev => prev.filter(e => e.id !== expense.id))
          alert('Expense deleted successfully')
        }
      } catch (error) {
        console.error('Delete error:', error)
        alert(`Failed to delete expense: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }

  // Use real statistics from API or fallback to filtered data
  const totalAmount = stats?.overview.totalAmount || filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0)
  const pendingCount = stats?.statusBreakdown.pending.count || filteredExpenses.filter(e => e.status === 'PENDING').length
  const approvedCount = stats?.statusBreakdown.approved.count || filteredExpenses.filter(e => e.status === 'APPROVED').length
  const paidCount = stats?.statusBreakdown.paid.count || filteredExpenses.filter(e => e.status === 'PAID').length

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading expenses...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('expenses.title')}</h2>
          <p className="text-muted-foreground">
            {t('expenses.description')}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/expense-types')}
          >
            {t('expenses.manageTypes')}
          </Button>
          <Button onClick={() => router.push('/dashboard/expenses/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('expenses.addExpense')}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('expenses.searchExpenses')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder={t('expenses.status')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('expenses.allStatuses')}</SelectItem>
            <SelectItem value="PENDING">{t('expenses.pending')}</SelectItem>
            <SelectItem value="APPROVED">{t('expenses.approved')}</SelectItem>
            <SelectItem value="REJECTED">{t('expenses.rejected')}</SelectItem>
            <SelectItem value="PAID">{t('expenses.paid')}</SelectItem>
          </SelectContent>
        </Select>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('expenses.type')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('expenses.allCategories')}</SelectItem>
            {expenseTypes.map((type) => (
              <SelectItem key={type.id} value={type.id}>
                {type.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <div className="rounded-lg border p-6 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-blue-700">{formatCurrency(totalAmount)}</div>
              <div className="text-sm text-blue-600 font-medium">{t('expenses.totalExpenses')}</div>
              {stats && (
                <div className="text-xs text-blue-500 mt-1">
                  {stats.overview.totalExpenses} {t('expenses.expensesLabel')} • {t('expenses.averageLabel')} {formatCurrency(stats.overview.avgExpenseAmount)}
                </div>
              )}
            </div>
            <div className="p-3 bg-blue-500 rounded-full">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border p-6 bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-yellow-700">{pendingCount}</div>
              <div className="text-sm text-yellow-600 font-medium">{t('expenses.pendingExpenses')}</div>
              {stats && (
                <div className="text-xs text-yellow-500 mt-1">
                  {formatCurrency(stats.statusBreakdown.pending.amount)} {t('expenses.pendingLabel')}
                </div>
              )}
            </div>
            <div className="p-3 bg-yellow-500 rounded-full">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-green-700">{approvedCount}</div>
              <div className="text-sm text-green-600 font-medium">{t('expenses.approved')}</div>
              {stats && (
                <div className="text-xs text-green-500 mt-1">
                  {formatCurrency(stats.statusBreakdown.approved.amount)} {t('expenses.approvedLabel')}
                </div>
              )}
            </div>
            <div className="p-3 bg-green-500 rounded-full">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border p-6 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-purple-700">{paidCount}</div>
              <div className="text-sm text-purple-600 font-medium">{t('expenses.paid')}</div>
              {stats && (
                <div className="text-xs text-purple-500 mt-1">
                  {formatCurrency(stats.statusBreakdown.paid.amount)} {t('expenses.paidLabel')}
                </div>
              )}
            </div>
            <div className="p-3 bg-purple-500 rounded-full">
              <CreditCard className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('expenses.expense')} #</TableHead>
              <TableHead>{t('common.date')}</TableHead>
              <TableHead>{t('common.description')}</TableHead>
              <TableHead>{t('expenses.type')}</TableHead>
              <TableHead>{t('common.amount')}</TableHead>
              <TableHead>{t('expenses.paymentMethod')}</TableHead>
              <TableHead>{t('expenses.status')}</TableHead>
              <TableHead>{t('expenses.receipt')}</TableHead>
              <TableHead className="text-right">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredExpenses.map((expense) => (
              <TableRow key={expense.id}>
                <TableCell className="font-medium">{expense.number}</TableCell>
                <TableCell>{new Date(expense.date).toLocaleDateString()}</TableCell>
                <TableCell className="max-w-[200px]">
                  <p className="truncate">{expense.description}</p>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{expense.expenseType.name}</Badge>
                </TableCell>
                <TableCell className="font-medium">{formatCurrency(expense.amount)}</TableCell>
                <TableCell>
                  <Badge className={paymentMethodColors[expense.paymentMethod]}>
                    {t(`expenses.paymentMethodLabels.${expense.paymentMethod}`)}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge className={statusColors[expense.status]}>
                    {t(`expenses.statusLabels.${expense.status}`)}
                  </Badge>
                </TableCell>
                <TableCell>
                  {expense.receipt ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDownloadReceipt(expense)}
                    >
                      <Receipt className="h-4 w-4" />
                    </Button>
                  ) : (
                    <span className="text-muted-foreground text-sm">{t('expenses.noReceipt')}</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">{t('navigation.openMenu')}</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewExpense(expense)}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t('expenses.viewExpense')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditExpense(expense)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('expenses.editExpense')}
                      </DropdownMenuItem>
                      {expense.receipt && (
                        <DropdownMenuItem onClick={() => handleDownloadReceipt(expense)}>
                          <Download className="mr-2 h-4 w-4" />
                          {t('common.download')} {t('expenses.receipt')}
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      {expense.status === 'PENDING' && (
                        <>
                          <DropdownMenuItem
                            onClick={() => handleApproveExpense(expense)}
                            className="text-green-600 focus:text-green-600"
                          >
                            <DollarSign className="mr-2 h-4 w-4" />
                            {t('expenses.approve')}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleRejectExpense(expense)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            {t('expenses.reject')}
                          </DropdownMenuItem>
                        </>
                      )}
                      {expense.status === 'APPROVED' && (
                        <DropdownMenuItem
                          onClick={() => handleMarkAsPaid(expense)}
                          className="text-blue-600 focus:text-blue-600"
                        >
                          <DollarSign className="mr-2 h-4 w-4" />
                          {t('expenses.markAsPaid')}
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteExpense(expense)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('common.delete')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
