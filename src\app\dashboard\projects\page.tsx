"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  FolderOpen,
  Calendar,
  Users,
  Target,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3,
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"

interface Project {
  id: string
  name: string
  nameAr?: string
  description?: string
  status: 'PLANNING' | 'IN_PROGRESS' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  startDate: string
  endDate?: string
  budget?: number
  actualCost?: number
  progress: number
  clientId?: string
  client?: {
    id: string
    name: string
    company?: string
  }
  managerId?: string
  manager?: {
    id: string
    name: string
  }
  teamMembers?: any[]
  tasks?: any[]
  createdAt: string
  updatedAt: string
}

const statusColors = {
  PLANNING: "bg-blue-100 text-blue-800",
  IN_PROGRESS: "bg-yellow-100 text-yellow-800",
  ON_HOLD: "bg-orange-100 text-orange-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
}

const priorityColors = {
  LOW: "bg-gray-100 text-gray-800",
  MEDIUM: "bg-blue-100 text-blue-800",
  HIGH: "bg-orange-100 text-orange-800",
  URGENT: "bg-red-100 text-red-800",
}

export default function ProjectsPage() {
  const router = useRouter()
  const { t, language, direction } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [projects, setProjects] = useState<Project[]>([])
  const [statusFilter, setStatusFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [loading, setLoading] = useState(true)

  // Load projects from API
  useEffect(() => {
    const loadProjects = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/projects')
        if (response.ok) {
          const data = await response.json()
          setProjects(data)
        }
      } catch (error) {
        console.error('Error loading projects:', error)
      } finally {
        setLoading(false)
      }
    }

    loadProjects()
  }, [])

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.client?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.client?.company?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === "all" || project.status.toLowerCase() === statusFilter.toLowerCase()
    const matchesPriority = priorityFilter === "all" || project.priority.toLowerCase() === priorityFilter.toLowerCase()

    return matchesSearch && matchesStatus && matchesPriority
  })

  const handleViewProject = (project: Project) => {
    router.push(`/dashboard/projects/${project.id}`)
  }

  const handleEditProject = (project: Project) => {
    router.push(`/dashboard/projects/${project.id}/edit`)
  }

  const handleDeleteProject = async (project: Project) => {
    if (confirm(`Are you sure you want to delete project "${project.name}"?`)) {
      try {
        const response = await fetch(`/api/projects/${project.id}`, {
          method: 'DELETE'
        })
        
        if (response.ok) {
          setProjects(prev => prev.filter(p => p.id !== project.id))
          alert(`Project "${project.name}" deleted successfully!`)
        }
      } catch (error) {
        console.error('Error deleting project:', error)
        alert('Failed to delete project')
      }
    }
  }

  // Calculate statistics
  const totalProjects = projects.length
  const activeProjects = projects.filter(p => p.status === 'IN_PROGRESS').length
  const completedProjects = projects.filter(p => p.status === 'COMPLETED').length
  const totalBudget = projects.reduce((sum, p) => sum + (Number(p.budget) || 0), 0)
  const totalActualCost = projects.reduce((sum, p) => sum + (Number(p.actualCost) || 0), 0)

  return (
    <div className={`flex-1 space-y-6 p-8 pt-6 ${direction === 'rtl' ? 'font-arabic' : ''}`} dir={direction}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('projects.projectManagement') || 'Project Management'}</h2>
          <p className="text-muted-foreground">
            {t('projects.manageDescription') || 'Manage your projects, track progress, and collaborate with your team'}
          </p>
        </div>
        <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
          <Button variant="outline" onClick={() => router.push('/dashboard/projects/dashboard')}>
            <BarChart3 className={`${direction === 'rtl' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
            {t('projects.dashboard') || 'Dashboard'}
          </Button>
          <Button onClick={() => router.push('/dashboard/projects/create')}>
            <Plus className={`${direction === 'rtl' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
            {t('projects.createProject') || 'Create Project'}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <div className="rounded-lg border bg-gradient-to-br from-blue-50 to-indigo-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">{t('projects.totalProjects') || 'Total Projects'}</p>
              <p className="text-3xl font-bold text-blue-900">{totalProjects}</p>
            </div>
            <div className="rounded-full bg-blue-100 p-3">
              <FolderOpen className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-yellow-50 to-orange-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-600">{t('projects.activeProjects') || 'Active Projects'}</p>
              <p className="text-3xl font-bold text-yellow-900">{activeProjects}</p>
            </div>
            <div className="rounded-full bg-yellow-100 p-3">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-green-50 to-emerald-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">{t('projects.completedProjects') || 'Completed'}</p>
              <p className="text-3xl font-bold text-green-900">{completedProjects}</p>
            </div>
            <div className="rounded-full bg-green-100 p-3">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-purple-50 to-pink-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">{t('projects.totalBudget') || 'Total Budget'}</p>
              <p className="text-2xl font-bold text-purple-900">{formatCurrency(totalBudget)}</p>
            </div>
            <div className="rounded-full bg-purple-100 p-3">
              <Target className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="rounded-lg border bg-gradient-to-br from-red-50 to-pink-50 p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-red-600">{t('projects.actualCost') || 'Actual Cost'}</p>
              <p className="text-2xl font-bold text-red-900">{formatCurrency(totalActualCost)}</p>
            </div>
            <div className="rounded-full bg-red-100 p-3">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('projects.searchPlaceholder') || 'Search projects, clients...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={`${t('common.filterBy') || 'Filter by'} ${t('common.status') || 'Status'}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('common.allStatuses') || 'All Status'}</SelectItem>
            <SelectItem value="planning">{t('projects.statuses.planning') || 'Planning'}</SelectItem>
            <SelectItem value="in_progress">{t('projects.statuses.inProgress') || 'In Progress'}</SelectItem>
            <SelectItem value="on_hold">{t('projects.statuses.onHold') || 'On Hold'}</SelectItem>
            <SelectItem value="completed">{t('projects.statuses.completed') || 'Completed'}</SelectItem>
            <SelectItem value="cancelled">{t('projects.statuses.cancelled') || 'Cancelled'}</SelectItem>
          </SelectContent>
        </Select>
        <Select value={priorityFilter} onValueChange={setPriorityFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={`${t('common.filterBy') || 'Filter by'} ${t('common.priority') || 'Priority'}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('common.allPriorities') || 'All Priorities'}</SelectItem>
            <SelectItem value="low">{t('projects.priorities.low') || 'Low'}</SelectItem>
            <SelectItem value="medium">{t('projects.priorities.medium') || 'Medium'}</SelectItem>
            <SelectItem value="high">{t('projects.priorities.high') || 'High'}</SelectItem>
            <SelectItem value="urgent">{t('projects.priorities.urgent') || 'Urgent'}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Projects Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('projects.projectName') || 'Project'}</TableHead>
              <TableHead>{t('common.client') || 'Client'}</TableHead>
              <TableHead>{t('common.manager') || 'Manager'}</TableHead>
              <TableHead>{t('common.status') || 'Status'}</TableHead>
              <TableHead>{t('common.priority') || 'Priority'}</TableHead>
              <TableHead>{t('common.progress') || 'Progress'}</TableHead>
              <TableHead>{t('common.budget') || 'Budget'}</TableHead>
              <TableHead>{t('common.dueDate') || 'Due Date'}</TableHead>
              <TableHead className="text-right">{t('common.actions') || 'Actions'}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span>Loading projects...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredProjects.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm ? (t('projects.noProjectsFound') || 'No projects found matching your search.') : (t('projects.createFirstProject') || 'No projects found. Create your first project!')}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredProjects.map((project) => (
                <TableRow key={project.id}>
                  <TableCell>
                    <div>
                      <Button
                        variant="link"
                        className="p-0 h-auto font-medium"
                        onClick={() => handleViewProject(project)}
                      >
                        {project.name}
                      </Button>
                      {project.nameAr && (
                        <div className="text-sm text-muted-foreground">{project.nameAr}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{project.client?.name || (t('projects.noClient') || 'No Client')}</div>
                      {project.client?.company && (
                        <div className="text-sm text-muted-foreground">{project.client.company}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Users className="mr-1 h-3 w-3" />
                      {project.manager?.name || (t('common.unassigned') || 'Unassigned')}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={statusColors[project.status as keyof typeof statusColors]}>
                      {project.status.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={priorityColors[project.priority as keyof typeof priorityColors]}>
                      {project.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${project.progress}%` }}
                        ></div>
                      </div>
                      <span className="text-sm">{project.progress}%</span>
                    </div>
                  </TableCell>
                  <TableCell>{formatCurrency(Number(project.budget) || 0)}</TableCell>
                  <TableCell>
                    {project.endDate ? new Date(project.endDate).toLocaleDateString() : (t('projects.noDueDate') || 'No due date')}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewProject(project)}>
                          <Eye className="mr-2 h-4 w-4" />
                          {t('common.view') || 'View Details'}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditProject(project)}>
                          <Edit className="mr-2 h-4 w-4" />
                          {t('projects.editProject') || 'Edit Project'}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteProject(project)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          {t('projects.deleteProject') || 'Delete Project'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
