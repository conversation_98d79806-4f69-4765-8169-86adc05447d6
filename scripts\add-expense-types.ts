import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Adding expense types...')

  const expenseTypes = [
    {
      name: 'Office Supplies',
      nameAr: 'المستلزمات المكتبية',
      description: 'Stationery, paper, pens, and other office materials',
      isActive: true,
    },
    {
      name: 'Travel & Transportation',
      nameAr: 'السفر والمواصلات',
      description: 'Business travel, fuel, taxi, and transportation costs',
      isActive: true,
    },
    {
      name: 'Utilities',
      nameAr: 'المرافق',
      description: 'Electricity, water, internet, and phone bills',
      isActive: true,
    },
    {
      name: 'Marketing & Advertising',
      nameAr: 'التسويق والإعلان',
      description: 'Promotional materials, online ads, and marketing campaigns',
      isActive: true,
    },
    {
      name: 'Equipment & Maintenance',
      nameAr: 'المعدات والصيانة',
      description: 'Equipment purchases, repairs, and maintenance',
      isActive: true,
    },
    {
      name: 'Professional Services',
      nameAr: 'الخدمات المهنية',
      description: 'Legal, accounting, consulting, and other professional services',
      isActive: true,
    },
    {
      name: 'Meals & Entertainment',
      nameAr: 'الوجبات والترفيه',
      description: 'Business meals, client entertainment, and team events',
      isActive: true,
    },
    {
      name: 'Software & Subscriptions',
      nameAr: 'البرمجيات والاشتراكات',
      description: 'Software licenses, SaaS subscriptions, and digital tools',
      isActive: true,
    },
  ]

  for (const expenseType of expenseTypes) {
    try {
      const existing = await prisma.expenseType.findUnique({
        where: { name: expenseType.name }
      })

      if (!existing) {
        await prisma.expenseType.create({
          data: expenseType
        })
        console.log(`✅ Created expense type: ${expenseType.name}`)
      } else {
        console.log(`⚠️  Expense type already exists: ${expenseType.name}`)
      }
    } catch (error) {
      console.error(`❌ Error creating expense type ${expenseType.name}:`, error)
    }
  }

  console.log('✅ Expense types setup completed!')
}

main()
  .catch((e) => {
    console.error('❌ Error:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 