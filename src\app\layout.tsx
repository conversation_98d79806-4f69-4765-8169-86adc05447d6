import type { Metada<PERSON> } from "next";
import { <PERSON>, <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { SessionProvider } from "@/components/providers/session-provider";
import { Toaster } from "@/components/ui/sonner";
import { I18nProvider } from "@/lib/i18n";

const inter = Inter({
  subsets: ["latin"],
  variable: '--font-inter'
});

const tajawal = Tajawal({
  subsets: ["arabic"],
  weight: ['200', '300', '400', '500', '700', '800', '900'],
  variable: '--font-arabic'
});

export const metadata: Metadata = {
  title: "Office Sales & Services Management",
  description: "نظام إدارة المبيعات والخدمات المكتبية",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${tajawal.variable}`}>
      <body suppressHydrationWarning={true}>
        <SessionProvider>
          <I18nProvider>
            {children}
            <Toaster />
          </I18nProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
