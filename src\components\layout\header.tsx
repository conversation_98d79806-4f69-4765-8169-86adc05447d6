"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Search, User, LogOut, Settings, ShoppingCart, Menu, PanelLeftClose, PanelLeftOpen } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/hooks/use-auth"
import { useRouter } from "next/navigation"
import { NotificationCenter } from "@/components/notifications/notification-center"
import { ColorThemeSelector } from "@/components/ui/color-theme-selector"
import { useI18n } from "@/lib/i18n"
import { useCompanySettings } from "@/hooks/use-company-settings"

interface HeaderProps {
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
  sidebarCollapsed?: boolean
  onToggleSidebarCollapse?: () => void
}

export function Header({ sidebarOpen, setSidebarOpen, sidebarCollapsed, onToggleSidebarCollapse }: HeaderProps) {
  const { user, logout } = useAuth()
  const router = useRouter()
  const { language, setLanguage, t, direction } = useI18n()
  const { companyName, companyNameAr } = useCompanySettings()

  const handleSignOut = async () => {
    await logout()
  }

  const getInitials = (name: string) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'U'
  }

  return (
    <header className={`flex items-center justify-between px-6 py-4 bg-white border-b border-gray-200 ${direction === 'rtl' ? 'rtl' : 'ltr'}`} dir={direction}>
      <div className={`flex items-center flex-1 ${direction === 'rtl' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
        {/* Mobile menu button */}
        <Button
          variant="ghost"
          size="sm"
          className="lg:hidden"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          <Menu className="h-5 w-5" />
        </Button>

        {/* Desktop sidebar toggle button */}
        {onToggleSidebarCollapse && (
          <Button
            variant="ghost"
            size="sm"
            className="hidden lg:flex"
            onClick={onToggleSidebarCollapse}
            title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {sidebarCollapsed ? (
              <PanelLeftOpen className="h-5 w-5" />
            ) : (
              <PanelLeftClose className="h-5 w-5" />
            )}
          </Button>
        )}

        <h2 className={`text-lg font-semibold text-gray-800 ${direction === 'rtl' ? 'text-right' : 'text-left'}`}>
          {direction === 'rtl' ? companyNameAr : companyName}
        </h2>
        <div className="relative w-64 hidden sm:block">
          <Search className={`absolute top-2.5 h-4 w-4 text-muted-foreground ${direction === 'rtl' ? 'right-2' : 'left-2'}`} />
          <Input
            placeholder={t('common.search')}
            className={direction === 'rtl' ? 'pr-8 text-right' : 'pl-8 text-left'}
            dir={direction}
          />
        </div>
      </div>

      <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
        {/* POS Button - Hidden on mobile */}
        <Button
          variant="default"
          size="sm"
          onClick={() => window.open('/pos', '_blank')}
          className="theme-accent hidden md:flex"
          dir={direction}
        >
          <ShoppingCart className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
          {t('navigation.pos')}
        </Button>

        {/* Language Switcher */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" dir={direction}>
              {language === 'ar' ? 'ع' : 'EN'}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align={direction === 'rtl' ? 'start' : 'end'}>
            <DropdownMenuItem
              onClick={() => setLanguage('en')}
              className={`${language === 'en' ? 'bg-accent' : ''} ${direction === 'rtl' ? 'text-right' : 'text-left'}`}
            >
              English
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => setLanguage('ar')}
              className={`${language === 'ar' ? 'bg-accent' : ''} ${direction === 'rtl' ? 'text-right' : 'text-left'}`}
            >
              العربية
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Color Theme Selector */}
        <ColorThemeSelector />

        {/* Notifications */}
        <NotificationCenter />

        {/* User Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-8 w-8 rounded-full">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.avatar || "/avatars/01.png"} alt="@user" />
                <AvatarFallback>{getInitials(user?.name || "User")}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align={direction === 'rtl' ? 'start' : 'end'} forceMount>
            <DropdownMenuLabel className={`font-normal ${direction === 'rtl' ? 'text-right' : 'text-left'}`}>
              <div className={`flex flex-col space-y-1 ${direction === 'rtl' ? 'items-end' : 'items-start'}`}>
                <p className={`text-sm font-medium leading-none ${direction === 'rtl' ? 'text-right' : 'text-left'}`}>{user?.name || "User"}</p>
                <p className={`text-xs leading-none text-muted-foreground ${direction === 'rtl' ? 'text-right' : 'text-left'}`}>
                  {user?.email || "<EMAIL>"}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem className={`${direction === 'rtl' ? 'flex-row-reverse text-right' : 'text-left'}`}>
              <User className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
              <span>{t('common.profile')}</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => router.push('/dashboard/settings')} className={`${direction === 'rtl' ? 'flex-row-reverse text-right' : 'text-left'}`}>
              <Settings className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
              <span>{t('common.settings')}</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSignOut} className={`${direction === 'rtl' ? 'flex-row-reverse text-right' : 'text-left'}`}>
              <LogOut className={`h-4 w-4 ${direction === 'rtl' ? 'ml-2' : 'mr-2'}`} />
              <span>{t('common.logout')}</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}
