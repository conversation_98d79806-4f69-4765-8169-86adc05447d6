"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useI18n } from "@/lib/i18n"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  ArrowLeft,
  Download,
  Mail,
  Printer,
  Edit,
  CreditCard,
  Calendar,
  User,
  DollarSign,
  Clock,
  FileText,
  Share,
  MoreHorizontal,
  Trash2,
  Copy
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { InvoiceTemplate } from "@/components/invoices/invoice-template"
import { PaymentDialog } from "@/components/invoices/payment-dialog"

interface Invoice {
  id: string
  number: string
  date: string
  dueDate: string
  customer: {
    id: string
    name: string
    email?: string
    phone: string
    company?: string
    address?: string
  }
  status: 'PAID' | 'UNPAID' | 'PARTIAL' | 'OVERDUE'
  subtotal: number
  taxAmount: number
  discount: number
  total: number
  notes?: string
  items: Array<{
    id: string
    description: string
    quantity: number
    unitPrice: number
    total: number
    product?: {
      name: string
      unit: string
    }
  }>
  payments: Array<{
    id: string
    amount: number
    method: string
    date: string
    reference?: string
    notes?: string
  }>
}

export default function InvoiceDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { t } = useI18n()
  const [invoice, setInvoice] = useState<Invoice | null>(null)
  const [loading, setLoading] = useState(true)
  const [isPrintView, setIsPrintView] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [companyData, setCompanyData] = useState({
    name: 'Print & Copy Services',
    nameAr: 'خدمات الطباعة والنسخ',
    address: '123 Business Street, Muscat, Sultanate of Oman',
    addressAr: '123 شارع الأعمال، مسقط، سلطنة عمان',
    phone: '+968 2234 5678',
    email: '<EMAIL>',
    taxNumber: 'OM123456789',
    logo: '',
    termsConditions: '',
    termsConditionsAr: '',
    signature: '',
    stamp: ''
  })

  const statusColors = {
    PAID: "bg-green-100 text-green-800",
    UNPAID: "bg-red-100 text-red-800",
    PARTIAL: "bg-yellow-100 text-yellow-800",
    OVERDUE: "bg-purple-100 text-purple-800",
  }

  useEffect(() => {
    if (params.id) {
      fetchInvoice(params.id as string)
    }

    // Load company data from settings API
    const loadCompanyData = async () => {
      try {
        const response = await fetch('/api/settings')
        if (response.ok) {
          const { settingsObject } = await response.json()
          setCompanyData(prev => ({
            ...prev,
            name: settingsObject.company_name?.value || prev.name,
            nameAr: settingsObject.company_name_ar?.value || prev.nameAr,
            address: settingsObject.company_address?.value || prev.address,
            addressAr: settingsObject.company_address_ar?.value || prev.addressAr,
            phone: settingsObject.company_phone?.value || prev.phone,
            email: settingsObject.company_email?.value || prev.email,
            taxNumber: settingsObject.company_tax_number?.value || prev.taxNumber,
            termsConditions: settingsObject.terms_conditions?.value || prev.termsConditions,
            termsConditionsAr: settingsObject.terms_conditions_ar?.value || prev.termsConditionsAr,
            signature: settingsObject.company_signature?.value || prev.signature,
            stamp: settingsObject.company_stamp?.value || prev.stamp,
            logo: settingsObject.company_logo?.value || prev.logo,
          }))
        }
      } catch (error) {
        console.error('Error loading company data:', error)
      }
    }

    loadCompanyData()
  }, [params.id])

  const fetchInvoice = async (id: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/invoices/${id}`)
      if (response.ok) {
        const data = await response.json()
        setInvoice(data)
      } else {
        console.error('Failed to fetch invoice')
        setInvoice(null)
      }
    } catch (error) {
      console.error('Error fetching invoice:', error)
      setInvoice(null)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="text-center py-8">{t('invoices.loadingInvoice')}</div>
      </div>
    )
  }

  if (!invoice) {
    return (
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="text-center py-8">{t('invoices.invoiceNotFound')}</div>
      </div>
    )
  }

  const amountPaid = invoice.payments.reduce((sum, payment) => sum + payment.amount, 0)
  const balance = invoice.total - amountPaid
  const isOverdue = invoice.status === 'OVERDUE'
  const daysPastDue = isOverdue ? Math.floor((new Date().getTime() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24)) : 0

  const handlePrint = () => {
    setIsPrintView(true)
    setTimeout(() => {
      window.print()
      setIsPrintView(false)
    }, 100)
  }

  const handleEmail = () => {
    console.log('Sending email for invoice:', invoice.number)
    alert('Email functionality would be implemented here')
  }

  const handleDownload = () => {
    console.log('Downloading invoice:', invoice.number)
    alert('PDF download functionality would be implemented here')
  }

  const handleEdit = () => {
    router.push(`/dashboard/invoices/${invoice.id}/edit`)
  }

  const handleDuplicate = () => {
    router.push(`/dashboard/invoices/create?duplicate=${invoice.id}`)
  }

  const handleDelete = () => {
    if (confirm(t('invoices.deleteConfirm'))) {
      console.log('Deleting invoice:', invoice.number)
      alert('Delete functionality would be implemented here')
      router.push('/dashboard/invoices')
    }
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `Invoice ${invoice.number}`,
        text: `Invoice ${invoice.number} for ${invoice.customer.name}`,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      alert(t('invoices.invoiceLinkCopied'))
    }
  }

  if (isPrintView) {
    return (
      <div className="print:block">
        <InvoiceTemplate
          invoice={{
            id: invoice.id,
            number: invoice.number,
            date: invoice.date,
            dueDate: invoice.dueDate,
            status: invoice.status,
            subtotal: invoice.subtotal,
            taxAmount: invoice.taxAmount,
            discount: invoice.discount,
            total: invoice.total,
            notes: invoice.notes,
            customer: {
              name: invoice.customer.name,
              company: invoice.customer.company,
              phone: invoice.customer.phone,
              email: invoice.customer.email,
            },
            items: invoice.items.map(item => ({
              id: item.id,
              description: item.description,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              total: item.total,
              product: item.product
            })),
            payments: invoice.payments,
            amountPaid: amountPaid,
            balance: balance
          }}
          company={companyData}
        />
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('invoices.back')}
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Invoice {invoice.number}</h1>
            <p className="text-muted-foreground">
              {t('invoices.createdOn')} {new Date(invoice.date).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Badge className={statusColors[invoice.status]}>
            {invoice.status}
          </Badge>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <MoreHorizontal className="mr-2 h-4 w-4" />
                {t('invoices.actionsMenu')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="mr-2 h-4 w-4" />
                {t('invoices.editInvoice')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDuplicate}>
                <Copy className="mr-2 h-4 w-4" />
                {t('invoices.duplicate')}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handlePrint}>
                <Printer className="mr-2 h-4 w-4" />
                {t('invoices.printPdf')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEmail}>
                <Mail className="mr-2 h-4 w-4" />
                {t('invoices.emailInvoice')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDownload}>
                <Download className="mr-2 h-4 w-4" />
                {t('invoices.download')}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleShare}>
                <Share className="mr-2 h-4 w-4" />
                {t('invoices.shareLabel')}
              </DropdownMenuItem>
              {balance > 0 && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => setShowPaymentDialog(true)}
                    className="text-green-600 focus:text-green-600"
                  >
                    <CreditCard className="mr-2 h-4 w-4" />
                    {t('invoices.recordPayment')}
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {t('invoices.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Invoice Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('invoices.customer')}</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{invoice.customer.name}</div>
            <p className="text-xs text-muted-foreground">{invoice.customer.company}</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('invoices.totalAmount')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">{formatCurrency(invoice.total)}</div>
            <p className="text-xs text-muted-foreground">
              {t('invoices.including')} {formatCurrency(invoice.taxAmount)} {t('invoices.tax')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('invoices.amountPaid')}</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold text-green-600">{formatCurrency(amountPaid)}</div>
            <p className="text-xs text-muted-foreground">
              {invoice.payments.length} {t('invoices.paymentsCount')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('invoices.balanceDue')}</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold text-orange-600">{formatCurrency(balance)}</div>
            <p className="text-xs text-muted-foreground">
              {t('invoices.due')} {new Date(invoice.dueDate).toLocaleDateString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Balance Alert */}
      {balance > 0 && (
        <div className={`p-4 rounded-lg border ${isOverdue ? 'bg-red-50 border-red-200' : 'bg-yellow-50 border-yellow-200'}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Clock className={`h-5 w-5 ${isOverdue ? 'text-red-600' : 'text-yellow-600'}`} />
              <div>
                <p className={`font-medium ${isOverdue ? 'text-red-800' : 'text-yellow-800'}`}>
                  {t('invoices.outstandingBalance')}: {formatCurrency(balance)}
                </p>
                <p className={`text-sm ${isOverdue ? 'text-red-600' : 'text-yellow-600'}`}>
                  {isOverdue ? `${t('invoices.paymentIs')} ${daysPastDue} ${t('invoices.daysOverdue')}` : t('invoices.paymentPending')}
                </p>
              </div>
            </div>
            <Button
              size="sm"
              variant={isOverdue ? "destructive" : "default"}
              onClick={() => setShowPaymentDialog(true)}
            >
              <CreditCard className="mr-2 h-4 w-4" />
              {t('invoices.recordPayment')}
            </Button>
          </div>
        </div>
      )}

      {/* Customer Information */}
      <Card>
        <CardHeader>
          <CardTitle>{t('invoices.customerInformation')}</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium mb-2">{t('invoices.contactDetails')}</h4>
            <div className="space-y-1 text-sm">
              <p><strong>{t('invoices.nameLabel')}:</strong> {invoice.customer.name}</p>
              {invoice.customer.company && <p><strong>{t('invoices.companyLabel')}:</strong> {invoice.customer.company}</p>}
              <p><strong>{t('invoices.phoneLabel')}:</strong> {invoice.customer.phone}</p>
              {invoice.customer.email && <p><strong>{t('invoices.emailLabel')}:</strong> {invoice.customer.email}</p>}
            </div>
          </div>
          <div>
            <h4 className="font-medium mb-2">{t('invoices.billingAddress')}</h4>
            <div className="text-sm">
              <p>{invoice.customer.address || t('invoices.noAddressProvided')}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoice Items */}
      <Card>
        <CardHeader>
          <CardTitle>{t('invoices.invoiceItems')}</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('invoices.itemDescription')}</TableHead>
                <TableHead className="text-center">{t('invoices.quantity')}</TableHead>
                <TableHead className="text-right">{t('invoices.unitPrice')}</TableHead>
                <TableHead className="text-right">{t('invoices.total')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoice.items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">{item.description}</p>
                      {item.product && (
                        <p className="text-sm text-muted-foreground">
                          {t('invoices.unitLabel')}: {item.product.unit}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">{item.quantity}</TableCell>
                  <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
                  <TableCell className="text-right font-medium">{formatCurrency(item.total)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Totals */}
          <div className="mt-6 flex justify-end">
            <div className="w-80 space-y-2">
              <div className="flex justify-between">
                <span>{t('invoices.subtotal')}:</span>
                <span>{formatCurrency(invoice.subtotal)}</span>
              </div>
              {invoice.discount > 0 && (
                <div className="flex justify-between">
                  <span>{t('invoices.discount')}:</span>
                  <span>-{formatCurrency(invoice.discount)}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span>{t('invoices.tax')} (5%):</span>
                <span>{formatCurrency(invoice.taxAmount)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-bold text-lg">
                <span>{t('invoices.total')}:</span>
                <span>{formatCurrency(invoice.total)}</span>
              </div>
              {amountPaid > 0 && (
                <>
                  <div className="flex justify-between text-green-600">
                    <span>{t('invoices.amountPaid')}:</span>
                    <span>{formatCurrency(amountPaid)}</span>
                  </div>
                  <div className="flex justify-between font-medium text-orange-600">
                    <span>{t('invoices.balanceDue')}:</span>
                    <span>{formatCurrency(balance)}</span>
                  </div>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment History */}
      {invoice.payments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{t('invoices.paymentHistory')}</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('invoices.dateLabel')}</TableHead>
                  <TableHead>{t('invoices.amount')}</TableHead>
                  <TableHead>{t('invoices.methodLabel')}</TableHead>
                  <TableHead>{t('invoices.referenceLabel')}</TableHead>
                  <TableHead>{t('invoices.notes')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoice.payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                    <TableCell className="text-green-600 font-medium">
                      {formatCurrency(payment.amount)}
                    </TableCell>
                    <TableCell>{payment.method}</TableCell>
                    <TableCell className="font-mono text-sm">{payment.reference || '-'}</TableCell>
                    <TableCell className="text-sm">{payment.notes || '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Notes */}
      {invoice.notes && (
        <Card>
          <CardHeader>
            <CardTitle>{t('invoices.notes')}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{invoice.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Payment Dialog */}
      {invoice && (
        <PaymentDialog
          open={showPaymentDialog}
          onOpenChange={setShowPaymentDialog}
          invoice={{
            id: invoice.id,
            number: invoice.number,
            date: invoice.date,
            dueDate: invoice.dueDate,
            customer: invoice.customer.name,
            status: invoice.status,
            subtotal: invoice.subtotal,
            taxAmount: invoice.taxAmount,
            total: invoice.total,
            amountPaid: amountPaid,
          }}
          onPaymentSuccess={() => {
            // Refresh invoice data
            fetchInvoice(invoice.id)
          }}
        />
      )}

    </div>
  )
}
