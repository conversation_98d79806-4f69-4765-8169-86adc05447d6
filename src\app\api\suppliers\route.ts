import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const where = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
            { company: { contains: search, mode: 'insensitive' as const } },
            { contactPerson: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : {}

    const [suppliers, total] = await Promise.all([
      prisma.supplier.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          purchases: {
            select: {
              id: true,
              total: true,
              status: true,
            },
          },
        },
      }),
      prisma.supplier.count({ where }),
    ])

    // Calculate totals for each supplier
    const suppliersWithStats = suppliers.map(supplier => {
      const totalPurchases = supplier.purchases.reduce((sum, purchase) => sum + Number(purchase.total), 0)
      const outstandingPayments = supplier.purchases
        .filter(purchase => purchase.status === 'PENDING' || purchase.status === 'ORDERED')
        .reduce((sum, purchase) => sum + Number(purchase.total), 0)

      return {
        ...supplier,
        totalPurchases,
        outstandingPayments,
        purchaseOrderCount: supplier.purchases.length,
        purchases: undefined, // Remove the purchases array from response
      }
    })

    return NextResponse.json({
      suppliers: suppliersWithStats,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching suppliers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, email, phone, mobile, address, company, taxNumber, contactPerson, notes } = body

    if (!name || !mobile) {
      return NextResponse.json(
        { error: 'Name and mobile are required' },
        { status: 400 }
      )
    }

    const supplier = await prisma.supplier.create({
      data: {
        name,
        email,
        phone,
        mobile,
        address,
        company,
        taxNumber,
        contactPerson,
        notes,
      },
    })

    return NextResponse.json(supplier, { status: 201 })
  } catch (error) {
    console.error('Error creating supplier:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
