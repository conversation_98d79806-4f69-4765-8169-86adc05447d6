import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const invoice = await prisma.invoice.findUnique({
      where: { id },
      include: {
        customer: true,
        user: true,
        task: {
          include: {
            project: true
          }
        },
        items: {
          include: {
            product: true,
          },
        },
        payments: true,
      },
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Calculate payments and balance
    const amountPaid = invoice.payments.reduce((sum, payment) => sum + Number(payment.amount), 0)
    const balance = Number(invoice.total) - amountPaid

    // Update status based on payment
    let status = invoice.status
    if (amountPaid >= Number(invoice.total)) {
      status = 'PAID'
    } else if (amountPaid > 0) {
      status = 'PARTIAL'
    } else if (invoice.dueDate && new Date() > invoice.dueDate) {
      status = 'OVERDUE'
    } else {
      status = 'UNPAID'
    }

    const responseInvoice = {
      id: invoice.id,
      number: invoice.number,
      date: invoice.date.toISOString().split('T')[0],
      dueDate: invoice.dueDate?.toISOString().split('T')[0],
      status,
      subtotal: Number(invoice.subtotal),
      taxAmount: Number(invoice.taxAmount),
      discount: Number(invoice.discount),
      total: Number(invoice.total),
      notes: invoice.notes,
      customer: {
        id: invoice.customer?.id || '',
        name: invoice.customer?.name || 'Unknown Customer',
        email: invoice.customer?.email,
        phone: invoice.customer?.mobile || invoice.customer?.phone || '',
        company: invoice.customer?.company,
        address: invoice.customer?.address,
      },
      items: invoice.items.map(item => ({
        id: item.id,
        description: item.description,
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        total: Number(item.total),
        product: item.product ? {
          name: item.product.name,
          unit: item.product.unit,
        } : undefined,
      })),
      payments: invoice.payments.map(payment => ({
        id: payment.id,
        amount: Number(payment.amount),
        method: payment.method,
        date: payment.date.toISOString().split('T')[0],
        reference: payment.reference,
        notes: payment.notes,
      })),
      amountPaid,
      balance,
    }

    return NextResponse.json(responseInvoice)
  } catch (error) {
    console.error('Error fetching invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const {
      customerId,
      taskId,
      dueDate,
      items,
      taxAmount,
      discount,
      notes,
      status,
    } = body

    // Check if invoice exists
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id },
      include: { items: true },
    })

    if (!existingInvoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Calculate totals
    const subtotal = items.reduce((sum: number, item: { quantity: string; unitPrice: string }) =>
      sum + (parseFloat(item.quantity) * parseFloat(item.unitPrice)), 0
    )

    const calculatedTaxAmount = taxAmount || (subtotal * 0.05) // Default 5% tax
    const calculatedDiscount = discount || 0
    const total = subtotal + calculatedTaxAmount - calculatedDiscount

    // Update invoice using transaction
    const updatedInvoice = await prisma.$transaction(async (tx) => {
      // Delete existing items
      await tx.invoiceItem.deleteMany({
        where: { invoiceId: id },
      })

      // Update invoice
      const invoice = await tx.invoice.update({
        where: { id },
        data: {
          customerId,
          taskId: taskId || null,
          dueDate: dueDate ? new Date(dueDate) : null,
          subtotal,
          taxAmount: calculatedTaxAmount,
          discount: calculatedDiscount,
          total,
          notes,
          status: status || existingInvoice.status,
        },
        include: {
          customer: true,
          user: true,
          task: true,
          items: {
            include: {
              product: true,
            },
          },
          payments: true,
        },
      })

      // Create new items
      await tx.invoiceItem.createMany({
        data: items.map((item: { description: string; quantity: string; unitPrice: string; productId?: string }) => ({
          invoiceId: id,
          description: item.description,
          quantity: parseFloat(item.quantity),
          unitPrice: parseFloat(item.unitPrice),
          total: parseFloat(item.quantity) * parseFloat(item.unitPrice),
          productId: item.productId || null,
        })),
      })

      return invoice
    })

    return NextResponse.json(updatedInvoice)
  } catch (error) {
    console.error('Error updating invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if invoice exists
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id },
      include: { payments: true },
    })

    if (!existingInvoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Check if invoice has payments
    if (existingInvoice.payments.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete invoice with payments' },
        { status: 400 }
      )
    }

    // Delete invoice (items will be deleted automatically due to cascade)
    await prisma.invoice.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Invoice deleted successfully' })
  } catch (error) {
    console.error('Error deleting invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 