export const auth = {
  // Login page
  login: {
    title: "Sign In",
    subtitle: "Enter your email and password to access your account",
    welcomeBack: "Welcome Back",
    signIn: "Sign In",
    signInToAccount: "Sign in to your account",
    
    // Form fields
    email: "Email",
    emailPlaceholder: "Enter your email",
    password: "Password",
    passwordPlaceholder: "Enter your password",
    
    // Actions
    signInButton: "Sign In",
    signingIn: "Signing in...",
    forgotPassword: "Forgot password?",
    rememberMe: "Remember me",
    
    // Messages
    invalidCredentials: "Invalid email or password",
    loginError: "An error occurred. Please try again.",
    loginSuccess: "Successfully signed in",
    
    // Links
    noAccount: "Don't have an account?",
    createAccount: "Create account",
    backToHome: "Back to home",
  },
  
  // Registration
  register: {
    title: "Create New Account",
    subtitle: "Create your account to get started",
    createAccount: "Create Account",
    
    // Form fields
    firstName: "First Name",
    lastName: "Last Name",
    confirmPassword: "Confirm Password",
    agreeToTerms: "I agree to the terms and conditions",
    
    // Messages
    passwordMismatch: "Passwords do not match",
    registrationSuccess: "Account created successfully",
    registrationError: "Failed to create account",
    
    // Links
    haveAccount: "Already have an account?",
    signInHere: "Sign in here",
  },
  
  // Password reset
  resetPassword: {
    title: "Reset Password",
    subtitle: "Enter your email to reset your password",
    sendResetLink: "Send Reset Link",
    resetLinkSent: "Reset link sent to your email",
    backToLogin: "Back to login",
    
    // New password
    newPassword: "New Password",
    confirmNewPassword: "Confirm New Password",
    updatePassword: "Update Password",
    passwordUpdated: "Password updated successfully",
  },
  
  // Logout
  logout: {
    title: "Sign Out",
    message: "Are you sure you want to sign out?",
    confirm: "Sign Out",
    cancel: "Cancel",
    loggingOut: "Signing out...",
    loggedOut: "Successfully signed out",
  },
  
  // Session
  session: {
    expired: "Session expired",
    expiringSoon: "Your session will expire soon",
    extend: "Extend Session",
    sessionExtended: "Session extended",
  },
  
  // Security
  security: {
    twoFactorAuth: "Two-Factor Authentication",
    enableTwoFactor: "Enable Two-Factor Auth",
    disableTwoFactor: "Disable Two-Factor Auth",
    verificationCode: "Verification Code",
    enterCode: "Enter verification code",
    invalidCode: "Invalid verification code",
    codeExpired: "Verification code expired",
    resendCode: "Resend code",
  }
}; 