const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting simple database seeding...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10)

  const adminUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Admin User',
      role: 'ADMIN',
      phone: '+968 9123 4567',
      isActive: true,
      joinDate: new Date('2023-01-01'),
      tasksCompleted: 50,
      tasksInProgress: 2,
      completionRate: 96.0,
    },
  })

  // Create employee user
  const employeeUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: '<PERSON>',
      role: 'EMPLOYEE',
      phone: '+968 9876 5432',
      isActive: true,
      joinDate: new Date('2023-06-15'),
      tasksCompleted: 45,
      tasksInProgress: 3,
      completionRate: 95.0,
    },
  })

  // Create manager user
  const managerUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Jane Smith',
      role: 'MANAGER',
      phone: '+968 9555 1234',
      isActive: true,
      joinDate: new Date('2023-03-10'),
      tasksCompleted: 32,
      tasksInProgress: 2,
      completionRate: 88.0,
    },
  })

  // Create sales data
  await prisma.salesData.create({
    data: {
      userId: employeeUser.id,
      totalSales: 125600.000,
      monthlyTarget: 100000.000,
      invoicesCount: 28,
      avgOrderValue: 4485.000,
      conversionRate: 78.0,
      commission: 6280.000,
      rank: 1,
      growth: 15.8,
    },
  })

  await prisma.salesData.create({
    data: {
      userId: managerUser.id,
      totalSales: 98400.000,
      monthlyTarget: 80000.000,
      invoicesCount: 22,
      avgOrderValue: 4472.000,
      conversionRate: 82.0,
      commission: 4920.000,
      rank: 2,
      growth: 23.1,
    },
  })

  // Create categories
  const electronicsCategory = await prisma.category.create({
    data: {
      name: 'Electronics',
      nameAr: 'الإلكترونيات',
      description: 'Electronic devices and accessories',
    },
  })

  const furnitureCategory = await prisma.category.create({
    data: {
      name: 'Furniture',
      nameAr: 'الأثاث',
      description: 'Office and home furniture',
    },
  })

  const accessoriesCategory = await prisma.category.create({
    data: {
      name: 'Accessories',
      nameAr: 'الإكسسوارات',
      description: 'Various accessories and supplies',
    },
  })

  // Create units
  const pieceUnit = await prisma.unit.create({
    data: {
      name: 'Piece',
      nameAr: 'قطعة',
      symbol: 'pcs',
    },
  })

  const setUnit = await prisma.unit.create({
    data: {
      name: 'Set',
      nameAr: 'مجموعة',
      symbol: 'set',
    },
  })

  // Create suppliers
  const supplier1 = await prisma.supplier.create({
    data: {
      name: 'Tech Supplier Co.',
      nameAr: 'شركة المورد التقني',
      email: '<EMAIL>',
      mobile: '+968 9123 4567',
      phone: '+968 2456 7890',
      address: 'Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      company: 'Tech Supplier Co.',
      contactPerson: 'Ahmed Al-Rashid',
    },
  })

  const supplier2 = await prisma.supplier.create({
    data: {
      name: 'Furniture Plus',
      nameAr: 'فرنتشر بلس',
      email: '<EMAIL>',
      mobile: '+968 9876 5432',
      phone: '+968 2567 8901',
      address: 'Sohar, Oman',
      city: 'Sohar',
      country: 'Oman',
      company: 'Furniture Plus LLC',
      contactPerson: 'Fatima Al-Zahra',
    },
  })

  // Create products
  const product1 = await prisma.product.create({
    data: {
      name: 'Wireless Headphones',
      nameAr: 'سماعات لاسلكية',
      description: 'High-quality wireless headphones',
      sku: 'WH-001',
      barcode: '1234567890123',
      price: 45.500,
      costPrice: 30.000,
      currentStock: 15,
      minStock: 20,
      maxStock: 100,
      location: 'A1-B2',
      categoryId: electronicsCategory.id,
      unitId: pieceUnit.id,
      supplierId: supplier1.id,
      lastRestocked: new Date('2024-01-15'),
    },
  })

  const product2 = await prisma.product.create({
    data: {
      name: 'Office Chair',
      nameAr: 'كرسي مكتب',
      description: 'Ergonomic office chair',
      sku: 'OC-002',
      barcode: '1234567890124',
      price: 125.000,
      costPrice: 85.000,
      currentStock: 8,
      minStock: 10,
      maxStock: 50,
      location: 'B2-C1',
      categoryId: furnitureCategory.id,
      unitId: pieceUnit.id,
      supplierId: supplier2.id,
      lastRestocked: new Date('2024-01-10'),
    },
  })

  const product3 = await prisma.product.create({
    data: {
      name: 'Laptop Stand',
      nameAr: 'حامل لابتوب',
      description: 'Adjustable laptop stand',
      sku: 'LS-003',
      barcode: '1234567890125',
      price: 22.750,
      costPrice: 15.000,
      currentStock: 25,
      minStock: 15,
      maxStock: 80,
      location: 'A3-B1',
      categoryId: accessoriesCategory.id,
      unitId: pieceUnit.id,
      supplierId: supplier1.id,
      lastRestocked: new Date('2024-01-12'),
    },
  })

  // Create stock movements
  await prisma.stockMovement.create({
    data: {
      productId: product1.id,
      type: 'IN',
      quantity: 50,
      reason: 'Purchase Order #PO-001',
      date: new Date('2024-01-15'),
    },
  })

  await prisma.stockMovement.create({
    data: {
      productId: product1.id,
      type: 'OUT',
      quantity: 35,
      reason: 'Sales',
      date: new Date('2024-01-20'),
    },
  })

  await prisma.stockMovement.create({
    data: {
      productId: product2.id,
      type: 'IN',
      quantity: 20,
      reason: 'Purchase Order #PO-002',
      date: new Date('2024-01-10'),
    },
  })

  await prisma.stockMovement.create({
    data: {
      productId: product2.id,
      type: 'OUT',
      quantity: 12,
      reason: 'Sales',
      date: new Date('2024-01-18'),
    },
  })

  // Create customers
  const customer1 = await prisma.customer.create({
    data: {
      name: 'Ahmed Al-Balushi',
      nameAr: 'أحمد البلوشي',
      email: '<EMAIL>',
      mobile: '+968 9111 2222',
      phone: '+968 2444 5555',
      address: 'Ruwi, Muscat',
      city: 'Muscat',
      company: 'Al-Balushi Trading',
      taxNumber: 'TAX123456',
    },
  })

  const customer2 = await prisma.customer.create({
    data: {
      name: 'Fatima Al-Zahra',
      nameAr: 'فاطمة الزهراء',
      email: '<EMAIL>',
      mobile: '+968 9333 4444',
      address: 'Sohar Industrial Area',
      city: 'Sohar',
      company: 'Zahra Enterprises',
    },
  })

  const customer3 = await prisma.customer.create({
    data: {
      name: 'Mohammed Al-Hinai',
      nameAr: 'محمد الهنائي',
      email: '<EMAIL>',
      mobile: '+968 9777 8888',
      address: 'Nizwa, Oman',
      city: 'Nizwa',
      company: 'Al-Hinai Group',
    },
  })

  // Create expense types
  const officeSuppliesType = await prisma.expenseType.create({
    data: {
      name: 'Office Supplies',
      nameAr: 'المستلزمات المكتبية',
      description: 'Stationery, paper, pens, and other office materials',
      isActive: true,
    },
  })

  const travelType = await prisma.expenseType.create({
    data: {
      name: 'Travel & Transportation',
      nameAr: 'السفر والمواصلات',
      description: 'Business travel, fuel, taxi, and transportation costs',
      isActive: true,
    },
  })

  const utilitiesType = await prisma.expenseType.create({
    data: {
      name: 'Utilities',
      nameAr: 'المرافق',
      description: 'Electricity, water, internet, and phone bills',
      isActive: true,
    },
  })

  const marketingType = await prisma.expenseType.create({
    data: {
      name: 'Marketing & Advertising',
      nameAr: 'التسويق والإعلان',
      description: 'Promotional materials, online ads, and marketing campaigns',
      isActive: true,
    },
  })

  // Create expenses
  await prisma.expense.create({
    data: {
      number: 'EXP-2024-0001',
      description: 'Office supplies - Paper and stationery',
      amount: 125.500,
      expenseTypeId: officeSuppliesType.id,
      paymentMethod: 'CARD',
      status: 'APPROVED',
      notes: 'Monthly office supplies purchase',
      createdById: employeeUser.id,
      date: new Date('2024-01-15'),
    },
  })

  await prisma.expense.create({
    data: {
      number: 'EXP-2024-0002',
      description: 'Fuel for company vehicle',
      amount: 85.000,
      expenseTypeId: travelType.id,
      paymentMethod: 'CASH',
      status: 'PENDING',
      createdById: managerUser.id,
      date: new Date('2024-01-16'),
    },
  })

  await prisma.expense.create({
    data: {
      number: 'EXP-2024-0003',
      description: 'Internet bill - January 2024',
      amount: 45.000,
      expenseTypeId: utilitiesType.id,
      paymentMethod: 'BANK_TRANSFER',
      status: 'PAID',
      createdById: adminUser.id,
      date: new Date('2024-01-17'),
    },
  })

  await prisma.expense.create({
    data: {
      number: 'EXP-2024-0004',
      description: 'Marketing materials for trade show',
      amount: 350.000,
      expenseTypeId: marketingType.id,
      paymentMethod: 'CARD',
      status: 'REJECTED',
      notes: 'Exceeded budget limit',
      createdById: employeeUser.id,
      date: new Date('2024-01-18'),
    },
  })

  // Create settings
  await prisma.setting.create({
    data: {
      key: 'company_name',
      value: 'Office Sales & Services Management',
      description: 'Company name in English',
    },
  })

  await prisma.setting.create({
    data: {
      key: 'company_name_ar',
      value: 'نظام إدارة المبيعات والخدمات المكتبية',
      description: 'Company name in Arabic',
    },
  })

  await prisma.setting.create({
    data: {
      key: 'tax_rate',
      value: '5',
      description: 'Default tax rate percentage',
    },
  })

  await prisma.setting.create({
    data: {
      key: 'currency',
      value: 'OMR',
      description: 'Default currency',
    },
  })

  console.log('✅ Simple database seeding completed successfully!')
  console.log('📊 Created:')
  console.log('   - 3 Users with Sales Data')
  console.log('   - 3 Categories')
  console.log('   - 2 Units')
  console.log('   - 2 Suppliers')
  console.log('   - 3 Products')
  console.log('   - 4 Stock Movements')
  console.log('   - 3 Customers')
  console.log('   - 4 Expense Types')
  console.log('   - 4 Expenses')
  console.log('   - 4 Settings')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
