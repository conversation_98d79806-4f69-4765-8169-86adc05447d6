import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const supplierId = searchParams.get('supplierId')
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const where: Record<string, unknown> = {}

    if (supplierId) {
      where.supplierId = supplierId
    }

    if (status) {
      where.status = status
    }

    const [payouts, total] = await Promise.all([
      prisma.supplierPayout.findMany({
        where,
        skip,
        take: limit,
        include: {
          supplier: true,
          purchase: true,
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.supplierPayout.count({ where }),
    ])

    return NextResponse.json({
      payouts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching supplier payouts:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      supplierId,
      amount,
      method,
      reference,
      description,
      date,
      dueDate,
      purchaseId,
      notes,
    } = body

    if (!supplierId || !amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Supplier ID and amount are required' },
        { status: 400 }
      )
    }

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
    })

    if (!supplier) {
      return NextResponse.json(
        { error: 'Supplier not found' },
        { status: 404 }
      )
    }

    // Generate payout number
    const lastPayout = await prisma.supplierPayout.findFirst({
      orderBy: { createdAt: 'desc' },
    })

    const nextNumber = lastPayout
      ? `PAY-${String(parseInt(lastPayout.number.split('-')[1]) + 1).padStart(4, '0')}`
      : 'PAY-0001'

    // Create the payout
    const payout = await prisma.supplierPayout.create({
      data: {
        number: nextNumber,
        amount: parseFloat(amount),
        method: method || 'BANK_TRANSFER',
        reference: reference || null,
        description: description || null,
        date: date ? new Date(date) : new Date(),
        dueDate: dueDate ? new Date(dueDate) : null,
        notes: notes || null,
        supplierId,
        purchaseId: purchaseId || null,
        createdById: session.user?.id || '',
      },
      include: {
        supplier: true,
        purchase: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return NextResponse.json(payout, { status: 201 })
  } catch (error) {
    console.error('Error creating supplier payout:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
