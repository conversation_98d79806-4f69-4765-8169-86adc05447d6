import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { Decimal } from '@prisma/client/runtime/library'

// Helper function to calculate actual cost from expenses
async function calculateActualCost(projectId: string): Promise<Decimal> {
  const expenses = await prisma.expense.findMany({
    where: { 
      projectId,
      status: { in: ['APPROVED', 'PAID'] } // Only count approved or paid expenses
    },
    select: { amount: true }
  })
  
  const total = expenses.reduce((sum, expense) => sum + Number(expense.amount), 0)
  return new Decimal(total)
}

// Helper function to calculate project progress from tasks
async function calculateProjectProgress(projectId: string): Promise<number> {
  const tasks = await prisma.task.findMany({
    where: { projectId },
    select: { status: true }
  })
  
  if (tasks.length === 0) return 0
  
  const completedTasks = tasks.filter(task => task.status === 'COMPLETED').length
  return Math.round((completedTasks / tasks.length) * 100)
}

// GET /api/projects - Get all projects
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const projects = await prisma.project.findMany({
      include: {
        client: {
          select: {
            id: true,
            name: true,
            company: true,
            mobile: true,
            phone: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        tasks: {
          select: {
            id: true,
            title: true,
            status: true,
            priority: true,
            createdAt: true,
          },
        },
        teamMembers: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            joinedAt: 'asc',
          },
        },
        _count: {
          select: {
            tasks: true,
            teamMembers: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    // Update calculations for each project
    const updatedProjects = await Promise.all(
      projects.map(async (project) => {
        const actualCost = await calculateActualCost(project.id)
        const progress = await calculateProjectProgress(project.id)
        
        // Check if values need updating
        const needsUpdate = 
          Number(project.actualCost || 0) !== actualCost.toNumber() || 
          project.progress !== progress ||
          (progress === 100 && project.status === 'IN_PROGRESS')
        
        if (needsUpdate) {
          const updateData: any = {
            actualCost,
            progress,
            updatedAt: new Date()
          }
          
          // Auto-complete project when all tasks are done
          if (progress === 100 && project.status === 'IN_PROGRESS') {
            updateData.status = 'COMPLETED'
          }
          
          const updatedProject = await prisma.project.update({
            where: { id: project.id },
            data: updateData,
            include: {
              client: {
                select: {
                  id: true,
                  name: true,
                  company: true,
                  mobile: true,
                  phone: true,
                },
              },
              manager: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              createdBy: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              tasks: {
                select: {
                  id: true,
                  title: true,
                  status: true,
                  priority: true,
                  createdAt: true,
                },
              },
              teamMembers: {
                include: {
                  user: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                      role: true,
                    },
                  },
                },
                orderBy: {
                  joinedAt: 'asc',
                },
              },
              _count: {
                select: {
                  tasks: true,
                  teamMembers: true,
                },
              },
            },
          })
          
          return updatedProject
        }
        
        return project
      })
    )

    return NextResponse.json(updatedProjects)
  } catch (error) {
    console.error('Error fetching projects:', error)
    // Return empty array if project model is not available
    if (error.message?.includes('project')) {
      return NextResponse.json([])
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/projects - Create new project
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if project model is available
    if (!prisma.project) {
      return NextResponse.json(
        { error: 'Project model not available. Please restart the server.' },
        { status: 503 }
      )
    }

    const body = await request.json()
    const {
      name,
      nameAr,
      description,
      status = 'PLANNING',
      priority = 'MEDIUM',
      startDate,
      endDate,
      budget,
      clientId,
      managerId,
      notes,
    } = body

    if (!name || !startDate) {
      return NextResponse.json(
        { error: 'Name and start date are required' },
        { status: 400 }
      )
    }

    // Generate project code
    const lastProject = await prisma.project.findFirst({
      orderBy: { createdAt: 'desc' },
    })

    const nextNumber = lastProject
      ? `PRJ-${String(parseInt(lastProject.code.split('-')[1]) + 1).padStart(4, '0')}`
      : 'PRJ-0001'

    const project = await prisma.project.create({
      data: {
        code: nextNumber,
        name,
        nameAr,
        description,
        status,
        priority,
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : null,
        budget: budget ? parseFloat(budget) : null,
        clientId: clientId || null,
        managerId: managerId || null,
        notes,
        progress: 0,
        createdById: session.user.id,
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            company: true,
          },
        },
        manager: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return NextResponse.json(project, { status: 201 })
  } catch (error) {
    console.error('Error creating project:', error)
    // Return specific error if project model is not available
    if (error.message?.includes('project') || error.message?.includes('findFirst')) {
      return NextResponse.json(
        { error: 'Project model not available. Please restart the server.' },
        { status: 503 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    )
  }
}
