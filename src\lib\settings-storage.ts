/**
 * Settings API utility - replaces localStorage with database API calls
 */

export interface Setting {
  id: string
  key: string
  value: string
  description?: string
  createdAt: string
  updatedAt: string
}

export interface SettingsResponse {
  settings: Setting[]
  settingsObject: Record<string, {
    id: string
    value: string
    description?: string
    createdAt: string
    updatedAt: string
  }>
}

// Get all settings from API
export const getSettings = async (): Promise<SettingsResponse> => {
  try {
    const response = await fetch('/api/settings')
    if (!response.ok) {
      throw new Error('Failed to fetch settings')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading settings:', error)
    return {
      settings: [],
      settingsObject: {},
    }
  }
}

// Get setting value by key
export const getSettingValue = async (key: string): Promise<string | null> => {
  try {
    const { settingsObject } = await getSettings()
    return settingsObject[key]?.value || null
  } catch (error) {
    console.error('Error loading setting value:', error)
    return null
  }
}

// Add new setting via API
export const addSetting = async (setting: {
  key: string
  value: string
  description?: string
}): Promise<Setting | null> => {
  try {
    const response = await fetch('/api/settings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(setting),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create setting')
    }

    return await response.json()
  } catch (error) {
    console.error('Error creating setting:', error)
    return null
  }
}

// Update existing setting via API
export const updateSetting = async (id: string, updates: {
  key?: string
  value?: string
  description?: string
}): Promise<Setting | null> => {
  try {
    const response = await fetch('/api/settings', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ id, ...updates }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update setting')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating setting:', error)
    return null
  }
}

// Update setting by key (convenience method)
export const updateSettingByKey = async (key: string, value: string): Promise<Setting | null> => {
  try {
    const { settingsObject } = await getSettings()
    const setting = settingsObject[key]

    if (!setting) {
      // Create new setting if it doesn't exist
      return await addSetting({ key, value })
    }

    // Update existing setting
    return await updateSetting(setting.id, { value })
  } catch (error) {
    console.error('Error updating setting by key:', error)
    return null
  }
}

// Delete setting via API
export const deleteSetting = async (id: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/settings/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete setting')
    }

    return true
  } catch (error) {
    console.error('Error deleting setting:', error)
    return false
  }
}

// Get setting by ID via API
export const getSettingById = async (id: string): Promise<Setting | null> => {
  try {
    const response = await fetch(`/api/settings/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch setting')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading setting:', error)
    return null
  }
}

// Bulk update settings
export const updateMultipleSettings = async (updates: Array<{
  key: string
  value: string
}>): Promise<boolean> => {
  try {
    const { settingsObject } = await getSettings()

    const updatePromises = updates.map(async ({ key, value }) => {
      const setting = settingsObject[key]
      if (setting) {
        // Update existing setting (allow empty values)
        return updateSetting(setting.id, { value })
      } else {
        // Create new setting only if value is not empty
        if (value) {
          return addSetting({ key, value })
        }
        return null
      }
    })

    await Promise.all(updatePromises)
    return true
  } catch (error) {
    console.error('Error updating multiple settings:', error)
    return false
  }
}
