"use client"

import { useState, useEffect } from "react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Clock, X, Moon } from "lucide-react"
import { getCurrentMuscatTime, isNearPrayerTime, getPrayerTimesText } from "@/lib/localization"

interface PrayerTime {
  name: string
  nameAr: string
  time: string
  icon: string
}

// Prayer times for Muscat, Oman - Based on Ministry of Religious Affairs calculations
const getTodaysPrayerTimes = (): PrayerTime[] => {
  const today = getCurrentMuscatTime()
  const month = today.getMonth() + 1
  const day = today.getDate()

  // These times are calculated based on Oman's official prayer time calculations
  // Ministry of Religious Affairs (وزارة الأوقاف والشؤون الدينية) standards
  // Times vary by season - this is a seasonal approximation for Muscat

  // Winter times (November - February)
  if (month >= 11 || month <= 2) {
    return [
      { name: "<PERSON><PERSON><PERSON>", nameAr: "الفجر", time: "05:45", icon: "🌅" },
      { name: "<PERSON><PERSON><PERSON>", nameAr: "الظهر", time: "12:00", icon: "☀️" },
      { name: "Asr", nameAr: "العصر", time: "15:15", icon: "🌤️" },
      { name: "Maghrib", nameAr: "المغرب", time: "17:45", icon: "🌆" },
      { name: "Isha", nameAr: "العشاء", time: "19:15", icon: "🌙" },
    ]
  }

  // Summer times (May - August)
  if (month >= 5 && month <= 8) {
    return [
      { name: "Fajr", nameAr: "الفجر", time: "04:45", icon: "🌅" },
      { name: "Dhuhr", nameAr: "الظهر", time: "12:15", icon: "☀️" },
      { name: "Asr", nameAr: "العصر", time: "15:45", icon: "🌤️" },
      { name: "Maghrib", nameAr: "المغرب", time: "19:15", icon: "🌆" },
      { name: "Isha", nameAr: "العشاء", time: "20:45", icon: "🌙" },
    ]
  }

  // Spring/Fall times (March-April, September-October)
  return [
    { name: "Fajr", nameAr: "الفجر", time: "05:15", icon: "🌅" },
    { name: "Dhuhr", nameAr: "الظهر", time: "12:10", icon: "☀️" },
    { name: "Asr", nameAr: "العصر", time: "15:30", icon: "🌤️" },
    { name: "Maghrib", nameAr: "المغرب", time: "18:30", icon: "🌆" },
    { name: "Isha", nameAr: "العشاء", time: "20:00", icon: "🌙" },
  ]
}

const getNextPrayer = (prayerTimes: PrayerTime[]): PrayerTime | null => {
  const now = getCurrentMuscatTime()
  const currentTime = now.getHours() * 60 + now.getMinutes()

  for (const prayer of prayerTimes) {
    const [hours, minutes] = prayer.time.split(':').map(Number)
    const prayerTime = hours * 60 + minutes

    if (prayerTime > currentTime) {
      return prayer
    }
  }

  // If no prayer is left today, return Fajr of next day
  return prayerTimes[0]
}

const getTimeUntilPrayer = (prayerTime: string): string => {
  const now = getCurrentMuscatTime()
  const [hours, minutes] = prayerTime.split(':').map(Number)

  const prayerDate = new Date(now)
  prayerDate.setHours(hours, minutes, 0, 0)

  // If prayer time has passed today, set it for tomorrow
  if (prayerDate <= now) {
    prayerDate.setDate(prayerDate.getDate() + 1)
  }

  const diff = prayerDate.getTime() - now.getTime()
  const diffHours = Math.floor(diff / (1000 * 60 * 60))
  const diffMinutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

  if (diffHours > 0) {
    return `${diffHours}h ${diffMinutes}m`
  }
  return `${diffMinutes}m`
}

export function PrayerTimesBanner() {
  const [isVisible, setIsVisible] = useState(false)
  const [prayerTimes, setPrayerTimes] = useState<PrayerTime[]>([])
  const [nextPrayer, setNextPrayer] = useState<PrayerTime | null>(null)
  const [timeUntilNext, setTimeUntilNext] = useState<string>("")
  const [currentTime, setCurrentTime] = useState<Date>(new Date())

  useEffect(() => {
    const times = getTodaysPrayerTimes()
    setPrayerTimes(times)

    const updatePrayerInfo = () => {
      const next = getNextPrayer(times)
      setNextPrayer(next)

      if (next) {
        setTimeUntilNext(getTimeUntilPrayer(next.time))
      }

      setCurrentTime(getCurrentMuscatTime())

      // Show banner if near prayer time or during business hours
      const nearPrayer = isNearPrayerTime()
      setIsVisible(nearPrayer)
    }

    updatePrayerInfo()

    // Update every minute
    const interval = setInterval(updatePrayerInfo, 60000)

    return () => clearInterval(interval)
  }, [])

  if (!isVisible || !nextPrayer) {
    return null
  }

  return (
    <Alert className="mb-4 border-green-200 bg-green-50">
      <Moon className="h-4 w-4 text-green-600" />
      <AlertDescription className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{nextPrayer.icon}</span>
            <div>
              <p className="font-medium text-green-800">
                Next Prayer: {nextPrayer.name} ({nextPrayer.nameAr}) at {nextPrayer.time}
              </p>
              <p className="text-sm text-green-600">
                Time remaining: {timeUntilNext} • Current time: {currentTime.toLocaleTimeString('en-OM', { timeZone: 'Asia/Muscat' })}
              </p>
            </div>
          </div>

          <div className="hidden md:flex items-center space-x-2">
            <Badge variant="outline" className="text-green-700 border-green-300">
              <Clock className="w-3 h-3 mr-1" />
              Muscat Time
            </Badge>
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsVisible(false)}
          className="text-green-600 hover:text-green-800"
        >
          <X className="h-4 w-4" />
        </Button>
      </AlertDescription>
    </Alert>
  )
}

export function PrayerTimesWidget() {
  const [prayerTimes, setPrayerTimes] = useState<PrayerTime[]>([])
  const [nextPrayer, setNextPrayer] = useState<PrayerTime | null>(null)

  useEffect(() => {
    const times = getTodaysPrayerTimes()
    setPrayerTimes(times)
    setNextPrayer(getNextPrayer(times))
  }, [])

  return (
    <div className="bg-white rounded-lg border p-4 shadow-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-gray-900 flex items-center">
          <Moon className="w-4 h-4 mr-2 text-green-600" />
          Prayer Times
        </h3>
        <Badge variant="outline" className="text-xs">
          Muscat
        </Badge>
      </div>

      <div className="space-y-2">
        {prayerTimes.map((prayer, index) => (
          <div
            key={index}
            className={`flex items-center justify-between py-1 px-2 rounded ${
              nextPrayer?.name === prayer.name ? 'bg-green-50 border border-green-200' : ''
            }`}
          >
            <div className="flex items-center space-x-2">
              <span className="text-sm">{prayer.icon}</span>
              <div>
                <span className="text-sm font-medium">{prayer.name}</span>
                <span className="text-xs text-gray-500 mr-2">({prayer.nameAr})</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-mono">{prayer.time}</span>
              {nextPrayer?.name === prayer.name && (
                <Badge variant="secondary" className="text-xs">Next</Badge>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-3 pt-3 border-t text-xs text-gray-500">
        <p>Based on Ministry of Religious Affairs, Sultanate of Oman</p>
        <p className="mt-1" dir="rtl">وزارة الأوقاف والشؤون الدينية - سلطنة عمان</p>
      </div>
    </div>
  )
}
