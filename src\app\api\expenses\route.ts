import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { Decimal } from '@prisma/client/runtime/library'

// Helper function to calculate and update project actual cost
async function updateProjectActualCost(projectId: string) {
  if (!projectId) return

  const expenses = await prisma.expense.findMany({
    where: { 
      projectId,
      status: { in: ['APPROVED', 'PAID'] } // Only count approved or paid expenses
    },
    select: { amount: true }
  })
  
  const total = expenses.reduce((sum, expense) => sum + Number(expense.amount), 0)
  const actualCost = new Decimal(total)
  
  await prisma.project.update({
    where: { id: projectId },
    data: { 
      actualCost,
      updatedAt: new Date()
    }
  })
}

// GET /api/expenses - Get all expenses
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const projectId = searchParams.get('projectId')
    const skip = (page - 1) * limit

    const whereClause: any = {}
    if (projectId) {
      whereClause.projectId = projectId
    }

    const [expenses, total] = await Promise.all([
      prisma.expense.findMany({
        where: whereClause,
        skip,
        take: limit,
        include: {
          expenseType: {
            select: {
              id: true,
              name: true,
              nameAr: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          project: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.expense.count({ where: whereClause }),
    ])

    return NextResponse.json({
      expenses,
      total,
      pages: Math.ceil(total / limit),
      currentPage: page,
    })
  } catch (error) {
    console.error('Error fetching expenses:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/expenses - Create new expense
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      description,
      amount,
      paymentMethod = 'CASH',
      status = 'PENDING',
      receipt,
      notes,
      expenseTypeId,
      projectId,
      createdById,
    } = body

    if (!description || !amount || !expenseTypeId) {
      return NextResponse.json(
        { error: 'Description, amount, and expense type are required' },
        { status: 400 }
      )
    }

    // Generate expense number
    const lastExpense = await prisma.expense.findFirst({
      orderBy: { createdAt: 'desc' },
    })

    const nextNumber = lastExpense
      ? `EXP-${String(parseInt(lastExpense.number.split('-')[1]) + 1).padStart(6, '0')}`
      : 'EXP-000001'

    const expense = await prisma.expense.create({
      data: {
        number: nextNumber,
        description,
        amount: parseFloat(amount),
        paymentMethod,
        status,
        receipt,
        notes,
        expenseTypeId,
        projectId: projectId || null,
        createdById: createdById || session.user.id,
      },
      include: {
        expenseType: {
          select: {
            id: true,
            name: true,
            nameAr: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        project: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    })

    // Update project actual cost if expense is linked to a project
    if (projectId && (status === 'APPROVED' || status === 'PAID')) {
      await updateProjectActualCost(projectId)
    }

    return NextResponse.json(expense, { status: 201 })
  } catch (error) {
    console.error('Error creating expense:', error)
    return NextResponse.json(
      { error: 'Failed to create expense' },
      { status: 500 }
    )
  }
}
