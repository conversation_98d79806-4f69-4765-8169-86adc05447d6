import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { whatsappService } from '@/lib/whatsapp'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const customerId = searchParams.get('customerId')
    const projectId = searchParams.get('projectId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const where: Record<string, unknown> = {}

    if (search) {
      where.OR = [
        { number: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (customerId) {
      where.customerId = customerId
    }

    if (projectId) {
      where.task = {
        projectId: projectId
      }
    }

    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where,
        skip,
        take: limit,
        include: {
          customer: true,
          user: true,
          task: true,
          items: {
            include: {
              product: true,
            },
          },
          payments: true,
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.invoice.count({ where }),
    ])

    return NextResponse.json({
      invoices,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching invoices:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const {
      customerId,
      taskId,
      projectId,
      dueDate,
      items,
      taxAmount,
      discount,
      notes,
    } = body

    if (!customerId || !items || items.length === 0) {
      return NextResponse.json(
        { error: 'Customer and items are required' },
        { status: 400 }
      )
    }

    // Generate invoice number
    const lastInvoice = await prisma.invoice.findFirst({
      orderBy: { createdAt: 'desc' },
    })

    const nextNumber = lastInvoice
      ? `INV-${String(parseInt(lastInvoice.number.split('-')[1]) + 1).padStart(3, '0')}`
      : 'INV-001'

    // Calculate totals
    const subtotal = items.reduce((sum: number, item: { quantity: string; unitPrice: string }) =>
      sum + (parseFloat(item.quantity) * parseFloat(item.unitPrice)), 0
    )

    const calculatedTaxAmount = taxAmount || (subtotal * 0.15) // Default 15% tax
    const calculatedDiscount = discount || 0
    const total = subtotal + calculatedTaxAmount - calculatedDiscount

    // If projectId is provided but no taskId, try to find or create a default task
    let finalTaskId = taskId
    if (projectId && !taskId) {
      // Find existing task for this project or create a generic one
      const existingTask = await prisma.task.findFirst({
        where: { 
          projectId: projectId,
          title: 'General Project Work'
        }
      })
      
      if (existingTask) {
        finalTaskId = existingTask.id
      } else {
        // Create a generic task for this project
        const newTask = await prisma.task.create({
          data: {
            title: 'General Project Work',
            description: 'Default task for project invoices',
            status: 'COMPLETED',
            priority: 'MEDIUM',
            projectId: projectId,
            customerId: customerId,
            createdById: session.user?.id || '',
            estimatedHours: 0,
          }
        })
        finalTaskId = newTask.id
      }
    }

    const invoice = await prisma.invoice.create({
      data: {
        number: nextNumber,
        customerId,
        userId: session.user?.id || '',
        taskId: finalTaskId || null,
        dueDate: dueDate ? new Date(dueDate) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        subtotal,
        taxAmount: calculatedTaxAmount,
        discount: calculatedDiscount,
        total,
        notes,
        items: {
          create: items.map((item: { description: string; quantity: string; unitPrice: string; productId?: string }) => ({
            description: item.description,
            quantity: parseFloat(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            total: parseFloat(item.quantity) * parseFloat(item.unitPrice),
            productId: item.productId || null,
          })),
        },
      },
      include: {
        customer: true,
        user: true,
        task: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    })

    // Update product stock for physical items
    for (const item of items) {
      if (item.productId) {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
        })

        if (product && product.type === 'PHYSICAL') {
          await prisma.product.update({
            where: { id: item.productId },
            data: {
              currentStock: {
                decrement: parseInt(item.quantity),
              },
            },
          })
        }
      }
    }

    // Send WhatsApp notification to customer
    if (invoice.customer?.mobile) {
      await whatsappService.sendInvoiceNotification(
        invoice.customer.mobile,
        invoice.number,
        total,
        'en'
      )
    }

    return NextResponse.json(invoice, { status: 201 })
  } catch (error) {
    console.error('Error creating invoice:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
