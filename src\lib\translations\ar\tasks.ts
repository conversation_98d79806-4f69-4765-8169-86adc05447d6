export const tasks = {
  // Page titles and descriptions
  title: "المهام",
  description: "إدارة وتتبع مهام العمل",
  addTask: "إضافة مهمة",
  createTask: "إنشاء مهمة",
  editTask: "تعديل المهمة",
  viewDetails: "عرض التفاصيل",
  
  // Task properties
  task: "المهمة",
  taskTitle: "عنوان المهمة",
  taskDescription: "وصف المهمة",
  customer: "العميل",
  assignedTo: "مُكلف إلى",
  priority: "الأولوية",
  status: "الحالة",
  estHours: "الساعات المقدرة",
  estimatedHours: "الساعات المقدرة",
  actualHours: "الساعات الفعلية",
  dueDate: "تاريخ الاستحقاق",
  startDate: "تاريخ البداية",
  completedDate: "تاريخ الإنجاز",
  
  // Task status
  new: "جديدة",
  inProgress: "قيد التنفيذ",
  completed: "مكتملة",
  cancelled: "ملغية",
  paused: "متوقفة",
  onHold: "معلقة",
  
  // Task priority
  low: "منخفضة",
  medium: "متوسطة",
  high: "عالية",
  urgent: "عاجلة",
  
  // Task actions
  startTask: "بدء المهمة",
  pauseTask: "إيقاف المهمة",
  resumeTask: "استئناف المهمة",
  completeTask: "إنجاز المهمة",
  cancelTask: "إلغاء المهمة",
  delete: "حذف",
  actions: "الإجراءات",
  
  // Search and filters
  searchTasks: "البحث في المهام...",
  searchPlaceholder: "البحث في المهام أو العملاء أو المكلفين...",
  allStatus: "جميع الحالات",
  allPriority: "جميع الأولويات",
  filterByStatus: "تصفية حسب الحالة",
  filterByPriority: "تصفية حسب الأولوية",
  filterByAssignee: "تصفية حسب المكلف",
  
  // Statistics
  totalTasks: "إجمالي المهام",
  newTasks: "المهام الجديدة",
  tasksInProgress: "المهام قيد التنفيذ",
  completedTasks: "المهام المكتملة",
  overdueTasks: "المهام المتأخرة",
  
  // Messages and confirmations
  taskStartedSuccessfully: "تم بدء المهمة بنجاح",
  taskPausedSuccessfully: "تم إيقاف المهمة بنجاح",
  taskCompletedSuccessfully: "تم إنجاز المهمة بنجاح",
  taskCancelledSuccessfully: "تم إلغاء المهمة بنجاح",
  taskDeletedSuccessfully: "تم حذف المهمة بنجاح",
  taskCreatedSuccessfully: "تم إنشاء المهمة بنجاح",
  taskUpdatedSuccessfully: "تم تحديث المهمة بنجاح",
  
  // Error messages
  failedToStartTask: "فشل في بدء المهمة",
  failedToPauseTask: "فشل في إيقاف المهمة",
  failedToCompleteTask: "فشل في إنجاز المهمة",
  failedToCancelTask: "فشل في إلغاء المهمة",
  failedToDeleteTask: "فشل في حذف المهمة",
  failedToCreateTask: "فشل في إنشاء المهمة",
  failedToUpdateTask: "فشل في تحديث المهمة",
  errorStartingTask: "خطأ في بدء المهمة",
  errorPausingTask: "خطأ في إيقاف المهمة",
  errorCompletingTask: "خطأ في إنجاز المهمة",
  errorCancellingTask: "خطأ في إلغاء المهمة",
  errorDeletingTask: "خطأ في حذف المهمة",
  
  // Confirmations
  confirmStartTask: "هل أنت متأكد من بدء هذه المهمة؟",
  confirmPauseTask: "هل أنت متأكد من إيقاف هذه المهمة؟",
  confirmCompleteTask: "هل أنت متأكد من إنجاز هذه المهمة؟",
  confirmCancelTask: "هل أنت متأكد من إلغاء المهمة",
  confirmDeleteTask: "هل أنت متأكد من حذف المهمة",
  
  // Form placeholders
  taskTitlePlaceholder: "أدخل عنوان المهمة...",
  taskDescriptionPlaceholder: "اوصف متطلبات وأهداف المهمة...",
  selectCustomer: "اختر العميل...",
  selectAssignee: "اختر المكلف...",
  selectPriority: "اختر الأولوية...",
  selectStatus: "اختر الحالة...",
  
  // Empty states
  noTasksFound: "لم يتم العثور على مهام",
  noTasksFoundSearch: "لم يتم العثور على مهام تطابق بحثك",
  createFirstTask: "أنشئ مهمتك الأولى",
  noCustomer: "لا يوجد عميل",
  unassigned: "غير مكلف",
  
  // Task details
  taskDetails: "تفاصيل المهمة",
  taskInformation: "معلومات المهمة",
  timeTracking: "تتبع الوقت",
  taskProgress: "تقدم المهمة",
  taskHistory: "تاريخ المهمة",
  taskComments: "تعليقات المهمة",
  taskAttachments: "مرفقات المهمة",
  
  // Time tracking
  timeSpent: "الوقت المستغرق",
  timeRemaining: "الوقت المتبقي",
  progressPercentage: "نسبة التقدم",
  startTime: "وقت البداية",
  endTime: "وقت الانتهاء",
  
  // Task categories
  development: "التطوير",
  design: "التصميم",
  testing: "الاختبار",
  documentation: "التوثيق",
  meeting: "اجتماع",
  research: "البحث",
  maintenance: "الصيانة",
  support: "الدعم",
  
  // Loading states
  loadingTasks: "جاري تحميل المهام...",
  savingTask: "جاري حفظ المهمة...",
  updatingTask: "جاري تحديث المهمة...",
  deletingTask: "جاري حذف المهمة...",
  
  // Validation
  taskTitleRequired: "عنوان المهمة مطلوب",
  customerRequired: "العميل مطلوب",
  assigneeRequired: "المكلف مطلوب",
  dueDateRequired: "تاريخ الاستحقاق مطلوب",
  estimatedHoursRequired: "الساعات المقدرة مطلوبة",
  invalidEstimatedHours: "يجب أن تكون الساعات المقدرة رقمًا موجبًا"
}; 