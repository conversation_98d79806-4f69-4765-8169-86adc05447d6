import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    // Test database connection
    const result = await prisma.$queryRaw`SELECT 1 as test`
    
    // Test expense types
    const expenseTypesCount = await prisma.expenseType.count()
    
    // Test expenses
    const expensesCount = await prisma.expense.count()
    
    return NextResponse.json({
      message: 'Database connection successful',
      dbTest: result,
      expenseTypesCount,
      expensesCount,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json(
      { 
        error: 'Database connection failed', 
        details: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
