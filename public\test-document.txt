This is a test document for the Print Next project management system.

Features implemented:
- Document viewing with preview support
- Proper file upload with storage in the file system
- Document name display instead of file name
- Support for various file types:
  * Images (JPG, PNG, GIF, etc.)
  * PDFs (with iframe preview)
  * Text files
  * Office documents
  * And more...

The document manager now includes:
✓ Real file upload (not just blob URLs)
✓ Document viewer dialog with preview
✓ Download functionality
✓ Folder organization
✓ Search functionality
✓ Category filtering
✓ Proper error handling

This system now properly stores files on the server and provides a complete document management experience. 