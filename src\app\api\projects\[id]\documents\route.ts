import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { serializeBigInt } from '@/lib/json-utils'
import { uploadFile, getFileTypeFromMimeType } from '@/lib/file-upload'

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: projectId } = await params

    const documents = await prisma.projectDocument.findMany({
      where: {
        projectId
      },
      include: {
        uploadedBy: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        parentFolder: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(serializeBigInt(documents))
  } catch (error) {
    console.error('Error fetching project documents:', error)
    return NextResponse.json({ error: 'Failed to fetch documents' }, { status: 500 })
  }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: projectId } = await params
    
    // Check if this is a multipart form data (file upload) or JSON data
    const contentType = request.headers.get('content-type')
    
    if (contentType?.includes('multipart/form-data')) {
      // Handle actual file upload
      const formData = await request.formData()
      const file = formData.get('file') as File
      const category = formData.get('category') as string
      const description = formData.get('description') as string
      const parentFolderId = formData.get('parentFolderId') as string
      
      if (!file) {
        return NextResponse.json({ error: 'File is required' }, { status: 400 })
      }

      // Upload the file
      const uploadResult = await uploadFile(file, projectId)
      
      // Determine file type from MIME type or extension
      const fileType = getFileTypeFromMimeType(file.type) || file.name.split('.').pop()?.toLowerCase() || 'file'

      // For now, we'll use a default user ID - in a real app, get this from authentication
      const uploadedById = "user_1" // Replace with actual user ID from session

      const document = await prisma.projectDocument.create({
        data: {
          name: file.name,
          type: fileType,
          size: BigInt(file.size),
          category: category || null,
          description: description || null,
          url: uploadResult.url,
          parentFolderId: parentFolderId && parentFolderId !== "root" ? parentFolderId : null,
          projectId,
          uploadedById
        },
        include: {
          uploadedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          parentFolder: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      return NextResponse.json(serializeBigInt(document), { status: 201 })
    } else {
      // Handle JSON data (legacy support for metadata-only uploads)
      const { name, type, size, category, description, url, parentFolderId } = await request.json()

      if (!name?.trim()) {
        return NextResponse.json({ error: 'Name is required' }, { status: 400 })
      }

      // For now, we'll use a default user ID - in a real app, get this from authentication
      const uploadedById = "user_1" // Replace with actual user ID from session

      const document = await prisma.projectDocument.create({
        data: {
          name: name.trim(),
          type: type || 'file',
          size: size ? BigInt(size) : null,
          category,
          description,
          url,
          parentFolderId: parentFolderId || null,
          projectId,
          uploadedById
        },
        include: {
          uploadedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          parentFolder: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })

      return NextResponse.json(serializeBigInt(document), { status: 201 })
    }
  } catch (error) {
    console.error('Error creating project document:', error)
    return NextResponse.json({ error: 'Failed to create document' }, { status: 500 })
  }
} 