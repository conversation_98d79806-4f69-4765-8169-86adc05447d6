export const common = {
  // Basic actions
  save: "Save",
  cancel: "Cancel",
  delete: "Delete",
  edit: "Edit",
  add: "Add",
  create: "Create",
  update: "Update",
  view: "View",
  search: "Search",
  filter: "Filter",
  export: "Export",
  import: "Import",
  print: "Print",
  download: "Download",
  upload: "Upload",
  loading: "Loading...",
  noData: "No data available",
  confirm: "Confirm",
  yes: "Yes",
  no: "No",
  back: "Back",
  next: "Next",
  previous: "Previous",
  close: "Close",
  submit: "Submit",
  reset: "Reset",
  clear: "Clear",
  select: "Select",
  all: "All",
  none: "None",

  // Status and states
  actions: "Actions",
  status: "Status",
  active: "Active",
  inactive: "Inactive",
  pending: "Pending",
  approved: "Approved",
  rejected: "Rejected",
  completed: "Completed",
  cancelled: "Cancelled",
  draft: "Draft",
  sent: "Sent",
  paid: "Paid",
  unpaid: "Unpaid",
  partial: "Partially Paid",
  overdue: "Overdue",

  // Data fields
  date: "Date",
  time: "Time",
  total: "Total",
  subtotal: "Subtotal",
  tax: "Tax",
  discount: "Discount",
  amount: "Amount",
  quantity: "Quantity",
  price: "Price",
  cost: "Cost",
  balance: "Balance",
  
  // Contact information
  name: "Name",
  email: "Email",
  phone: "Phone",
  mobile: "Mobile",
  address: "Address",
  city: "City",
  country: "Country",
  company: "Company",
  contact: "Contact",
  contactInformation: "Contact Information",

  // Business entities
  customer: "Customer",
  supplier: "Supplier",
  employee: "Employee",
  product: "Product",
  service: "Service",
  category: "Category",
  invoice: "Invoice",
  quotation: "Quotation",
  task: "Task",
  project: "Project",
  report: "Report",

  // Dates and time
  startDate: "Start Date",
  endDate: "End Date",
  dueDate: "Due Date",
  createdAt: "Created At",
  updatedAt: "Updated At",
  created: "Created",
  today: "Today",
  yesterday: "Yesterday",
  tomorrow: "Tomorrow",
  thisWeek: "This Week",
  thisMonth: "This Month",
  thisYear: "This Year",

  // Priorities and levels
  priority: "Priority",
  low: "Low",
  medium: "Medium",
  high: "High",
  urgent: "Urgent",
  critical: "Critical",

  // Common UI elements
  description: "Description",
  notes: "Notes",
  comments: "Comments",
  attachments: "Attachments",
  settings: "Settings",
  profile: "Profile",
  logout: "Logout",
  dashboard: "Dashboard",
  menu: "Menu",
  home: "Home",
  
  // Theme and appearance
  theme: "Theme",
  light: "Light",
  dark: "Dark",
  system: "System",
  language: "Language",

  // Search and filters
  searchPlaceholder: "Search...",
  filterBy: "Filter by",
  sortBy: "Sort by",
  allStatuses: "All Statuses",
  allCategories: "All Categories",
  allTypes: "All Types",
  allPriorities: "All Priorities",
  allSources: "All Sources",

  // Assignment and ownership
  assignedTo: "Assigned To",
  createdBy: "Created By",
  updatedBy: "Updated By",
  owner: "Owner",
  manager: "Manager",
  client: "Client",
  unassigned: "Unassigned",

  // Progress and completion
  progress: "Progress",
  percentage: "Percentage",
  budget: "Budget",
  spent: "Spent",
  remaining: "Remaining",

  // Common messages
  success: "Success",
  error: "Error",
  warning: "Warning",
  info: "Information",
  required: "Required",
  optional: "Optional",
  
  // Other common terms
  source: "Source",
  type: "Type",
  reference: "Reference",
  code: "Code",
  number: "Number",
  version: "Version",
  other: "Other",
  unknown: "Unknown",
  
  // File and document related
  file: "File",
  document: "Document",
  image: "Image",
  folder: "Folder",
  size: "Size",
  format: "Format",
}; 