"use client"

export interface ThemeColors {
  name: string
  displayName: string
  primary: string
  primaryHover: string
  primaryActive: string
  primaryForeground: string
  secondary: string
  accent: string
  muted: string
  border: string
  ring: string
  gradient: string
  shadow: string
}

export const themes: Record<string, ThemeColors> = {
  blue: {
    name: 'blue',
    displayName: 'Ocean Blue',
    primary: '#3b82f6',
    primaryHover: '#2563eb',
    primaryActive: '#1d4ed8',
    primaryForeground: '#ffffff',
    secondary: '#e0f2fe',
    accent: '#0ea5e9',
    muted: '#f1f5f9',
    border: '#e2e8f0',
    ring: '#3b82f6',
    gradient: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
    shadow: 'rgba(59, 130, 246, 0.25)'
  },
  green: {
    name: 'green',
    displayName: 'Forest Green',
    primary: '#10b981',
    primaryHover: '#059669',
    primaryActive: '#047857',
    primaryForeground: '#ffffff',
    secondary: '#d1fae5',
    accent: '#34d399',
    muted: '#f0fdf4',
    border: '#d1d5db',
    ring: '#10b981',
    gradient: 'linear-gradient(135deg, #10b981 0%, #047857 100%)',
    shadow: 'rgba(16, 185, 129, 0.25)'
  },
  purple: {
    name: 'purple',
    displayName: 'Royal Purple',
    primary: '#8b5cf6',
    primaryHover: '#7c3aed',
    primaryActive: '#6d28d9',
    primaryForeground: '#ffffff',
    secondary: '#ede9fe',
    accent: '#a78bfa',
    muted: '#faf5ff',
    border: '#d1d5db',
    ring: '#8b5cf6',
    gradient: 'linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%)',
    shadow: 'rgba(139, 92, 246, 0.25)'
  },
  orange: {
    name: 'orange',
    displayName: 'Sunset Orange',
    primary: '#f97316',
    primaryHover: '#ea580c',
    primaryActive: '#c2410c',
    primaryForeground: '#ffffff',
    secondary: '#fed7aa',
    accent: '#fb923c',
    muted: '#fff7ed',
    border: '#d1d5db',
    ring: '#f97316',
    gradient: 'linear-gradient(135deg, #f97316 0%, #c2410c 100%)',
    shadow: 'rgba(249, 115, 22, 0.25)'
  },
  teal: {
    name: 'teal',
    displayName: 'Ocean Teal',
    primary: '#14b8a6',
    primaryHover: '#0d9488',
    primaryActive: '#0f766e',
    primaryForeground: '#ffffff',
    secondary: '#ccfbf1',
    accent: '#2dd4bf',
    muted: '#f0fdfa',
    border: '#d1d5db',
    ring: '#14b8a6',
    gradient: 'linear-gradient(135deg, #14b8a6 0%, #0f766e 100%)',
    shadow: 'rgba(20, 184, 166, 0.25)'
  },
  rose: {
    name: 'rose',
    displayName: 'Rose Pink',
    primary: '#f43f5e',
    primaryHover: '#e11d48',
    primaryActive: '#be123c',
    primaryForeground: '#ffffff',
    secondary: '#fecdd3',
    accent: '#fb7185',
    muted: '#fff1f2',
    border: '#d1d5db',
    ring: '#f43f5e',
    gradient: 'linear-gradient(135deg, #f43f5e 0%, #be123c 100%)',
    shadow: 'rgba(244, 63, 94, 0.25)'
  },
  indigo: {
    name: 'indigo',
    displayName: 'Deep Indigo',
    primary: '#6366f1',
    primaryHover: '#4f46e5',
    primaryActive: '#4338ca',
    primaryForeground: '#ffffff',
    secondary: '#e0e7ff',
    accent: '#818cf8',
    muted: '#f8fafc',
    border: '#d1d5db',
    ring: '#6366f1',
    gradient: 'linear-gradient(135deg, #6366f1 0%, #4338ca 100%)',
    shadow: 'rgba(99, 102, 241, 0.25)'
  },
  amber: {
    name: 'amber',
    displayName: 'Golden Amber',
    primary: '#f59e0b',
    primaryHover: '#d97706',
    primaryActive: '#b45309',
    primaryForeground: '#ffffff',
    secondary: '#fef3c7',
    accent: '#fbbf24',
    muted: '#fffbeb',
    border: '#d1d5db',
    ring: '#f59e0b',
    gradient: 'linear-gradient(135deg, #f59e0b 0%, #b45309 100%)',
    shadow: 'rgba(245, 158, 11, 0.25)'
  },
  slate: {
    name: 'slate',
    displayName: 'Modern Slate',
    primary: '#475569',
    primaryHover: '#334155',
    primaryActive: '#1e293b',
    primaryForeground: '#ffffff',
    secondary: '#f1f5f9',
    accent: '#64748b',
    muted: '#f8fafc',
    border: '#e2e8f0',
    ring: '#475569',
    gradient: 'linear-gradient(135deg, #475569 0%, #1e293b 100%)',
    shadow: 'rgba(71, 85, 105, 0.25)'
  }
}

export function getTheme(themeName: string): ThemeColors {
  return themes[themeName] || themes.blue
}

export function applyTheme(themeName: string) {
  const theme = getTheme(themeName)
  const root = document.documentElement

  // Apply CSS custom properties
  root.style.setProperty('--theme-primary', theme.primary)
  root.style.setProperty('--theme-primary-hover', theme.primaryHover)
  root.style.setProperty('--theme-primary-active', theme.primaryActive)
  root.style.setProperty('--theme-primary-foreground', theme.primaryForeground)
  root.style.setProperty('--theme-secondary', theme.secondary)
  root.style.setProperty('--theme-accent', theme.accent)
  root.style.setProperty('--theme-muted', theme.muted)
  root.style.setProperty('--theme-border', theme.border)
  root.style.setProperty('--theme-ring', theme.ring)
  root.style.setProperty('--theme-gradient', theme.gradient)
  root.style.setProperty('--theme-shadow', theme.shadow)

  // Force a repaint to ensure theme changes are applied immediately
  root.style.display = 'none'
  void root.offsetHeight // Trigger reflow
  root.style.display = ''

  // Save to localStorage
  localStorage.setItem('selectedTheme', themeName)

  // Dispatch a custom event to notify components of theme change
  window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme: themeName } }))
}

export function getStoredTheme(): string {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('selectedTheme') || 'blue'
  }
  return 'blue'
}

export function initializeTheme() {
  if (typeof window !== 'undefined') {
    const storedTheme = getStoredTheme()
    applyTheme(storedTheme)
  }
}
