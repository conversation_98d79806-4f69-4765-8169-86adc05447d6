"use client"

import { formatCurrency } from "@/lib/localization"
import { Package } from "lucide-react"

interface InvoiceItem {
  id: string
  name: string
  nameAr?: string
  image?: string
  quantity: number
  price: number
  total: number
  unit: string
}

interface InvoiceData {
  invoiceNumber: string
  date: string
  dueDate: string
  customer: {
    name: string
    email: string
    phone: string
    address: string
  }
  items: InvoiceItem[]
  subtotal: number
  tax: number
  total: number
  notes?: string
}

interface InvoiceTemplateProps {
  invoice: InvoiceData
  showImages?: boolean
}

export function InvoiceTemplate({ invoice, showImages = true }: InvoiceTemplateProps) {
  return (
    <div className="max-w-4xl mx-auto p-8 bg-white">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">INVOICE</h1>
          <p className="text-gray-600 mt-1">فاتورة</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-blue-600">#{invoice.invoiceNumber}</div>
          <div className="text-sm text-gray-600 mt-1">
            <div>Date: {invoice.date}</div>
            <div>Due: {invoice.dueDate}</div>
          </div>
        </div>
      </div>

      {/* Company & Customer Info */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">From:</h3>
          <div className="text-sm text-gray-600">
            <div className="font-medium">Office Sales & Services</div>
            <div>مبيعات وخدمات المكتب</div>
            <div>Muscat, Sultanate of Oman</div>
            <div>مسقط، سلطنة عمان</div>
            <div>Phone: +968 1234 5678</div>
            <div>Email: <EMAIL></div>
          </div>
        </div>
        <div>
          <h3 className="font-semibold text-gray-900 mb-2">Bill To:</h3>
          <div className="text-sm text-gray-600">
            <div className="font-medium">{invoice.customer.name}</div>
            <div>{invoice.customer.address}</div>
            <div>Phone: {invoice.customer.phone}</div>
            <div>Email: {invoice.customer.email}</div>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-8">
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-50">
              {showImages && <th className="border border-gray-300 px-4 py-3 text-left">Image</th>}
              <th className="border border-gray-300 px-4 py-3 text-left">Item / العنصر</th>
              <th className="border border-gray-300 px-4 py-3 text-center">Qty / الكمية</th>
              <th className="border border-gray-300 px-4 py-3 text-right">Price / السعر</th>
              <th className="border border-gray-300 px-4 py-3 text-right">Total / المجموع</th>
            </tr>
          </thead>
          <tbody>
            {invoice.items.map((item) => (
              <tr key={item.id}>
                {showImages && (
                  <td className="border border-gray-300 px-4 py-3">
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                      {item.image ? (
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                  </td>
                )}
                <td className="border border-gray-300 px-4 py-3">
                  <div className="font-medium">{item.name}</div>
                  {item.nameAr && (
                    <div className="text-sm text-gray-600" dir="rtl">{item.nameAr}</div>
                  )}
                </td>
                <td className="border border-gray-300 px-4 py-3 text-center">
                  {item.quantity} {item.unit}
                </td>
                <td className="border border-gray-300 px-4 py-3 text-right">
                  {formatCurrency(item.price)}
                </td>
                <td className="border border-gray-300 px-4 py-3 text-right font-medium">
                  {formatCurrency(item.total)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-64">
          <div className="flex justify-between py-2 border-b border-gray-200">
            <span>Subtotal / المجموع الفرعي:</span>
            <span>{formatCurrency(invoice.subtotal)}</span>
          </div>
          <div className="flex justify-between py-2 border-b border-gray-200">
            <span>Tax / الضريبة:</span>
            <span>{formatCurrency(invoice.tax)}</span>
          </div>
          <div className="flex justify-between py-3 font-bold text-lg border-b-2 border-gray-900">
            <span>Total / المجموع الكلي:</span>
            <span>{formatCurrency(invoice.total)}</span>
          </div>
        </div>
      </div>

      {/* Notes */}
      {invoice.notes && (
        <div className="mb-8">
          <h3 className="font-semibold text-gray-900 mb-2">Notes / ملاحظات:</h3>
          <p className="text-sm text-gray-600">{invoice.notes}</p>
        </div>
      )}

      {/* Footer */}
      <div className="text-center text-sm text-gray-500 border-t border-gray-200 pt-4">
        <p>Thank you for your business! / شكراً لتعاملكم معنا!</p>
        <p className="mt-1">Office Sales & Services - Muscat, Sultanate of Oman</p>
      </div>
    </div>
  )
}

// Sample invoice data for testing
export const sampleInvoiceData: InvoiceData = {
  invoiceNumber: "INV-001",
  date: "2024-01-20",
  dueDate: "2024-02-20",
  customer: {
    name: "ABC Corporation",
    email: "<EMAIL>",
    phone: "+968 9876 5432",
    address: "Al Khuwair, Muscat, Sultanate of Oman"
  },
  items: [
    {
      id: "1",
      name: "A4 Copy Paper",
      nameAr: "ورق نسخ A4",
      image: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?w=400&h=400&fit=crop&crop=center",
      quantity: 10,
      price: 12.500,
      total: 125.000,
      unit: "ream"
    },
    {
      id: "2",
      name: "Business Card Printing",
      nameAr: "طباعة بطاقات العمل",
      image: "https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=400&fit=crop&crop=center",
      quantity: 1,
      price: 25.000,
      total: 25.000,
      unit: "set"
    },
    {
      id: "3",
      name: "Color Ink Cartridge HP",
      nameAr: "خرطوشة حبر ملونة HP",
      image: "https://images.unsplash.com/photo-1612198188060-c7c2a3b66eae?w=400&h=400&fit=crop&crop=center",
      quantity: 2,
      price: 45.900,
      total: 91.800,
      unit: "piece"
    }
  ],
  subtotal: 241.800,
  tax: 24.180,
  total: 265.980,
  notes: "Payment terms: Net 30 days. Late payment charges may apply."
}
