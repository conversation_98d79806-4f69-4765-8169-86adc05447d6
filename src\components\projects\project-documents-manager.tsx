"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { 
  FileText, 
  Upload, 
  FolderPlus, 
  Search, 
  Download, 
  Eye, 
  MoreHorizontal,
  Folder,
  File,
  Trash2,
  Edit,
  Share,
  Calendar,
  User,
  ChevronDown,
  ChevronRight
} from "lucide-react"

interface ProjectDocumentsManagerProps {
  projectId: string
  isEditable?: boolean
  documents?: any[]
  setDocuments?: React.Dispatch<React.SetStateAction<any[]>>
  folders?: any[]
  setFolders?: React.Dispatch<React.SetStateAction<any[]>>
  expandedFolders?: Set<string>
  setExpandedFolders?: React.Dispatch<React.SetStateAction<Set<string>>>
}

export function ProjectDocumentsManager({ 
  projectId, 
  isEditable = true,
  documents: externalDocuments,
  setDocuments: externalSetDocuments,
  folders: externalFolders,
  setFolders: externalSetFolders,
  expandedFolders: externalExpandedFolders,
  setExpandedFolders: externalSetExpandedFolders
}: ProjectDocumentsManagerProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [folderDialogOpen, setFolderDialogOpen] = useState(false)
  const [viewDialogOpen, setViewDialogOpen] = useState(false)
  const [selectedDocument, setSelectedDocument] = useState<any>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadCategory, setUploadCategory] = useState("")
  const [uploadDescription, setUploadDescription] = useState("")
  const [selectedFolder, setSelectedFolder] = useState("root")
  const [folderName, setFolderName] = useState("")
  const [folderDescription, setFolderDescription] = useState("")
  const [loading, setLoading] = useState(false)
  
  // Use external state if provided, otherwise use internal state
  const [internalDocuments, setInternalDocuments] = useState<any[]>([])
  const [internalFolders, setInternalFolders] = useState<any[]>([])
  const [internalExpandedFolders, setInternalExpandedFolders] = useState<Set<string>>(new Set())
  
  const documents = externalDocuments || internalDocuments
  const setDocuments = externalSetDocuments || setInternalDocuments
  const folders = externalFolders || internalFolders
  const setFolders = externalSetFolders || setInternalFolders
  const expandedFolders = externalExpandedFolders || internalExpandedFolders
  const setExpandedFolders = externalSetExpandedFolders || setInternalExpandedFolders

  useEffect(() => {
    // Only fetch once when component mounts if no data exists
    const shouldFetch = (!externalDocuments || (Array.isArray(externalDocuments) && externalDocuments.length === 0)) &&
                       (!externalFolders || (Array.isArray(externalFolders) && externalFolders.length === 0))
    
    if (shouldFetch) {
      fetchDocumentsAndFolders()
    }
  }, [projectId]) // Only depend on projectId to avoid infinite loops

  const fetchDocumentsAndFolders = async () => {
    try {
      const [documentsResponse, foldersResponse] = await Promise.all([
        fetch(`/api/projects/${projectId}/documents`),
        fetch(`/api/projects/${projectId}/folders`)
      ])

      if (documentsResponse.ok) {
        const documentsData = await documentsResponse.json()
        
        // Update the appropriate state (external if provided, otherwise internal)
        if (externalSetDocuments) {
          externalSetDocuments(documentsData)
        } else {
          setInternalDocuments(documentsData)
        }
      }

      if (foldersResponse.ok) {
        const foldersData = await foldersResponse.json()
        
        // Update the appropriate state (external if provided, otherwise internal)
        if (externalSetFolders) {
          externalSetFolders(foldersData)
        } else {
          setInternalFolders(foldersData)
        }
      }
    } catch (error) {
      console.error("Error fetching documents and folders:", error)
    }
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
    }
  }

  const handleFileUpload = async () => {
    if (!selectedFile) {
      alert("Please select a file first")
      return
    }

    if (!uploadCategory) {
      alert("Please select a category")
      return
    }

    try {
      setLoading(true)
      
      // Create FormData for proper file upload
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('category', uploadCategory)
      formData.append('description', uploadDescription)
      formData.append('parentFolderId', selectedFolder && selectedFolder !== "root" ? selectedFolder : "")
      
      const response = await fetch(`/api/projects/${projectId}/documents`, {
        method: 'POST',
        body: formData // Don't set Content-Type header - browser will set it with boundary
      })

      if (response.ok) {
        const newDocument = await response.json()
        
        // Add to documents list
        setDocuments(prev => [newDocument, ...prev])
        
        // Update folder item count if file is placed in a folder
        if (selectedFolder && selectedFolder !== "root") {
          setFolders(prev => prev.map(folder => 
            folder.id === selectedFolder 
              ? { ...folder, itemCount: (folder.itemCount || 0) + 1 }
              : folder
          ))
        }

        alert(`File "${selectedFile.name}" uploaded successfully!`)
        
        // Reset form
        setSelectedFile(null)
        setUploadCategory("")
        setUploadDescription("")
        setSelectedFolder("root")
        setUploadDialogOpen(false)
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to upload file')
      }
      
    } catch (error) {
      console.error("Upload error:", error)
      alert(`Failed to upload file: ${error instanceof Error ? error.message : 'Please try again.'}`)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateFolder = async () => {
    if (!folderName.trim()) {
      alert("Please enter a folder name")
      return
    }

    try {
      setLoading(true)
      
      const response = await fetch(`/api/projects/${projectId}/folders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: folderName,
          description: folderDescription
        })
      })

      if (response.ok) {
        const newFolder = await response.json()
        
        // Add to folders list
        setFolders(prev => [newFolder, ...prev])

        alert(`Folder "${folderName}" created successfully!`)
        
        // Reset form
        setFolderName("")
        setFolderDescription("")
        setFolderDialogOpen(false)
      } else {
        throw new Error('Failed to create folder')
      }
      
    } catch (error) {
      console.error("Folder creation error:", error)
      alert("Failed to create folder")
    } finally {
      setLoading(false)
    }
  }

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-600" />
      case 'doc':
      case 'docx':
        return <FileText className="h-5 w-5 text-blue-600" />
      case 'xls':
      case 'xlsx':
        return <FileText className="h-5 w-5 text-green-600" />
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FileText className="h-5 w-5 text-purple-600" />
      case 'folder':
        return <Folder className="h-5 w-5 text-blue-600" />
      default:
        return <File className="h-5 w-5 text-gray-600" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDeleteItem = async (id: string, type: 'file' | 'folder') => {
    if (confirm(`Are you sure you want to delete this ${type}?`)) {
      try {
        if (type === 'file') {
          const response = await fetch(`/api/projects/${projectId}/documents/${id}`, {
            method: 'DELETE'
          })

          if (response.ok) {
            const fileToDelete = documents.find(doc => doc.id === id)
            setDocuments(prev => prev.filter(doc => doc.id !== id))
            
            // Update folder count if file was in a folder
            if (fileToDelete?.parentFolderId) {
              setFolders(prev => prev.map(folder => 
                folder.id === fileToDelete.parentFolderId 
                  ? { ...folder, itemCount: Math.max(0, (folder.itemCount || 0) - 1) }
                  : folder
              ))
            }
            alert("File deleted successfully")
          } else {
            throw new Error('Failed to delete file')
          }
        } else {
          const response = await fetch(`/api/projects/${projectId}/folders/${id}`, {
            method: 'DELETE'
          })

          if (response.ok) {
            // Delete folder and all its files from UI
            const filesToDelete = documents.filter(doc => doc.parentFolderId === id)
            setDocuments(prev => prev.filter(doc => doc.parentFolderId !== id))
            setFolders(prev => prev.filter(folder => folder.id !== id))
            
            if (filesToDelete.length > 0) {
              alert(`Folder and ${filesToDelete.length} files deleted successfully`)
            } else {
              alert("Folder deleted successfully")
            }
          } else {
            throw new Error('Failed to delete folder')
          }
        }
      } catch (error) {
        console.error(`Error deleting ${type}:`, error)
        alert(`Failed to delete ${type}`)
      }
    }
  }

  const getCategoryCount = (category: string) => {
    return documents.filter(doc => doc.category === category).length
  }

  const toggleFolder = (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(folderId)) {
        newSet.delete(folderId)
      } else {
        newSet.add(folderId)
      }
      return newSet
    })
  }

  const getFilesInFolder = (folderId: string) => {
    return documents.filter(doc => doc.parentFolderId === folderId)
  }

  const getRootFiles = () => {
    return documents.filter(doc => !doc.parentFolderId)
  }

  // Filter folders and root files for search
  const filteredFolders = folders.filter(folder => 
    folder.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    folder.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  const filteredRootFiles = getRootFiles().filter(file => 
    file.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    file.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )
  
  const handleViewDocument = (document: any) => {
    setSelectedDocument(document)
    setViewDialogOpen(true)
  }

  const handleDownloadDocument = (document: any) => {
    if (document.url) {
      // Create a temporary anchor element to trigger download
      const link = document.createElement('a')
      link.href = document.url
      link.download = document.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else {
      alert("Document file is not available for download")
    }
  }

  const getFilePreviewContent = (document: any) => {
    const fileType = document.type.toLowerCase()
    
    // For images, show preview
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileType)) {
      return (
        <div className="flex justify-center p-4">
          <img 
            src={document.url} 
            alt={document.name}
            className="max-w-full max-h-96 object-contain rounded-lg"
            onError={(e) => {
              (e.target as HTMLImageElement).style.display = 'none'
            }}
          />
        </div>
      )
    }
    
    // For PDFs, try to embed
    if (fileType === 'pdf' && document.url) {
      return (
        <div className="w-full h-96">
          <iframe 
            src={document.url}
            className="w-full h-full border rounded-lg"
            title={`Preview of ${document.name}`}
          />
        </div>
      )
    }
    
    // For text files, try to load content
    if (['txt', 'md', 'json', 'css', 'js', 'html'].includes(fileType)) {
      return (
        <div className="p-4 bg-muted rounded-lg">
          <p className="text-sm text-muted-foreground mb-2">Text content preview not available</p>
          <p className="text-sm">Click download to view the full content</p>
        </div>
      )
    }
    
    // For other file types, show file info
    return (
      <div className="p-8 text-center">
        <div className="flex justify-center mb-4">
          {getFileIcon(fileType)}
        </div>
        <h3 className="text-lg font-semibold mb-2">{document.name}</h3>
        <p className="text-muted-foreground mb-4">
          Preview not available for this file type
        </p>
        <Button onClick={() => handleDownloadDocument(document)}>
          <Download className="h-4 w-4 mr-2" />
          Download to View
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Project Documents</h3>
          <Badge variant="secondary">{documents.length + folders.length} items</Badge>
        </div>
        
        {isEditable && (
          <div className="flex gap-2">
            <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Upload Document</DialogTitle>
                  <DialogDescription>
                    Upload a new document to this project
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>File</Label>
                    <Input
                      type="file"
                      onChange={handleFileSelect}
                      accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif"
                    />
                    {selectedFile && (
                      <p className="text-sm text-muted-foreground">
                        Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                      </p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label>Category</Label>
                    <Select value={uploadCategory} onValueChange={setUploadCategory}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="contracts">Contracts</SelectItem>
                        <SelectItem value="specifications">Specifications</SelectItem>
                        <SelectItem value="reports">Reports</SelectItem>
                        <SelectItem value="images">Images</SelectItem>
                        <SelectItem value="presentations">Presentations</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Folder (Optional)</Label>
                    <Select value={selectedFolder} onValueChange={setSelectedFolder}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select folder or leave empty for root" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="root">Root (No Folder)</SelectItem>
                        {folders.map(folder => (
                          <SelectItem key={folder.id} value={folder.id}>
                            <div className="flex items-center gap-2">
                              <Folder className="h-4 w-4" />
                              {folder.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Description (Optional)</Label>
                    <Textarea 
                      placeholder="Brief description of the document..."
                      value={uploadDescription}
                      onChange={(e) => setUploadDescription(e.target.value)}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleFileUpload} className="flex-1" disabled={!selectedFile || loading}>
                      {loading ? "Uploading..." : "Upload Document"}
                    </Button>
                    <Button variant="outline" onClick={() => setUploadDialogOpen(false)} disabled={loading}>
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Dialog open={folderDialogOpen} onOpenChange={setFolderDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <FolderPlus className="h-4 w-4 mr-2" />
                  New Folder
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Folder</DialogTitle>
                  <DialogDescription>
                    Create a new folder to organize documents
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Folder Name</Label>
                    <Input 
                      placeholder="Enter folder name..."
                      value={folderName}
                      onChange={(e) => setFolderName(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Description (Optional)</Label>
                    <Textarea 
                      placeholder="Brief description of the folder..."
                      value={folderDescription}
                      onChange={(e) => setFolderDescription(e.target.value)}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleCreateFolder} className="flex-1" disabled={!folderName.trim() || loading}>
                      {loading ? "Creating..." : "Create Folder"}
                    </Button>
                    <Button variant="outline" onClick={() => setFolderDialogOpen(false)} disabled={loading}>
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search documents..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Document Categories */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {[
          { name: "Contracts", value: "contracts", color: "bg-blue-100 text-blue-800" },
          { name: "Specifications", value: "specifications", color: "bg-green-100 text-green-800" },
          { name: "Reports", value: "reports", color: "bg-purple-100 text-purple-800" },
          { name: "Images", value: "images", color: "bg-orange-100 text-orange-800" },
          { name: "Presentations", value: "presentations", color: "bg-yellow-100 text-yellow-800" },
          { name: "Other", value: "other", color: "bg-gray-100 text-gray-800" }
        ].map(category => (
          <Card key={category.name} className="cursor-pointer hover:bg-muted/50">
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{getCategoryCount(category.value)}</div>
              <div className="text-sm text-muted-foreground">{category.name}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Files and Folders List */}
      {filteredFolders.length === 0 && filteredRootFiles.length === 0 ? (
        <div className="space-y-6">
          {/* Upload Area when empty */}
          <Card>
            <CardContent className="p-8">
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                <FileText className="mx-auto h-12 w-12 text-muted-foreground/50 mb-4" />
                <h3 className="text-lg font-semibold mb-2">Upload Documents</h3>
                <p className="text-muted-foreground mb-4">
                  Drag and drop files here, or click to browse
                </p>
                <p className="text-xs text-muted-foreground">
                  Supports: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, JPG, PNG (Max 10MB)
                </p>
              </div>
            </CardContent>
          </Card>
          
          {/* Empty State */}
          <div className="text-center py-12 text-muted-foreground">
            <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <h3 className="text-lg font-semibold mb-2">No documents uploaded yet</h3>
            <p className="text-sm">Upload your first document to get started</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Folders */}
          {filteredFolders.map((folder) => (
            <Card key={folder.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-0">
                {/* Folder Header */}
                <div 
                  className="p-4 cursor-pointer flex items-center justify-between hover:bg-muted/50"
                  onClick={() => toggleFolder(folder.id)}
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="flex items-center gap-2">
                      {expandedFolders.has(folder.id) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                      <Folder className="h-5 w-5 text-blue-600" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium truncate">{folder.name}</h4>
                        <Badge variant="secondary" className="text-xs">
                          {getFilesInFolder(folder.id).length} files
                        </Badge>
                      </div>
                      
                      {folder.description && (
                        <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                          {folder.description}
                        </p>
                      )}
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {folder.createdBy?.name || 'Unknown'}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(folder.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {isEditable && (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteItem(folder.id, 'folder')
                        }}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>

                {/* Folder Contents */}
                {expandedFolders.has(folder.id) && (
                  <div className="border-t bg-muted/25">
                    {getFilesInFolder(folder.id).map((file) => (
                      <div key={file.id} className="p-4 pl-12 border-b last:border-b-0 hover:bg-muted/50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 flex-1">
                            <div className="flex-shrink-0">
                              {getFileIcon(file.type)}
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h5 className="font-medium truncate">{file.name}</h5>
                                {file.category && (
                                  <Badge variant="secondary" className="text-xs">
                                    {file.category}
                                  </Badge>
                                )}
                              </div>
                              
                              {file.description && (
                                <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                                  {file.description}
                                </p>
                              )}
                              
                              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                <span className="flex items-center gap-1">
                                  <User className="h-3 w-3" />
                                  {file.uploadedBy?.name || 'Unknown'}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {new Date(file.uploadedAt).toLocaleDateString()}
                                </span>
                                <span>{formatFileSize(file.size)}</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleViewDocument(file)}
                              title="View document"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            
                            {isEditable && (
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                onClick={() => handleDeleteItem(file.id, 'file')}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                title="Delete document"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {getFilesInFolder(folder.id).length === 0 && (
                      <div className="p-4 pl-12 text-center text-muted-foreground">
                        <p className="text-sm">No files in this folder</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}

          {/* Root Files (files not in any folder) */}
          {filteredRootFiles.map((file) => (
            <Card key={file.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 flex-1">
                    <div className="flex-shrink-0">
                      {getFileIcon(file.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium truncate">{file.name}</h4>
                        {file.category && (
                          <Badge variant="secondary" className="text-xs">
                            {file.category}
                          </Badge>
                        )}
                      </div>
                      
                      {file.description && (
                        <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                          {file.description}
                        </p>
                      )}
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {file.uploadedBy?.name || 'Unknown'}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(file.uploadedAt).toLocaleDateString()}
                        </span>
                        <span>{formatFileSize(file.size)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleViewDocument(file)}
                      title="View document"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    
                    {isEditable && (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleDeleteItem(file.id, 'file')}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        title="Delete document"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Document Viewer Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedDocument && getFileIcon(selectedDocument.type)}
              {selectedDocument?.name}
            </DialogTitle>
            <DialogDescription>
              {selectedDocument && (
                <div className="flex items-center gap-4 text-sm">
                  <span>Size: {formatFileSize(Number(selectedDocument.size))}</span>
                  {selectedDocument.category && (
                    <Badge variant="secondary">{selectedDocument.category}</Badge>
                  )}
                  <span>Uploaded: {new Date(selectedDocument.createdAt).toLocaleDateString()}</span>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          
          <div className="mt-4">
            {selectedDocument && getFilePreviewContent(selectedDocument)}
          </div>
          
          {selectedDocument?.description && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-sm text-muted-foreground">{selectedDocument.description}</p>
            </div>
          )}
          
          <div className="flex gap-2 mt-4">
            <Button 
              onClick={() => handleDownloadDocument(selectedDocument)}
              className="flex-1"
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setViewDialogOpen(false)}
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
} 