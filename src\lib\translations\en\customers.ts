export const customers = {
  // Page titles and descriptions
  title: "Customers",
  manageCustomers: "Manage Customers",
  dashboard: "Customers Dashboard",
  dashboardDescription: "Manage your customers and view their statistics",
  
  // Customer management
  addCustomer: "Add Customer",
  editCustomer: "Edit Customer",
  createCustomer: "Create Customer",
  updateCustomer: "Update Customer",
  deleteCustomer: "Delete Customer",
  confirmDelete: "Are you sure you want to delete this customer?",
  
  // Customer information
  customerName: "Customer Name",
  customerEmail: "Customer Email",
  customerPhone: "Customer Phone",
  customerCompany: "Customer Company",
  customerAddress: "Customer Address",
  customerDetails: "Customer Details",
  customerInformation: "Customer Information",
  contactInformation: "Contact Information",
  customerNotFound: "Customer Not Found",
  customerDetailsAndHistory: "Customer details and transaction history",
  customerSince: "Customer Since",
  lastOrderDate: "Last Order Date",
  
  // Search and filters
  searchCustomers: "Search Customers",
  noCustomersFound: "No customers found",
  tryDifferentSearch: "Try a different search",
  
  // Dashboard metrics
  repeatCustomers: "Repeat Customers",
  avgOrderValue: "Avg. Order Value",
  totalRevenue: "Total Revenue",
  totalCustomers: "Total Customers",
  outstandingBalance: "Outstanding Balance",
  topCustomers: "Top Customers",
  inactiveCustomers: "Inactive Customers",
  activeCustomers: "Active Customers",
  customerTypeDistribution: "Customer Type Distribution",
  monthlyCustomerGrowth: "Monthly Customer Growth",
  
  // Dashboard descriptions
  highValueCustomers: "High-value customers",
  needReEngagement: "Need re-engagement",
  fromLastMonth: "from last month",
  thisMonth: "this month",
  breakdownOfCustomersByType: "Breakdown of customers by type",
  newCustomersAndRevenueOverLast5Months: "New customers and revenue over the last 5 months",
  nanPercentOfTotal: "NaN% of total",
  
  // Financial information
  totalSpent: "Total Spent",
  totalInvoices: "Total Invoices",
  totalQuotations: "Total Quotations",
  customerSummary: "Customer Summary",
  
  // Invoices and payments
  invoices: "Invoices",
  customerInvoices: "Customer Invoices",
  invoiceNumber: "Invoice Number",
  dueDate: "Due Date",
  balance: "Balance",
  paid: "Paid",
  noInvoicesFound: "No invoices found for this customer",
  paymentHistory: "Payment History",
  method: "Method",
  reference: "Reference",
  paymentStatus: "Payment Status",
  paidInvoices: "Paid Invoices",
  sentInvoices: "Sent Invoices",
  overdueInvoices: "Overdue Invoices",
  lastPayment: "Last Payment",
  
  // Quotations
  customerQuotations: "Customer Quotations",
  quotationNumber: "Quotation Number",
  validUntil: "Valid Until",
  noQuotationsFound: "No quotations found for this customer",
  
  // Actions
  quickActions: "Quick Actions",
  createInvoice: "Create Invoice",
  createQuotation: "Create Quotation",
  sendEmail: "Send Email",
  callCustomer: "Call Customer",
  viewDetails: "View Details",
  
  // Other fields
  taxNumber: "Tax Number",
  company: "Company",
  name: "Name",
  email: "Email",
  phone: "Phone",
  address: "Address",
  
  // Messages
  customerDeleted: "Customer deleted successfully",
  customerUpdated: "Customer updated successfully",
  customerAdded: "Customer added successfully",
  
  // New keys from the code block
  viewAllCustomers: "View All Customers",
  recentCustomers: "Recent Customers",
  latestCustomers: "Latest customers and their payment status",
  viewAll: "View All",
  customer: "Customer",
  contact: "Contact",
  outstanding: "Outstanding",
  lastOrder: "Last Order",
  status: "Status",
  actions: "Actions"
} as const; 