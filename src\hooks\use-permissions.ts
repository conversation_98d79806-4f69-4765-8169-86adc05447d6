import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

export interface Permission {
  id: string
  name: string
  nameAr?: string
  module: string
  action: string
  resource?: string
  description?: string
}

export interface UserPermissions {
  permissions: Permission[]
  permissionsByModule: Record<string, Permission[]>
  permissionMap: Record<string, boolean>
  loading: boolean
  error: string | null
}

export function usePermissions(): UserPermissions {
  const { data: session } = useSession()
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [permissionsByModule, setPermissionsByModule] = useState<Record<string, Permission[]>>({})
  const [permissionMap, setPermissionMap] = useState<Record<string, boolean>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadPermissions = async () => {
      if (!session?.user?.id) {
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/users/${session.user.id}/permissions`)
        if (!response.ok) {
          // If user permissions fail, provide default empty permissions
          console.warn('Failed to fetch user permissions, using empty permissions')
          setPermissions([])
          setPermissionsByModule({})
          setPermissionMap({})
          setLoading(false)
          return
        }

        const data = await response.json()
        setPermissions(data.permissions || [])
        setPermissionsByModule(data.permissionsByModule || {})
        setPermissionMap(data.permissionMap || {})
      } catch (err) {
        console.error('Error loading permissions:', err)
        // Provide empty permissions as fallback
        setPermissions([])
        setPermissionsByModule({})
        setPermissionMap({})
        setError(err instanceof Error ? err.message : 'Failed to load permissions')
      } finally {
        setLoading(false)
      }
    }

    loadPermissions()
  }, [session?.user?.id])

  return {
    permissions,
    permissionsByModule,
    permissionMap,
    loading,
    error,
  }
}

// Hook for checking specific permission
export function useHasPermission(module: string, action: string, resource?: string): boolean {
  const { permissionMap } = usePermissions()
  
  const key = resource 
    ? `${module}.${action}.${resource}`
    : `${module}.${action}`
    
  return permissionMap[key] || false
}

// Hook for checking multiple permissions (any)
export function useHasAnyPermission(
  permissions: Array<{ module: string; action: string; resource?: string }>
): boolean {
  const { permissionMap } = usePermissions()
  
  return permissions.some(({ module, action, resource }) => {
    const key = resource 
      ? `${module}.${action}.${resource}`
      : `${module}.${action}`
    return permissionMap[key]
  })
}

// Hook for checking multiple permissions (all)
export function useHasAllPermissions(
  permissions: Array<{ module: string; action: string; resource?: string }>
): boolean {
  const { permissionMap } = usePermissions()
  
  return permissions.every(({ module, action, resource }) => {
    const key = resource 
      ? `${module}.${action}.${resource}`
      : `${module}.${action}`
    return permissionMap[key]
  })
}

// Hook for checking if user can access a module
export function useCanAccessModule(module: string): boolean {
  const { permissionsByModule } = usePermissions()
  return (permissionsByModule[module]?.length || 0) > 0
}
