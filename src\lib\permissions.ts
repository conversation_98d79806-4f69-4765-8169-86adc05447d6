import { prisma } from './prisma'

export interface Permission {
  id: string
  name: string
  nameAr?: string
  module: string
  action: string
  resource?: string
  description?: string
}

export interface UserPermissions {
  permissions: Permission[]
  permissionsByModule: Record<string, Permission[]>
  permissionMap: Record<string, boolean>
}

// Get user permissions from database
export async function getUserPermissions(userId: string): Promise<UserPermissions | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRole: {
          include: {
            permissions: {
              where: {
                granted: true,
              },
              include: {
                permission: {
                  where: {
                    isActive: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    if (!user) {
      return null
    }

    // Extract permissions from role
    const permissions = user.userRole?.permissions
      .map(rp => rp.permission)
      .filter(p => p !== null) || []

    // Group permissions by module
    const permissionsByModule = permissions.reduce((acc, permission) => {
      if (!acc[permission.module]) {
        acc[permission.module] = []
      }
      acc[permission.module].push(permission)
      return acc
    }, {} as Record<string, Permission[]>)

    // Create permission map for quick checks
    const permissionMap = permissions.reduce((acc, permission) => {
      const key = permission.resource 
        ? `${permission.module}.${permission.action}.${permission.resource}`
        : `${permission.module}.${permission.action}`
      acc[key] = true
      return acc
    }, {} as Record<string, boolean>)

    return {
      permissions,
      permissionsByModule,
      permissionMap,
    }
  } catch (error) {
    console.error('Error fetching user permissions:', error)
    return null
  }
}

// Check if user has specific permission
export async function hasPermission(
  userId: string,
  module: string,
  action: string,
  resource?: string
): Promise<boolean> {
  try {
    const userPermissions = await getUserPermissions(userId)
    if (!userPermissions) return false

    const key = resource 
      ? `${module}.${action}.${resource}`
      : `${module}.${action}`

    return userPermissions.permissionMap[key] || false
  } catch (error) {
    console.error('Error checking permission:', error)
    return false
  }
}

// Check multiple permissions at once
export async function hasAnyPermission(
  userId: string,
  permissions: Array<{ module: string; action: string; resource?: string }>
): Promise<boolean> {
  try {
    const userPermissions = await getUserPermissions(userId)
    if (!userPermissions) return false

    return permissions.some(({ module, action, resource }) => {
      const key = resource 
        ? `${module}.${action}.${resource}`
        : `${module}.${action}`
      return userPermissions.permissionMap[key]
    })
  } catch (error) {
    console.error('Error checking permissions:', error)
    return false
  }
}

// Check if user has all specified permissions
export async function hasAllPermissions(
  userId: string,
  permissions: Array<{ module: string; action: string; resource?: string }>
): Promise<boolean> {
  try {
    const userPermissions = await getUserPermissions(userId)
    if (!userPermissions) return false

    return permissions.every(({ module, action, resource }) => {
      const key = resource 
        ? `${module}.${action}.${resource}`
        : `${module}.${action}`
      return userPermissions.permissionMap[key]
    })
  } catch (error) {
    console.error('Error checking permissions:', error)
    return false
  }
}

// Default permissions for different modules
export const DEFAULT_PERMISSIONS = {
  // Dashboard permissions
  DASHBOARD_VIEW: { module: 'dashboard', action: 'view' },
  
  // Customer permissions
  CUSTOMERS_VIEW: { module: 'customers', action: 'view' },
  CUSTOMERS_CREATE: { module: 'customers', action: 'create' },
  CUSTOMERS_EDIT: { module: 'customers', action: 'edit' },
  CUSTOMERS_DELETE: { module: 'customers', action: 'delete' },
  CUSTOMERS_EXPORT: { module: 'customers', action: 'export' },

  // Product permissions
  PRODUCTS_VIEW: { module: 'products', action: 'view' },
  PRODUCTS_CREATE: { module: 'products', action: 'create' },
  PRODUCTS_EDIT: { module: 'products', action: 'edit' },
  PRODUCTS_DELETE: { module: 'products', action: 'delete' },
  PRODUCTS_EXPORT: { module: 'products', action: 'export' },

  // Invoice permissions
  INVOICES_VIEW: { module: 'invoices', action: 'view' },
  INVOICES_CREATE: { module: 'invoices', action: 'create' },
  INVOICES_EDIT: { module: 'invoices', action: 'edit' },
  INVOICES_DELETE: { module: 'invoices', action: 'delete' },
  INVOICES_PRINT: { module: 'invoices', action: 'print' },

  // Task permissions
  TASKS_VIEW: { module: 'tasks', action: 'view' },
  TASKS_CREATE: { module: 'tasks', action: 'create' },
  TASKS_EDIT: { module: 'tasks', action: 'edit' },
  TASKS_DELETE: { module: 'tasks', action: 'delete' },
  TASKS_ASSIGN: { module: 'tasks', action: 'assign' },

  // Expense permissions
  EXPENSES_VIEW: { module: 'expenses', action: 'view' },
  EXPENSES_CREATE: { module: 'expenses', action: 'create' },
  EXPENSES_EDIT: { module: 'expenses', action: 'edit' },
  EXPENSES_DELETE: { module: 'expenses', action: 'delete' },
  EXPENSES_APPROVE: { module: 'expenses', action: 'approve' },

  // Settings permissions
  SETTINGS_VIEW: { module: 'settings', action: 'view' },
  SETTINGS_EDIT: { module: 'settings', action: 'edit' },

  // User management permissions
  USERS_VIEW: { module: 'users', action: 'view' },
  USERS_CREATE: { module: 'users', action: 'create' },
  USERS_EDIT: { module: 'users', action: 'edit' },
  USERS_DELETE: { module: 'users', action: 'delete' },

  // Role management permissions
  ROLES_VIEW: { module: 'roles', action: 'view' },
  ROLES_CREATE: { module: 'roles', action: 'create' },
  ROLES_EDIT: { module: 'roles', action: 'edit' },
  ROLES_DELETE: { module: 'roles', action: 'delete' },
}
