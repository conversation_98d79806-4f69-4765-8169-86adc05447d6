"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { Badge } from "@/components/ui/badge"
import { Plus, Search, Edit, Trash2, MoreHorizontal, Eye, BarChart3, FileSpreadsheet, Package, AlertTriangle } from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

interface Unit {
  id: string
  name: string
  nameAr?: string
  symbol: string
  symbolAr?: string
}

interface Category {
  id: string
  name: string
  nameAr?: string
}

interface Supplier {
  id: string
  name: string
}

interface Product {
  id: string
  name: string
  nameAr?: string
  sku?: string
  type: string
  price: number
  currentStock: number
  stock?: number
  minStock: number
  unit?: string
  isActive: boolean
  unitModel?: Unit
  category?: Category
  supplier?: Supplier
  image?: string
}

export default function ProductsPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [filterStatus, setFilterStatus] = useState("all")

  // Fetch products from API
  const fetchProducts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/products')
      if (response.ok) {
        const data = await response.json()
        setProducts(data.products || [])
      } else {
        toast.error('Failed to load products')
      }
    } catch (error) {
      console.error('Error fetching products:', error)
      toast.error('Error loading products')
    } finally {
      setLoading(false)
    }
  }

  // Load products on component mount and when returning from other pages
  useEffect(() => {
    fetchProducts()
  }, [])

  // Refresh products when window becomes visible/focused
  useEffect(() => {
    const handleFocus = () => {
      fetchProducts()
    }

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchProducts()
      }
    }

    window.addEventListener('focus', handleFocus)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('focus', handleFocus)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  // Refresh products when navigating back to this page
  useEffect(() => {
    const handlePageShow = () => {
      fetchProducts()
    }

    window.addEventListener('pageshow', handlePageShow)

    return () => {
      window.removeEventListener('pageshow', handlePageShow)
    }
  }, [])

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.category?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.supplier?.name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesType = filterType === "all" || product.type === filterType
    const matchesStatus = filterStatus === "all" ||
                         (filterStatus === "active" && product.isActive !== false) ||
                         (filterStatus === "inactive" && product.isActive === false) ||
                         (filterStatus === "low_stock" && isLowStock(product))

    return matchesSearch && matchesType && matchesStatus
  })

  const isLowStock = (product: Product) => {
    return product.type === 'PHYSICAL' && (product.currentStock || product.stock || 0) <= product.minStock
  }

  const handleViewProduct = (product: Product) => {
    router.push(`/dashboard/products/${product.id}`)
  }

  const handleEditProduct = (product: Product) => {
    router.push(`/dashboard/products/${product.id}/edit`)
  }

  const handleAddProduct = () => {
    router.push('/dashboard/products/create')
  }

  const handleDeleteProduct = async (product: Product) => {
    if (confirm(`${t('products.confirmDelete')}\n\n${product.name} - ${product.sku}`)) {
      try {
        const response = await fetch(`/api/products/${product.id}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          setProducts(products.filter(p => p.id !== product.id))
          toast.success(`🗑️ ${t('products.productDeleted')}: ${product.name}`)
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to delete product')
        }
      } catch (error) {
        console.error('Error deleting product:', error)
        toast.error('Error deleting product')
      }
    }
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('products.title')}</h2>
          <p className="text-muted-foreground">
            {t('products.manageProducts')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/products/dashboard')}>
            <BarChart3 className="mr-2 h-4 w-4" />
            {t('products.dashboard')}
          </Button>
          <Button onClick={handleAddProduct}>
            <Plus className="mr-2 h-4 w-4" />
            {t('products.addProduct')}
          </Button>
          <Button variant="outline" onClick={() => document.getElementById('excel-upload')?.click()}>
            <FileSpreadsheet className="mr-2 h-4 w-4" />
            {t('products.uploadExcel')}
          </Button>
          <input
            id="excel-upload"
            type="file"
            accept=".xlsx,.xls,.csv"
            className="hidden"
            onChange={(e) => {
              const file = e.target.files?.[0]
              if (file) {
                alert('Excel upload functionality would be implemented here')
                console.log('Excel file selected:', file.name)
              }
            }}
          />
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('products.searchProducts') || "Search products, SKU, category, or supplier..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={filterType} onValueChange={setFilterType}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="PHYSICAL">Physical Products</SelectItem>
            <SelectItem value="SERVICE">Services</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="low_stock">Low Stock</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="table-container">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-left font-semibold text-foreground">{t('common.actions')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('products.productName')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('products.sku')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('products.category')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('products.type')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('products.price')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('products.stock')}</TableHead>
              <TableHead className="font-semibold text-foreground">{t('products.supplier')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-12 text-muted-foreground">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="text-4xl">⏳</div>
                    <div className="text-lg font-medium">Loading products...</div>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredProducts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-12 text-muted-foreground">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="text-4xl">📦</div>
                    <div className="text-lg font-medium">No products found</div>
                    <div className="text-sm">{searchTerm ? 'Try a different search term' : 'Start by adding a new product'}</div>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredProducts.map((product) => (
              <TableRow key={product.id} className="hover:bg-muted/30 transition-colors">
                <TableCell className="text-left">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewProduct(product)}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t('products.viewProduct')}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditProduct(product)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('products.editProduct')}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteProduct(product)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('products.deleteProduct')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    {/* Product Image */}
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0 border">
                      {product.image ? (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="h-5 w-5 text-gray-400" />
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      {isLowStock(product) && (
                        <AlertTriangle className="h-4 w-4 text-orange-500" />
                      )}
                      <div>
                        <Button
                          variant="link"
                          className="p-0 h-auto font-medium text-primary hover:text-primary/80 hover:underline"
                          onClick={() => handleViewProduct(product)}
                        >
                          {product.name}
                        </Button>
                        <div className="text-sm text-muted-foreground" dir="rtl">
                          {product.nameAr}
                        </div>
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="font-mono text-sm">{product.sku || '-'}</TableCell>
                <TableCell>
                  <Badge variant="outline">{product.category?.name || 'No Category'}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={product.type === 'PHYSICAL' ? 'default' : 'secondary'}>
                    {product.type === 'PHYSICAL' ? t('products.physical') : t('products.service')}
                  </Badge>
                </TableCell>
                <TableCell className="font-medium">{formatCurrency(product.price)}</TableCell>
                <TableCell>
                  {product.type === 'PHYSICAL' ? (
                    <div className={isLowStock(product) ? 'text-orange-600 font-medium' : ''}>
                      <div className="flex flex-col">
                        <span>
                          {product.currentStock || product.stock || 0} {product.unitModel?.symbol || product.unit || 'pcs'}
                        </span>
                        {product.unitModel?.name && (
                          <span className="text-xs text-muted-foreground">
                            {product.unitModel.name}
                            {product.unitModel.nameAr && ` • ${product.unitModel.nameAr}`}
                          </span>
                        )}
                      </div>
                      {isLowStock(product) && (
                        <div className="text-xs text-orange-500 mt-1">{t('products.lowStockAlert')}</div>
                      )}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">N/A</span>
                  )}
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">{product.supplier?.name || 'No Supplier'}</TableCell>
              </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
