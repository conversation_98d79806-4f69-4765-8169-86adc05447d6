"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { CustomerSelector } from "@/components/ui/customer-selector"
import { Plus, CheckCircle, Clock, AlertTriangle, X, Edit, Eye } from "lucide-react"
import Link from "next/link"

interface Task {
  id: string
  title: string
  description?: string
  status: string
  priority: string
  estimatedHours?: number
  createdAt: string
  customer?: {
    id: string
    name: string
    company?: string
  }
  assignedTo?: {
    id: string
    name: string
    email: string
  }
}

interface User {
  id: string
  name: string
  email: string
}

interface ProjectTasksManagerProps {
  projectId: string
  isEditable?: boolean
}

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  IN_PROGRESS: "bg-blue-100 text-blue-800",
  COMPLETED: "bg-green-100 text-green-800",
  ON_HOLD: "bg-orange-100 text-orange-800",
  CANCELLED: "bg-red-100 text-red-800",
}

const priorityColors = {
  LOW: "bg-gray-100 text-gray-800",
  MEDIUM: "bg-blue-100 text-blue-800",
  HIGH: "bg-orange-100 text-orange-800",
  URGENT: "bg-red-100 text-red-800",
}

const statusIcons = {
  PENDING: Clock,
  IN_PROGRESS: AlertTriangle,
  COMPLETED: CheckCircle,
  ON_HOLD: X,
  CANCELLED: X,
}

export function ProjectTasksManager({ projectId, isEditable = true }: ProjectTasksManagerProps) {
  const [tasks, setTasks] = useState<Task[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)

  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    customerId: '',
    assignedToId: '',
    priority: 'MEDIUM',
    estimatedHours: '',
  })

  useEffect(() => {
    fetchTasks()
    if (isEditable) {
      fetchUsers()
    }
  }, [projectId, isEditable])

  const fetchTasks = async () => {
    try {
      const response = await fetch(`/api/tasks?projectId=${projectId}`)
      if (response.ok) {
        const data = await response.json()
        setTasks(data.tasks || [])
      }
    } catch (error) {
      console.error('Error fetching tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users')
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users || data)
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    }
  }

  const handleCreateTask = async () => {
    if (!newTask.title) return

    try {
      setCreating(true)
      const taskData = {
        ...newTask,
        projectId,
        customerId: newTask.customerId || null,
        assignedToId: newTask.assignedToId || null,
        estimatedHours: newTask.estimatedHours ? parseFloat(newTask.estimatedHours) : null,
      }

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(taskData),
      })

      if (response.ok) {
        await fetchTasks()
        setNewTask({
          title: '',
          description: '',
          customerId: '',
          assignedToId: '',
          priority: 'MEDIUM',
          estimatedHours: '',
        })
        setDialogOpen(false)
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error creating task:', error)
      alert('Error creating task')
    } finally {
      setCreating(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Project Tasks</h3>
          <Badge variant="secondary">{tasks.length}</Badge>
        </div>
        
        {isEditable && (
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Task
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Task</DialogTitle>
                <DialogDescription>
                  Add a new task to this project
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4 max-h-96 overflow-y-auto">
                <div>
                  <Label htmlFor="title">Task Title *</Label>
                  <Input
                    id="title"
                    placeholder="Enter task title"
                    value={newTask.title}
                    onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Enter task description"
                    value={newTask.description}
                    onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="customer">Customer</Label>
                    <CustomerSelector
                      value={newTask.customerId}
                      onValueChange={(value) => setNewTask(prev => ({ ...prev, customerId: value }))}
                      placeholder="Search and select customer..."
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Label htmlFor="assignedTo">Assign To</Label>
                    <Select value={newTask.assignedToId || "unassigned"} onValueChange={(value) => setNewTask(prev => ({ ...prev, assignedToId: value === "unassigned" ? "" : value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select assignee" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="unassigned">Unassigned</SelectItem>
                        {users.map(user => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.name} ({user.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select value={newTask.priority} onValueChange={(value) => setNewTask(prev => ({ ...prev, priority: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="LOW">Low</SelectItem>
                        <SelectItem value="MEDIUM">Medium</SelectItem>
                        <SelectItem value="HIGH">High</SelectItem>
                        <SelectItem value="URGENT">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="estimatedHours">Estimated Hours</Label>
                    <Input
                      id="estimatedHours"
                      type="number"
                      step="0.5"
                      placeholder="Enter estimated hours"
                      value={newTask.estimatedHours}
                      onChange={(e) => setNewTask(prev => ({ ...prev, estimatedHours: e.target.value }))}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button onClick={handleCreateTask} disabled={!newTask.title || creating} className="flex-1">
                    {creating ? 'Creating...' : 'Create Task'}
                  </Button>
                  <Button variant="outline" onClick={() => setDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {tasks.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <CheckCircle className="mx-auto h-12 w-12 mb-3 opacity-50" />
          <p>No tasks created yet</p>
          {isEditable && (
            <p className="text-sm">Click &quot;Add Task&quot; to create the first task</p>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {tasks.map((task) => {
            const StatusIcon = statusIcons[task.status as keyof typeof statusIcons]
            return (
              <div key={task.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-start gap-3 flex-1">
                  <StatusIcon className="h-5 w-5 mt-0.5 text-muted-foreground" />
                  
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{task.title}</h4>
                      <Badge variant="secondary" className={statusColors[task.status as keyof typeof statusColors]}>
                        {task.status.replace('_', ' ')}
                      </Badge>
                      <Badge variant="outline" className={priorityColors[task.priority as keyof typeof priorityColors]}>
                        {task.priority}
                      </Badge>
                    </div>
                    
                    {task.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">{task.description}</p>
                    )}
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Created {formatDate(task.createdAt)}</span>
                      {task.estimatedHours && <span>{task.estimatedHours}h estimated</span>}
                      {task.customer && <span>Client: {task.customer.name}</span>}
                      {task.assignedTo && <span>Assigned to: {task.assignedTo.name}</span>}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/dashboard/tasks/${task.id}`}>
                      <Eye className="h-4 w-4" />
                    </Link>
                  </Button>
                  {isEditable && (
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/dashboard/tasks/${task.id}/edit`}>
                        <Edit className="h-4 w-4" />
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
} 