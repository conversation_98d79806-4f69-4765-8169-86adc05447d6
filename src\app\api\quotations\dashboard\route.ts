import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get current date for calculations
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)

    // Get quotation statistics
    const [
      totalQuotations,
      pendingQuotations,
      approvedQuotations,
      convertedQuotations,
      thisMonthQuotations,
      lastMonthQuotations,
      totalValue,
      recentQuotations
    ] = await Promise.all([
      // Total quotations
      prisma.quotation.count(),
      
      // Pending quotations
      prisma.quotation.count({
        where: { status: 'PENDING' }
      }),
      
      // Approved quotations
      prisma.quotation.count({
        where: { status: 'APPROVED' }
      }),
      
      // Converted quotations
      prisma.quotation.count({
        where: { status: 'CONVERTED' }
      }),
      
      // This month quotations
      prisma.quotation.count({
        where: {
          createdAt: {
            gte: startOfMonth
          }
        }
      }),
      
      // Last month quotations
      prisma.quotation.count({
        where: {
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      }),
      
      // Total value
      prisma.quotation.aggregate({
        _sum: {
          total: true
        }
      }),
      
      // Recent quotations
      prisma.quotation.findMany({
        take: 10,
        include: {
          customer: true,
          user: true,
        },
        orderBy: {
          createdAt: 'desc'
        }
      })
    ])

    // Calculate conversion rate
    const conversionRate = totalQuotations > 0 
      ? ((convertedQuotations / totalQuotations) * 100).toFixed(1)
      : '0.0'

    // Calculate month-over-month growth
    const monthGrowth = lastMonthQuotations > 0
      ? (((thisMonthQuotations - lastMonthQuotations) / lastMonthQuotations) * 100).toFixed(1)
      : '0.0'

    // Get status breakdown
    const statusBreakdown = await prisma.quotation.groupBy({
      by: ['status'],
      _count: {
        status: true
      },
      _sum: {
        total: true
      }
    })

    // Format status breakdown
    const formattedStatusBreakdown = statusBreakdown.reduce((acc, item) => {
      acc[item.status.toLowerCase()] = {
        count: item._count.status,
        amount: item._sum.total || 0
      }
      return acc
    }, {} as Record<string, { count: number; amount: number }>)

    const stats = {
      overview: {
        totalQuotations,
        totalValue: totalValue._sum.total || 0,
        conversionRate: parseFloat(conversionRate),
        monthGrowth: parseFloat(monthGrowth),
        avgQuotationValue: totalQuotations > 0 
          ? (totalValue._sum.total || 0) / totalQuotations 
          : 0
      },
      statusBreakdown: {
        pending: formattedStatusBreakdown.pending || { count: 0, amount: 0 },
        approved: formattedStatusBreakdown.approved || { count: 0, amount: 0 },
        rejected: formattedStatusBreakdown.rejected || { count: 0, amount: 0 },
        converted: formattedStatusBreakdown.converted || { count: 0, amount: 0 },
        expired: formattedStatusBreakdown.expired || { count: 0, amount: 0 }
      },
      monthlyData: {
        thisMonth: thisMonthQuotations,
        lastMonth: lastMonthQuotations,
        growth: parseFloat(monthGrowth)
      }
    }

    return NextResponse.json({
      stats,
      recentQuotations
    })
  } catch (error) {
    console.error('Error fetching quotation dashboard data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
