export const customers = {
  // Page titles and descriptions
  title: "العملاء",
  manageCustomers: "إدارة العملاء",
  dashboard: "لوحة تحكم العملاء",
  dashboardDescription: "إدارة العملاء وعرض إحصائياتهم",
  
  // Customer management
  addCustomer: "إضافة عميل",
  editCustomer: "تعديل عميل",
  createCustomer: "إنشاء عميل",
  updateCustomer: "تحديث عميل",
  deleteCustomer: "حذف عميل",
  confirmDelete: "هل أنت متأكد من أنك تريد حذف هذا العميل؟",
  
  // Customer information
  customerName: "اسم العميل",
  customerEmail: "بريد العميل الإلكتروني",
  customerPhone: "هاتف العميل",
  customerCompany: "شركة العميل",
  customerAddress: "عنوان العميل",
  customerDetails: "تفاصيل العميل",
  customerInformation: "معلومات العميل",
  contactInformation: "معلومات الاتصال",
  customerNotFound: "العميل غير موجود",
  customerDetailsAndHistory: "تفاصيل العميل وتاريخ المعاملات",
  customerSince: "عميل منذ",
  lastOrderDate: "تاريخ آخر طلب",
  
  // Search and filters
  searchCustomers: "البحث في العملاء",
  noCustomersFound: "لم يتم العثور على عملاء",
  tryDifferentSearch: "جرب بحثاً مختلفاً",
  
  // Dashboard metrics
  repeatCustomers: "العملاء المتكررين",
  avgOrderValue: "متوسط قيمة الطلب",
  totalRevenue: "إجمالي الإيرادات",
  totalCustomers: "إجمالي العملاء",
  outstandingBalance: "الرصيد المستحق",
  topCustomers: "أفضل العملاء",
  inactiveCustomers: "العملاء غير النشطين",
  activeCustomers: "العملاء النشطين",
  customerTypeDistribution: "توزيع أنواع العملاء",
  monthlyCustomerGrowth: "نمو العملاء الشهري",
  
  // Dashboard descriptions
  highValueCustomers: "عملاء عالي القيمة",
  needReEngagement: "يحتاجون إعادة تفاعل",
  fromLastMonth: "من الشهر الماضي",
  thisMonth: "هذا الشهر",
  breakdownOfCustomersByType: "تفصيل العملاء حسب النوع",
  newCustomersAndRevenueOverLast5Months: "العملاء الجدد والإيرادات خلال آخر 5 أشهر",
  nanPercentOfTotal: "NaN% من الإجمالي",
  
  // Financial information
  totalSpent: "إجمالي الإنفاق",
  totalInvoices: "إجمالي الفواتير",
  totalQuotations: "إجمالي عروض الأسعار",
  customerSummary: "ملخص العميل",
  
  // Invoices and payments
  invoices: "الفواتير",
  customerInvoices: "فواتير العميل",
  invoiceNumber: "رقم الفاتورة",
  dueDate: "تاريخ الاستحقاق",
  balance: "الرصيد",
  paid: "مدفوع",
  noInvoicesFound: "لم يتم العثور على فواتير لهذا العميل",
  paymentHistory: "تاريخ المدفوعات",
  method: "الطريقة",
  reference: "المرجع",
  paymentStatus: "حالة الدفع",
  paidInvoices: "الفواتير المدفوعة",
  sentInvoices: "الفواتير المرسلة",
  overdueInvoices: "الفواتير المتأخرة",
  lastPayment: "آخر دفعة",
  
  // Quotations
  customerQuotations: "عروض أسعار العميل",
  quotationNumber: "رقم عرض السعر",
  validUntil: "صالح حتى",
  noQuotationsFound: "لم يتم العثور على عروض أسعار لهذا العميل",
  
  // Actions
  quickActions: "الإجراءات السريعة",
  createInvoice: "إنشاء فاتورة",
  createQuotation: "إنشاء عرض سعر",
  sendEmail: "إرسال بريد إلكتروني",
  callCustomer: "الاتصال بالعميل",
  viewDetails: "عرض التفاصيل",
  
  // Other fields
  taxNumber: "الرقم الضريبي",
  company: "الشركة",
  name: "الاسم",
  email: "البريد الإلكتروني",
  phone: "الهاتف",
  address: "العنوان",
  
  // Messages
  customerDeleted: "تم حذف العميل بنجاح",
  customerUpdated: "تم تحديث العميل بنجاح",
  customerAdded: "تم إضافة العميل بنجاح",
  viewAllCustomers: "عرض جميع العملاء",
  recentCustomers: "العملاء الجدد",
  latestCustomers: "أحدث العملاء وحالة الدفع الخاصة بهم",
  viewAll: "عرض الكل",
  customer: "العميل",
  contact: "جهة الاتصال",
  outstanding: "الرصيد المستحق",
  lastOrder: "آخر طلب",
  status: "الحالة",
  actions: "الإجراءات"
} as const; 