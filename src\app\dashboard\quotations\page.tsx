"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import { Plus, Search, Edit, Trash2, Download, Send, Receipt, MoreHorizontal, Eye, Printer, Mail, BarChart3 } from "lucide-react"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

const statusColors: { [key: string]: string } = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
  EXPIRED: "bg-gray-100 text-gray-800",
  CONVERTED: "bg-blue-100 text-blue-800",
}

export default function QuotationsPage() {
  const router = useRouter()
  const { t, formatCurrency } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [quotations, setQuotations] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  const quotationStatuses = ["PENDING", "APPROVED", "REJECTED", "EXPIRED", "CONVERTED"];

  useEffect(() => {
    const loadQuotations = async () => {
      setLoading(true)
      try {
        const response = await fetch('/api/quotations')
        if (response.ok) {
          const data = await response.json()
          setQuotations(Array.isArray(data) ? data : data.quotations || [])
        } else {
          toast.error(t('quotations.failedToFetch'))
          setQuotations([])
        }
      } catch (error) {
        console.error('Error loading quotations:', error)
        toast.error(t('quotations.loadingQuotations'))
        setQuotations([])
      } finally {
        setLoading(false)
      }
    }
    loadQuotations()
  }, [t])

  console.log("Quotations data from API:", quotations)

  const filteredQuotations = quotations.filter(quotation => {
    const customerName = typeof quotation.customer === 'object' && quotation.customer !== null
      ? quotation.customer.name
      : quotation.customer;

    const searchMatch =
      quotation.number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (customerName && customerName.toLowerCase().includes(searchTerm.toLowerCase()));

    const statusMatch = statusFilter === 'all' || quotation.status === statusFilter
    return searchMatch && statusMatch
  })

  const handleViewQuotation = (quotation: any) => router.push(`/dashboard/quotations/${quotation.id}`)
  const handleEditQuotation = (quotation: any) => router.push(`/dashboard/quotations/${quotation.id}/edit`)
  const handlePrintQuotation = (quotation: any) => router.push(`/dashboard/quotations/print/${quotation.id}`)

  const handleConvertToInvoice = (quotation: any) => {
    router.push(`/dashboard/invoices/create?quotation=${quotation.id}`)
  }

  const handleDeleteQuotation = async (quotation: any) => {
    const customerName = typeof quotation.customer === 'object' && quotation.customer !== null ? quotation.customer.name : quotation.customer;
    if (confirm(t('quotations.list.confirmDelete').replace('{number}', quotation.number).replace('{customer}', customerName))) {
      try {
        const response = await fetch(`/api/quotations/${quotation.id}`, { method: 'DELETE' })
        if (response.ok) {
          setQuotations(prev => prev.filter(q => q.id !== quotation.id))
          toast.success(t('quotations.list.deleteSuccess').replace('{number}', quotation.number))
        } else {
          const error = await response.json()
          toast.error(t('quotations.list.deleteFailed').replace('{error}', error.error))
        }
      } catch (error) {
        console.error('Error deleting quotation:', error)
        toast.error(t('quotations.list.deleteFailedGeneric'))
      }
    }
  }

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('quotations.title')}</h2>
          <p className="text-muted-foreground">{t('quotations.list.description')}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push('/dashboard/quotations/dashboard')}>
            <BarChart3 className="mr-2 h-4 w-4" />
            {t('quotations.dashboard')}
          </Button>
          <Button onClick={() => router.push('/dashboard/quotations/create')}>
            <Plus className="mr-2 h-4 w-4" />
            {t('quotations.createQuotation')}
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('quotations.searchQuotations')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('quotations.list.filterByStatus')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('quotations.list.allStatuses')}</SelectItem>
            {quotationStatuses.map((statusKey) => (
              <SelectItem key={statusKey} value={statusKey}>
                {t(`quotations.statusLabels.${statusKey}`)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="table-container rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('quotations.quotationNumber')}</TableHead>
              <TableHead>{t('quotations.customer')}</TableHead>
              <TableHead>{t('quotations.quotationDate')}</TableHead>
              <TableHead>{t('quotations.validUntil')}</TableHead>
              <TableHead>{t('quotations.status')}</TableHead>
              <TableHead className="text-right">{t('quotations.total')}</TableHead>
              <TableHead className="text-center">{t('common.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span>{t('quotations.list.loading')}</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredQuotations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm ? t('quotations.list.emptySearch') : t('quotations.list.empty')}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredQuotations.map((quotation) => (
                <TableRow key={quotation.id}>
                  <TableCell className="font-medium">
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium"
                      onClick={() => handleViewQuotation(quotation)}
                    >
                      {quotation.number}
                    </Button>
                  </TableCell>
                  <TableCell>{typeof quotation.customer === 'object' && quotation.customer !== null ? quotation.customer.name : quotation.customer}</TableCell>
                  <TableCell>{quotation.date}</TableCell>
                  <TableCell>{quotation.validUntil}</TableCell>
                  <TableCell>
                    <Badge className={`${statusColors[quotation.status as keyof typeof statusColors]} hover:bg-opacity-80`}>
                      {t(`quotations.statusLabels.${quotation.status}`)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right font-medium">{formatCurrency(Number(quotation.total))}</TableCell>
                  <TableCell className="text-center">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">{t('quotations.list.openMenu')}</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewQuotation(quotation)}>
                          <Eye className="mr-2 h-4 w-4" />
                          {t('quotations.quotationDetails')}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditQuotation(quotation)}>
                          <Edit className="mr-2 h-4 w-4" />
                          {t('quotations.editQuotation')}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handlePrintQuotation(quotation)}>
                          <Printer className="mr-2 h-4 w-4" />
                          {t('quotations.printQuotation')}
                        </DropdownMenuItem>
                        <DropdownMenuItem disabled>
                          <Mail className="mr-2 h-4 w-4" />
                          {t('quotations.emailQuotation')}
                        </DropdownMenuItem>
                        <DropdownMenuItem disabled>
                          <Download className="mr-2 h-4 w-4" />
                          {t('quotations.downloadQuotation')}
                        </DropdownMenuItem>
                        <DropdownMenuItem disabled>
                          <Send className="mr-2 h-4 w-4" />
                          {t('quotations.sendToCustomer')}
                        </DropdownMenuItem>
                        {['PENDING', 'APPROVED'].includes(quotation.status) && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleConvertToInvoice(quotation)}
                              className="text-green-600 focus:text-green-600"
                            >
                              <Receipt className="mr-2 h-4 w-4" />
                              {t('quotations.convertToInvoice')}
                            </DropdownMenuItem>
                          </>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteQuotation(quotation)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          {t('quotations.deleteQuotation')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
