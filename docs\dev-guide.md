# Development Guide

## Overview
This is the main index for all technical documentation in the project management system.

## Documentation Files

### Project Management
- [Project Management Fixes](./project-management-fixes.md) - Comprehensive fixes for budget calculations, cost tracking, and progress management

### Invoicing System
- [Invoicing System Fixes](./invoicing-system-fixes.md) - Complete fixes for payment recording, task integration, and invoice actions
- [Invoice Creation Translation Update](./translations/invoice-creation-translation-update.md) - Comprehensive Arabic translation for invoice creation components

### Features
- [Arabization Implementation](./features/arabization.md) - Complete Arabic/RTL support implementation
- [Arabization Examples](./features/arabization-examples.md) - Practical code examples for RTL components

### Bug Fixes
- [Project Management Issues](./project-management-fixes.md) - Fixed budget display, automatic cost calculation, and progress tracking
- [Tasks API Fix](./troubleshooting/tasks-api-fix.md) - Fixed database import issue causing "Failed to fetch tasks" error

### Technical Specifications
- Coming soon...

## Quick Reference

### Critical Path Documentation
- Project Management System fixes and improvements
- Automatic calculation implementations
- Real-time budget and progress tracking

### Major Features
- **Arabization System**: Complete Arabic/RTL support with bilingual forms, RTL tables, and Arabic typography
- **RTL Component Library**: Reusable components for consistent RTL behavior across the application
- Automated project cost calculation from expenses
- Automatic progress calculation from task completion
- Real-time budget usage tracking
- Enhanced financial overview components

## Development Guidelines

### Documentation Requirements
- All major features must have corresponding documentation
- Bug fixes should be documented with before/after states
- API changes require updated technical specifications
- UI/UX changes need user guide updates

### File Naming Convention
- Feature documentation: `features/<feature-name>.md`
- Bug fix documentation: `bugfix-<issue-description>.md`
- Technical specs: `specs/<component-name>.md`
- User guides: `guides/<guide-name>.md`

## Recent Updates

### 2024-12-19
- **Tasks API Fix**: Fixed database import issue causing "Failed to fetch tasks" error in invoice creation
- **Invoice Creation Translation**: Complete Arabic translation for invoice creation components including forms, dialogs, and validation messages
- **Arabization Implementation**: Complete Arabic/RTL support with enhanced table components, comprehensive translations, and RTL utility library
- **RTL Component System**: Created RTLWrapper, RTLText, RTLNumeric components and useRTLClasses hook for consistent RTL behavior
- **Enhanced Translations**: Extended Arabic translations for UI elements, tables, and forms with proper terminology
- **Project Management Fixes**: Implemented comprehensive fixes for budget calculations, cost tracking, and progress management
- **Automatic Calculations**: Added real-time calculation of project costs and progress
- **UI Improvements**: Made calculated fields read-only with proper user feedback

## Contributing

When adding new features or fixing bugs:

1. Update relevant documentation before code implementation
2. Follow the documentation templates in `/docs/templates/`
3. Add references to this index file
4. Ensure all code changes are documented with examples

## Support

For questions about the documentation or development guidelines, refer to the specific documentation files or contact the development team. 