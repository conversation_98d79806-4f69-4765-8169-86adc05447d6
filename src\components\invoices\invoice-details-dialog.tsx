"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  FileText, 
  Download, 
  Mail, 
  Printer, 
  Edit, 
  CreditCard,
  Calendar,
  User,
  DollarSign,
  Clock
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"

interface Invoice {
  id: string
  number: string
  date: string
  dueDate: string
  customer: string
  status: 'PAID' | 'UNPAID' | 'PARTIAL' | 'OVERDUE'
  subtotal: number
  taxAmount: number
  total: number
  amountPaid?: number
}

interface InvoiceDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  invoice: Invoice
}

export function InvoiceDetailsDialog({ open, onOpenChange, invoice }: InvoiceDetailsDialogProps) {
  const [loading, setLoading] = useState(false)

  const statusColors = {
    PAID: "bg-green-100 text-green-800",
    UNPAID: "bg-red-100 text-red-800", 
    PARTIAL: "bg-yellow-100 text-yellow-800",
    OVERDUE: "bg-purple-100 text-purple-800",
  }

  const balance = invoice.total - (invoice.amountPaid || 0)
  const isOverdue = invoice.status === 'OVERDUE'
  const daysPastDue = isOverdue ? Math.floor((new Date().getTime() - new Date(invoice.dueDate).getTime()) / (1000 * 60 * 60 * 24)) : 0

  // Mock invoice items - in real app, this would be fetched from API
  const invoiceItems = [
    { id: "1", description: "Business Cards - Premium", quantity: 500, unitPrice: 0.25, total: 125.00 },
    { id: "2", description: "A4 Color Printing", quantity: 100, unitPrice: 0.75, total: 75.00 },
    { id: "3", description: "Document Binding", quantity: 5, unitPrice: 8.00, total: 40.00 },
  ]

  // Mock payment history
  const paymentHistory = invoice.status === 'PAID' ? [
    { id: "1", date: "2024-01-20", amount: invoice.total, method: "Bank Transfer", reference: "TXN-001" }
  ] : invoice.status === 'PARTIAL' ? [
    { id: "1", date: "2024-01-18", amount: invoice.amountPaid || 0, method: "Cash", reference: "CASH-001" }
  ] : []

  const handlePrint = () => {
    setLoading(true)
    // Simulate PDF generation
    setTimeout(() => {
      console.log('Generating PDF for invoice:', invoice.number)
      setLoading(false)
    }, 1000)
  }

  const handleEmail = () => {
    setLoading(true)
    // Simulate email sending
    setTimeout(() => {
      console.log('Sending email for invoice:', invoice.number)
      setLoading(false)
    }, 1000)
  }

  const handleDownload = () => {
    console.log('Downloading invoice:', invoice.number)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl">Invoice {invoice.number}</DialogTitle>
              <DialogDescription>
                View invoice details, payment history, and manage actions
              </DialogDescription>
            </div>
            <Badge className={statusColors[invoice.status]}>
              {invoice.status}
            </Badge>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Invoice Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Customer</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">{invoice.customer}</div>
                <p className="text-xs text-muted-foreground">
                  Invoice Date: {new Date(invoice.date).toLocaleDateString()}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Amount</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">{formatCurrency(invoice.total)}</div>
                <p className="text-xs text-muted-foreground">
                  Paid: {formatCurrency(invoice.amountPaid || 0)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Due Date</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">{new Date(invoice.dueDate).toLocaleDateString()}</div>
                {isOverdue && (
                  <p className="text-xs text-red-600">
                    {daysPastDue} days overdue
                  </p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Balance Alert */}
          {balance > 0 && (
            <div className={`p-4 rounded-lg border ${isOverdue ? 'bg-red-50 border-red-200' : 'bg-yellow-50 border-yellow-200'}`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className={`h-5 w-5 ${isOverdue ? 'text-red-600' : 'text-yellow-600'}`} />
                  <div>
                    <p className={`font-medium ${isOverdue ? 'text-red-800' : 'text-yellow-800'}`}>
                      Outstanding Balance: {formatCurrency(balance)}
                    </p>
                    <p className={`text-sm ${isOverdue ? 'text-red-600' : 'text-yellow-600'}`}>
                      {isOverdue ? `Payment is ${daysPastDue} days overdue` : 'Payment is pending'}
                    </p>
                  </div>
                </div>
                <Button size="sm" variant={isOverdue ? "destructive" : "default"}>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Record Payment
                </Button>
              </div>
            </div>
          )}

          {/* Invoice Items */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Invoice Items</h3>
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-center">Quantity</TableHead>
                    <TableHead className="text-right">Unit Price</TableHead>
                    <TableHead className="text-right">Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoiceItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.description}</TableCell>
                      <TableCell className="text-center">{item.quantity}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
                      <TableCell className="text-right">{formatCurrency(item.total)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Totals */}
            <div className="mt-4 flex justify-end">
              <div className="w-80 space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(invoice.subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax (5%):</span>
                  <span>{formatCurrency(invoice.taxAmount)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold text-lg">
                  <span>Total:</span>
                  <span>{formatCurrency(invoice.total)}</span>
                </div>
                {invoice.amountPaid && invoice.amountPaid > 0 && (
                  <>
                    <div className="flex justify-between text-green-600">
                      <span>Amount Paid:</span>
                      <span>{formatCurrency(invoice.amountPaid)}</span>
                    </div>
                    <div className="flex justify-between font-medium text-orange-600">
                      <span>Balance Due:</span>
                      <span>{formatCurrency(balance)}</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Payment History */}
          {paymentHistory.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Payment History</h3>
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead>Reference</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paymentHistory.map((payment) => (
                      <TableRow key={payment.id}>
                        <TableCell>{new Date(payment.date).toLocaleDateString()}</TableCell>
                        <TableCell className="text-green-600 font-medium">
                          {formatCurrency(payment.amount)}
                        </TableCell>
                        <TableCell>{payment.method}</TableCell>
                        <TableCell className="font-mono text-sm">{payment.reference}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex flex-wrap gap-2 pt-4 border-t">
            <Button onClick={handlePrint} disabled={loading}>
              <Printer className="mr-2 h-4 w-4" />
              {loading ? "Generating..." : "Print/PDF"}
            </Button>
            
            <Button variant="outline" onClick={handleEmail} disabled={loading}>
              <Mail className="mr-2 h-4 w-4" />
              Email Invoice
            </Button>
            
            <Button variant="outline" onClick={handleDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
            
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              Edit Invoice
            </Button>
            
            {balance > 0 && (
              <Button variant="default">
                <CreditCard className="mr-2 h-4 w-4" />
                Record Payment
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
