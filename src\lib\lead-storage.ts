/**
 * Lead API utility functions
 */

export interface Lead {
  id: string
  name: string
  nameAr?: string
  email?: string
  mobile: string
  phone?: string
  address?: string
  city?: string
  country?: string
  company?: string
  source: 'WEBSITE' | 'PHONE' | 'EMAIL' | 'REFERRAL' | 'SOCIAL_MEDIA' | 'ADVERTISEMENT' | 'WALK_IN' | 'OTHER'
  status: 'NEW' | 'CONTACTED' | 'QUALIFIED' | 'PROPOSAL' | 'NEGOTIATION' | 'CONVERTED' | 'LOST' | 'UNQUALIFIED'
  notes?: string
  assignedToId?: string
  assignedTo?: any
  convertedAt?: string
  customerId?: string
  customer?: any
  createdAt: string
  updatedAt: string
}

export interface LeadStats {
  totalLeads: number
  newLeads: number
  qualifiedLeads: number
  convertedLeads: number
  lostLeads: number
  conversionRate: number
  averageLeadValue: number
  leadsBySource: Record<string, number>
  leadsByStatus: Record<string, number>
  recentLeads: Lead[]
}

// Get all leads from API
export const getLeads = async (params?: {
  search?: string
  status?: string
  source?: string
  assignedToId?: string
  page?: number
  limit?: number
}): Promise<{ leads: Lead[]; pagination: any }> => {
  try {
    const searchParams = new URLSearchParams()
    
    if (params?.search) searchParams.append('search', params.search)
    if (params?.status) searchParams.append('status', params.status)
    if (params?.source) searchParams.append('source', params.source)
    if (params?.assignedToId) searchParams.append('assignedToId', params.assignedToId)
    if (params?.page) searchParams.append('page', params.page.toString())
    if (params?.limit) searchParams.append('limit', params.limit.toString())

    const response = await fetch(`/api/leads?${searchParams.toString()}`)
    if (!response.ok) {
      throw new Error('Failed to fetch leads')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading leads:', error)
    return { leads: [], pagination: { page: 1, limit: 10, total: 0, pages: 0 } }
  }
}

// Get lead by ID
export const getLeadById = async (id: string): Promise<Lead | null> => {
  try {
    const response = await fetch(`/api/leads/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch lead')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading lead:', error)
    return null
  }
}

// Create new lead
export const createLead = async (lead: {
  name: string
  nameAr?: string
  email?: string
  mobile: string
  phone?: string
  address?: string
  city?: string
  country?: string
  company?: string
  source?: string
  status?: string
  notes?: string
  assignedToId?: string
}): Promise<Lead | null> => {
  try {
    const response = await fetch('/api/leads', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(lead),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to create lead')
    }

    return await response.json()
  } catch (error) {
    console.error('Error creating lead:', error)
    throw error
  }
}

// Update lead
export const updateLead = async (
  id: string,
  updates: Partial<Lead>
): Promise<Lead | null> => {
  try {
    const response = await fetch(`/api/leads/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update lead')
    }

    return await response.json()
  } catch (error) {
    console.error('Error updating lead:', error)
    throw error
  }
}

// Delete lead
export const deleteLead = async (id: string): Promise<boolean> => {
  try {
    const response = await fetch(`/api/leads/${id}`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete lead')
    }

    return true
  } catch (error) {
    console.error('Error deleting lead:', error)
    throw error
  }
}

// Convert lead to customer
export const convertLeadToCustomer = async (id: string): Promise<{ lead: Lead; customer: any } | null> => {
  try {
    const response = await fetch(`/api/leads/${id}/convert`, {
      method: 'POST',
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to convert lead')
    }

    return await response.json()
  } catch (error) {
    console.error('Error converting lead:', error)
    throw error
  }
}

// Get lead statistics
export const getLeadStats = async (period?: string): Promise<LeadStats | null> => {
  try {
    const searchParams = new URLSearchParams()
    if (period) searchParams.append('period', period)

    const response = await fetch(`/api/leads/stats?${searchParams.toString()}`)
    if (!response.ok) {
      throw new Error('Failed to fetch lead statistics')
    }
    return await response.json()
  } catch (error) {
    console.error('Error loading lead statistics:', error)
    return null
  }
}

// Get leads by status
export const getLeadsByStatus = async (status: string): Promise<Lead[]> => {
  const result = await getLeads({ status, limit: 100 })
  return result.leads
}

// Get leads by source
export const getLeadsBySource = async (source: string): Promise<Lead[]> => {
  const result = await getLeads({ source, limit: 100 })
  return result.leads
}

// Get leads assigned to user
export const getLeadsByAssignee = async (assignedToId: string): Promise<Lead[]> => {
  const result = await getLeads({ assignedToId, limit: 100 })
  return result.leads
}

// Search leads
export const searchLeads = async (query: string): Promise<Lead[]> => {
  const result = await getLeads({ search: query, limit: 50 })
  return result.leads
}

// Lead source options
export const LEAD_SOURCES = [
  { value: 'WEBSITE', label: 'Website' },
  { value: 'PHONE', label: 'Phone' },
  { value: 'EMAIL', label: 'Email' },
  { value: 'REFERRAL', label: 'Referral' },
  { value: 'SOCIAL_MEDIA', label: 'Social Media' },
  { value: 'ADVERTISEMENT', label: 'Advertisement' },
  { value: 'WALK_IN', label: 'Walk In' },
  { value: 'OTHER', label: 'Other' },
]

// Lead status options
export const LEAD_STATUSES = [
  { value: 'NEW', label: 'New' },
  { value: 'CONTACTED', label: 'Contacted' },
  { value: 'QUALIFIED', label: 'Qualified' },
  { value: 'PROPOSAL', label: 'Proposal' },
  { value: 'NEGOTIATION', label: 'Negotiation' },
  { value: 'CONVERTED', label: 'Converted' },
  { value: 'LOST', label: 'Lost' },
  { value: 'UNQUALIFIED', label: 'Unqualified' },
]
