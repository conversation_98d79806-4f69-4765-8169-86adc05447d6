import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

// GET /api/expense-types/[id] - Get expense type by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const expenseType = await prisma.expenseType.findUnique({
      where: { id },
      include: {
        expenses: {
          orderBy: { createdAt: 'desc' },
          take: 10, // Get last 10 expenses
        },
        _count: {
          select: {
            expenses: true,
          },
        },
      },
    })

    if (!expenseType) {
      return NextResponse.json(
        { error: 'Expense type not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(expenseType)
  } catch (error) {
    console.error('Error fetching expense type:', error)
    return NextResponse.json(
      { error: 'Failed to fetch expense type' },
      { status: 500 }
    )
  }
}

// PUT /api/expense-types/[id] - Update expense type
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const { name, nameAr, description, isActive } = body

    // Check if expense type exists
    const existingType = await prisma.expenseType.findUnique({
      where: { id },
    })

    if (!existingType) {
      return NextResponse.json(
        { error: 'Expense type not found' },
        { status: 404 }
      )
    }

    // Check if name already exists for other expense types
    if (name && name !== existingType.name) {
      const nameExists = await prisma.expenseType.findFirst({
        where: {
          name,
          id: { not: id },
        },
      })

      if (nameExists) {
        return NextResponse.json(
          { error: 'Expense type name already exists' },
          { status: 400 }
        )
      }
    }

    // Update expense type
    const updatedType = await prisma.expenseType.update({
      where: { id },
      data: {
        name,
        nameAr,
        description,
        isActive,
        updatedAt: new Date(),
      },
      include: {
        _count: {
          select: {
            expenses: true,
          },
        },
      },
    })

    return NextResponse.json(updatedType)
  } catch (error) {
    console.error('Error updating expense type:', error)
    return NextResponse.json(
      { error: 'Failed to update expense type' },
      { status: 500 }
    )
  }
}

// DELETE /api/expense-types/[id] - Delete expense type
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    // Check if expense type exists
    const existingType = await prisma.expenseType.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            expenses: true,
          },
        },
      },
    })

    if (!existingType) {
      return NextResponse.json(
        { error: 'Expense type not found' },
        { status: 404 }
      )
    }

    // Check if expense type has associated expenses
    if (existingType._count.expenses > 0) {
      return NextResponse.json(
        { error: 'Cannot delete expense type with associated expenses' },
        { status: 400 }
      )
    }

    // Delete expense type
    await prisma.expenseType.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Expense type deleted successfully' })
  } catch (error) {
    console.error('Error deleting expense type:', error)
    return NextResponse.json(
      { error: 'Failed to delete expense type' },
      { status: 500 }
    )
  }
}
