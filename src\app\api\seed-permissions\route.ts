import { NextRequest, NextResponse } from 'next/server'
import { seedPermissionsAndRoles } from '@/lib/seed-permissions'

export async function POST() {
  try {
    const success = await seedPermissionsAndRoles()
    
    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Permissions and roles seeded successfully',
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to seed permissions and roles',
          timestamp: new Date().toISOString()
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Seeding error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Seeding failed', 
        details: error.message,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
