"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { RoleForm } from "./role-form"
import { useI18n } from "@/lib/i18n"
import { toast } from "sonner"

interface Role {
  id?: string
  name: string
  nameAr?: string
  description?: string
  permissions?: Array<{
    permission: {
      id: string
      name: string
      nameAr?: string
      module: string
      action: string
      resource?: string
      description?: string
    }
  }>
}

interface RoleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  role?: Role
  onSuccess: () => void
}

export function RoleDialog({ open, onOpenChange, role, onSuccess }: RoleDialogProps) {
  const { direction } = useI18n()
  const [loading, setLoading] = useState(false)

  const handleSave = async (roleData: any) => {
    try {
      setLoading(true)
      
      const url = role?.id ? '/api/roles' : '/api/roles'
      const method = role?.id ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(roleData),
      })

      if (response.ok) {
        toast.success(
          role?.id 
            ? (direction === 'rtl' ? 'تم تحديث الدور بنجاح' : 'Role updated successfully')
            : (direction === 'rtl' ? 'تم إنشاء الدور بنجاح' : 'Role created successfully')
        )
        onSuccess()
        onOpenChange(false)
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to save role')
      }
    } catch (error) {
      console.error('Error saving role:', error)
      toast.error(direction === 'rtl' ? 'حدث خطأ أثناء حفظ الدور' : 'Error saving role')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {role?.id 
              ? (direction === 'rtl' ? 'تعديل الدور' : 'Edit Role')
              : (direction === 'rtl' ? 'إضافة دور جديد' : 'Add New Role')
            }
          </DialogTitle>
          <DialogDescription>
            {role?.id 
              ? (direction === 'rtl' 
                  ? 'قم بتعديل معلومات الدور والصلاحيات' 
                  : 'Modify the role information and permissions'
                )
              : (direction === 'rtl' 
                  ? 'أنشئ دور جديد وحدد الصلاحيات المناسبة' 
                  : 'Create a new role and assign appropriate permissions'
                )
            }
          </DialogDescription>
        </DialogHeader>
        
        <RoleForm
          role={role}
          onSave={handleSave}
          onCancel={handleCancel}
          loading={loading}
        />
      </DialogContent>
    </Dialog>
  )
}
