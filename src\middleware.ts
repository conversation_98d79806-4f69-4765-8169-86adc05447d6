import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // Redirect authenticated users away from auth pages
    if (pathname.startsWith('/auth') && token) {
      return NextResponse.redirect(new URL('/dashboard', req.url))
    }

    // Check for admin-only routes
    const adminRoutes = ['/dashboard/users', '/dashboard/roles', '/dashboard/settings']
    if (adminRoutes.some(route => pathname.startsWith(route))) {
      if (token?.role !== 'ADMIN') {
        return NextResponse.redirect(new URL('/dashboard', req.url))
      }
    }

    // Check for manager+ routes
    const managerRoutes = ['/dashboard/reports', '/dashboard/analytics']
    if (managerRoutes.some(route => pathname.startsWith(route))) {
      if (!['ADMIN', 'MANAGER'].includes(token?.role as string)) {
        return NextResponse.redirect(new URL('/dashboard', req.url))
      }
    }

    // Add security headers
    const response = NextResponse.next()
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

    return response
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // Allow access to auth pages without token
        if (pathname.startsWith('/auth')) {
          return true
        }

        // Allow access to API auth routes
        if (pathname.startsWith('/api/auth')) {
          return true
        }

        // Allow access to public API routes (settings for login page)
        if (pathname === '/api/settings') {
          return true
        }

        // Require valid token for protected routes
        if (pathname.startsWith('/dashboard') || pathname.startsWith('/api')) {
          if (!token) return false

          // Check if token is still valid (not expired)
          if (token.lastActivity && Date.now() - (token.lastActivity as number) > 8 * 60 * 60 * 1000) {
            return false
          }

          return !!token.id && !!token.role
        }

        // Allow access to public routes
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    '/dashboard/:path*',
    '/auth/:path*',
    '/api/:path*',
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ]
}
