"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  CreditCard, 
  DollarSign, 
  Calculator,
  AlertCircle,
  CheckCircle,
  Calendar,
  FileText
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"

interface Invoice {
  id: string
  number: string
  date: string
  dueDate: string
  customer: string
  status: 'PAID' | 'UNPAID' | 'PARTIAL' | 'OVERDUE'
  subtotal: number
  taxAmount: number
  total: number
  amountPaid?: number
}

interface PaymentDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  invoice: Invoice
  onPaymentSuccess: () => void
}

export function PaymentDialog({ open, onOpenChange, invoice, onPaymentSuccess }: PaymentDialogProps) {
  const [loading, setLoading] = useState(false)
  const [paymentData, setPaymentData] = useState({
    amount: "",
    method: "",
    reference: "",
    notes: "",
    date: new Date().toISOString().split('T')[0]
  })
  
  const balance = invoice.total - (invoice.amountPaid || 0)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!paymentData.amount || !paymentData.method) {
      alert('Please fill in all required fields')
      return
    }

    if (parseFloat(paymentData.amount) <= 0) {
      alert('Payment amount must be greater than 0')
      return
    }

    if (parseFloat(paymentData.amount) > balance) {
      alert(`Payment amount cannot exceed remaining balance of ${formatCurrency(balance)}`)
      return
    }

    try {
      setLoading(true)
      
      const response = await fetch(`/api/invoices/${invoice.id}/payments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(paymentData.amount),
          method: paymentData.method,
          reference: paymentData.reference,
          notes: paymentData.notes,
          date: paymentData.date,
        }),
      })

      if (response.ok) {
        const result = await response.json()
        alert('✅ Payment recorded successfully!')
        
        // Reset form
        setPaymentData({
          amount: "",
          method: "",
          reference: "",
          notes: "",
          date: new Date().toISOString().split('T')[0]
        })
        
        onPaymentSuccess()
        onOpenChange(false)
      } else {
        const error = await response.json()
        alert(`❌ Failed to record payment: ${error.error}`)
      }
    } catch (error) {
      console.error('Error recording payment:', error)
      alert('❌ Failed to record payment')
    } finally {
      setLoading(false)
    }
  }

  const paymentMethods = [
    { value: 'CASH', label: 'Cash' },
    { value: 'CARD', label: 'Credit/Debit Card' },
    { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
    { value: 'CHECK', label: 'Check' },
    { value: 'OTHER', label: 'Other' },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Record Payment
          </DialogTitle>
          <DialogDescription>
            Record a payment for invoice {invoice.number}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Invoice Info */}
          <div className="bg-gray-50 p-3 rounded-lg space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Customer:</span>
              <span className="font-medium">{invoice.customer}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Invoice Total:</span>
              <span className="font-medium">{formatCurrency(invoice.total)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Amount Paid:</span>
              <span className="font-medium text-green-600">{formatCurrency(invoice.amountPaid || 0)}</span>
            </div>
            <div className="flex justify-between text-sm border-t pt-2">
              <span className="text-gray-600">Outstanding Balance:</span>
              <span className="font-bold text-orange-600">{formatCurrency(balance)}</span>
            </div>
          </div>

          {/* Payment Amount */}
          <div className="space-y-2">
            <Label htmlFor="amount">Payment Amount (OMR) *</Label>
            <div className="relative">
              <span className="absolute left-3 top-2.5 text-sm text-gray-400 font-medium">OMR</span>
              <Input
                id="amount"
                type="number"
                step="0.001"
                max={balance}
                placeholder="0.000"
                value={paymentData.amount}
                onChange={(e) => setPaymentData(prev => ({ ...prev, amount: e.target.value }))}
                className="pl-12"
                required
              />
            </div>
            <p className="text-xs text-gray-500">
              Maximum: {formatCurrency(balance)}
            </p>
          </div>

          {/* Payment Method */}
          <div className="space-y-2">
            <Label htmlFor="method">Payment Method *</Label>
            <Select value={paymentData.method} onValueChange={(value) => setPaymentData(prev => ({ ...prev, method: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent>
                {paymentMethods.map((method) => (
                  <SelectItem key={method.value} value={method.value}>
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Payment Date */}
          <div className="space-y-2">
            <Label htmlFor="date">Payment Date</Label>
            <div className="relative">
              <Calendar className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                id="date"
                type="date"
                value={paymentData.date}
                onChange={(e) => setPaymentData(prev => ({ ...prev, date: e.target.value }))}
                className="pl-10"
              />
            </div>
          </div>

          {/* Reference */}
          <div className="space-y-2">
            <Label htmlFor="reference">Reference Number</Label>
            <Input
              id="reference"
              placeholder="Transaction ID, check number, etc."
              value={paymentData.reference}
              onChange={(e) => setPaymentData(prev => ({ ...prev, reference: e.target.value }))}
            />
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Additional notes about this payment..."
              value={paymentData.notes}
              onChange={(e) => setPaymentData(prev => ({ ...prev, notes: e.target.value }))}
              rows={2}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Recording...
                </>
              ) : (
                <>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Record Payment
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
