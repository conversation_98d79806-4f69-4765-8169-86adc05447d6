"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Plus, Receipt, Eye, DollarSign, Clock, CheckCircle, X, Edit, MoreHorizontal, Settings, Info } from "lucide-react"
import Link from "next/link"
import { formatCurrency } from "@/lib/localization"
import { useSession } from "next-auth/react"

interface Expense {
  id: string
  number: string
  date: string
  description: string
  amount: number
  status: string
  paymentMethod: string
  expenseType: {
    id: string
    name: string
  }
  createdBy: {
    id: string
    name: string
  }
}

interface ExpenseType {
  id: string
  name: string
  nameAr?: string
}

interface ProjectExpensesManagerProps {
  projectId: string
  isEditable?: boolean
}

const statusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800",
  REJECTED: "bg-red-100 text-red-800",
  PAID: "bg-green-100 text-green-800",
}

const statusIcons = {
  PENDING: Clock,
  APPROVED: CheckCircle,
  REJECTED: X,
  PAID: CheckCircle,
}

const statusLabels = {
  PENDING: "Pending",
  APPROVED: "Approved", 
  REJECTED: "Rejected",
  PAID: "Processed",
}

export function ProjectExpensesManager({ projectId, isEditable = true }: ProjectExpensesManagerProps) {
  const { data: session } = useSession()
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [expenseTypes, setExpenseTypes] = useState<ExpenseType[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [statusDialogOpen, setStatusDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null)
  const [stats, setStats] = useState({
    total: 0,
    totalAmount: 0,
    pendingAmount: 0,
    approvedAmount: 0,
    paidAmount: 0
  })

  const [newExpense, setNewExpense] = useState({
    description: '',
    amount: '',
    expenseTypeId: '',
    paymentMethod: 'CASH',
    notes: '',
  })

  const [editExpense, setEditExpense] = useState({
    description: '',
    amount: '',
    expenseTypeId: '',
    paymentMethod: 'CASH',
    notes: '',
  })

  const [newStatus, setNewStatus] = useState('')

  useEffect(() => {
    fetchExpenses()
    if (isEditable) {
      fetchExpenseTypes()
    }
  }, [projectId, isEditable])

  const fetchExpenses = async () => {
    try {
      const response = await fetch(`/api/expenses?projectId=${projectId}&limit=100`)
      if (response.ok) {
        const data = await response.json()
        setExpenses(data.expenses || [])
        
        // Calculate stats
        const total = data.expenses?.length || 0
        const totalAmount = data.expenses?.reduce((sum: number, exp: Expense) => sum + Number(exp.amount), 0) || 0
        const pendingAmount = data.expenses?.filter((exp: Expense) => exp.status === 'PENDING').reduce((sum: number, exp: Expense) => sum + Number(exp.amount), 0) || 0
        const approvedAmount = data.expenses?.filter((exp: Expense) => exp.status === 'APPROVED').reduce((sum: number, exp: Expense) => sum + Number(exp.amount), 0) || 0
        const paidAmount = data.expenses?.filter((exp: Expense) => exp.status === 'PAID').reduce((sum: number, exp: Expense) => sum + Number(exp.amount), 0) || 0
        
        setStats({ total, totalAmount, pendingAmount, approvedAmount, paidAmount })
      }
    } catch (error) {
      console.error('Error fetching expenses:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchExpenseTypes = async () => {
    try {
      const response = await fetch('/api/expense-types')
      if (response.ok) {
        const data = await response.json()
        setExpenseTypes(data || [])
      }
    } catch (error) {
      console.error('Error fetching expense types:', error)
    }
  }

  const handleCreateExpense = async () => {
    if (!newExpense.description || !newExpense.amount || !newExpense.expenseTypeId) return

    try {
      setCreating(true)
      const expenseData = {
        ...newExpense,
        projectId,
        amount: parseFloat(newExpense.amount),
        createdById: session?.user?.id,
      }

      const response = await fetch('/api/expenses', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(expenseData),
      })

      if (response.ok) {
        await fetchExpenses()
        setNewExpense({
          description: '',
          amount: '',
          expenseTypeId: '',
          paymentMethod: 'CASH',
          notes: '',
        })
        setDialogOpen(false)
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error creating expense:', error)
      alert('Error creating expense')
    } finally {
      setCreating(false)
    }
  }

  const handleEditExpense = async () => {
    if (!selectedExpense || !editExpense.description || !editExpense.amount || !editExpense.expenseTypeId) return

    try {
      setUpdating(true)
      const expenseData = {
        ...editExpense,
        amount: parseFloat(editExpense.amount),
      }

      const response = await fetch(`/api/expenses/${selectedExpense.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(expenseData),
      })

      if (response.ok) {
        await fetchExpenses()
        setEditDialogOpen(false)
        setSelectedExpense(null)
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error updating expense:', error)
      alert('Error updating expense')
    } finally {
      setUpdating(false)
    }
  }

  const handleStatusChange = async () => {
    if (!selectedExpense || !newStatus) return

    try {
      setUpdating(true)
      const response = await fetch(`/api/expenses/${selectedExpense.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        await fetchExpenses()
        setStatusDialogOpen(false)
        setSelectedExpense(null)
        setNewStatus('')
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error updating expense status:', error)
      alert('Error updating expense status')
    } finally {
      setUpdating(false)
    }
  }

  const openEditDialog = (expense: Expense) => {
    setSelectedExpense(expense)
    setEditExpense({
      description: expense.description,
      amount: expense.amount.toString(),
      expenseTypeId: expense.expenseType.id,
      paymentMethod: expense.paymentMethod,
      notes: '', // Would need to fetch notes if stored
    })
    setEditDialogOpen(true)
  }

  const openStatusDialog = (expense: Expense) => {
    setSelectedExpense(expense)
    setNewStatus(expense.status)
    setStatusDialogOpen(true)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Receipt className="h-5 w-5" />
          <h3 className="text-lg font-semibold">Project Expenses</h3>
          <Badge variant="secondary">{stats.total}</Badge>
        </div>
        
        {isEditable && (
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Add Expense
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add Project Expense</DialogTitle>
                <DialogDescription>
                  Record a new expense for this project
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div>
                  <Label htmlFor="description">Description *</Label>
                  <Input
                    id="description"
                    placeholder="Enter expense description"
                    value={newExpense.description}
                    onChange={(e) => setNewExpense(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="amount">Amount (OMR) *</Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.001"
                      placeholder="0.000"
                      value={newExpense.amount}
                      onChange={(e) => setNewExpense(prev => ({ ...prev, amount: e.target.value }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="expenseType">Expense Type *</Label>
                    <Select value={newExpense.expenseTypeId} onValueChange={(value) => setNewExpense(prev => ({ ...prev, expenseTypeId: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        {expenseTypes.map(type => (
                          <SelectItem key={type.id} value={type.id}>
                            {type.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="paymentMethod">Payment Method</Label>
                  <Select value={newExpense.paymentMethod} onValueChange={(value) => setNewExpense(prev => ({ ...prev, paymentMethod: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CASH">Cash</SelectItem>
                      <SelectItem value="CARD">Card</SelectItem>
                      <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                      <SelectItem value="CHECK">Check</SelectItem>
                      <SelectItem value="OTHER">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Additional notes (optional)"
                    value={newExpense.notes}
                    onChange={(e) => setNewExpense(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                  />
                </div>
                
                <div className="flex gap-2 pt-4">
                  <Button onClick={handleCreateExpense} disabled={!newExpense.description || !newExpense.amount || !newExpense.expenseTypeId || creating} className="flex-1">
                    {creating ? 'Creating...' : 'Create Expense'}
                  </Button>
                  <Button variant="outline" onClick={() => setDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-red-50 to-red-100 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-red-600 font-medium">Total</p>
              <p className="text-xl font-bold text-red-900">{formatCurrency(stats.totalAmount)}</p>
            </div>
            <Receipt className="h-6 w-6 text-red-600" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-yellow-600 font-medium">Pending</p>
              <p className="text-xl font-bold text-yellow-900">{formatCurrency(stats.pendingAmount)}</p>
            </div>
            <Clock className="h-6 w-6 text-yellow-600" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-blue-600 font-medium">Approved</p>
              <p className="text-xl font-bold text-blue-900">{formatCurrency(stats.approvedAmount)}</p>
            </div>
            <CheckCircle className="h-6 w-6 text-blue-600" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-green-600 font-medium">Processed</p>
              <p className="text-xl font-bold text-green-900">{formatCurrency(stats.paidAmount)}</p>
            </div>
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
        </div>
      </div>

      {/* Clarification Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="text-sm font-medium text-blue-900 mb-1">Expense Status Guide</h4>
            <p className="text-sm text-blue-700">
              <span className="font-medium">Processed</span> means the expense has been approved and processed for payment. 
              Unlike invoices which have payment records, expenses track approval workflow status.
            </p>
          </div>
        </div>
      </div>

      {expenses.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Receipt className="mx-auto h-12 w-12 mb-3 opacity-50" />
          <p>No expenses recorded yet</p>
          {isEditable && (
            <p className="text-sm">Record the first expense for this project</p>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {expenses.map((expense) => {
            const StatusIcon = statusIcons[expense.status as keyof typeof statusIcons]
            return (
              <div key={expense.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-start gap-3 flex-1">
                  <StatusIcon className="h-5 w-5 mt-0.5 text-muted-foreground" />
                  
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{expense.number}</span>
                      <Badge variant="secondary" className={statusColors[expense.status as keyof typeof statusColors]}>
                        {statusLabels[expense.status as keyof typeof statusLabels]}
                      </Badge>
                      <span className="text-lg font-bold text-red-600">{formatCurrency(Number(expense.amount))}</span>
                    </div>
                    
                    <p className="text-sm text-muted-foreground">{expense.description}</p>
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>Date: {formatDate(expense.date)}</span>
                      <span>Type: {expense.expenseType.name}</span>
                      <span>Method: {expense.paymentMethod}</span>
                      <span>By: {expense.createdBy.name}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {/* Actions Dropdown */}
                  {isEditable && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openEditDialog(expense)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit Expense
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => openStatusDialog(expense)}>
                          <Settings className="mr-2 h-4 w-4" />
                          Change Status
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                  
                  {/* View Details Button */}
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/dashboard/expenses/${expense.id}`}>
                      <Eye className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Edit Expense Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Expense</DialogTitle>
            <DialogDescription>
              Update expense details
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div>
              <Label htmlFor="edit-description">Description *</Label>
              <Input
                id="edit-description"
                placeholder="Enter expense description"
                value={editExpense.description}
                onChange={(e) => setEditExpense(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-amount">Amount (OMR) *</Label>
                <Input
                  id="edit-amount"
                  type="number"
                  step="0.001"
                  placeholder="0.000"
                  value={editExpense.amount}
                  onChange={(e) => setEditExpense(prev => ({ ...prev, amount: e.target.value }))}
                />
              </div>

              <div>
                <Label htmlFor="edit-expenseType">Expense Type *</Label>
                <Select value={editExpense.expenseTypeId} onValueChange={(value) => setEditExpense(prev => ({ ...prev, expenseTypeId: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {expenseTypes.map(type => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="edit-paymentMethod">Payment Method</Label>
              <Select value={editExpense.paymentMethod} onValueChange={(value) => setEditExpense(prev => ({ ...prev, paymentMethod: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CASH">Cash</SelectItem>
                  <SelectItem value="CARD">Card</SelectItem>
                  <SelectItem value="BANK_TRANSFER">Bank Transfer</SelectItem>
                  <SelectItem value="CHECK">Check</SelectItem>
                  <SelectItem value="OTHER">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex gap-2 pt-4">
              <Button onClick={handleEditExpense} disabled={!editExpense.description || !editExpense.amount || !editExpense.expenseTypeId || updating} className="flex-1">
                {updating ? 'Updating...' : 'Update Expense'}
              </Button>
              <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Status Change Dialog */}
      <Dialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change Expense Status</DialogTitle>
            <DialogDescription>
              Update the status of expense: {selectedExpense?.number}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div>
              <Label htmlFor="status">New Status</Label>
              <Select value={newStatus} onValueChange={setNewStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select new status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">{statusLabels.PENDING}</SelectItem>
                  <SelectItem value="APPROVED">{statusLabels.APPROVED}</SelectItem>
                  <SelectItem value="REJECTED">{statusLabels.REJECTED}</SelectItem>
                  <SelectItem value="PAID">{statusLabels.PAID}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex gap-2 pt-4">
              <Button onClick={handleStatusChange} disabled={!newStatus || updating} className="flex-1">
                {updating ? 'Updating...' : 'Update Status'}
              </Button>
              <Button variant="outline" onClick={() => setStatusDialogOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
} 