"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { ArrowLeft, TrendingUp, TrendingDown, BarChart3, PieChart, Download, Printer, Share2, Calendar, Target, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { AreaChart, Area, BarChart, Bar, PieChart as RechartsPieChart, Cell, Pie, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'
import { useI18n } from '@/lib/i18n'

interface TaxTransaction {
  id: string
  date: string
  description: string
  invoiceNumber?: string
  customer?: string
  taxableAmount: number
  taxRate: number
  taxAmount: number
  category: 'vatOnSales' | 'vatOnPurchases' | 'inputVat' | 'outputVat'
  status: 'paid' | 'unpaid' | 'pending' | 'refunded'
  reference?: string
}

interface MonthlyTaxData {
  month: string
  outputVat: number
  inputVat: number
  netVat: number
  taxableRevenue: number
  taxableExpenses: number
}

interface TaxSummaryData {
  overview: {
    totalTaxCollected: number
    totalTaxPaid: number
    netTaxPosition: number
    taxableRevenue: number
    taxableExpenses: number
    vatRate: number
    taxPeriod: string
    complianceStatus: 'compliant' | 'pending' | 'overdue'
    nextFilingDate: string
    lastFilingDate: string
  }
  transactions: TaxTransaction[]
  monthlyTrends: MonthlyTaxData[]
  categoryBreakdown: {
    category: string
    amount: number
    percentage: number
    count: number
  }[]
  calculations: {
    grossSales: number
    exemptSales: number
    taxableSales: number
    outputVatCalculated: number
    inputVatClaimed: number
    vatPayable: number
    vatRefundable: number
  }
  period: string
  dateRange: {
    start: string
    end: string
  }
}

export default function TaxSummaryPage() {
  const router = useRouter()
  const { t, formatCurrency, language } = useI18n()

  const [data, setData] = useState<TaxSummaryData | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('currentYear')
  const [selectedTaxType, setSelectedTaxType] = useState('all')

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/financial-reports/tax-summary?period=${selectedPeriod}&taxType=${selectedTaxType}`)
      if (!response.ok) throw new Error('Failed to fetch data')
      const result = await response.json()
      setData(result)
    } catch (error) {
      console.error('Error fetching tax summary data:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [selectedPeriod, selectedTaxType])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      case 'paid': return 'bg-green-100 text-green-800'
      case 'unpaid': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="h-4 w-4" />
      case 'pending': return <AlertTriangle className="h-4 w-4" />
      case 'overdue': return <XCircle className="h-4 w-4" />
      default: return <Target className="h-4 w-4" />
    }
  }

  const chartColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00', '#ff00ff']

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {t('finance.financial.taxSummary.title')}
          </h2>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <h2 className="text-3xl font-bold tracking-tight">
            {t('finance.financial.taxSummary.title')}
          </h2>
        </div>
        <div className="text-center py-8">
          <p className="text-muted-foreground">{t('finance.financial.common.noDataAvailable')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('finance.financial.buttons.back')}
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">
              {t('finance.financial.taxSummary.title')}
            </h2>
            <p className="text-muted-foreground">
              {t('finance.financial.taxSummary.subtitle')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            {t('finance.financial.taxSummary.actions.exportReport')}
          </Button>
          <Button variant="outline" size="sm">
            <Printer className="mr-2 h-4 w-4" />
            {t('finance.financial.taxSummary.actions.printReport')}
          </Button>
          <Button variant="outline" size="sm">
            <Share2 className="mr-2 h-4 w-4" />
            {t('finance.financial.common.share')}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex items-center space-x-2">
          <Calendar className="h-4 w-4" />
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t('finance.financial.taxSummary.filters.period')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="currentMonth">{t('finance.financial.taxSummary.periods.currentMonth')}</SelectItem>
              <SelectItem value="lastMonth">{t('finance.financial.taxSummary.periods.lastMonth')}</SelectItem>
              <SelectItem value="currentQuarter">{t('finance.financial.taxSummary.periods.currentQuarter')}</SelectItem>
              <SelectItem value="lastQuarter">{t('finance.financial.taxSummary.periods.lastQuarter')}</SelectItem>
              <SelectItem value="currentYear">{t('finance.financial.taxSummary.periods.currentYear')}</SelectItem>
              <SelectItem value="lastYear">{t('finance.financial.taxSummary.periods.lastYear')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2">
          <Target className="h-4 w-4" />
          <Select value={selectedTaxType} onValueChange={setSelectedTaxType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder={t('finance.financial.taxSummary.filters.taxType')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('finance.financial.common.all')}</SelectItem>
              <SelectItem value="vat">{t('finance.financial.taxSummary.categories.vatOnSales')}</SelectItem>
              <SelectItem value="input">{t('finance.financial.taxSummary.categories.inputVat')}</SelectItem>
              <SelectItem value="output">{t('finance.financial.taxSummary.categories.outputVat')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.taxSummary.overview.totalTaxCollected')}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(data.overview.totalTaxCollected)}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('finance.financial.taxSummary.categories.outputVat')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.taxSummary.overview.totalTaxPaid')}
            </CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(data.overview.totalTaxPaid)}
            </div>
            <p className="text-xs text-muted-foreground">
              {t('finance.financial.taxSummary.categories.inputVat')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.taxSummary.overview.netTaxPosition')}
            </CardTitle>
            {data.overview.netTaxPosition >= 0 ? (
              <TrendingUp className="h-4 w-4 text-green-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${data.overview.netTaxPosition >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(Math.abs(data.overview.netTaxPosition))}
            </div>
            <p className="text-xs text-muted-foreground">
              {data.overview.netTaxPosition >= 0
                ? t('finance.financial.taxSummary.categories.vatPayable')
                : t('finance.financial.taxSummary.categories.vatRefund')
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t('finance.financial.taxSummary.overview.complianceStatus')}
            </CardTitle>
            {getStatusIcon(data.overview.complianceStatus)}
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge className={getStatusColor(data.overview.complianceStatus)}>
                {t(`finance.financial.taxSummary.status.${data.overview.complianceStatus}`)}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              {t('finance.financial.taxSummary.overview.nextFilingDate')}: {' '}
              {new Date(data.overview.nextFilingDate).toLocaleDateString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Monthly Tax Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5" />
              {t('finance.financial.taxSummary.charts.monthlyTaxTrend')}
            </CardTitle>
            <CardDescription>
              {t('finance.financial.taxSummary.charts.inputVsOutputVat')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={data.monthlyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip
                  formatter={(value: number) => [formatCurrency(value), '']}
                  labelFormatter={(label) => `${t('finance.financial.common.month')}: ${label}`}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="outputVat"
                  stackId="1"
                  stroke="#8884d8"
                  fill="#8884d8"
                  name={t('finance.financial.taxSummary.categories.outputVat')}
                />
                <Area
                  type="monotone"
                  dataKey="inputVat"
                  stackId="1"
                  stroke="#82ca9d"
                  fill="#82ca9d"
                  name={t('finance.financial.taxSummary.categories.inputVat')}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Tax Category Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="mr-2 h-5 w-5" />
              {t('finance.financial.taxSummary.charts.taxByCategory')}
            </CardTitle>
            <CardDescription>
              {t('finance.financial.taxSummary.charts.vatAnalysis')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RechartsPieChart>
                <Pie
                  data={data.categoryBreakdown}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ category, percentage }) =>
                    `${t(`finance.financial.taxSummary.categories.${category}`)} (${percentage.toFixed(1)}%)`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="amount"
                >
                  {data.categoryBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => [formatCurrency(value), '']} />
              </RechartsPieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Tax Calculations Summary */}
      <Card>
        <CardHeader>
          <CardTitle>{t('finance.financial.taxSummary.calculations.grossSales')}</CardTitle>
          <CardDescription>
            {t('finance.financial.taxSummary.subtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {t('finance.financial.taxSummary.calculations.grossSales')}
              </p>
              <p className="text-2xl font-bold">
                {formatCurrency(data.calculations.grossSales)}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {t('finance.financial.taxSummary.calculations.exemptSales')}
              </p>
              <p className="text-2xl font-bold">
                {formatCurrency(data.calculations.exemptSales)}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {t('finance.financial.taxSummary.calculations.taxableSales')}
              </p>
              <p className="text-2xl font-bold">
                {formatCurrency(data.calculations.taxableSales)}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {t('finance.financial.taxSummary.calculations.outputVatCalculated')}
              </p>
              <p className="text-2xl font-bold">
                {formatCurrency(data.calculations.outputVatCalculated)}
              </p>
            </div>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 mt-6">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {t('finance.financial.taxSummary.calculations.inputVatClaimed')}
              </p>
              <p className="text-2xl font-bold">
                {formatCurrency(data.calculations.inputVatClaimed)}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {t('finance.financial.taxSummary.calculations.vatPayable')}
              </p>
              <p className="text-2xl font-bold text-red-600">
                {formatCurrency(data.calculations.vatPayable)}
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">
                {t('finance.financial.taxSummary.calculations.vatRefundable')}
              </p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(data.calculations.vatRefundable)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tax Transactions Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('finance.financial.taxSummary.table.description')}</CardTitle>
          <CardDescription>
            {t('finance.financial.common.recentTransactions')} ({data.transactions.length})
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('finance.financial.taxSummary.table.date')}</TableHead>
                <TableHead>{t('finance.financial.taxSummary.table.description')}</TableHead>
                <TableHead>{t('finance.financial.taxSummary.table.customer')}</TableHead>
                <TableHead>{t('finance.financial.taxSummary.table.taxableAmount')}</TableHead>
                <TableHead>{t('finance.financial.taxSummary.table.taxRate')}</TableHead>
                <TableHead>{t('finance.financial.taxSummary.table.taxAmount')}</TableHead>
                <TableHead>{t('finance.financial.taxSummary.table.category')}</TableHead>
                <TableHead>{t('finance.financial.taxSummary.table.status')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>
                    {new Date(transaction.date).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      {transaction.invoiceNumber && (
                        <p className="text-sm text-muted-foreground">
                          {transaction.invoiceNumber}
                        </p>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {transaction.customer || '-'}
                  </TableCell>
                  <TableCell>
                    {formatCurrency(transaction.taxableAmount)}
                  </TableCell>
                  <TableCell>
                    {transaction.taxRate.toFixed(1)}%
                  </TableCell>
                  <TableCell>
                    {formatCurrency(transaction.taxAmount)}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {t(`finance.financial.taxSummary.categories.${transaction.category}`)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(transaction.status)}>
                      {t(`finance.financial.taxSummary.status.${transaction.status}`)}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {data.transactions.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                {t('finance.financial.common.noTransactionsFound')}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Compliance Information */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>{t('finance.financial.taxSummary.compliance.filingStatus')}</CardTitle>
            <CardDescription>
              {t('finance.financial.taxSummary.overview.vatRate')}: {data.overview.vatRate}%
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">
                {t('finance.financial.taxSummary.compliance.nextDueDate')}
              </span>
              <span className="text-sm">
                {new Date(data.overview.nextFilingDate).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">
                {t('finance.financial.taxSummary.overview.lastFilingDate')}
              </span>
              <span className="text-sm">
                {new Date(data.overview.lastFilingDate).toLocaleDateString()}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">
                {t('finance.financial.taxSummary.overview.taxPeriod')}
              </span>
              <Badge variant="outline">
                {t(`finance.financial.taxSummary.periods.${data.overview.taxPeriod}`)}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('finance.financial.taxSummary.summary.recommendations')}</CardTitle>
            <CardDescription>
              {t('finance.financial.taxSummary.summary.complianceScore')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                {getStatusIcon(data.overview.complianceStatus)}
                <span className="text-sm font-medium">
                  {t('finance.financial.taxSummary.overview.complianceStatus')}
                </span>
                <Badge className={getStatusColor(data.overview.complianceStatus)}>
                  {t(`finance.financial.taxSummary.status.${data.overview.complianceStatus}`)}
                </Badge>
              </div>
              {data.overview.complianceStatus === 'overdue' && (
                <p className="text-sm text-red-600">
                  {t('finance.financial.common.actionRequired')}
                </p>
              )}
              {data.overview.complianceStatus === 'pending' && (
                <p className="text-sm text-yellow-600">
                  {t('finance.financial.common.upcomingDeadline')}
                </p>
              )}
              {data.overview.complianceStatus === 'compliant' && (
                <p className="text-sm text-green-600">
                  {t('finance.financial.common.allUpToDate')}
                </p>
              )}
            </div>
            <div className="pt-4">
              <Button className="w-full">
                {t('finance.financial.taxSummary.actions.fileReturn')}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}