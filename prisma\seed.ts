import { PrismaClient } from '@prisma/client'
import * as bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create superadmin user
  const superadminPassword = await bcrypt.hash('123456', 10)

  const superadminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: superadminPassword,
      name: 'Super Admin',
      role: 'ADMIN',
      phone: '+968 9999 9999',
      isActive: true,
    },
  })

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10)

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Admin User',
      role: 'ADMI<PERSON>',
      phone: '+1234567890',
      isActive: true,
    },
  })

  // Create manager user
  const managerPassword = await bcrypt.hash('manager123', 10)

  const managerUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: managerPassword,
      name: 'Manager User',
      role: 'MANAGER',
      phone: '+1234567891',
      isActive: true,
    },
  })

  // Create employee user
  const employeePassword = await bcrypt.hash('employee123', 10)

  const employeeUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: employeePassword,
      name: 'Employee User',
      role: 'EMPLOYEE',
      phone: '+1234567892',
      isActive: true,
    },
  })

  // Create categories
  const printingCategory = await prisma.category.create({
    data: {
      name: 'Printing Services',
      nameAr: 'خدمات الطباعة',
      description: 'All printing related services',
    },
  })

  const suppliesCategory = await prisma.category.create({
    data: {
      name: 'Office Supplies',
      nameAr: 'المستلزمات المكتبية',
      description: 'Office supplies and materials',
    },
  })

  const designCategory = await prisma.category.create({
    data: {
      name: 'Design Services',
      nameAr: 'خدمات التصميم',
      description: 'Graphic design and creative services',
    },
  })

  const bindingCategory = await prisma.category.create({
    data: {
      name: 'Binding & Finishing',
      nameAr: 'التجليد والتشطيب',
      description: 'Document binding and finishing services',
    },
  })

  // Create suppliers
  const supplier1 = await prisma.supplier.create({
    data: {
      name: 'Al Warak Trading LLC',
      nameAr: 'شركة الورق التجارية',
      email: '<EMAIL>',
      mobile: '+968 9123 4567',
      phone: '+968 24123456',
      company: 'Al Warak Trading LLC',
      address: 'Ruwi Commercial District, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      contactPerson: 'Ahmed Al Rashid',
      taxNumber: 'OM1234567890',
      notes: 'Main paper and office supplies distributor',
    },
  })

  const supplier2 = await prisma.supplier.create({
    data: {
      name: 'Gulf Printing Solutions',
      nameAr: 'حلول الطباعة الخليجية',
      email: '<EMAIL>',
      mobile: '+968 9876 5432',
      phone: '+968 24567890',
      company: 'Gulf Printing Solutions',
      address: 'Al Khuwair Industrial Area, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      contactPerson: 'Fatima Al Zahra',
      taxNumber: 'OM0987654321',
      notes: 'Specialized in printing equipment and ink supplies',
    },
  })

  const supplier3 = await prisma.supplier.create({
    data: {
      name: 'Oman Office Equipment',
      nameAr: 'معدات المكاتب العمانية',
      email: '<EMAIL>',
      mobile: '+968 9555 1234',
      phone: '+968 24789012',
      company: 'Oman Office Equipment LLC',
      address: 'Ghala Industrial Estate, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      contactPerson: 'Mohammed Al Balushi',
      taxNumber: 'OM1122334455',
      notes: 'Office furniture and equipment supplier',
    },
  })

  const supplier4 = await prisma.supplier.create({
    data: {
      name: 'Digital Print House',
      nameAr: 'دار الطباعة الرقمية',
      email: '<EMAIL>',
      mobile: '+968 9777 8888',
      phone: '+968 24345678',
      company: 'Digital Print House LLC',
      address: 'Bausher Commercial Complex, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      contactPerson: 'Khalid Al Hinai',
      taxNumber: 'OM5566778899',
      notes: 'Digital printing and design services',
    },
  })

  // Create products
  const product1 = await prisma.product.create({
    data: {
      name: 'A4 Paper - Premium',
      nameAr: 'ورق A4 - ممتاز',
      description: 'High quality A4 printing paper 80gsm',
      sku: 'A4-PAPER-001',
      barcode: '1234567890123',
      type: 'PHYSICAL',
      price: 2.500, // OMR
      costPrice: 1.800,
      currentStock: 1000,
      minStock: 100,
      maxStock: 2000,
      location: 'A1-B1',
      unit: 'ream',
      categoryId: suppliesCategory.id,
      supplierId: supplier1.id,
      lastRestocked: new Date('2024-01-15'),
    },
  })

  const product2 = await prisma.product.create({
    data: {
      name: 'Business Card Printing',
      nameAr: 'طباعة بطاقات العمل',
      description: 'Professional business card printing service (500 cards)',
      sku: 'BC-PRINT-001',
      barcode: '1234567890124',
      type: 'SERVICE',
      price: 15.000,
      costPrice: 8.000,
      currentStock: 0,
      minStock: 0,
      maxStock: 0,
      location: 'SERVICE',
      unit: 'set',
      categoryId: printingCategory.id,
      supplierId: supplier4.id,
    },
  })

  const product3 = await prisma.product.create({
    data: {
      name: 'Color Ink Cartridge HP',
      nameAr: 'خرطوشة حبر ملونة HP',
      description: 'Original HP color ink cartridge',
      sku: 'INK-HP-COLOR-001',
      type: 'PHYSICAL',
      price: 25.000,
      costPrice: 18.000,
      currentStock: 50,
      minStock: 10,
      unit: 'piece',
      categoryId: suppliesCategory.id,
      supplierId: supplier2.id,
    },
  })

  const product4 = await prisma.product.create({
    data: {
      name: 'A3 Paper - Photo Quality',
      nameAr: 'ورق A3 - جودة الصور',
      description: 'High quality A3 photo paper 200gsm',
      sku: 'A3-PHOTO-001',
      type: 'PHYSICAL',
      price: 8.500,
      costPrice: 6.200,
      currentStock: 200,
      minStock: 50,
      unit: 'ream',
      categoryId: suppliesCategory.id,
      supplierId: supplier1.id,
    },
  })

  const product5 = await prisma.product.create({
    data: {
      name: 'Logo Design Service',
      nameAr: 'خدمة تصميم الشعار',
      description: 'Professional logo design with 3 concepts',
      sku: 'LOGO-DESIGN-001',
      type: 'SERVICE',
      price: 50.000,
      costPrice: 25.000,
      currentStock: 0,
      minStock: 0,
      unit: 'project',
      categoryId: designCategory.id,
      supplierId: supplier4.id,
    },
  })

  const product6 = await prisma.product.create({
    data: {
      name: 'Document Binding - Spiral',
      nameAr: 'تجليد المستندات - حلزوني',
      description: 'Spiral binding service for documents',
      sku: 'BIND-SPIRAL-001',
      type: 'SERVICE',
      price: 3.000,
      costPrice: 1.500,
      currentStock: 0,
      minStock: 0,
      unit: 'document',
      categoryId: bindingCategory.id,
    },
  })

  const product7 = await prisma.product.create({
    data: {
      name: 'Black Ink Cartridge Canon',
      nameAr: 'خرطوشة حبر أسود كانون',
      description: 'Original Canon black ink cartridge',
      sku: 'INK-CANON-BLACK-001',
      type: 'PHYSICAL',
      price: 18.000,
      costPrice: 12.500,
      currentStock: 75,
      minStock: 15,
      unit: 'piece',
      categoryId: suppliesCategory.id,
      supplierId: supplier2.id,
    },
  })

  const product8 = await prisma.product.create({
    data: {
      name: 'Lamination Service A4',
      nameAr: 'خدمة التغليف A4',
      description: 'Professional document lamination A4 size',
      sku: 'LAM-A4-001',
      type: 'SERVICE',
      price: 1.000,
      costPrice: 0.500,
      currentStock: 0,
      minStock: 0,
      unit: 'sheet',
      categoryId: printingCategory.id,
    },
  })

  const product9 = await prisma.product.create({
    data: {
      name: 'Office Chair - Executive',
      nameAr: 'كرسي مكتب - تنفيذي',
      description: 'High-back executive office chair with lumbar support',
      sku: 'CHAIR-EXEC-001',
      type: 'PHYSICAL',
      price: 120.000,
      costPrice: 85.000,
      currentStock: 15,
      minStock: 5,
      unit: 'piece',
      categoryId: suppliesCategory.id,
      supplierId: supplier3.id,
    },
  })

  const product10 = await prisma.product.create({
    data: {
      name: 'Poster Printing A1',
      nameAr: 'طباعة البوستر A1',
      description: 'High quality poster printing A1 size',
      sku: 'POSTER-A1-001',
      type: 'SERVICE',
      price: 12.000,
      costPrice: 7.000,
      currentStock: 0,
      minStock: 0,
      unit: 'piece',
      categoryId: printingCategory.id,
      supplierId: supplier4.id,
    },
  })

  // Create customers
  const customer1 = await prisma.customer.create({
    data: {
      name: 'Muscat Trading Company LLC',
      nameAr: 'شركة مسقط التجارية',
      email: '<EMAIL>',
      mobile: '+968 9111 2222',
      phone: '+968 24111222',
      company: 'Muscat Trading Company LLC',
      address: 'Al Khuwair, Way 3018, Building 145, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      taxNumber: 'OM1001234567',
      notes: 'Regular corporate customer, bulk printing orders',
    },
  })

  const customer2 = await prisma.customer.create({
    data: {
      name: 'Al Noor Legal Consultancy',
      nameAr: 'مكتب النور للاستشارات القانونية',
      email: '<EMAIL>',
      mobile: '+968 9333 4444',
      phone: '+968 24333444',
      company: 'Al Noor Legal Consultancy',
      address: 'Ruwi Commercial District, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      taxNumber: 'OM2002345678',
      notes: 'Legal firm requiring document services',
    },
  })

  const customer3 = await prisma.customer.create({
    data: {
      name: 'Oman Medical Center',
      email: '<EMAIL>',
      mobile: '+968 9555 6666',
      phone: '+968 24555666',
      company: 'Oman Medical Center',
      address: 'Al Khoud, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      taxNumber: 'OM3003456789',
      notes: 'Medical center requiring forms and documentation',
    },
  })

  const customer4 = await prisma.customer.create({
    data: {
      name: 'Ahmed Al Kindi',
      email: '<EMAIL>',
      mobile: '+968 9912 3456',
      phone: '+968 99123456',
      company: 'Individual',
      address: 'Al Mawaleh, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      notes: 'Individual customer, occasional printing needs',
    },
  })

  const customer5 = await prisma.customer.create({
    data: {
      name: 'Gulf Engineering Consultants',
      email: '<EMAIL>',
      mobile: '+968 9777 8888',
      phone: '+968 24777888',
      company: 'Gulf Engineering Consultants LLC',
      address: 'Ghala Industrial Estate, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      taxNumber: 'OM4004567890',
      notes: 'Engineering firm, large format printing requirements',
    },
  })

  const customer6 = await prisma.customer.create({
    data: {
      name: 'Fatima Al Zahra Restaurant',
      email: '<EMAIL>',
      mobile: '+968 9999 0000',
      phone: '+968 24999000',
      company: 'Fatima Al Zahra Restaurant LLC',
      address: 'Qurum, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      taxNumber: 'OM5005678901',
      notes: 'Restaurant chain, menu and promotional material printing',
    },
  })

  const customer7 = await prisma.customer.create({
    data: {
      name: 'Oman International School',
      email: '<EMAIL>',
      mobile: '+968 9121 3141',
      phone: '+968 24121314',
      company: 'Oman International School',
      address: 'Al Hail, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      taxNumber: 'OM6006789012',
      notes: 'Educational institution, regular stationery and printing needs',
    },
  })

  const customer8 = await prisma.customer.create({
    data: {
      name: 'Mohammed Al Balushi',
      email: '<EMAIL>',
      mobile: '+968 9987 6543',
      phone: '+968 99876543',
      company: 'Freelance Designer',
      address: 'Bausher, Muscat, Oman',
      city: 'Muscat',
      country: 'Oman',
      notes: 'Freelance designer, design and printing services',
    },
  })

  // Create tasks
  const task1 = await prisma.task.create({
    data: {
      title: 'Print 500 Business Cards',
      description: 'High quality business cards for ABC Corporation',
      status: 'NEW',
      priority: 'HIGH',
      estimatedHours: 2.5,
      customerId: customer1.id,
      assignedToId: employeeUser.id,
      createdById: adminUser.id,
      notes: 'Use premium cardstock',
    },
  })

  const task2 = await prisma.task.create({
    data: {
      title: 'Copy Legal Documents',
      description: 'Make 100 copies of legal contracts',
      status: 'IN_PROGRESS',
      priority: 'MEDIUM',
      estimatedHours: 1.0,
      startTime: new Date(),
      customerId: customer3.id,
      assignedToId: employeeUser.id,
      createdById: managerUser.id,
      notes: 'Double-sided copying required',
    },
  })

  const task3 = await prisma.task.create({
    data: {
      title: 'Design Marketing Brochure',
      description: 'Create marketing brochure for XYZ Enterprises',
      status: 'COMPLETED',
      priority: 'LOW',
      estimatedHours: 4.0,
      actualHours: 3.5,
      startTime: new Date(Date.now() - 86400000), // 1 day ago
      endTime: new Date(Date.now() - 3600000), // 1 hour ago
      customerId: customer2.id,
      assignedToId: employeeUser.id,
      createdById: adminUser.id,
      notes: 'Customer approved final design',
    },
  })

  // Create invoices
  const invoice1 = await prisma.invoice.create({
    data: {
      number: 'INV-2024-001',
      date: new Date('2024-01-15'),
      dueDate: new Date('2024-02-15'),
      status: 'PAID',
      subtotal: 45.000,
      taxAmount: 2.250, // 5% VAT
      total: 47.250,
      customerId: customer1.id,
      userId: adminUser.id,
      notes: 'Business cards and letterheads for corporate identity',
      items: {
        create: [
          {
            description: 'Business Card Printing (500 cards)',
            quantity: 1,
            unitPrice: 15.000,
            total: 15.000,
            productId: product2.id,
          },
          {
            description: 'A4 Paper - Premium (10 reams)',
            quantity: 10,
            unitPrice: 2.500,
            total: 25.000,
            productId: product1.id,
          },
          {
            description: 'Logo Design Service',
            quantity: 1,
            unitPrice: 5.000,
            total: 5.000,
          },
        ],
      },
    },
  })

  const invoice2 = await prisma.invoice.create({
    data: {
      number: 'INV-2024-002',
      date: new Date('2024-01-20'),
      dueDate: new Date('2024-02-20'),
      status: 'PARTIAL',
      subtotal: 85.000,
      taxAmount: 4.250,
      total: 89.250,
      customerId: customer2.id,
      userId: managerUser.id,
      notes: 'Legal document printing and binding services',
      items: {
        create: [
          {
            description: 'Document Binding - Spiral (50 documents)',
            quantity: 50,
            unitPrice: 3.000,
            total: 150.000,
            productId: product6.id,
          },
          {
            description: 'A4 Paper - Premium (5 reams)',
            quantity: 5,
            unitPrice: 2.500,
            total: 12.500,
            productId: product1.id,
          },
          {
            description: 'Lamination Service A4 (100 sheets)',
            quantity: 100,
            unitPrice: 1.000,
            total: 100.000,
            productId: product8.id,
          },
        ],
      },
    },
  })

  const invoice3 = await prisma.invoice.create({
    data: {
      number: 'INV-2024-003',
      date: new Date('2024-01-25'),
      dueDate: new Date('2024-02-25'),
      status: 'UNPAID',
      subtotal: 180.000,
      taxAmount: 9.000,
      total: 189.000,
      customerId: customer5.id,
      userId: employeeUser.id,
      notes: 'Engineering drawings and large format printing',
      items: {
        create: [
          {
            description: 'Poster Printing A1 (15 pieces)',
            quantity: 15,
            unitPrice: 12.000,
            total: 180.000,
            productId: product10.id,
          },
        ],
      },
    },
  })

  const invoice4 = await prisma.invoice.create({
    data: {
      number: 'INV-2024-004',
      date: new Date('2024-02-01'),
      dueDate: new Date('2024-03-01'),
      status: 'PAID',
      subtotal: 35.000,
      taxAmount: 1.750,
      total: 36.750,
      customerId: customer4.id,
      userId: adminUser.id,
      notes: 'Personal printing services',
      items: {
        create: [
          {
            description: 'A3 Paper - Photo Quality (2 reams)',
            quantity: 2,
            unitPrice: 8.500,
            total: 17.000,
            productId: product4.id,
          },
          {
            description: 'Color Ink Cartridge HP',
            quantity: 1,
            unitPrice: 25.000,
            total: 25.000,
            productId: product3.id,
          },
        ],
      },
    },
  })

  // Create purchases
  const purchase1 = await prisma.purchase.create({
    data: {
      number: 'PO-2024-001',
      date: new Date('2024-01-10'),
      status: 'RECEIVED',
      subtotal: 500.000,
      taxAmount: 25.000,
      total: 525.000,
      supplierId: supplier1.id,
      userId: adminUser.id,
      notes: 'Monthly paper stock replenishment',
      items: {
        create: [
          {
            description: 'A4 Paper - Premium (200 reams)',
            quantity: 200,
            unitPrice: 1.800,
            total: 360.000,
            productId: product1.id,
          },
          {
            description: 'A3 Paper - Photo Quality (50 reams)',
            quantity: 50,
            unitPrice: 6.200,
            total: 310.000,
            productId: product4.id,
          },
        ],
      },
    },
  })

  const purchase2 = await prisma.purchase.create({
    data: {
      number: 'PO-2024-002',
      date: new Date('2024-01-15'),
      status: 'RECEIVED',
      subtotal: 450.000,
      taxAmount: 22.500,
      total: 472.500,
      supplierId: supplier2.id,
      userId: managerUser.id,
      notes: 'Ink cartridge stock replenishment',
      items: {
        create: [
          {
            description: 'Color Ink Cartridge HP (25 pieces)',
            quantity: 25,
            unitPrice: 18.000,
            total: 450.000,
            productId: product3.id,
          },
          {
            description: 'Black Ink Cartridge Canon (30 pieces)',
            quantity: 30,
            unitPrice: 12.500,
            total: 375.000,
            productId: product7.id,
          },
        ],
      },
    },
  })

  const purchase3 = await prisma.purchase.create({
    data: {
      number: 'PO-2024-003',
      date: new Date('2024-01-20'),
      status: 'PENDING',
      subtotal: 850.000,
      taxAmount: 42.500,
      total: 892.500,
      supplierId: supplier3.id,
      userId: adminUser.id,
      notes: 'Office furniture for expansion',
      items: {
        create: [
          {
            description: 'Office Chair - Executive (10 pieces)',
            quantity: 10,
            unitPrice: 85.000,
            total: 850.000,
            productId: product9.id,
          },
        ],
      },
    },
  })

  // Create payments for invoices
  await prisma.payment.create({
    data: {
      amount: 47.250,
      method: 'BANK_TRANSFER',
      reference: 'TXN-2024-001',
      notes: 'Full payment received',
      date: new Date('2024-01-18'),
      invoiceId: invoice1.id,
      customerId: customer1.id,
    },
  })

  await prisma.payment.create({
    data: {
      amount: 50.000,
      method: 'CASH',
      reference: 'CASH-2024-001',
      notes: 'Partial payment received',
      date: new Date('2024-01-25'),
      invoiceId: invoice2.id,
      customerId: customer2.id,
    },
  })

  await prisma.payment.create({
    data: {
      amount: 36.750,
      method: 'CARD',
      reference: 'CARD-2024-001',
      notes: 'Payment by credit card',
      date: new Date('2024-02-02'),
      invoiceId: invoice4.id,
      customerId: customer4.id,
    },
  })

  // Create settings
  await prisma.setting.createMany({
    data: [
      {
        key: 'company_name',
        value: 'Muscat Print & Design Center',
        description: 'Company name',
      },
      {
        key: 'company_name_ar',
        value: 'مركز مسقط للطباعة والتصميم',
        description: 'Company name in Arabic',
      },
      {
        key: 'company_address',
        value: 'Al Khuwair, Way 3015, Building 123, Muscat, Sultanate of Oman',
        description: 'Company address',
      },
      {
        key: 'company_phone',
        value: '+968 24123456',
        description: 'Company phone number',
      },
      {
        key: 'company_email',
        value: '<EMAIL>',
        description: 'Company email',
      },
      {
        key: 'company_tax_number',
        value: 'OM1234567890',
        description: 'Company tax registration number',
      },
      {
        key: 'default_tax_rate',
        value: '5',
        description: 'Default VAT rate percentage (Oman)',
      },
      {
        key: 'default_currency',
        value: 'OMR',
        description: 'Default currency (Omani Rial)',
      },
      {
        key: 'timezone',
        value: 'Asia/Muscat',
        description: 'Default timezone',
      },
      {
        key: 'invoice_terms',
        value: 'Payment due within 30 days. Late payments subject to 2% monthly charge.',
        description: 'Default invoice terms and conditions',
      },
      {
        key: 'invoice_terms_ar',
        value: 'الدفع مستحق خلال 30 يوماً. المدفوعات المتأخرة تخضع لرسوم شهرية 2%.',
        description: 'Default invoice terms in Arabic',
      },
    ],
  })

  console.log('✅ Database seeded successfully!')
  console.log('')
  console.log('👤 Users Created:')
  console.log('   Super Admin: <EMAIL> / 123456')
  console.log('   Admin: <EMAIL> / admin123')
  console.log('   Manager: <EMAIL> / manager123')
  console.log('   Employee: <EMAIL> / employee123')
  console.log('')
  console.log('📊 Sample Data Created:')
  console.log('   📁 Categories: 4 (Printing, Supplies, Design, Binding)')
  console.log('   🏢 Suppliers: 4 (Omani companies)')
  console.log('   📦 Products: 10 (Physical & Service items)')
  console.log('   👥 Customers: 8 (Omani businesses & individuals)')
  console.log('   📋 Tasks: 3 (Various statuses)')
  console.log('   🧾 Invoices: 4 (Different payment statuses)')
  console.log('   💰 Payments: 3 (Various payment methods)')
  console.log('   🛒 Purchases: 3 (Supplier orders)')
  console.log('   ⚙️  Settings: 11 (Omani business configuration)')
  console.log('')
  console.log('💰 Currency: Omani Rial (OMR)')
  console.log('🌍 Timezone: Asia/Muscat')
  console.log('📍 Location: Muscat, Sultanate of Oman')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
