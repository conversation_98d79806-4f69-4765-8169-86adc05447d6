import { PrismaClient } from '@prisma/client'
import * as bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database with users...')

  try {
    // Create superadmin user
    const superadminPassword = await bcrypt.hash('123456', 10)

    const superadminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: superadminPassword,
        name: 'Super Admin',
        role: 'ADMIN',
        phone: '+968 9999 9999',
        isActive: true,
      },
    })

    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 10)

    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Ad<PERSON> User',
        role: 'ADMI<PERSON>',
        phone: '+968 24123456',
        isActive: true,
      },
    })

    // Create manager user
    const managerPassword = await bcrypt.hash('manager123', 10)

    const managerUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: managerPassword,
        name: 'Manager User',
        role: 'MANAGER',
        phone: '+968 24567890',
        isActive: true,
      },
    })

    // Create employee user
    const employeePassword = await bcrypt.hash('employee123', 10)

    const employeeUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: employeePassword,
        name: 'Employee User',
        role: 'EMPLOYEE',
        phone: '+968 24789012',
        isActive: true,
      },
    })

    // Create basic settings
    await prisma.setting.upsert({
      where: { key: 'company_name' },
      update: { value: 'Muscat Print & Design Center' },
      create: {
        key: 'company_name',
        value: 'Muscat Print & Design Center',
        description: 'Company name',
      },
    })

    await prisma.setting.upsert({
      where: { key: 'company_name_ar' },
      update: { value: 'مركز مسقط للطباعة والتصميم' },
      create: {
        key: 'company_name_ar',
        value: 'مركز مسقط للطباعة والتصميم',
        description: 'Company name in Arabic',
      },
    })

    await prisma.setting.upsert({
      where: { key: 'default_currency' },
      update: { value: 'OMR' },
      create: {
        key: 'default_currency',
        value: 'OMR',
        description: 'Default currency (Omani Rial)',
      },
    })

    await prisma.setting.upsert({
      where: { key: 'default_tax_rate' },
      update: { value: '5' },
      create: {
        key: 'default_tax_rate',
        value: '5',
        description: 'Default VAT rate percentage (Oman)',
      },
    })

    await prisma.setting.upsert({
      where: { key: 'timezone' },
      update: { value: 'Asia/Muscat' },
      create: {
        key: 'timezone',
        value: 'Asia/Muscat',
        description: 'Default timezone',
      },
    })

    console.log('✅ Users and basic settings seeded successfully!')
    console.log('')
    console.log('👤 Users Created:')
    console.log('   Super Admin: <EMAIL> / 123456')
    console.log('   Admin: <EMAIL> / admin123')
    console.log('   Manager: <EMAIL> / manager123')
    console.log('   Employee: <EMAIL> / employee123')
    console.log('')
    console.log('⚙️  Basic Settings Created')
    console.log('💰 Currency: Omani Rial (OMR)')
    console.log('🌍 Timezone: Asia/Muscat')
    console.log('📍 Location: Muscat, Sultanate of Oman')

  } catch (error) {
    console.error('❌ Error seeding database:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
