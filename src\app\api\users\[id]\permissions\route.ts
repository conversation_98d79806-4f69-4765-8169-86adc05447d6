import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/users/[id]/permissions - Get user permissions
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        userRole: {
          include: {
            permissions: {
              where: {
                granted: true,
              },
              include: {
                permission: {
                  where: {
                    isActive: true,
                  },
                },
              },
            },
          },
        },
      },
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Extract permissions from role (handle case where user has no role)
    const permissions = user.userRole?.permissions
      ?.map(rp => rp.permission)
      ?.filter(p => p !== null) || []

    // Group permissions by module for easier access
    const permissionsByModule = permissions.reduce((acc, permission) => {
      if (!acc[permission.module]) {
        acc[permission.module] = []
      }
      acc[permission.module].push(permission)
      return acc
    }, {} as Record<string, typeof permissions>)

    // Create a simple permission check object
    const permissionMap = permissions.reduce((acc, permission) => {
      const key = permission.resource
        ? `${permission.module}.${permission.action}.${permission.resource}`
        : `${permission.module}.${permission.action}`
      acc[key] = true
      return acc
    }, {} as Record<string, boolean>)

    return NextResponse.json({
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        roleId: user.roleId,
        roleName: user.userRole?.name,
      },
      permissions,
      permissionsByModule,
      permissionMap,
    })
  } catch (error) {
    console.error('Error fetching user permissions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user permissions' },
      { status: 500 }
    )
  }
}
