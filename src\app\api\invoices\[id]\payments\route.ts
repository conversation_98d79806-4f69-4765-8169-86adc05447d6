import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: invoiceId } = await params
    const body = await request.json()
    const { amount, method, reference, notes, date } = body

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Payment amount is required and must be greater than 0' },
        { status: 400 }
      )
    }

    if (!method) {
      return NextResponse.json(
        { error: 'Payment method is required' },
        { status: 400 }
      )
    }

    // Check if invoice exists
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: { 
        customer: true,
        payments: true 
      },
    })

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 })
    }

    // Calculate current paid amount
    const currentPaidAmount = invoice.payments.reduce(
      (sum, payment) => sum + Number(payment.amount), 
      0
    )

    // Check if payment amount doesn't exceed remaining balance
    const remainingBalance = Number(invoice.total) - currentPaidAmount
    if (Number(amount) > remainingBalance) {
      return NextResponse.json(
        { error: `Payment amount cannot exceed remaining balance of ${remainingBalance.toFixed(3)} OMR` },
        { status: 400 }
      )
    }

    // Create payment
    const payment = await prisma.payment.create({
      data: {
        amount: Number(amount),
        method: method.toUpperCase(),
        reference: reference || null,
        notes: notes || null,
        date: date ? new Date(date) : new Date(),
        invoiceId,
        customerId: invoice.customerId || '',
      },
    })

    // Update invoice status based on total payments
    const totalPaidAmount = currentPaidAmount + Number(amount)
    let newStatus = invoice.status

    if (totalPaidAmount >= Number(invoice.total)) {
      newStatus = 'PAID'
    } else if (totalPaidAmount > 0) {
      newStatus = 'PARTIAL'
    }

    // Update invoice status if changed
    if (newStatus !== invoice.status) {
      await prisma.invoice.update({
        where: { id: invoiceId },
        data: { status: newStatus },
      })
    }

    // Return the payment with invoice details
    const updatedInvoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        customer: true,
        payments: true,
      },
    })

    return NextResponse.json({
      payment,
      invoice: updatedInvoice,
      message: 'Payment recorded successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error recording payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: invoiceId } = await params

    // Get all payments for this invoice
    const payments = await prisma.payment.findMany({
      where: { invoiceId },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            company: true,
          },
        },
      },
      orderBy: { date: 'desc' },
    })

    return NextResponse.json({ payments })

  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 