import React from "react"
import { useSession } from "next-auth/react"

// Simple admin check based on user role enum
export function useIsAdmin() {
  const { data: session } = useSession()
  
  // Check if user has ADMIN role (from the UserRole enum)
  return session?.user?.role === 'ADMIN'
}

// Check if user is admin or manager
export function useIsAdminOrManager() {
  const { data: session } = useSession()
  
  return session?.user?.role === 'ADMIN' || session?.user?.role === 'MANAGER'
}

// Simple admin guard component
export function AdminGuard({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  const isAdmin = useIsAdmin()
  
  return isAdmin ? <>{children}</> : <>{fallback}</>
}

// Manager or admin guard component
export function ManagerGuard({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  const isAdminOrManager = useIsAdminOrManager()
  
  return isAdminOrManager ? <>{children}</> : <>{fallback}</>
}
