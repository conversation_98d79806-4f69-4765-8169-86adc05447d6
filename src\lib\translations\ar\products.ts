export const products = {
  // Page titles and descriptions
  title: "المنتجات",
  dashboard: "لوحة تحكم المنتجات",
  manageProducts: "إدارة المنتجات",
  createProductTitle: "إنشاء منتج جديد",
  createProductDescription: "أضف منتج أو خدمة جديدة إلى مخزونك",
  
  // Table headers and display
  productName: "اسم المنتج",
  sku: "رمز المنتج",
  category: "الفئة", 
  type: "النوع",
  price: "السعر",
  stock: "المخزون",
  supplier: "المورد",
  
  // Product types
  physical: "منتج مادي",
  service: "خدمة",
  digitalProduct: "منتج رقمي",
  
  // Product information
  productInformation: "معلومات المنتج",
  basicProductDetails: "تفاصيل المنتج الأساسية",
  productServiceNameRequired: "اسم المنتج أو الخدمة مطلوب",
  enterProductName: "أدخل اسم المنتج",
  nameArabic: "الاسم بالعربية",
  productNameArabicPlaceholder: "أدخل اسم المنتج بالعربية",
  productSku: "رمز المنتج (SKU)",
  selectType: "اختر النوع",
  physicalProduct: "منتج مادي",
  selectCategory: "اختر الفئة",
  searchCategories: "البحث في الفئات",
  noCategoryFound: "لم يتم العثور على فئة",
  addNewCategory: "إضافة فئة جديدة",
  selectSupplier: "اختر المورد",
  searchSuppliers: "البحث في الموردين",
  noSupplierFound: "لم يتم العثور على مورد",
  addNewSupplier: "إضافة مورد جديد",
  description: "الوصف",
  productDescription: "وصف المنتج",
  
  // Actions
  addProduct: "إضافة منتج",
  editProduct: "تعديل المنتج",
  viewProduct: "عرض المنتج",
  deleteProduct: "حذف المنتج",
  confirmDelete: "هل أنت متأكد من حذف هذا المنتج؟",
  productDeleted: "تم حذف المنتج بنجاح",
  cancel: "إلغاء",
  uploadingImage: "جاري رفع الصورة...",
  creating: "جاري الإنشاء...",
  createProductButton: "إنشاء المنتج",
  
  // Product image
  productImage: "صورة المنتج",
  noImageSelected: "لم يتم اختيار صورة",
  clickToUpload: "انقر للرفع",
  dragAndDrop: "أو اسحب وأفلت",
  imageFormats: "PNG، JPG، GIF حتى 10MB",
  pleaseSelectImageFile: "يرجى اختيار ملف صورة",
  imageSizeMustBeLess: "يجب أن يكون حجم الصورة أقل من 10MB",
  imageUploadedSuccessfully: "تم رفع الصورة بنجاح",
  failedToUploadImage: "فشل في رفع الصورة",
  
  // Pricing and stock
  pricingStock: "التسعير والمخزون",
  sellingPriceRequired: "سعر البيع مطلوب",
  costPrice: "سعر التكلفة",
  currentStock: "المخزون الحالي",
  minStockAlert: "تنبيه الحد الأدنى للمخزون",
  selectUnit: "اختر الوحدة",
  searchUnits: "البحث في الوحدات",
  noUnitFound: "لم يتم العثور على وحدة",
  addNewUnit: "إضافة وحدة جديدة",
  lowStockAlert: "تنبيه انخفاض المخزون",
  
  // Search and filters
  searchProducts: "البحث في المنتجات",
  uploadExcel: "رفع ملف Excel",
  
  // Messages
  productNamePriceRequired: "اسم المنتج والسعر مطلوبان",
  productCreatedSuccessfully: "تم إنشاء المنتج بنجاح",
  failedToCreateProduct: "فشل في إنشاء المنتج",
  errorCreatingProduct: "خطأ في إنشاء المنتج",
  failedToLoadCategoriesSuppliers: "فشل في تحميل الفئات والموردين",
  
  // Category management
  categoryNameRequired: "اسم الفئة مطلوب",
  categoryCreatedSuccessfully: "تم إنشاء الفئة بنجاح",
  failedToCreateCategory: "فشل في إنشاء الفئة",
  errorCreatingCategory: "خطأ في إنشاء الفئة",
  addNewCategoryTitle: "إضافة فئة جديدة",
  addNewCategoryDescription: "أنشئ فئة جديدة لتنظيم منتجاتك",
  enterCategoryName: "أدخل اسم الفئة",
  categoryNameArabic: "اسم الفئة بالعربية",
  categoryNameArabicPlaceholder: "أدخل اسم الفئة بالعربية",
  categoryDescription: "وصف الفئة",
  categoryDescriptionPlaceholder: "أدخل وصف الفئة",
  createCategory: "إنشاء الفئة",
  
  // Supplier management
  supplierNameMobileRequired: "اسم المورد ورقم الجوال مطلوبان",
  supplierCreatedSuccessfully: "تم إنشاء المورد بنجاح",
  failedToCreateSupplier: "فشل في إنشاء المورد",
  errorCreatingSupplier: "خطأ في إنشاء المورد",
  
  // Unit management
  unitNameSymbolRequired: "اسم الوحدة ورمزها مطلوبان",
  unitCreatedSuccessfully: "تم إنشاء الوحدة بنجاح",
  failedToCreateUnit: "فشل في إنشاء الوحدة",
  errorCreatingUnit: "خطأ في إنشاء الوحدة",

  dashboardTitle: "لوحة تحكم المنتجات",
  dashboardOverview: "نظرة عامة على كتالوج المنتجات وأداء المخزون",
  viewAllProducts: "عرض جميع المنتجات",
  totalProducts: "إجمالي المنتجات",
  activeProducts: "المنتجات النشطة",
  totalRevenue: "إجمالي الإيرادات",
  lowStockItems: "منتجات منخفضة المخزون",
  fromLastMonth: "من الشهر الماضي",
  fewerThanLastMonth: "أقل من الشهر الماضي",
  monthlyPerformance: "الأداء الشهري",
  productTypeDistribution: "توزيع أنواع المنتجات",
  physicalProducts: "منتجات فعلية",
  services: "خدمات",
  digitalProducts: "منتجات رقمية",
  topPerformingProducts: "أفضل المنتجات أداءً",
  product: "المنتج",
  sold: "تم البيع",
  revenue: "الإيرادات",
  actions: "الإجراءات",
  current: "الحالي",
  minStock: "الحد الأدنى للمخزون"
}; 