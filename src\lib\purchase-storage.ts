/**
 * Shared purchase order storage utility
 */

import { getStorageItem, setStorageItem } from './storage'

export interface Purchase {
  id: string
  number: string
  date: string
  expectedDate: string
  supplier: string
  supplierData?: any
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'ORDERED' | 'RECEIVED' | 'CANCELLED'
  subtotal: number
  taxAmount: number
  total: number
  items: PurchaseItem[]
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface PurchaseItem {
  id: string
  description: string
  productId?: string
  quantity: number
  unitPrice: number
  total: number
}

const PURCHASES_STORAGE_KEY = 'purchases_data'

// Get all purchases from storage
export const getPurchases = (): Purchase[] => {
  try {
    const purchasesData = getStorageItem(PURCHASES_STORAGE_KEY)
    if (purchasesData) {
      return JSON.parse(purchasesData)
    }
    return getDefaultPurchases()
  } catch (error) {
    console.error('Error loading purchases:', error)
    return getDefaultPurchases()
  }
}

// Save purchases to storage
export const savePurchases = (purchases: Purchase[]): void => {
  try {
    setStorageItem(PURCHASES_STORAGE_KEY, JSON.stringify(purchases))
  } catch (error) {
    console.error('Error saving purchases:', error)
  }
}

// Add new purchase
export const addPurchase = (purchase: Omit<Purchase, 'id' | 'number' | 'createdAt' | 'updatedAt'>): Purchase => {
  const purchases = getPurchases()
  const newPurchase: Purchase = {
    ...purchase,
    id: generatePurchaseId(),
    number: generatePurchaseNumber(purchases.length + 1),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
  
  purchases.unshift(newPurchase) // Add to beginning for latest first
  savePurchases(purchases)
  return newPurchase
}

// Update existing purchase
export const updatePurchase = (id: string, updates: Partial<Purchase>): Purchase | null => {
  const purchases = getPurchases()
  const index = purchases.findIndex(purchase => purchase.id === id)
  
  if (index === -1) return null
  
  purchases[index] = {
    ...purchases[index],
    ...updates,
    updatedAt: new Date().toISOString(),
  }
  
  savePurchases(purchases)
  return purchases[index]
}

// Delete purchase
export const deletePurchase = (id: string): boolean => {
  const purchases = getPurchases()
  const filteredPurchases = purchases.filter(purchase => purchase.id !== id)
  
  if (filteredPurchases.length === purchases.length) return false
  
  savePurchases(filteredPurchases)
  return true
}

// Get purchase by ID
export const getPurchaseById = (id: string): Purchase | null => {
  const purchases = getPurchases()
  return purchases.find(purchase => purchase.id === id) || null
}

// Helper functions
const generatePurchaseId = (): string => {
  return `purchase_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

const generatePurchaseNumber = (sequence: number): string => {
  const year = new Date().getFullYear()
  const paddedSequence = sequence.toString().padStart(4, '0')
  return `PO-${year}-${paddedSequence}`
}

// Default purchases for demo
const getDefaultPurchases = (): Purchase[] => {
  return [
    {
      id: 'purchase_1',
      number: 'PO-2024-0001',
      date: '2024-01-15',
      expectedDate: '2024-01-25',
      supplier: 'ABC Paper Supplies',
      supplierData: {
        id: 'supplier_1',
        name: 'ABC Paper Supplies',
        mobile: '+968 9123 4567',
        email: '<EMAIL>'
      },
      status: 'RECEIVED',
      subtotal: 450.000,
      taxAmount: 22.500,
      total: 472.500,
      items: [
        {
          id: 'item_1',
          description: 'A4 Copy Paper - Premium Quality',
          productId: 'product_1',
          quantity: 100,
          unitPrice: 2.500,
          total: 250.000
        },
        {
          id: 'item_2',
          description: 'Business Cards Stock - Glossy',
          productId: 'product_2',
          quantity: 50,
          unitPrice: 4.000,
          total: 200.000
        }
      ],
      notes: 'Monthly paper stock replenishment',
      createdAt: '2024-01-15T08:00:00.000Z',
      updatedAt: '2024-01-20T10:30:00.000Z'
    },
    {
      id: 'purchase_2',
      number: 'PO-2024-0002',
      date: '2024-01-20',
      expectedDate: '2024-01-30',
      supplier: 'Tech Solutions LLC',
      supplierData: {
        id: 'supplier_2',
        name: 'Tech Solutions LLC',
        mobile: '+968 9876 5432',
        email: '<EMAIL>'
      },
      status: 'PENDING',
      subtotal: 850.000,
      taxAmount: 42.500,
      total: 892.500,
      items: [
        {
          id: 'item_3',
          description: 'Printer Cartridges - Black',
          productId: 'product_3',
          quantity: 10,
          unitPrice: 45.000,
          total: 450.000
        },
        {
          id: 'item_4',
          description: 'Printer Cartridges - Color',
          productId: 'product_4',
          quantity: 8,
          unitPrice: 50.000,
          total: 400.000
        }
      ],
      notes: 'Urgent order for printer supplies',
      createdAt: '2024-01-20T09:15:00.000Z',
      updatedAt: '2024-01-20T09:15:00.000Z'
    }
  ]
}
