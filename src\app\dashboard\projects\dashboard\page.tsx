"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  ArrowLeft,
  Folder<PERSON><PERSON>,
  Clock,
  CheckCircle,
  AlertCircle,
  Target,
  Users,
  Calendar,
  TrendingUp,
  DollarSign,
  BarChart3,
  PieChart,
  Activity
} from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from '@/lib/i18n'

interface Project {
  id: string
  name: string
  status: string
  priority: string
  progress: number
  budget?: number
  actualCost?: number
  startDate: string
  endDate?: string
  client?: {
    name: string
    company?: string
  }
  manager?: {
    name: string
  }
}

interface ProjectStats {
  total: number
  planning: number
  inProgress: number
  onHold: number
  completed: number
  cancelled: number
  totalBudget: number
  totalActualCost: number
  avgProgress: number
  overdueProjects: number
}

const statusColors = {
  PLANNING: "bg-blue-100 text-blue-800",
  IN_PROGRESS: "bg-yellow-100 text-yellow-800",
  ON_HOLD: "bg-orange-100 text-orange-800",
  COMPLETED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
}

export default function ProjectDashboardPage() {
  const router = useRouter()
  const { t } = useI18n()
  const [projects, setProjects] = useState<Project[]>([])
  const [stats, setStats] = useState<ProjectStats>({
    total: 0,
    planning: 0,
    inProgress: 0,
    onHold: 0,
    completed: 0,
    cancelled: 0,
    totalBudget: 0,
    totalActualCost: 0,
    avgProgress: 0,
    overdueProjects: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/projects')
        if (response.ok) {
          const data = await response.json()
          setProjects(data)
          
          // Calculate statistics
          const total = data.length
          const planning = data.filter((p: Project) => p.status === 'PLANNING').length
          const inProgress = data.filter((p: Project) => p.status === 'IN_PROGRESS').length
          const onHold = data.filter((p: Project) => p.status === 'ON_HOLD').length
          const completed = data.filter((p: Project) => p.status === 'COMPLETED').length
          const cancelled = data.filter((p: Project) => p.status === 'CANCELLED').length
          
          const totalBudget = data.reduce((sum: number, p: Project) => sum + (Number(p.budget) || 0), 0)
          const totalActualCost = data.reduce((sum: number, p: Project) => sum + (Number(p.actualCost) || 0), 0)
          const avgProgress = total > 0 ? data.reduce((sum: number, p: Project) => sum + p.progress, 0) / total : 0
          
          const now = new Date()
          const overdueProjects = data.filter((p: Project) => 
            p.endDate && new Date(p.endDate) < now && p.status !== 'COMPLETED'
          ).length

          setStats({
            total,
            planning,
            inProgress,
            onHold,
            completed,
            cancelled,
            totalBudget,
            totalActualCost,
            avgProgress,
            overdueProjects
          })
        }
      } catch (error) {
        console.error('Error loading projects:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  const getRecentProjects = () => {
    return projects
      .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
      .slice(0, 5)
  }

  const getOverdueProjects = () => {
    const now = new Date()
    return projects.filter(p => 
      p.endDate && new Date(p.endDate) < now && p.status !== 'COMPLETED'
    )
  }

  const getHighPriorityProjects = () => {
    return projects.filter(p => 
      (p.priority === 'HIGH' || p.priority === 'URGENT') && 
      p.status === 'IN_PROGRESS'
    )
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>{t('projects.back')}</span>
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{t('projects.dashboardTitle')}</h2>
            <p className="text-muted-foreground">
              {t('projects.dashboardOverview')}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={() => router.push('/dashboard/projects/create')}>
            <FolderOpen className="mr-2 h-4 w-4" />
            {t('projects.newProject')}
          </Button>
        </div>
      </div>

      {/* Key Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('projects.totalProjects')}</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.inProgress} {t('projects.activeProjects')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('projects.completionRate')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.avgProgress.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              {t('projects.avgAcrossAll')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('projects.totalBudget')}</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalBudget)}</div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(stats.totalActualCost)} {t('projects.spent')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('projects.overdueProjects')}</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.overdueProjects}</div>
            <p className="text-xs text-muted-foreground">
              {t('projects.requireAttention')}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Status Distribution */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChart className="h-5 w-5" />
              <span>{t('projects.statusDistribution')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">Planning</span>
                </div>
                <span className="font-medium">{stats.planning}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm">In Progress</span>
                </div>
                <span className="font-medium">{stats.inProgress}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                  <span className="text-sm">On Hold</span>
                </div>
                <span className="font-medium">{stats.onHold}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Completed</span>
                </div>
                <span className="font-medium">{stats.completed}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">Cancelled</span>
                </div>
                <span className="font-medium">{stats.cancelled}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Budget vs Actual Cost</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Budget</span>
                  <span>{formatCurrency(stats.totalBudget)}</span>
                </div>
                <Progress value={100} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Actual Cost</span>
                  <span>{formatCurrency(stats.totalActualCost)}</span>
                </div>
                <Progress 
                  value={stats.totalBudget > 0 ? (stats.totalActualCost / stats.totalBudget) * 100 : 0} 
                  className="h-2" 
                />
              </div>
              <div className="pt-2">
                <div className="text-sm text-muted-foreground">
                  Budget Utilization: {stats.totalBudget > 0 ? ((stats.totalActualCost / stats.totalBudget) * 100).toFixed(1) : 0}%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Projects and Alerts */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Recent Projects</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {getRecentProjects().map((project) => (
                <div key={project.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium"
                      onClick={() => router.push(`/dashboard/projects/${project.id}`)}
                    >
                      {project.name}
                    </Button>
                    <div className="text-sm text-muted-foreground">
                      {project.client?.name || 'No client'}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={statusColors[project.status as keyof typeof statusColors]}>
                      {project.status.replace('_', ' ')}
                    </Badge>
                    <span className="text-sm">{project.progress}%</span>
                  </div>
                </div>
              ))}
              {getRecentProjects().length === 0 && (
                <div className="text-center text-muted-foreground py-4">
                  No projects found
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <span>Attention Required</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Overdue Projects */}
              {getOverdueProjects().length > 0 && (
                <div>
                  <h4 className="font-medium text-red-600 mb-2">Overdue Projects</h4>
                  {getOverdueProjects().slice(0, 3).map((project) => (
                    <div key={project.id} className="flex items-center justify-between mb-2">
                      <Button
                        variant="link"
                        className="p-0 h-auto text-red-600"
                        onClick={() => router.push(`/dashboard/projects/${project.id}`)}
                      >
                        {project.name}
                      </Button>
                      <span className="text-xs text-red-500">
                        Due {new Date(project.endDate!).toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              )}

              {/* High Priority Projects */}
              {getHighPriorityProjects().length > 0 && (
                <div>
                  <h4 className="font-medium text-orange-600 mb-2">High Priority</h4>
                  {getHighPriorityProjects().slice(0, 3).map((project) => (
                    <div key={project.id} className="flex items-center justify-between mb-2">
                      <Button
                        variant="link"
                        className="p-0 h-auto text-orange-600"
                        onClick={() => router.push(`/dashboard/projects/${project.id}`)}
                      >
                        {project.name}
                      </Button>
                      <Badge className="bg-orange-100 text-orange-800">
                        {project.priority}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}

              {getOverdueProjects().length === 0 && getHighPriorityProjects().length === 0 && (
                <div className="text-center text-muted-foreground py-4">
                  <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                  All projects are on track!
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
