"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Edit, Trash2, MoreHorizontal, Eye, BarChart3, UserPlus, Phone, Mail, Building, Calendar, Star, TrendingUp, MessageSquare, FileText, Copy, Archive, Clock, Target } from "lucide-react"
import { formatCurrency } from "@/lib/localization"
import { useI18n } from "@/lib/i18n"
import {
  getLeads,
  createLead,
  updateLead,
  deleteLead,
  convertLeadToCustomer,
  LEAD_SOURCES,
  LEAD_STATUSES,
  type Lead
} from "@/lib/lead-storage"

// Additional constants for form options
const PRIORITIES = [
  { value: 'LOW', label: 'Low' },
  { value: 'MEDIUM', label: 'Medium' },
  { value: 'HIGH', label: 'High' },
]

const INDUSTRIES = [
  { value: 'TRADING', label: 'Trading' },
  { value: 'CONSULTING', label: 'Consulting' },
  { value: 'REAL_ESTATE', label: 'Real Estate' },
  { value: 'SERVICES', label: 'Services' },
  { value: 'MANUFACTURING', label: 'Manufacturing' },
  { value: 'DEVELOPMENT', label: 'Development' },
  { value: 'TECHNOLOGY', label: 'Technology' },
  { value: 'HEALTHCARE', label: 'Healthcare' },
  { value: 'EDUCATION', label: 'Education' },
  { value: 'HOSPITALITY', label: 'Hospitality' },
  { value: 'OTHER', label: 'Other' },
]

export default function LeadsPage() {
  const router = useRouter()
  const { t, language, direction } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [filterStatus, setFilterStatus] = useState("all")
  const [filterSource, setFilterSource] = useState("all")
  const [editingLead, setEditingLead] = useState<Lead | null>(null)
  const [leads, setLeads] = useState<Lead[]>([])
  const [users, setUsers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  })

  // Form state for controlled inputs
  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    email: '',
    mobile: '',
    phone: '',
    address: '',
    city: '',
    country: 'Oman',
    company: '',
    source: 'WEBSITE',
    status: 'NEW',
    notes: '',
    assignedToId: '',
  })

  // Translated options
  const PRIORITIES = [
    { value: 'LOW', label: t('common.low') || 'Low' },
    { value: 'MEDIUM', label: t('common.medium') || 'Medium' },
    { value: 'HIGH', label: t('common.high') || 'High' },
  ]

  const INDUSTRIES = [
    { value: 'TRADING', label: t('leads.industries.trading') || 'Trading' },
    { value: 'CONSULTING', label: t('leads.industries.consulting') || 'Consulting' },
    { value: 'REAL_ESTATE', label: t('leads.industries.realEstate') || 'Real Estate' },
    { value: 'SERVICES', label: t('leads.industries.services') || 'Services' },
    { value: 'MANUFACTURING', label: t('leads.industries.manufacturing') || 'Manufacturing' },
    { value: 'DEVELOPMENT', label: t('leads.industries.development') || 'Development' },
    { value: 'TECHNOLOGY', label: t('leads.industries.technology') || 'Technology' },
    { value: 'HEALTHCARE', label: t('leads.industries.healthcare') || 'Healthcare' },
    { value: 'EDUCATION', label: t('leads.industries.education') || 'Education' },
    { value: 'HOSPITALITY', label: t('leads.industries.hospitality') || 'Hospitality' },
    { value: 'OTHER', label: t('common.other') || 'Other' },
  ]

  // Load leads and users
  useEffect(() => {
    loadLeads()
    loadUsers()
  }, [filterStatus, filterSource, pagination.page])

  const loadLeads = async () => {
    setLoading(true)
    try {
      const params = {
        search: searchTerm || undefined,
        status: filterStatus === 'all' ? undefined : filterStatus,
        source: filterSource === 'all' ? undefined : filterSource,
        page: pagination.page,
        limit: pagination.limit,
      }
      const result = await getLeads(params)
      setLeads(result.leads)
      setPagination(result.pagination)
    } catch (error) {
      console.error('Error loading leads:', error)
      setLeads([])
    } finally {
      setLoading(false)
    }
  }

  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users')
      if (response.ok) {
        const data = await response.json()
        setUsers(data.users || [])
      }
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  const filteredLeads = leads.filter(lead => {
    if (!searchTerm) return true
    const search = searchTerm.toLowerCase()
    return (
      lead.name.toLowerCase().includes(search) ||
      lead.nameAr?.toLowerCase().includes(search) ||
      lead.company?.toLowerCase().includes(search) ||
      lead.email?.toLowerCase().includes(search) ||
      lead.mobile.includes(search)
    )
  })

  const handleViewLead = (lead: Lead) => {
    router.push(`/dashboard/leads/${lead.id}`)
  }

  const handleEditLead = (lead: Lead) => {
    setEditingLead(lead)
    setFormData({
      name: lead.name || '',
      nameAr: lead.nameAr || '',
      email: lead.email || '',
      mobile: lead.mobile || '',
      phone: lead.phone || '',
      address: lead.address || '',
      city: lead.city || '',
      country: lead.country || 'Oman',
      company: lead.company || '',
      source: lead.source || 'WEBSITE',
      status: lead.status || 'NEW',
      notes: lead.notes || '',
      assignedToId: lead.assignedToId || '',
    })
    setIsDialogOpen(true)
  }

  const handleDeleteLead = async (lead: Lead) => {
    if (confirm(`Are you sure you want to delete ${lead.name}?`)) {
      try {
        await deleteLead(lead.id)
        loadLeads()
        alert('Lead deleted successfully!')
      } catch (error) {
        console.error('Delete error:', error)
        alert(`Failed to delete lead: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }

  const handleConvertToCustomer = async (lead: Lead) => {
    if (confirm(`Convert ${lead.name} to customer?`)) {
      try {
        const result = await convertLeadToCustomer(lead.id)
        if (result) {
          loadLeads()
          alert('Lead converted to customer successfully!')
          router.push(`/dashboard/customers/${result.customer.id}`)
        }
      } catch (error) {
        console.error('Convert error:', error)
        alert(`Failed to convert lead: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }

  const handleDuplicateLead = (lead: Lead) => {
    const duplicatedLead = { ...lead, name: `${lead.name} (Copy)`, nameAr: `${lead.nameAr} (Copy)` }
    setEditingLead(duplicatedLead)
    setIsDialogOpen(true)
  }

  const handleArchiveLead = (lead: Lead) => {
    if (confirm(`Are you sure you want to ${lead.status === 'LOST' ? 'reactivate' : 'archive'} ${lead.name}?`)) {
      console.log(`${lead.status === 'LOST' ? 'Reactivating' : 'Archiving'} lead:`, lead.name)
      alert('Archive/Reactivate functionality would be implemented here')
    }
  }

  const handleAddActivity = (lead: Lead) => {
    console.log('Adding activity for lead:', lead.name)
    alert('Add activity functionality would be implemented here')
    // This would open an activity dialog or redirect to lead details
  }

  const handleScheduleFollowUp = (lead: Lead) => {
    console.log('Scheduling follow-up for lead:', lead.name)
    alert('Schedule follow-up functionality would be implemented here')
    // This would open a follow-up scheduling dialog
  }

  const handleCreateQuotation = (lead: Lead) => {
    console.log('Creating quotation for lead:', lead.name)
    alert('Create quotation functionality would be implemented here')
    // This would redirect to quotation creation with lead pre-filled
  }

  const handleSendEmail = (lead: Lead) => {
    window.open(`mailto:${lead.email}?subject=Follow up - ${lead.company}&body=Dear ${lead.name},`)
  }

  const handleCallLead = (lead: Lead) => {
    window.open(`tel:${lead.phone}`)
  }

  const handleChangeStatus = (lead: Lead, newStatus: string) => {
    console.log(`Changing lead ${lead.name} status to:`, newStatus)
    alert(`Change status to ${newStatus} functionality would be implemented here`)
  }

  const handleSaveLead = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const leadData = {
        name: formData.name,
        nameAr: formData.nameAr,
        email: formData.email,
        mobile: formData.mobile,
        phone: formData.phone,
        address: formData.address,
        city: formData.city,
        country: formData.country,
        company: formData.company,
        source: formData.source,
        status: formData.status,
        notes: formData.notes,
        assignedToId: formData.assignedToId || null,
      }

      if (editingLead) {
        await updateLead(editingLead.id, leadData)
        alert('Lead updated successfully!')
      } else {
        await createLead(leadData)
        alert('Lead created successfully!')
      }

      setIsDialogOpen(false)
      setEditingLead(null)
      resetForm()
      loadLeads()
    } catch (error) {
      console.error('Save error:', error)
      alert(`Failed to save lead: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setSaving(false)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      nameAr: '',
      email: '',
      mobile: '',
      phone: '',
      address: '',
      city: '',
      country: 'Oman',
      company: '',
      source: 'WEBSITE',
      status: 'NEW',
      notes: '',
      assignedToId: '',
    })
  }

  const handleCloseDialog = () => {
    setIsDialogOpen(false)
    setEditingLead(null)
    resetForm()
  }

  const getStatusColor = (status: string) => {
    const colors = {
      NEW: "bg-blue-100 text-blue-800",
      QUALIFIED: "bg-green-100 text-green-800",
      PROPOSAL: "bg-yellow-100 text-yellow-800",
      NEGOTIATION: "bg-orange-100 text-orange-800",
      WON: "bg-emerald-100 text-emerald-800",
      LOST: "bg-red-100 text-red-800"
    }
    return colors[status] || "bg-gray-100 text-gray-800"
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      LOW: "bg-gray-100 text-gray-800",
      MEDIUM: "bg-yellow-100 text-yellow-800",
      HIGH: "bg-red-100 text-red-800"
    }
    return colors[priority] || "bg-gray-100 text-gray-800"
  }

  return (
    <div className={`flex-1 space-y-4 p-8 pt-6 ${direction === 'rtl' ? 'font-arabic' : ''}`} dir={direction}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">{t('leads.title') || 'Leads'}</h2>
          <p className="text-muted-foreground">
            {t('leads.manageDescription') || 'Manage your potential customers and sales opportunities'}
          </p>
        </div>
        <div className={`flex items-center ${direction === 'rtl' ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
          <Button variant="outline" onClick={() => router.push('/dashboard/leads/dashboard')}>
            <BarChart3 className={`${direction === 'rtl' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
            {t('leads.dashboard') || 'Dashboard'}
          </Button>
          <Button onClick={() => router.push('/dashboard/leads/create')}>
            <Plus className={`${direction === 'rtl' ? 'ml-2' : 'mr-2'} h-4 w-4`} />
            {t('leads.addLead') || 'Add Lead'}
          </Button>
          <Dialog open={isDialogOpen} onOpenChange={handleCloseDialog}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>{editingLead ? 'Edit Lead' : 'Add New Lead'}</DialogTitle>
                <DialogDescription>
                  {editingLead ? 'Update the lead details below.' : 'Enter the lead information below.'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSaveLead}>
                <div className="grid gap-6 py-4">
                {/* Two Column Layout */}
                <div className="grid grid-cols-2 gap-6">
                  {/* Left Column */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">{t('common.name') || 'Name'} *</Label>
                      <Input
                        id="name"
                        name="name"
                        placeholder={t('leads.leadName') || 'Lead name'}
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="nameAr">{t('leads.leadNameAr') || 'Name (Arabic)'}</Label>
                      <Input
                        id="nameAr"
                        name="nameAr"
                        placeholder="اسم العميل المحتمل"
                        dir="rtl"
                        value={formData.nameAr}
                        onChange={(e) => setFormData(prev => ({ ...prev, nameAr: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="company">{t('common.company') || 'Company'}</Label>
                      <Input
                        id="company"
                        name="company"
                        placeholder={t('common.company') || 'Company name'}
                        value={formData.company}
                        onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">{t('common.email') || 'Email'}</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="mobile">{t('common.mobile') || 'Mobile'} *</Label>
                      <Input
                        id="mobile"
                        name="mobile"
                        placeholder="+968 9123 4567"
                        value={formData.mobile}
                        onChange={(e) => setFormData(prev => ({ ...prev, mobile: e.target.value }))}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">{t('common.phone') || 'Phone'}</Label>
                      <Input
                        id="phone"
                        name="phone"
                        placeholder="+968 2456 7890"
                        value={formData.phone}
                        onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                      />
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="source">{t('leads.leadSource') || 'Lead Source'}</Label>
                      <Select value={formData.source} onValueChange={(value) => setFormData(prev => ({ ...prev, source: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder={t('common.select') + ' ' + (t('common.source') || 'source')} />
                        </SelectTrigger>
                        <SelectContent>
                          {LEAD_SOURCES.map(source => (
                            <SelectItem key={source.value} value={source.value}>{source.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="status">{t('common.status') || 'Status'}</Label>
                      <Select value={formData.status} onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder={t('common.select') + ' ' + (t('common.status') || 'status')} />
                        </SelectTrigger>
                        <SelectContent>
                          {LEAD_STATUSES.map(status => (
                            <SelectItem key={status.value} value={status.value}>{status.label}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="city">{t('common.city') || 'City'}</Label>
                      <Input
                        id="city"
                        name="city"
                        placeholder={t('common.city') || 'City'}
                        value={formData.city}
                        onChange={(e) => setFormData(prev => ({ ...prev, city: e.target.value }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="country">{t('common.country') || 'Country'}</Label>
                      <Input
                        id="country"
                        name="country"
                        placeholder={t('common.country') || 'Country'}
                        value={formData.country}
                        onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                      />
                    </div>
                  </div>
                </div>

                {/* Full Width Fields */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="address">{t('common.address') || 'Address'}</Label>
                    <Input
                      id="address"
                      name="address"
                      placeholder={t('common.address') || 'Full address'}
                      value={formData.address}
                      onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">{t('common.notes') || 'Notes'}</Label>
                    <Textarea
                      id="notes"
                      name="notes"
                      placeholder={t('common.notes') || 'Lead notes and comments'}
                      rows={3}
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    />
                  </div>
                </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" type="button" onClick={handleCloseDialog}>
                    {t('common.cancel') || 'Cancel'}
                  </Button>
                  <Button type="submit" disabled={saving}>
                    {saving ? (t('common.loading') || 'Saving...') : (editingLead ? (t('leads.updateLead') || 'Update Lead') : (t('common.save') || 'Save Lead'))}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Lead Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="rounded-lg border bg-white dark:bg-slate-800 p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold">{pagination.total}</div>
              <div className="text-sm text-muted-foreground">{t('leads.totalLeads') || 'Total Leads'}</div>
            </div>
            <Target className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        <div className="rounded-lg border bg-white dark:bg-slate-800 p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold">{leads.filter(l => ['QUALIFIED', 'PROPOSAL', 'NEGOTIATION'].includes(l.status)).length}</div>
              <div className="text-sm text-muted-foreground">{t('leads.activeLeads') || 'Active Leads'}</div>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
        </div>
        <div className="rounded-lg border bg-white dark:bg-slate-800 p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold">{leads.filter(l => l.status === 'CONVERTED').length}</div>
              <div className="text-sm text-muted-foreground">{t('leads.convertedLeads') || 'Converted'}</div>
            </div>
            <UserPlus className="h-8 w-8 text-emerald-500" />
          </div>
        </div>
        <div className="rounded-lg border bg-white dark:bg-slate-800 p-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold">{leads.filter(l => l.status === 'LOST').length}</div>
              <div className="text-sm text-muted-foreground">{t('leads.lostLeads') || 'Lost'}</div>
            </div>
            <Archive className="h-8 w-8 text-red-500" />
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('leads.searchPlaceholder') || 'Search leads, company, email, or phone...'}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Select value={filterStatus} onValueChange={setFilterStatus}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('common.filterBy') + ' ' + (t('common.status') || 'Status')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('common.allStatuses') || 'All Status'}</SelectItem>
            {LEAD_STATUSES.map(status => (
              <SelectItem key={status.value} value={status.value}>{status.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={filterSource} onValueChange={setFilterSource}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('common.filterBy') + ' ' + (t('common.source') || 'Source')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('common.allSources') || 'All Sources'}</SelectItem>
            {LEAD_SOURCES.map(source => (
              <SelectItem key={source.value} value={source.value}>{source.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('leads.leadName') || 'Lead'}</TableHead>
              <TableHead>{t('common.contact') || 'Contact'}</TableHead>
              <TableHead>{t('common.source') || 'Source'}</TableHead>
              <TableHead>{t('common.status') || 'Status'}</TableHead>
              <TableHead>{t('common.assignedTo') || 'Assigned To'}</TableHead>
              <TableHead>{t('common.created') || 'Created'}</TableHead>
              <TableHead className="text-right">{t('common.actions') || 'Actions'}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    <span>{t('common.loading') || 'Loading leads...'}</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : filteredLeads.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <div className="text-muted-foreground">
                    {searchTerm ? (t('leads.noLeadsFound') || 'No leads found matching your search.') : (t('leads.createFirstLead') || 'No leads found. Create your first lead!')}
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              filteredLeads.map((lead) => (
              <TableRow key={lead.id}>
                <TableCell>
                  <div>
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium"
                      onClick={() => handleViewLead(lead)}
                    >
                      {lead.name}
                    </Button>
                    <div className="text-sm text-muted-foreground">{lead.nameAr}</div>
                    <div className="text-sm text-muted-foreground">{lead.company}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center text-sm">
                      <Phone className="mr-1 h-3 w-3" />
                      {lead.mobile}
                    </div>
                    {lead.email && (
                      <div className="flex items-center text-sm">
                        <Mail className="mr-1 h-3 w-3" />
                        {lead.email}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">{lead.source}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {lead.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-sm">
                  {lead.assignedTo ? lead.assignedTo.name : 'Unassigned'}
                </TableCell>
                <TableCell className="text-sm">
                  {new Date(lead.createdAt).toLocaleDateString()}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleViewLead(lead)}>
                        <Eye className="mr-2 h-4 w-4" />
                        {t('common.view') || 'View Details'}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditLead(lead)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('leads.editLead') || 'Edit Lead'}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {lead.status !== 'CONVERTED' && (
                        <DropdownMenuItem onClick={() => handleConvertToCustomer(lead)}>
                          <UserPlus className="mr-2 h-4 w-4" />
                          {t('leads.convertToCustomer') || 'Convert to Customer'}
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteLead(lead)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('leads.deleteLead') || 'Delete Lead'}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
