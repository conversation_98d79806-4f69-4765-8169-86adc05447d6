"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { useI18n } from "@/lib/i18n"

interface RTLWrapperProps {
  children: React.ReactNode
  className?: string
  asChild?: boolean
  forceDirection?: "ltr" | "rtl"
  numericContent?: boolean // For content that should always be LTR (numbers, currencies)
}

export function RTLWrapper({ 
  children, 
  className, 
  forceDirection,
  numericContent = false
}: Omit<RTLWrapperProps, 'asChild'>) {
  const { direction } = useI18n()
  
  const effectiveDirection = forceDirection || direction
  const isRTL = effectiveDirection === 'rtl'
  
  const wrapperClasses = cn(
    // Base direction classes
    isRTL ? 'rtl' : 'ltr',
    
    // Font family based on direction
    isRTL ? 'font-arabic' : 'font-inter',
    
    // Numeric content should remain LTR
    numericContent && 'numeric-content',
    
    className
  )

  return (
    <div 
      dir={numericContent ? 'ltr' : effectiveDirection} 
      className={wrapperClasses}
    >
      {children}
    </div>
  )
}

// Specialized components for common use cases
export function RTLText({ children, className, ...props }: React.ComponentProps<"span">) {
  const { direction } = useI18n()
  
  return (
    <span 
      dir={direction}
      className={cn(
        direction === 'rtl' ? 'font-arabic text-right' : 'text-left',
        className
      )}
      {...props}
    >
      {children}
    </span>
  )
}

export function RTLNumeric({ children, className, ...props }: React.ComponentProps<"span">) {
  return (
    <span 
      dir="ltr"
      className={cn("numeric-content text-left", className)}
      {...props}
    >
      {children}
    </span>
  )
}

export function RTLContainer({ children, className, ...props }: React.ComponentProps<"div">) {
  const { direction } = useI18n()
  
  return (
    <div 
      dir={direction}
      className={cn(
        direction === 'rtl' ? 'font-arabic' : '',
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Hook for RTL-aware class names
export function useRTLClasses() {
  const { direction } = useI18n()
  const isRTL = direction === 'rtl'
  
  return {
    direction,
    isRTL,
    textAlign: isRTL ? 'text-right' : 'text-left',
    marginLeft: isRTL ? 'mr' : 'ml',
    marginRight: isRTL ? 'ml' : 'mr',
    paddingLeft: isRTL ? 'pr' : 'pl',
    paddingRight: isRTL ? 'pl' : 'pr',
    borderLeft: isRTL ? 'border-r' : 'border-l',
    borderRight: isRTL ? 'border-l' : 'border-r',
    roundedLeft: isRTL ? 'rounded-r' : 'rounded-l',
    roundedRight: isRTL ? 'rounded-l' : 'rounded-r',
    flexRow: isRTL ? 'flex-row-reverse' : 'flex-row',
    spaceX: isRTL ? 'space-x-reverse' : '',
    fontFamily: isRTL ? 'font-arabic' : '',
    
    // Helper function to get directional class
    dir: (ltrClass: string, rtlClass: string) => isRTL ? rtlClass : ltrClass,
    
    // Helper function for conditional RTL classes
    rtl: (classes: string) => isRTL ? classes : '',
    ltr: (classes: string) => !isRTL ? classes : ''
  }
} 